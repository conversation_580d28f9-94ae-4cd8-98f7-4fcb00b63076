package com.wnkx.biz.job.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.enums.CommissionUnitEnum;
import com.ruoyi.common.core.enums.ModelChangeTypeEnum;
import com.ruoyi.common.core.enums.ModelCooperationEnum;
import com.ruoyi.common.core.enums.ModelVideoResourceTypeEnum;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.entity.Model;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelCountAnalysisInfo;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelDataStatisticsDay;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelDataStatisticsMonth;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelOrderRankingInfo;
import com.ruoyi.system.api.domain.entity.biz.model.ModelAddedSnapShoot;
import com.ruoyi.system.api.domain.entity.biz.model.ModelChangeRecord;
import com.ruoyi.system.api.domain.entity.biz.model.ModelOustSnapShoot;
import com.ruoyi.system.api.domain.entity.biz.model.ModelVideoResource;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import com.ruoyi.system.api.domain.vo.order.OrderModelTimeoutVO;
import com.wnkx.biz.model.service.*;
import com.wnkx.biz.remote.RemoteService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/13 10:09
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ModelHandler {
    private final RemoteService remoteService;
    private final IModelService modelService;
    private final RedisService redisService;
    private final ModelChangeRecordService modelChangeRecordService;
    private final ModelDataStatisticsDayService modelDataStatisticsDayService;
    private final ModelDataStatisticsMonthService modelDataStatisticsMonthService;
    private final ModelAddedSnapShootService modelAddedSnapShootService;
    private final ModelOustSnapShootService modelOustSnapShootService;
    private final IModelVideoResourceService modelVideoResourceService;
    private final ModelDataTableService modelDataTableService;

    /**
     * 更新模特超时率、售后率
     */
    @XxlJob("updateModelOvertimeRateAndAfterSaleRateHandler")
    public void updateModelOvertimeRateAndAfterSaleRateHandler() {
        XxlJobHelper.log("任务updateModelOvertimeRateAndAfterSaleRateHandler执行中.....");

        List<OrderModelTimeoutVO> modelOvertimeRateAndAfterSaleRate = remoteService.getModelOvertimeRateAndAfterSaleRate();
        if (CollUtil.isEmpty(modelOvertimeRateAndAfterSaleRate)) {
            XxlJobHelper.log("模特订单信息不存在或调用失败.....");
            return;
        }
        List<Model> models = new ArrayList<>();
        for (OrderModelTimeoutVO orderModelTimeoutVO : modelOvertimeRateAndAfterSaleRate) {
            Model model = BeanUtil.copyProperties(orderModelTimeoutVO, Model.class);
            model.setId(orderModelTimeoutVO.getModelId());

            models.add(model);
        }

        modelService.updateBatchById(models);
    }

    /**
     * 模特行程时间开始与结束更新模特状态
     */
    @XxlJob("updateModelTravelStatusHandler")
    public void updateModelTravelStatusHandler() {
        XxlJobHelper.log("任务updateModelTravelStatusHandler执行中.....");

        modelService.updateModelTravelStatus();
    }

    /**
     * 删除模特每日申请单量
     */
    @XxlJob("deleteModelDailyApplyOrderCountHandler")
    public void deleteModelDailyApplyOrderCountHandler() {
        XxlJobHelper.log("任务deleteModelDailyApplyOrderCountHandler执行中.....");

        redisService.deleteKeysByPrefix(CacheConstants.MODEL_APPLY_KEY);
    }

    /**
     * （一次性）初始化model.status_time
     */
    @XxlJob("initModelStatusTimeHandler")
    public void initModelStatusTimeHandler() {
        XxlJobHelper.log("任务initModelStatusTimeHandler执行中.....");

        List<Model> list = modelService.list();

        if (CollUtil.isEmpty(list)) {
            return;
        }

        List<Long> modelIds = list.stream().map(Model::getId).collect(Collectors.toList());
        List<ModelChangeRecord> records = modelChangeRecordService.lambdaQuery()
                .in(ModelChangeRecord::getModelId, modelIds)
                .list();

        if (CollUtil.isEmpty(records)) {
            return;
        }

        Map<Long, List<ModelChangeRecord>> recordMap = records.stream().filter(record -> ModelChangeTypeEnum.CHANGE_STATUS.getCode().equals(record.getOperateType())).collect(Collectors.groupingBy(ModelChangeRecord::getModelId));

        for (Model model : list) {
            List<ModelChangeRecord> modelChangeRecords = recordMap.get(model.getId());
            if (CollUtil.isEmpty(modelChangeRecords)) {
                continue;
            }
            modelChangeRecords.sort(Comparator.comparing(ModelChangeRecord::getOperateTime).reversed());
            model.setStatusTime(modelChangeRecords.get(0).getOperateTime());
        }

        modelService.updateBatchById(list);
    }

    /**
     * 模特数据统计-按天统计模特数据
     */
    @XxlJob("modelDataStatisticsDayHandler")
    public void modelDataStatisticsDayHandler() {
        XxlJobHelper.log("任务modelDataStatisticsDayHandler执行中.....");

        DateTime yesterday = DateUtil.yesterday();
        ModelDataStatisticsDay modelDataStatisticsDay = new ModelDataStatisticsDay();
        modelDataStatisticsDay.setWriteTimeBegin(DateUtil.parse(DateUtil.format(DateUtil.beginOfDay(yesterday), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN));
        modelDataStatisticsDay.setWriteTimeEnd(DateUtil.parse(DateUtil.format(DateUtil.endOfDay(yesterday), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN));


        //  获取昨天最终修改为 暂停/取消合作 的模特ID
        List<Long> yesterdayFinalOustModelIds = modelChangeRecordService.getFinalOustModelIdsByDate("'%Y-%m-%d'", DateUtil.format(yesterday, DatePattern.NORM_DATE_PATTERN));
        modelDataStatisticsDay.setModelOustNumber(Convert.toLong(yesterdayFinalOustModelIds.size()));

        //  获取昨天新增模特数
        Long modelNewNumber = modelService.lambdaQuery()
                .between(Model::getCreateTime, DateUtil.beginOfDay(yesterday), DateUtil.endOfDay(yesterday))
                .count();
        modelDataStatisticsDay.setModelNewNumber(modelNewNumber);
        modelDataStatisticsDay.setModelOustNumber(modelDataStatisticsDay.getModelOustNumber() == null ? 0L : modelDataStatisticsDay.getModelOustNumber());


        modelDataStatisticsDayService.saveOrUpdate(modelDataStatisticsDay, new LambdaUpdateWrapper<ModelDataStatisticsDay>().eq(ModelDataStatisticsDay::getWriteTimeBegin, modelDataStatisticsDay.getWriteTimeBegin()).eq(ModelDataStatisticsDay::getWriteTimeEnd, modelDataStatisticsDay.getWriteTimeEnd()));
        XxlJobHelper.log("任务modelDataStatisticsDayHandler执行完成.....");
    }

    /**
     * 模特数据统计-按月统计模特数据
     */
    @XxlJob("modelDataStatisticsMonthHandler")
    public void modelDataStatisticsMonthHandler() {
        XxlJobHelper.log("任务modelDataStatisticsMonthHandler执行中.....");

        DateTime lastMonth = DateUtil.lastMonth();
        ModelDataStatisticsMonth modelDataStatisticsMonth = new ModelDataStatisticsMonth();
        modelDataStatisticsMonth.setWriteTimeBegin(DateUtil.parse(DateUtil.format(DateUtil.beginOfMonth(lastMonth), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN));
        modelDataStatisticsMonth.setWriteTimeEnd(DateUtil.parse(DateUtil.format(DateUtil.endOfMonth(lastMonth), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN));

        //  获取上月淘汰模特数量
        List<Long> eliminatedLastMonthModelIds = modelChangeRecordService.getFinalOustModelIdsByDate("'%Y-%m'", DateUtil.format(lastMonth, DatePattern.NORM_MONTH_PATTERN));
        ModelCountAnalysisInfo oustModelCountAnalysisInfo = new ModelCountAnalysisInfo();
        if (CollUtil.isNotEmpty(eliminatedLastMonthModelIds)) {
            modelDataStatisticsMonth.setModelOustNumber(Convert.toLong(eliminatedLastMonthModelIds.size()));

            //  记录模特每月淘汰模特分析
            List<ModelOustSnapShoot> modelOustSnapShoots = modelOustSnapShootService.lambdaQuery()
                    .in(ModelOustSnapShoot::getModelId, eliminatedLastMonthModelIds)
                    .between(ModelOustSnapShoot::getCreateTime, DateUtil.beginOfMonth(lastMonth), DateUtil.endOfMonth(lastMonth))
                    .list();

            if (CollUtil.isNotEmpty(modelOustSnapShoots)) {
                for (ModelOustSnapShoot modelOustSnapShoot : modelOustSnapShoots) {
                    modelOustSnapShoot.setCommission(modelOustSnapShoot.getCommission().multiply(CommissionUnitEnum.getByUnit(modelOustSnapShoot.getCommissionUnit()).getToUSDRate()).setScale(2, RoundingMode.DOWN));
                }
                oustModelCountAnalysisInfo.setModelNumber(Convert.toLong(modelOustSnapShoots.size()));
                oustModelCountAnalysisInfo.setModelAverageCommission(modelOustSnapShoots.stream()
                        .map(ModelOustSnapShoot::getCommission)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(new BigDecimal(modelOustSnapShoots.size()), 2, RoundingMode.DOWN));

                PieChartVO pieChartVO1 = new PieChartVO();
                pieChartVO1.setLabel(ModelCooperationEnum.ORDINARY.getLabel());
                pieChartVO1.setCount(modelOustSnapShoots.stream().filter(model -> ModelCooperationEnum.ORDINARY.getCode().equals(model.getCooperation())).count());
                pieChartVO1.setRatio(BigDecimal.valueOf(modelOustSnapShoots.stream()
                                .filter(model -> ModelCooperationEnum.ORDINARY.getCode().equals(model.getCooperation()))
                                .count())
                        .divide(BigDecimal.valueOf(modelOustSnapShoots.size()), 2, RoundingMode.HALF_UP));

                // PieChartVO pieChartVO2 = new PieChartVO();
                // pieChartVO2.setLabel(ModelCooperationEnum.MODERATE.getLabel());
                // pieChartVO2.setCount(modelOustSnapShoots.stream().filter(model -> ModelCooperationEnum.MODERATE.getCode().equals(model.getCooperation())).count());
                // pieChartVO2.setRatio(BigDecimal.valueOf(modelOustSnapShoots.stream()
                //                 .filter(model -> ModelCooperationEnum.MODERATE.getCode().equals(model.getCooperation()))
                //                 .count())
                //         .divide(BigDecimal.valueOf(modelOustSnapShoots.size()), 2, RoundingMode.HALF_UP));

                PieChartVO pieChartVO3 = new PieChartVO();
                pieChartVO3.setLabel(ModelCooperationEnum.QUALITY.getLabel());
                pieChartVO3.setCount(modelOustSnapShoots.stream().filter(model -> ModelCooperationEnum.QUALITY.getCode().equals(model.getCooperation())).count());
                pieChartVO3.setRatio(BigDecimal.valueOf(modelOustSnapShoots.stream()
                                .filter(model -> ModelCooperationEnum.QUALITY.getCode().equals(model.getCooperation()))
                                .count())
                        .divide(BigDecimal.valueOf(modelOustSnapShoots.size()), 2, RoundingMode.HALF_UP));

                oustModelCountAnalysisInfo.setPieChartVOS(List.of(pieChartVO1, pieChartVO3));
            } else {
                oustModelCountAnalysisInfo.setModelNumber(0L);
                oustModelCountAnalysisInfo.setModelAverageCommission(BigDecimal.ZERO);
                oustModelCountAnalysisInfo.setPieChartVOS(List.of(
                        PieChartVO.init(ModelCooperationEnum.ORDINARY.getLabel()),
                        // PieChartVO.init(ModelCooperationEnum.MODERATE.getLabel()),
                        PieChartVO.init(ModelCooperationEnum.QUALITY.getLabel())
                ));
            }
        } else {
            oustModelCountAnalysisInfo.setModelNumber(0L);
            oustModelCountAnalysisInfo.setModelAverageCommission(BigDecimal.ZERO);
            oustModelCountAnalysisInfo.setPieChartVOS(List.of(
                    PieChartVO.init(ModelCooperationEnum.ORDINARY.getLabel()),
                    // PieChartVO.init(ModelCooperationEnum.MODERATE.getLabel()),
                    PieChartVO.init(ModelCooperationEnum.QUALITY.getLabel())
            ));
        }

        //  获取上月新增模特数
        //  记录模特每月新增模特分析
        List<Model> modelList = modelService.lambdaQuery()
                .between(Model::getCreateTime, DateUtil.beginOfMonth(lastMonth), DateUtil.endOfMonth(lastMonth))
                .list();

        ModelCountAnalysisInfo newModelCountAnalysisInfo = new ModelCountAnalysisInfo();
        if (CollUtil.isNotEmpty(modelList)) {
            List<Long> modelIds = modelList.stream().map(Model::getId).collect(Collectors.toList());
            List<ModelAddedSnapShoot> modelAddedSnapShoots = modelAddedSnapShootService.lambdaQuery()
                    .in(ModelAddedSnapShoot::getModelId, modelIds)
                    .between(ModelAddedSnapShoot::getCreateTime, DateUtil.beginOfMonth(lastMonth), DateUtil.endOfMonth(lastMonth))
                    .list();
            if (CollUtil.isNotEmpty(modelAddedSnapShoots)) {
                for (ModelAddedSnapShoot modelAddedSnapShoot : modelAddedSnapShoots) {
                    modelAddedSnapShoot.setCommission(modelAddedSnapShoot.getCommission().multiply(CommissionUnitEnum.getByUnit(modelAddedSnapShoot.getCommissionUnit()).getToUSDRate()).setScale(2, RoundingMode.DOWN));
                }
                newModelCountAnalysisInfo.setModelNumber(Convert.toLong(modelAddedSnapShoots.size()));
                newModelCountAnalysisInfo.setModelAverageCommission(modelAddedSnapShoots.stream()
                        .map(ModelAddedSnapShoot::getCommission)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(new BigDecimal(modelAddedSnapShoots.size()), 2, RoundingMode.DOWN));

                PieChartVO pieChartVO1 = new PieChartVO();
                pieChartVO1.setLabel(ModelCooperationEnum.ORDINARY.getLabel());
                pieChartVO1.setCount(modelAddedSnapShoots.stream().filter(model -> ModelCooperationEnum.ORDINARY.getCode().equals(model.getCooperation())).count());
                pieChartVO1.setRatio(BigDecimal.valueOf(modelAddedSnapShoots.stream()
                                .filter(model -> ModelCooperationEnum.ORDINARY.getCode().equals(model.getCooperation()))
                                .count())
                        .divide(BigDecimal.valueOf(modelAddedSnapShoots.size()), 2, RoundingMode.HALF_UP));

                // PieChartVO pieChartVO2 = new PieChartVO();
                // pieChartVO2.setLabel(ModelCooperationEnum.MODERATE.getLabel());
                // pieChartVO2.setCount(modelAddedSnapShoots.stream().filter(model -> ModelCooperationEnum.MODERATE.getCode().equals(model.getCooperation())).count());
                // pieChartVO2.setRatio(BigDecimal.valueOf(modelAddedSnapShoots.stream()
                //                 .filter(model -> ModelCooperationEnum.MODERATE.getCode().equals(model.getCooperation()))
                //                 .count())
                //         .divide(BigDecimal.valueOf(modelAddedSnapShoots.size()), 2, RoundingMode.HALF_UP));

                PieChartVO pieChartVO3 = new PieChartVO();
                pieChartVO3.setLabel(ModelCooperationEnum.QUALITY.getLabel());
                pieChartVO3.setCount(modelAddedSnapShoots.stream().filter(model -> ModelCooperationEnum.QUALITY.getCode().equals(model.getCooperation())).count());
                pieChartVO3.setRatio(BigDecimal.valueOf(modelAddedSnapShoots.stream()
                                .filter(model -> ModelCooperationEnum.QUALITY.getCode().equals(model.getCooperation()))
                                .count())
                        .divide(BigDecimal.valueOf(modelAddedSnapShoots.size()), 2, RoundingMode.HALF_UP));

                newModelCountAnalysisInfo.setPieChartVOS(List.of(pieChartVO1, pieChartVO3));
            } else {
                newModelCountAnalysisInfo.setModelNumber(0L);
                newModelCountAnalysisInfo.setModelAverageCommission(BigDecimal.ZERO);
                newModelCountAnalysisInfo.setPieChartVOS(List.of(
                        PieChartVO.init(ModelCooperationEnum.ORDINARY.getLabel()),
                        // PieChartVO.init(ModelCooperationEnum.MODERATE.getLabel()),
                        PieChartVO.init(ModelCooperationEnum.QUALITY.getLabel())
                ));
            }
        } else {
            newModelCountAnalysisInfo.setModelNumber(0L);
            newModelCountAnalysisInfo.setModelAverageCommission(BigDecimal.ZERO);
            newModelCountAnalysisInfo.setPieChartVOS(List.of(
                    PieChartVO.init(ModelCooperationEnum.ORDINARY.getLabel()),
                    // PieChartVO.init(ModelCooperationEnum.MODERATE.getLabel()),
                    PieChartVO.init(ModelCooperationEnum.QUALITY.getLabel())
            ));
        }

        //  获取上月模特订单排行榜
        List<ModelOrderRankingInfo> modelOrderRanking = remoteService.getModelOrderRanking(DateUtil.format(lastMonth, DatePattern.NORM_MONTH_PATTERN));
        if (CollUtil.isNotEmpty(modelOrderRanking)) {
            Set<Long> modelIds = modelOrderRanking.stream().map(ModelOrderRankingInfo::getModelId).collect(Collectors.toSet());
            List<Model> modelList2 = modelService.listByIds(modelIds);
            Map<Long, Model> modelMap = modelList2.stream().collect(Collectors.toMap(Model::getId, Function.identity()));
            for (ModelOrderRankingInfo modelOrderRankingInfo : modelOrderRanking) {
                modelOrderRankingInfo.setModelName(modelMap.getOrDefault(modelOrderRankingInfo.getModelId(), new Model()).getName());
                modelOrderRankingInfo.setModelAvatar(modelMap.getOrDefault(modelOrderRankingInfo.getModelId(), new Model()).getModelPic());
                modelOrderRankingInfo.setModelCooperationScore(modelMap.getOrDefault(modelOrderRankingInfo.getModelId(), new Model()).getCooperationScore());
            }
            modelOrderRanking.sort(Comparator.comparing(ModelOrderRankingInfo::getOrderCount).reversed());
        } else {
            modelOrderRanking = Collections.emptyList();
        }

        modelDataStatisticsMonth.setNewModelAnalysisJson(JSONUtil.toJsonStr(newModelCountAnalysisInfo));
        modelDataStatisticsMonth.setOustModelAnalysisJson(JSONUtil.toJsonStr(oustModelCountAnalysisInfo));
        modelDataStatisticsMonth.setModelOustNumber(modelDataStatisticsMonth.getModelOustNumber() == null ? 0L : modelDataStatisticsMonth.getModelOustNumber());
        modelDataStatisticsMonth.setModelNewNumber(Convert.toLong(modelList.size()));
        modelDataStatisticsMonth.setRankingListJson(JSONUtil.toJsonStr(modelOrderRanking));

        modelDataStatisticsMonthService.saveOrUpdate(modelDataStatisticsMonth, new LambdaUpdateWrapper<ModelDataStatisticsMonth>().eq(ModelDataStatisticsMonth::getWriteTimeBegin, modelDataStatisticsMonth.getWriteTimeBegin()).eq(ModelDataStatisticsMonth::getWriteTimeEnd, modelDataStatisticsMonth.getWriteTimeEnd()));
        XxlJobHelper.log("任务modelDataStatisticsMonthHandler执行完成.....");
    }

    /**
     * （一次性）初始化模特数据-模特接单排行榜
     */
    @XxlJob("initModelRankingListHandler")
    public void initModelRankingListHandler() {
        List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.parse("2024-01-01 00:00:00"), DateUtil.parse("2025-03-01 00:00:00"), DateField.MONTH);
        List<String> collect = dateTimes.stream().map(item -> DateUtil.format(item, "yyyy-MM")).collect(Collectors.toList());
        Map<String, List<ModelOrderRankingInfo>> map = remoteService.getModelOrderRankings(collect);

        List<ModelDataStatisticsMonth> modelDataStatisticsMonths = new ArrayList<>();
        for (DateTime dateTime : dateTimes) {
            ModelDataStatisticsMonth modelDataStatisticsMonth = new ModelDataStatisticsMonth();
            modelDataStatisticsMonth.setWriteTimeBegin(DateUtil.parse(DateUtil.format(DateUtil.beginOfMonth(dateTime), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN));
            modelDataStatisticsMonth.setWriteTimeEnd(DateUtil.parse(DateUtil.format(DateUtil.endOfMonth(dateTime), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN));

            ModelCountAnalysisInfo newModelCountAnalysisInfo = new ModelCountAnalysisInfo();
            newModelCountAnalysisInfo.setModelNumber(0L);
            newModelCountAnalysisInfo.setModelAverageCommission(BigDecimal.ZERO);
            newModelCountAnalysisInfo.setPieChartVOS(List.of(
                    PieChartVO.init(ModelCooperationEnum.ORDINARY.getLabel()),
                    PieChartVO.init(ModelCooperationEnum.MODERATE.getLabel()),
                    PieChartVO.init(ModelCooperationEnum.QUALITY.getLabel())
            ));
            modelDataStatisticsMonth.setNewModelAnalysisJson(JSONUtil.toJsonStr(newModelCountAnalysisInfo));

            ModelCountAnalysisInfo oustModelCountAnalysisInfo = new ModelCountAnalysisInfo();
            oustModelCountAnalysisInfo.setModelNumber(0L);
            oustModelCountAnalysisInfo.setModelAverageCommission(BigDecimal.ZERO);
            oustModelCountAnalysisInfo.setPieChartVOS(List.of(
                    PieChartVO.init(ModelCooperationEnum.ORDINARY.getLabel()),
                    PieChartVO.init(ModelCooperationEnum.MODERATE.getLabel()),
                    PieChartVO.init(ModelCooperationEnum.QUALITY.getLabel())
            ));
            modelDataStatisticsMonth.setOustModelAnalysisJson(JSONUtil.toJsonStr(oustModelCountAnalysisInfo));

            List<ModelOrderRankingInfo> modelOrderRankingInfos = map.get(DateUtil.format(dateTime, DatePattern.NORM_MONTH_PATTERN));
            if (CollUtil.isNotEmpty(modelOrderRankingInfos)) {
                Set<Long> modelIds = modelOrderRankingInfos.stream().map(ModelOrderRankingInfo::getModelId).collect(Collectors.toSet());
                List<Model> modelList = modelService.listByIds(modelIds);
                Map<Long, Model> modelMap = modelList.stream().collect(Collectors.toMap(Model::getId, Function.identity()));
                for (ModelOrderRankingInfo modelOrderRankingInfo : modelOrderRankingInfos) {
                    modelOrderRankingInfo.setModelName(modelMap.getOrDefault(modelOrderRankingInfo.getModelId(), new Model()).getName());
                    modelOrderRankingInfo.setModelAvatar(modelMap.getOrDefault(modelOrderRankingInfo.getModelId(), new Model()).getModelPic());
                }
                modelOrderRankingInfos.sort(Comparator.comparing(ModelOrderRankingInfo::getOrderCount).reversed());
            } else {
                modelOrderRankingInfos = Collections.emptyList();
            }
            modelDataStatisticsMonth.setRankingListJson(JSONUtil.toJsonStr(modelOrderRankingInfos));

            modelDataStatisticsMonth.setModelNewNumber(0L);
            modelDataStatisticsMonth.setModelOustNumber(0L);
            modelDataStatisticsMonths.add(modelDataStatisticsMonth);
        }
        modelDataStatisticsMonthService.saveBatch(modelDataStatisticsMonths);
    }

    /**
     * （一次性）将model.amazon_video、tiktok_video 数据移到model_video_resource表中
     */
    @XxlJob("initModelVideoResourceHandler")
    public void initModelVideoResourceHandler() {
        XxlJobHelper.log("任务initModelVideoResourceHandler执行中.....");
        List<Model> list = modelService.lambdaQuery()
                .list();

        // 提取查询结果中的模特ID集合
        List<Long> modelResourceIds = new ArrayList<>();

        List<String> amazonVideoIds = list.stream().map(Model::getAmazonVideoId).filter(Objects::nonNull).filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
        modelResourceIds.addAll(StringUtils.splitToLong(amazonVideoIds, StrUtil.COMMA));

        List<String> tiktokVideoIds = list.stream().map(Model::getTiktokVideoId).filter(Objects::nonNull).filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
        modelResourceIds.addAll(StringUtils.splitToLong(tiktokVideoIds, StrUtil.COMMA));

        List<ModelVideoResource> modelVideoResources = modelVideoResourceService.listByIds(modelResourceIds);
        Map<Long, ModelVideoResource> modelVideoResourceHashMap = new HashMap<>();
        for (ModelVideoResource modelVideoResource : modelVideoResources) {
            modelVideoResourceHashMap.put(modelVideoResource.getId(), modelVideoResource);
        }

        for (Model model : list) {
            if (CharSequenceUtil.isNotBlank(model.getAmazonVideoId())) {
                List<Long> longs = StringUtils.splitToLong(model.getAmazonVideoId(), StrUtil.COMMA);
                for (Long aLong : longs) {
                    ModelVideoResource modelVideoResource = modelVideoResourceHashMap.get(aLong);
                    modelVideoResource.setModelId(model.getId());
                    modelVideoResource.setType(ModelVideoResourceTypeEnum.AMAZON_VIDEO.getCode());
                }
            }
            if (CharSequenceUtil.isNotBlank(model.getTiktokVideoId())) {
                List<Long> longs = StringUtils.splitToLong(model.getTiktokVideoId(), StrUtil.COMMA);
                for (Long aLong : longs) {
                    ModelVideoResource modelVideoResource = modelVideoResourceHashMap.get(aLong);
                    modelVideoResource.setModelId(model.getId());
                    modelVideoResource.setType(ModelVideoResourceTypeEnum.TIKTOK_VIDEO.getCode());
                }
            }
        }

        modelVideoResourceService.updateBatchById(modelVideoResources);
    }
}
