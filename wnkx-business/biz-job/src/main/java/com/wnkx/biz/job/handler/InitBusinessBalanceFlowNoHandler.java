package com.wnkx.biz.job.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceAuditFlow;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceFlow;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.wnkx.biz.business.service.IBusinessBalanceAuditFlowService;
import com.wnkx.biz.business.service.IBusinessBalanceFlowService;
import com.wnkx.biz.business.service.IBusinessBalancePrepayService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/12/30
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class InitBusinessBalanceFlowNoHandler {

    private final IBusinessBalanceFlowService businessBalanceFlowService;
    private final IBusinessBalancePrepayService businessBalancePrepayService;
    private final IBusinessBalanceAuditFlowService businessBalanceAuditFlowService;

    /**
     * 初始化 余额流水号
     */
    @XxlJob("initBusinessBalanceFlowNoHandler")
    @Transactional(rollbackFor = Exception.class)
    public void initBusinessBalanceFlowNoHandler() {
        List<BusinessBalanceFlow> list = businessBalanceFlowService.list(new LambdaQueryWrapper<BusinessBalanceFlow>()
                .eq(BusinessBalanceFlow::getFlowOrderNo, ""));

        if (CollUtil.isNotEmpty(list)) {
            List<String> flowOrderNos = new ArrayList<>();
            List<BusinessBalanceFlow> updateList = new ArrayList<>();
            for (BusinessBalanceFlow item : list) {
                int i = 1;
                BusinessBalanceFlow update = new BusinessBalanceFlow();
                update.setId(item.getId());
                String flowOrderNo = getFlowOrderNo(flowOrderNos, i);
                if (StrUtil.isBlank(flowOrderNo)) {
                    continue;
                }

                update.setFlowOrderNo(flowOrderNo);
                updateList.add(update);
            }
            businessBalanceFlowService.updateBatchById(updateList);
        }
        List<BusinessBalanceFlow> businessBalanceFlows = businessBalanceFlowService.list(new LambdaQueryWrapper<BusinessBalanceFlow>()
                .in(BusinessBalanceFlow::getOrigin, Arrays.asList(6, 7)));

        if (CollUtil.isNotEmpty(businessBalanceFlows)) {
            List<BusinessBalancePrepay> businessBalancePrepays = businessBalancePrepayService.list(new LambdaQueryWrapper<BusinessBalancePrepay>().eq(BusinessBalancePrepay::getAuditStatus, 1));
            List<BusinessBalanceAuditFlow> businessBalanceAuditFlows = businessBalanceAuditFlowService.list(new LambdaQueryWrapper<BusinessBalanceAuditFlow>().eq(BusinessBalanceAuditFlow::getAuditStatus, 1));
            Map<BigDecimal, List<BusinessBalancePrepay>> businessBalancePrepayMap = new HashMap<>();
            Map<BigDecimal, List<BusinessBalanceAuditFlow>> businessBalanceAuditFlowMap = new HashMap<>();
            if (CollUtil.isNotEmpty(businessBalancePrepays)) {
                businessBalancePrepayMap = businessBalancePrepays.stream().collect(Collectors.groupingBy(BusinessBalancePrepay::getRealAmount));
            }
            if (CollUtil.isNotEmpty(businessBalanceAuditFlows)) {

                businessBalanceAuditFlowMap = businessBalanceAuditFlows.stream().collect(Collectors.groupingBy(BusinessBalanceAuditFlow::getAmount));
            }
            List<BusinessBalanceFlow> updateList = new ArrayList<>();
            List<String> errorList = new ArrayList<>();
            List<String> vaildList = new ArrayList<>();
            //获取预付款数据
            for (BusinessBalanceFlow item : businessBalanceFlows) {
                BusinessBalanceFlow update = new BusinessBalanceFlow();
                if (item.getOrigin() == 6) {
                    if (StrUtil.isNotBlank(item.getWithdrawNumber())) {
                        continue;
                    }
                    List<BusinessBalanceAuditFlow> amountList = businessBalanceAuditFlowMap.get(item.getAmount().negate());
                    if (CollUtil.isNotEmpty(amountList)) {
                        for (BusinessBalanceAuditFlow itemSon : amountList) {
                            //两个时间相差多少
                            Long datePoorSec = Math.abs(DateUtils.getDatePoorSec(item.getCreateTime(), itemSon.getAuditTime()));
                            if (datePoorSec.compareTo(2L) <= 0 && StrUtil.isNotBlank(itemSon.getWithdrawNumber())) {
                                if (CollUtil.isNotEmpty(vaildList) && vaildList.contains(itemSon.getWithdrawNumber())) {
                                    errorList.add(itemSon.getWithdrawNumber());
                                }

                                //时间误差无2s内 认为是同一个数据
                                update.setId(item.getId());
                                update.setWithdrawNumber(itemSon.getWithdrawNumber());
                                vaildList.add(itemSon.getWithdrawNumber());
                                updateList.add(update);
                            }
                        }
                    }
                }
                if (item.getOrigin() == 7) {
                    if (StrUtil.isNotBlank(item.getPrepayNum())) {
                        continue;
                    }
                    List<BusinessBalancePrepay> amountList = businessBalancePrepayMap.get(item.getAmount());
                    if (CollUtil.isNotEmpty(amountList)) {
                        for (BusinessBalancePrepay itemSon : amountList) {
                            //两个时间相差多少
                            Long datePoorSec = Math.abs(DateUtils.getDatePoorSec(item.getCreateTime(), itemSon.getAuditTime()));
                            if (datePoorSec.compareTo(2L) <= 0) {
                                if (CollUtil.isNotEmpty(vaildList) && vaildList.contains(itemSon.getPrepayNum())) {
                                    errorList.add(itemSon.getPrepayNum());
                                }
                                //时间误差无2s内 认为是同一个数据
                                update.setId(item.getId());
                                update.setPrepayNum(itemSon.getPrepayNum());
                                vaildList.add(itemSon.getPrepayNum());
                                updateList.add(update);
                            }
                        }

                    }
                }
            }
            if (CollUtil.isNotEmpty(updateList)) {
                List<BusinessBalanceFlow> businessBalanceFlowList = updateList.stream().filter(item -> !(errorList.contains(item.getPrepayNum()) || errorList.contains(item.getWithdrawNumber()))).collect(Collectors.toList());
                businessBalanceFlowService.updateBatchById(updateList);
            }
        }


    }

    public String getFlowOrderNo(List<String> flowOrderNos, int i) {
        if (i > 3) {
            return null;
        }
        String flowOrderNo = IdUtils.createFlowOrderNo();
        if (CollUtil.isNotEmpty(flowOrderNos) && flowOrderNos.contains(flowOrderNo)) {
            return getFlowOrderNo(flowOrderNos, ++i);
        }
        return flowOrderNo;
    }
}
