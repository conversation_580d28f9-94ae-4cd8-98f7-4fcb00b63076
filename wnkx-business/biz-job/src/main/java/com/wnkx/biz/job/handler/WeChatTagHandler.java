package com.wnkx.biz.job.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.system.api.config.WorkWeChatConfig;
import com.ruoyi.system.api.domain.dto.biz.wechat.WeChatGroupTagDTO;
import com.ruoyi.system.api.domain.dto.biz.wechat.WeCropTagDTO;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatTag;
import com.wnkx.biz.wechat.service.IWeChatTagService;
import com.wnkx.biz.wechat.service.WorkWechatApiService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :微信标签定时任务
 * @create :2024-10-09 14:27
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class WeChatTagHandler {
    private final WorkWechatApiService workWechatApiService;
    private final WorkWeChatConfig workWeChatConfig;
    private final IWeChatTagService weChatTagService;

    @XxlJob("updateWeChatTag")
    public void updateWeChatTagHandler() {
        List<WeChatGroupTagDTO> weChatGroupTags = workWechatApiService.tagList(null, Arrays.asList(workWeChatConfig.getDistribution(), workWeChatConfig.getMarketing()));
        if (CollUtil.isEmpty(weChatGroupTags)) {
            return;
        }
        //获取数据库中所有标签数据
        List<WeChatTag> list = weChatTagService.list();
        Map<String, WeChatTag> tableTagMap = new HashMap<>();
        if (CollUtil.isNotEmpty(list)) {
            tableTagMap.putAll(list.stream().collect(Collectors.toMap(WeChatTag::getTagId, item -> item)));
        }

        List<WeChatTag> updateTags = new ArrayList<>();
        List<WeChatTag> addTags = new ArrayList<>();
        for (WeChatGroupTagDTO item : weChatGroupTags) {
            List<WeCropTagDTO> tags = item.getTag();
            if (CollUtil.isEmpty(tags)) {
                continue;
            }
            String groupName = item.getGroup_name();
            String groupId = item.getGroup_id();
            for (WeCropTagDTO tagItem : tags) {
                //数据库标签
                WeChatTag weChatTag = tableTagMap.get(tagItem.getId());

                //初始化接口获取标签
                WeChatTag remoteTag = WeChatTag.builder()
                        .createTime(new Date(tagItem.getCreate_time() * 1000))
                        .groupId(groupId)
                        .groupName(groupName)
                        .tagId(tagItem.getId())
                        .tagName(tagItem.getName())
                        .build();
                if (ObjectUtil.isNull(weChatTag)) {
                    //数据库标签为空
                    addTags.add(remoteTag);
                } else if (StrUtil.isNotBlank(weChatTag.getTagName()) && !weChatTag.getTagName().equals(tagItem.getName())) {
                    //数据库标签名称不一致
                    remoteTag.setId(weChatTag.getId());
                    updateTags.add(remoteTag);
                }
            }
        }
        weChatTagService.saveBatch(addTags);
        weChatTagService.updateBatchById(updateTags);
    }
}
