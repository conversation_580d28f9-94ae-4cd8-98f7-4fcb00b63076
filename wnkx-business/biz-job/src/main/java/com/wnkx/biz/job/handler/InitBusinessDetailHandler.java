package com.wnkx.biz.job.handler;

import java.math.BigDecimal;
import java.util.Date;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetail;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetailFlow;
import com.wnkx.biz.business.service.IBusinessBalanceDetailFlowService;
import com.wnkx.biz.business.service.IBusinessBalanceDetailService;
import com.wnkx.biz.business.service.IBusinessService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/12/30
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class InitBusinessDetailHandler {

    private final IBusinessService businessService;
    private final IBusinessBalanceDetailService businessBalanceDetailService;
    private final IBusinessBalanceDetailFlowService businessBalanceDetailFlowService;

    /**
     * 初始化 钱包详情数据
     */
    @XxlJob("initBusinessDetailHandler")
    @Transactional(rollbackFor = Exception.class)
    public void initBusinessDetailHandler() {
        List<Business> list = businessService.list(new LambdaQueryWrapper<Business>()
                .gt(Business::getBalance, 0));
        List<BusinessBalanceDetail> businessBalanceDetails = new ArrayList<>();
        List<BusinessBalanceDetailFlow> businessBalanceDetailFlowList = new ArrayList<>();
        BusinessBalanceDetail one = Optional.ofNullable(businessBalanceDetailService.getOne(new LambdaQueryWrapper<BusinessBalanceDetail>().orderByDesc(BusinessBalanceDetail::getId).last("limit 1"))).orElse(BusinessBalanceDetail.builder().id(0L).build());
        Long id = one.getId();

        List<BusinessBalanceDetail> businessBalanceDetailList = businessBalanceDetailService.list();
        List<Long> businessIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(businessBalanceDetailList)) {
            businessIds = businessBalanceDetailList.stream().map(BusinessBalanceDetail::getBusinessId).collect(Collectors.toList());

        }

        for (Business item : list) {
            if (CollUtil.isNotEmpty(businessIds) && businessIds.contains(item.getId())) {
                continue;
            }
            ++id;
            BusinessBalanceDetail businessBalanceDetail = new BusinessBalanceDetail();
            businessBalanceDetail.setBusinessId(item.getId());
            businessBalanceDetail.setNumber("YS" + String.format("%06d", id));
            businessBalanceDetail.setOriginNumber("YS" + String.format("%03d", id));
            businessBalanceDetail.setVideoCode("");
            businessBalanceDetail.setNumberType(2);
            businessBalanceDetail.setOrigin(7);
            businessBalanceDetail.setBalance(item.getBalance());
            businessBalanceDetail.setUseBalance(new BigDecimal("0"));
            businessBalanceDetail.setLockBalance(new BigDecimal("0"));
            businessBalanceDetail.setValidBalance(item.getBalance());
            businessBalanceDetail.setCreateOrderUserName("");
            businessBalanceDetail.setCreateOrderUserNickName("");
            businessBalanceDetail.setCreateById(1L);
            businessBalanceDetail.setCreateBy("admin");
            businessBalanceDetail.setCreateTime(new Date());
            businessBalanceDetail.setUpdateTime(new Date());
            businessBalanceDetails.add(businessBalanceDetail);


            BusinessBalanceDetailFlow businessBalanceDetailFlow = new BusinessBalanceDetailFlow();
            businessBalanceDetailFlow.setBalanceFlowId(0L);
            businessBalanceDetailFlow.setBalanceNumber(businessBalanceDetail.getNumber());
            businessBalanceDetailFlow.setNumber(businessBalanceDetail.getOriginNumber());
            businessBalanceDetailFlow.setVideoId(null);
            businessBalanceDetailFlow.setVideoCode("");
            businessBalanceDetailFlow.setUseBalance(item.getBalance());
            businessBalanceDetailFlow.setType(0);
            businessBalanceDetailFlow.setCreateById(1L);
            businessBalanceDetailFlow.setCreateBy("admin");
            businessBalanceDetailFlow.setCreateTime(new Date());
            businessBalanceDetailFlowList.add(businessBalanceDetailFlow);

        }

        businessBalanceDetailService.saveBatch(businessBalanceDetails);
        businessBalanceDetailFlowService.saveBatch(businessBalanceDetailFlowList);

    }
}
