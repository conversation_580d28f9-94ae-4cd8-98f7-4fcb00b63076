package com.wnkx.biz.job.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.RemoteOrderService;
import com.ruoyi.system.api.domain.dto.OrderVideoStatisticsDTO;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsDetailVO;
import com.wnkx.biz.business.service.IBusinessService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/30
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UpdateRecentOrderHandler {
    private final IBusinessService businessService;
    private final RemoteOrderService remoteOrderService;

    @XxlJob("updateRecentOrderHandler")
    @Transactional(rollbackFor = Exception.class)
    public void updateRecentOrderHandler() {
        XxlJobHelper.log("任务updateRecentOrderHandler执行中.....");
        OrderVideoStatisticsDTO orderVideoStatisticsDTO = new OrderVideoStatisticsDTO();
        List<OrderVideoStatisticsDetailVO> listR = remoteOrderService.orderVideoStatisticsDetail(orderVideoStatisticsDTO);
        List<OrderVideoStatisticsDetailVO> data = listR;
        //获取商家列表
        List<Business> list = businessService.list();
        if (StringUtils.isEmpty(list)) {
            return;
        }
        Map<Long, Integer> businessMap = data.stream().collect(Collectors.toMap(OrderVideoStatisticsDetailVO::getMerchantId, OrderVideoStatisticsDetailVO::getRecentOrderTotal));

        List<Business> businesses = new ArrayList<>();

        for (Business item :list){
            Business business = new Business();
            business.setId(item.getId());
            if (CollUtil.isEmpty(businessMap)|| ObjectUtil.isNull(businessMap.get(item.getId()))){
                business.setIsExistRecentOrder(StatusTypeEnum.NO.getCode());
                continue;
            }
            business.setIsExistRecentOrder(0 == businessMap.get(item.getId()) ? StatusTypeEnum.NO.getCode() : StatusTypeEnum.YES.getCode());
            businesses.add(business);
        }
        if (StringUtils.isEmpty(businesses)){
            XxlJobHelper.log("不存在需要更新数据！");
            return;
        }
        businessService.updateBatchById(businesses);
    }
}
