package com.wnkx.biz.job.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.SettleStatusEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccount;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderMemberChannel;
import com.wnkx.biz.business.service.IBusinessAccountService;
import com.wnkx.biz.business.service.IBusinessService;
import com.wnkx.biz.channel.service.IOrderMemberChannelService;
import com.wnkx.biz.remote.RemoteService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/16
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class BusinessHandler {

    private final IBusinessService businessService;
    private final IBusinessAccountService businessAccountService;
    private final RemoteService remoteService;
    private final IOrderMemberChannelService orderMemberChannelService;

    /**
     * （一次性）设置business.recent_order_time的值
     */
    @XxlJob("updateBusinessRecentOrderTime")
    public void updateBusinessRecentOrderTime() {
        List<Order> payedOrderList = remoteService.getPayedOrderList();
        if (CollUtil.isEmpty(payedOrderList)) {
            return;
        }

        // 通过 Stream 进行分组，并对每个分组按 recordTime 降序排序
        Map<Long, List<Order>> groupedAndSortedOrders = payedOrderList.stream()
                .collect(Collectors.groupingBy(
                        Order::getMerchantId  // 根据 merchantId 分组
                ));

        // 对每个分组按 recordTime 降序排序
        groupedAndSortedOrders.replaceAll((merchantId, orders) ->
                orders.stream()
                        .sorted(Comparator.comparing(Order::getRecordTime).reversed())  // 按 recordTime 降序排序
                        .collect(Collectors.toList())
        );

        List<Business> businesses = businessService.list();
        for (Business business : businesses) {
            List<Order> orders = groupedAndSortedOrders.get(business.getId());
            if (CollUtil.isEmpty(orders)) {
                continue;
            }

            //  最近排单时间等于最新的入账时间
            business.setRecentOrderTime(orders.get(0).getRecordTime());
        }

        businessService.updateBatchById(businesses);
    }


    /**
     * 定时更新回访表
     */
    @XxlJob("updateBusinessCallback")
    public void updateBusinessCallback() {
        businessService.updateBusinessCallback(null);
    }


    @XxlJob("updatePaySucceed")
    public void updatePaySucceed() {
        List<Business> businesses = businessService.list(new LambdaQueryWrapper<Business>()
                .eq(Business::getPaySucceed, StatusTypeEnum.NO.getCode()));
        if (CollUtil.isEmpty(businesses)) {
            return;
        }
        List<Order> payedUnCheckOrderList = remoteService.getPayedUnCheckOrderList();
        if (CollUtil.isEmpty(payedUnCheckOrderList)) {
            return;
        }
        Map<Long, List<Order>> groupedAndSortedOrders = payedUnCheckOrderList.stream()
                .collect(Collectors.groupingBy(
                        Order::getMerchantId  // 根据 merchantId 分组
                ));
        List<Business> updateBusinesses = new ArrayList<>();
        List<Long> businessIds = new ArrayList<>();
        for (Business item : businesses) {
            List<Order> orders = groupedAndSortedOrders.get(item.getId());
            if (CollUtil.isNotEmpty(orders)) {
                Business update = new Business();
                update.setId(item.getId());
                update.setPaySucceed(StatusTypeEnum.YES.getCode());
                updateBusinesses.add(update);
                businessIds.add(item.getId());
            }
        }
        if (CollUtil.isNotEmpty(updateBusinesses)) {
            businessService.updateBatchById(updateBusinesses);
            orderMemberChannelService.update(new LambdaUpdateWrapper<OrderMemberChannel>()
                    .set(OrderMemberChannel::getSettleStatus, SettleStatusEnum.UN_SETTLED.getCode())
                    .eq(OrderMemberChannel::getSettleStatus, SettleStatusEnum.WAIT_SETTLED.getCode())
                    .in(OrderMemberChannel::getBusinessId, businessIds)
            );
        }
    }

    /**
     * （一次性）补全business_account.owner_account,business_account.owner_account_biz_user_id
     */
    @XxlJob("updateBusinessAccount")
    public void updateBusinessAccount() {
        List<BusinessAccount> list = businessAccountService.list();
        Set<Long> businessIds = list.stream().filter(item -> item.getIsOwnerAccount() == 0).map(BusinessAccount::getBusinessId).collect(Collectors.toSet());

        Map<Long, Business> businesseMap = new HashMap<>();
        Map<String, BusinessAccount> ownerAccountMap = new HashMap<>();
        if (CollUtil.isNotEmpty(businessIds)) {
            List<Business> businesses = businessService.listByIds(businessIds);
            businesseMap = businesses.stream().collect(Collectors.toMap(Business::getId, Function.identity()));
            ownerAccountMap = businessAccountService.lambdaQuery()
                    .in(BusinessAccount::getAccount, businesses.stream().map(Business::getOwnerAccount).collect(Collectors.toList()))
                    .list()
                    .stream()
                    .collect(Collectors.toMap(BusinessAccount::getAccount, Function.identity()));
        }

        for (BusinessAccount businessAccount : list) {
            if (businessAccount.getIsOwnerAccount() == 0) {
                Business business = businesseMap.get(businessAccount.getBusinessId());
                if (ObjectUtil.isNotNull(business)) {
                    BusinessAccount ownerAccount = ownerAccountMap.get(business.getOwnerAccount());
                    if (ObjectUtil.isNotNull(ownerAccount)) {
                        businessAccount.setOwnerAccount(ownerAccount.getAccount());
                        businessAccount.setOwnerAccountBizUserId(ownerAccount.getBizUserId());
                    }
                }
            } else {
                businessAccount.setOwnerAccount(businessAccount.getAccount());
                businessAccount.setOwnerAccountBizUserId(businessAccount.getBizUserId());
            }
        }

        businessAccountService.updateBatchById(list);
    }
}
