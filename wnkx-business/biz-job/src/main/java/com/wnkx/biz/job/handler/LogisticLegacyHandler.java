package com.wnkx.biz.job.handler;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.system.api.domain.entity.LogisticInfo;
import com.ruoyi.system.api.domain.entity.LogisticInfoLegacy;
import com.wnkx.biz.logistic.service.LogisticInfoLegacyService;
import com.wnkx.biz.logistic.service.LogisticInfoService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class LogisticLegacyHandler {
    private final LogisticInfoLegacyService logisticInfoLegacyService;
    private final LogisticInfoService logisticInfoService;

    /**
     * 定期移动三个月以上的物流信息到历史表中
     */
    @XxlJob("logisticLegacyMover")
    public void logisticLegacyMover() {
        long afterDay = System.currentTimeMillis() - 90L * 24 * 60 * 60 * 1000;
        Date date = new Date();
        XxlJobHelper.log("开始移动三个月以上的物流信息到历史表中");
        moveLegacyData(afterDay, date);
        XxlJobHelper.handleSuccess("任务执行成功");
    }

    private void moveLegacyData(long afterDay, Date date) {
        List<LogisticInfo> list = logisticInfoService.list(new LambdaUpdateWrapper<LogisticInfo>()
                .last("limit 1000")
        );
        List<LogisticInfo> logisticInfos = list.stream().filter(logisticInfo -> logisticInfo.getCreateTime().getTime() < afterDay).collect(Collectors.toList());
        if (list.isEmpty()) {
            return;
        }
        logisticInfos.forEach(logisticInfo -> {
            LogisticInfoLegacy logisticInfoLegacy = new LogisticInfoLegacy();
            logisticInfoLegacy.setNumber(logisticInfo.getNumber());
            logisticInfoLegacy.setDescription(logisticInfo.getDescription());
            logisticInfoLegacy.setMainStatus(logisticInfo.getMainStatus());
            logisticInfoLegacy.setSubStatus(logisticInfo.getSubStatus());
            logisticInfoLegacy.setCurTime(logisticInfo.getCurTime());
            logisticInfoLegacy.setCountry(logisticInfo.getCountry());
            logisticInfoLegacy.setState(logisticInfo.getState());
            logisticInfoLegacy.setCity(logisticInfo.getCity());
            logisticInfoLegacy.setStreet(logisticInfo.getStreet());
            logisticInfoLegacy.setLongitude(logisticInfo.getLongitude());
            logisticInfoLegacy.setLatitude(logisticInfo.getLatitude());
            logisticInfoLegacy.setLocation(logisticInfo.getLocation());
            logisticInfoLegacy.setCreateTime(date);
            logisticInfoLegacyService.save(logisticInfoLegacy);
        });
        logisticInfoService.removeByIds(logisticInfos.stream().map(LogisticInfo::getId).collect(Collectors.toList()));
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            log.error("Thread sleep interrupted", e);
        }
        moveLegacyData(afterDay, date);
    }
}
