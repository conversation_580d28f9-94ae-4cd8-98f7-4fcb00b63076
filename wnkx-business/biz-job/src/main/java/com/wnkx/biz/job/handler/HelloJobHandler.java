package com.wnkx.biz.job.handler;

import com.wnkx.biz.business.service.IBusinessService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/30
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class HelloJobHandler {
    private final IBusinessService businessService;

    @XxlJob("helloJobHandler")
    public void helloJobHandler() {
        XxlJobHelper.log("XXL-JOB, Hello World.");
        XxlJobHelper.log("任务执行中.....");
        XxlJobHelper.log("任务执行中.....");
        XxlJobHelper.log("任务执行中.....");
        XxlJobHelper.log("任务执行中.....");
        XxlJobHelper.log("任务执行中.....");
        XxlJobHelper.log("任务执行中.....");
        XxlJobHelper.log("这是普通日志输出给xxljob");
        log.info("这是普通日志输出于控制台");
        XxlJobHelper.handleSuccess("任务执行成功");
        XxlJobHelper.handleSuccess("任务执行成功");

    }
}
