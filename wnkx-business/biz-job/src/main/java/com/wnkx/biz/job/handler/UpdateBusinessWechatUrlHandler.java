package com.wnkx.biz.job.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.enums.WechatContactUserTagEnum;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.config.WorkWeChatConfig;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessOwnerAccountContactUserDTO;
import com.ruoyi.system.api.domain.dto.biz.wechat.ContactUserCountDTO;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatContactUserConfig;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatContactUserConfigInfo;
import com.wnkx.biz.business.service.IBusinessService;
import com.wnkx.biz.wechat.mapper.WeChatContactUserConfigInfoMapper;
import com.wnkx.biz.wechat.mapper.WeChatExternalUserMapper;
import com.wnkx.biz.wechat.service.WeChatContactUserConfigService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 批量更新商家联系我url
 *
 * <AUTHOR>
 * @date 2024/11/17
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UpdateBusinessWechatUrlHandler {
    private final IBusinessService businessService;
    private final WorkWeChatConfig workWeChatConfig;
    private final WeChatContactUserConfigService weChatContactUserConfigService;
    private final WeChatContactUserConfigInfoMapper weChatContactUserConfigInfoMapper;
    private final WeChatExternalUserMapper weChatExternalUserMapper;
    private final RedisService redisService;


    @XxlJob("updateBusinessWechatUrl")
    public void updateBusinessWechatUrl() {
        XxlJobHelper.log("开始执行更新urljob");
        List<Business> unInitWechatBusinessList = businessService.getUnInitWechatBusinessList();
        XxlJobHelper.log("共计数量为", +unInitWechatBusinessList.size());
        businessService.initBusinessWechatUrl(unInitWechatBusinessList);
        XxlJobHelper.handleSuccess("任务执行成功");

    }

    @XxlJob("updateNewBusinessWechatUrl")
    public void updateNewBusinessWechatUrl() {
        XxlJobHelper.log("开始刷新库中所有商家子账号二维码");
        List<Business> allBusinessList = businessService.getAllBusinessCodeList();
        XxlJobHelper.log("共计数量为", +allBusinessList.size());
        businessService.initBusinessWechatUrl(allBusinessList);
        XxlJobHelper.handleSuccess("任务执行成功");
    }

    /**
     * 更新子账号邀请时添加客服与主账号添加客服一致
     */
    @XxlJob("updateSubAccountWechatUrl")
    public void updateSubAccountWechatUrl() {
        XxlJobHelper.log("开始更新子账号邀请时添加客服与主账号添加客服一致");
        List<BusinessOwnerAccountContactUserDTO> allBusinessList = businessService.getBusinessOwnerUserContactUserName();
        XxlJobHelper.log("共计数量为", +allBusinessList.size());
        HashMap<String, String> kvMap = new HashMap<>();
        kvMap.put("夜雨寄北", "YeYuJiBei");
        kvMap.put("张翠华", "RuYan");
        kvMap.put("蜗牛海拍-周巧云", "365up");
        kvMap.put("蜗牛海拍-秦美丽", "susie");
        kvMap.put("蜗牛海拍-陈艺婷", "HongRunXin");
        businessService.initBusinessWechatUrlBaseOnContactUser(allBusinessList, kvMap);
        XxlJobHelper.handleSuccess("任务执行成功");
    }

    @XxlJob("initWechatContactData")
    public void initWechatContactData() {
        XxlJobHelper.log("开始初始化记录");
        for (int i = 0; i < workWeChatConfig.getContactUser().size(); i++) {
            XxlJobHelper.log("开始初始化" + workWeChatConfig.getContactUser().get(i));
            weChatContactUserConfigService.init(workWeChatConfig.getContactUser().get(i), workWeChatConfig.getContactUserName().get(i));
        }
    }

    @XxlJob("checkOutRedisContactUser")
    public void checkOutRedisContactUser() {
        List<ContactUserCountDTO> contactUserList = weChatExternalUserMapper.todayContactUser();
        if (CollectionUtil.isEmpty(contactUserList)) {
//            今日还未有数据，跳过切换
            return;
        }
        List<String> ignoreUserList = List.of("");

        List<WeChatContactUserConfig> configList = weChatContactUserConfigService.getConfigList();
        String activeUserId = redisService.getCacheObject(CacheConstants.CURRENT_CONTACT_USER_ID);
        if (StringUtils.isBlank(activeUserId)) {
            setRedisContactUser(contactUserList.get(0).getContactUserId());
            return;
        }
        List<ContactUserCountDTO> formatList = format(contactUserList, configList, ignoreUserList);
//       第一位是否为当前客服，如果是的话保持不变，如果是表里现在也只有一个的话也保持不变
        if (formatList.get(0).getContactUserId().equals(activeUserId)) {
            return;
        }
        if (formatList.size() == 1) {
            return;
        }
        setRedisContactUser(formatList.get(0).getContactUserId());
    }

    private List<ContactUserCountDTO> format(List<ContactUserCountDTO> contactUserList, List<WeChatContactUserConfig> configList, List<String> ignoreUserList) {
//          contactUserList中contactUserId   不在  configList中connect_user_id 的数据移除
        List<String> configUidList = configList.stream().map(WeChatContactUserConfig::getContactUserId).collect(Collectors.toList());
        contactUserList.forEach(contactUser -> {
            for (WeChatContactUserConfig config : configList) {
                if (contactUser.getContactUserId().equals(config.getContactUserId())) {
                    return;
                }
            }
            contactUserList.remove(contactUser);
        });
        List<String> todayUidList = contactUserList.stream().map(ContactUserCountDTO::getContactUserId).collect(Collectors.toList());
        List<ContactUserCountDTO> resultList = new ArrayList<>(16);
        configUidList.forEach(uid -> {
            if (!todayUidList.contains(uid)) {
                resultList.add(ContactUserCountDTO.builder()
                        .contactUserId(uid)
                        .addCount(0L)
                        .build());
            }
        });
        resultList.addAll(contactUserList);
//        超过1的话再过滤ignoreList
        if (resultList.size()>1){
            return resultList.stream().filter(contactUserCountDTO -> !ignoreUserList.contains(contactUserCountDTO.getContactUserId())).collect(Collectors.toList());
        }
        return resultList;
    }

    private void setRedisContactUser(String contactUserId) {
        List<WeChatContactUserConfigInfo> weChatContactUserConfigInfos = weChatContactUserConfigInfoMapper.getConfigUrlByContactUserId(contactUserId);
        XxlJobHelper.log("切换客服为" + contactUserId);
        redisService.setCacheObject(CacheConstants.CURRENT_CONTACT_USER_ID, contactUserId);
        weChatContactUserConfigInfos.forEach(weChatContactUserConfigInfo -> {
            redisService.setCacheObject(CacheConstants.CURRENT_ACTIVE_URL_PREFIX + WechatContactUserTagEnum.getShortTagByCode(weChatContactUserConfigInfo.getUrlType()), weChatContactUserConfigInfo.getConfigUrl());
        });
    }

    @XxlJob("initChannelContactUser")
    private void initChannelContactUser() {
        List<String> contactUser = workWeChatConfig.getContactUser();
        initializeList(contactUser);

    }

    public void initializeList(List<String> items) {
        for (String item : items) {
            String key = CacheConstants.CURRENT_CHANNEL_ACTIVE_KEY_PREFIX + item;
            redisService.setCacheObject(key, 0);
        }
    }

}
