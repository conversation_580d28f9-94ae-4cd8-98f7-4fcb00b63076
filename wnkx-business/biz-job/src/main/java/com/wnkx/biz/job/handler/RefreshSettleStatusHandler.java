package com.wnkx.biz.job.handler;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum;
import com.ruoyi.common.core.enums.SettleStatusEnum;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecord;
import com.ruoyi.system.api.domain.entity.order.OrderMemberChannel;
import com.wnkx.biz.channel.mapper.MemberSeedRecordRelevanceMapper;
import com.wnkx.biz.channel.service.IOrderMemberChannelService;
import com.wnkx.biz.channel.service.MemberSeedRecordService;
import com.wnkx.biz.channel.service.MemberSeedRecordWithdrawalService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/12/30
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RefreshSettleStatusHandler {

    private final IOrderMemberChannelService orderMemberChannelService;
    private final MemberSeedRecordService memberSeedRecordService;
    /**
     * 刷新结算状态
     */
    @XxlJob("refreshSettleStatusHandler")
    @Transactional(rollbackFor = Exception.class)
    public void refreshSettleStatusHandler() {
        List<OrderMemberChannel> list = orderMemberChannelService.list(new LambdaQueryWrapper<OrderMemberChannel>()
                .eq(OrderMemberChannel::getSettleStatus, SettleStatusEnum.WAIT_SETTLED.getCode())
                .lt(OrderMemberChannel::getCreateTime, DateUtils.addDays(DateUtils.getNowDate(), -7))
        );
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<OrderMemberChannel> updateList = new ArrayList<>();

        for (OrderMemberChannel item :list){
            OrderMemberChannel update = new OrderMemberChannel();
            update.setId(item.getId());
            update.setSettleStatus(SettleStatusEnum.UN_SETTLED.getCode());
            updateList.add(update);
        }

        orderMemberChannelService.updateBatchById(updateList);
    }
    /**
     * 刷新会员种草记录状态
     */
    @XxlJob("refreshMemberSeedRecordStatusHandler")
    @Transactional(rollbackFor = Exception.class)
    public void refreshMemberSeedRecordStatusHandler() {
        List<MemberSeedRecord> list = memberSeedRecordService.list(new LambdaQueryWrapper<MemberSeedRecord>()
                .eq(MemberSeedRecord::getStatus, MemberSeedRecordStatusEnum.PENDING_DEPOSIT.getCode())
                .lt(MemberSeedRecord::getCreateTime, DateUtils.addDays(DateUtils.getNowDate(), -7))
        );
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<MemberSeedRecord> updateList = new ArrayList<>();

        for (MemberSeedRecord item : list) {
            MemberSeedRecord update = new MemberSeedRecord();
            update.setId(item.getId());
            update.setStatus(MemberSeedRecordStatusEnum.PENDING_WITHDRAWAL.getCode());
            if (item.getSettleAmount().compareTo(BigDecimal.ZERO) == 0) {
                update.setStatus(MemberSeedRecordStatusEnum.UN_NEED_TRANSFERRED.getCode());
            }
            updateList.add(update);
        }
        memberSeedRecordService.updateBatchById(updateList);
    }
}
