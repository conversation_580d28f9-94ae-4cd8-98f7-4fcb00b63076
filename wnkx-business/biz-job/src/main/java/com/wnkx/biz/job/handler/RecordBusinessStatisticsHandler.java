package com.wnkx.biz.job.handler;

import java.util.Date;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.BusinessValidityChangeReasonTypeEnum;
import com.ruoyi.common.core.enums.MemberValidTypeEnum;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.dto.OrderVideoStatisticsDTO;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberValidityFlow;
import com.ruoyi.system.api.domain.entity.biz.statistics.BusinessMemberDataStatistics;
import com.ruoyi.system.api.domain.entity.biz.statistics.BusinessOrderDataStatistics;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsDetailVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.member.MemberExpireCountVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.member.MemberRechargeCountVO;
import com.wnkx.biz.business.mapper.BusinessMapper;
import com.wnkx.biz.business.mapper.BusinessMemberValidityFlowMapper;
import com.wnkx.biz.remote.RemoteService;
import com.wnkx.biz.statistics.mapper.BusinessMemberDataStatisticsMapper;
import com.wnkx.biz.statistics.mapper.BusinessOrderDataStatisticsMapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :商家统计
 * @create :2025-06-20 13:45
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class RecordBusinessStatisticsHandler {
    private final BusinessMemberDataStatisticsMapper businessMemberDataStatisticsMapper;
    private final BusinessOrderDataStatisticsMapper businessOrderDataStatisticsMapper;
    private final RemoteService remoteService;
    private final BusinessMapper businessMapper;
    private final BusinessMemberValidityFlowMapper businessMemberValidityFlowMapper;

    /**
     * 记录商家会员统计数据
     */
    @XxlJob("recordBusinessMemberData")
    public void recordBusinessMemberData() {
        XxlJobHelper.log("任务recordBusinessMemberData执行中.....");
        //新会员数、续费会员数、退回会员数、到期会员数
        MemberRechargeCountVO memberRechargeCountVO = Optional.ofNullable(businessMemberDataStatisticsMapper.getYesterdayMemberRechargeCountVO()).orElse(new MemberRechargeCountVO());
        Date startOfDay = DateUtils.getStartOfDay(DateUtils.addDays(new Date(), -1));
        Date endOfDay = DateUtils.getEndOfDay(DateUtils.addDays(new Date(), -1));
        BusinessMemberDataStatistics businessMemberDataStatistics = new BusinessMemberDataStatistics();
        businessMemberDataStatistics.setRecordTime(startOfDay);
        businessMemberDataStatistics.setMemberCount(Optional.ofNullable(memberRechargeCountVO.getYesterdayMemberCount()).orElse(0L));
        businessMemberDataStatistics.setRenewMemberCount(Optional.ofNullable(memberRechargeCountVO.getYesterdayRenewMemberCount()).orElse(0L));

        businessMemberDataStatistics.setExitMemberCount(Optional.ofNullable(businessMemberDataStatisticsMapper.getExitMemberCount(startOfDay, endOfDay)).orElse(0L));
        MemberExpireCountVO overMemberCount = Optional.ofNullable(businessMemberDataStatisticsMapper.getOverMemberCount()).orElse(new MemberExpireCountVO());
        businessMemberDataStatistics.setExpireMemberCount(Optional.ofNullable(overMemberCount.getYesterdayExpireMemberCount()).orElse(0L));
        businessMemberDataStatisticsMapper.delete(new LambdaQueryWrapper<BusinessMemberDataStatistics>()
                .between(BusinessMemberDataStatistics::getRecordTime, startOfDay, endOfDay));
        businessMemberDataStatisticsMapper.insert(businessMemberDataStatistics);
        XxlJobHelper.log("任务recordBusinessMemberData执行结束.....");
    }

    /**
     * 记录商家订单统计数据
     */
    @XxlJob("recordBusinessOrderData")
    public void recordBusinessOrderData() {
        XxlJobHelper.log("任务recordBusinessOrderData执行中.....");
        Date startOfDay = DateUtils.getStartOfDay(DateUtils.addDays(new Date(), -1));
        Date endOfDay = DateUtils.getEndOfDay(DateUtils.addDays(new Date(), -1));
        List<OrderVideoStatisticsDetailVO> orderVideoStatisticsDetail = remoteService.orderVideoStatisticsDetail(OrderVideoStatisticsDTO.builder()
                .payTimeBegin(startOfDay)
                .payTimeEnd(endOfDay)
                .build());
        if (CollUtil.isEmpty(orderVideoStatisticsDetail)) {
            return;
        }
        List<Long> businessId = orderVideoStatisticsDetail.stream().map(OrderVideoStatisticsDetailVO::getMerchantId).collect(Collectors.toList());
        List<Business> businessList = businessMapper.selectBatchIds(businessId);
        Map<Long, Business> businessMap = new HashMap<>();

        if (CollUtil.isNotEmpty(businessList)) {
            businessMap = businessList.stream().collect(Collectors.toMap(Business::getId, Function.identity()));
        }


        List<BusinessOrderDataStatistics> businessOrderDataStatistics = new ArrayList<>();
        for (OrderVideoStatisticsDetailVO item : orderVideoStatisticsDetail) {
            Business business = businessMap.get(item.getMerchantId());
            BusinessOrderDataStatistics entity = new BusinessOrderDataStatistics();
            entity.setRecordTime(startOfDay);
            entity.setBusinessId(item.getMerchantId());
            if (ObjectUtil.isNotNull(business) && ObjectUtil.isNotNull(business.getMemberFirstTime()) && DateUtils.getDayDifference(business.getMemberFirstTime(), startOfDay) > 365) {
                entity.setBusinessMemberType(2);
            } else {
                entity.setBusinessMemberType(1);
            }

            entity.setVideoCount(item.getOrderVideoTotal());
            businessOrderDataStatistics.add(entity);
        }

        businessOrderDataStatisticsMapper.delete(new LambdaQueryWrapper<BusinessOrderDataStatistics>()
                .between(BusinessOrderDataStatistics::getRecordTime, startOfDay, endOfDay));
        businessOrderDataStatisticsMapper.saveBatch(businessOrderDataStatistics);
        XxlJobHelper.log("任务recordBusinessOrderData执行结束.....");
    }


    /**
     * 记录商家订单统计数据
     */
    @XxlJob("initBusinessMemberValidityFlowRechargeCount")
    public void initBusinessMemberValidityFlowRechargeCount() {
        XxlJobHelper.log("任务initBusinessMemberValidityFlowRechargeCount执行中.....");
        List<BusinessMemberValidityFlow> businessMemberValidityFlows = businessMemberValidityFlowMapper.selectList(new LambdaQueryWrapper<BusinessMemberValidityFlow>());
        if (CollUtil.isEmpty(businessMemberValidityFlows)) {
            return;
        }
        Map<Long, List<BusinessMemberValidityFlow>> businessMemberValidityFlowMap = businessMemberValidityFlows.stream()
                .collect(Collectors.groupingBy(
                        BusinessMemberValidityFlow::getBusinessId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(BusinessMemberValidityFlow::getCreateTime))
                                        .collect(Collectors.toList())
                        )
                ));
        List<BusinessMemberValidityFlow> updateList = new ArrayList<>();

        for (List<BusinessMemberValidityFlow> itemList : businessMemberValidityFlowMap.values()) {
            Long rechargeCount = 0L;
            for (BusinessMemberValidityFlow item : itemList) {
                if (MemberValidTypeEnum.USER.getCode().equals(item.getType())) {
                    rechargeCount += 1;
                    BusinessMemberValidityFlow update = new BusinessMemberValidityFlow();
                    update.setId(item.getId());
                    update.setRechargeCount(rechargeCount);
                    updateList.add(update);
                }
                if (BusinessValidityChangeReasonTypeEnum.SEVEN_DAYS_WITHOUT_REASON.getCode().equals(item.getChangeReasonType())) {
                    rechargeCount -= 1;
                }
            }
        }
        if (CollUtil.isNotEmpty(updateList)){
            businessMemberValidityFlowMapper.updateBatchById(updateList);
        }
        XxlJobHelper.log("任务initBusinessMemberValidityFlowRechargeCount执行结束.....");
    }
}




