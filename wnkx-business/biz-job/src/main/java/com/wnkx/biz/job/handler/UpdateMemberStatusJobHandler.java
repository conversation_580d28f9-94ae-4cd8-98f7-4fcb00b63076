package com.wnkx.biz.job.handler;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.constant.BusinessConstants;
import com.ruoyi.common.core.enums.BizUserAccountTypeEnum;
import com.ruoyi.common.core.enums.MemberTypeEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.enums.UpdateWechatRemarkType;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.wnkx.biz.business.service.IBusinessAccountService;
import com.wnkx.biz.business.service.IBusinessService;
import com.wnkx.biz.wechat.service.WechatService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/30
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UpdateMemberStatusJobHandler {
    private final IBusinessService businessService;
    private final WechatService wechatService;
    private final IBusinessAccountService businessAccountService;

    /**
     * 更新会员状态
     */
    @XxlJob("updateMemberStatus")
    @Transactional(rollbackFor = Exception.class)
    public void updateMemberStatus() {
        XxlJobHelper.log("任务updateMemberStatus执行中.....");
        BusinessDTO dto = new BusinessDTO();
        //是否会员
        dto.setMemberType(StatusTypeEnum.YES.getCode());
        //获取为会员状态下的商家列表
        List<Business> businesses = businessService.queryList(dto);
        if (StringUtils.isEmpty(businesses)) {
            XxlJobHelper.log("不存在会员数据.....");
            return;
        }
        //获取正常、即将过期的数据列表
        List<Business> businessList = new ArrayList<>();
        List<Business> expireBusinessList = new ArrayList<>();
        for (Business item : businesses) {
            if (StringUtils.isNull(item.getMemberValidity())) {
                continue;
            }
            Business business = new Business();
            business.setId(item.getId());
            if (DateUtils.getDatePoorDay(DateUtils.getStartOfDay(DateUtil.offsetDay(item.getMemberValidity(), 1)), new Date()) > BusinessConstants.preTimeout) {
                // 会员状态 且离过期时间大于30天
                business.setMemberCode(item.getMemberCode());
                business.setMemberType(StatusTypeEnum.YES.getCode());
                business.setMemberStatus(MemberTypeEnum.RECHARGE.getCode());
                businessList.add(business);
            } else if (DateUtils.getDatePoorDay(DateUtils.getStartOfDay(item.getMemberValidity()), new Date()) >= 0) {
                business.setMemberCode(item.getMemberCode());
                business.setMemberType(StatusTypeEnum.YES.getCode());
                business.setMemberStatus(MemberTypeEnum.NO_EXPIRE.getCode());
                businessList.add(business);
            } else if (DateUtils.getDatePoorDay(DateUtils.getStartOfDay(item.getMemberValidity()), new Date()) <= 0) {
                business.setMemberCode(item.getMemberCode());
                business.setMemberStatus(MemberTypeEnum.EXPIRE.getCode());
                business.setMemberType(StatusTypeEnum.NO.getCode());
                expireBusinessList.add(business);
                businessList.add(business);
            }
        }
        if (StringUtils.isNotEmpty(businessList)) {
            XxlJobHelper.log("执行更新会员操作....");
            businessService.updateBatchById(businessList);
        }
        if (StringUtils.isNotEmpty(expireBusinessList)) {
            XxlJobHelper.log("执行更新会员过期操作....");
            BusinessAccountDTO businessAccountDTO = new BusinessAccountDTO();
            businessAccountDTO.setBusinessIds(expireBusinessList.stream().map(Business::getId).collect(Collectors.toList()));
            Map<Long, String> bidCodeMap = businessList.stream().collect(Collectors.toMap(Business::getId, Business::getMemberCode));
            List<BusinessAccountVO> businessAccountVOS = businessAccountService.queryList(businessAccountDTO);
            Map<String, String> accountmap = new HashMap<>();
            Map<String, BizUserAccountTypeEnum> accountTypeEnumMap = new HashMap<>();
            for (BusinessAccountVO item : businessAccountVOS){
                accountmap.put(item.getExternalUserId(),bidCodeMap.get(item.getBusinessId()));
                accountTypeEnumMap.put(item.getExternalUserId(), StatusTypeEnum.YES.getCode().equals(item.getIsOwnerAccount()) ? BizUserAccountTypeEnum.OWNER_ACCOUNT : BizUserAccountTypeEnum.ACCOUNT);
            }
            try {
                wechatService.updateAccountRemarkBatch(
                        accountmap,
                        UpdateWechatRemarkType.EXPIRE,
                        accountTypeEnumMap
                );
            }catch (Exception e){
                XxlJobHelper.log("更新微信备注失败....商家编码:"+expireBusinessList.stream().map(Business::getMemberCode).collect(Collectors.joining(",")));
            }
        }
    }
}
