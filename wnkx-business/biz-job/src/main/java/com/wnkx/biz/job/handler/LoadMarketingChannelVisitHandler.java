package com.wnkx.biz.job.handler;

import java.math.BigDecimal;
import java.util.Date;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelVisitFlow;
import com.ruoyi.system.api.domain.entity.biz.channel.MarketingChannel;
import com.ruoyi.system.api.domain.entity.biz.channel.MarketingChannelVisitFlow;
import com.wnkx.biz.channel.service.DistributionChannelVisitFlowService;
import com.wnkx.biz.channel.service.IDistributionChannelService;
import com.wnkx.biz.channel.service.IMarketingChannelService;
import com.wnkx.biz.channel.service.IMarketingChannelVisitFlowService;
import com.wnkx.biz.core.ChannelCore;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;


/**
 * 批量更新商家联系我url
 *
 * <AUTHOR>
 * @date 2024/11/17
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class LoadMarketingChannelVisitHandler {
    private final IMarketingChannelVisitFlowService marketingChannelVisitFlowService;
    private final IMarketingChannelService marketingChannelService;
    private final IDistributionChannelService distributionChannelService;
    private final DistributionChannelVisitFlowService distributionChannelVisitFlowService;
    private final ChannelCore channelCore;
    private final RedisService redisService;

    @XxlJob("loadMarketingChannelVisitHandler")
    @Transactional(rollbackFor = Exception.class)
    public void loadMarketingChannelVisitHandler() {
        XxlJobHelper.log("开始执行加载独立访客数据");

        //加载今天数据
        loadDay(DateUtils.dateTime(DateUtils.YYYY_MM_DD, DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, new Date())));
        //加载昨天
        loadDay(DateUtils.dateTime(DateUtils.YYYY_MM_DD, DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.addDays(new Date(), -1))));

        //刷新市场渠道数量
        marketingChannelService.refreshUniqueVisitor();
        XxlJobHelper.handleSuccess("任务执行成功");
    }

    private void loadDay(Date loadDay) {
        List<MarketingChannel> marketingChannels = marketingChannelService.list();
        if (CollUtil.isEmpty(marketingChannels)) {
            return;
        }
        loadMarketingData(marketingChannels, loadDay);

        List<DistributionChannel> distributionChannels = distributionChannelService.list();
        if (CollUtil.isEmpty(distributionChannels)) {
            return;
        }
        loadDistributionData(distributionChannels, loadDay);

    }

    private void loadDistributionData(List<DistributionChannel> distributionChannels, Date loadDay) {
        //获取日期流水数据
        List<DistributionChannelVisitFlow> distributionChannelVisitFlows =
                distributionChannelVisitFlowService.list(new LambdaQueryWrapper<DistributionChannelVisitFlow>()
                        .eq(DistributionChannelVisitFlow::getCreateTime, loadDay));
        Map<Long, Long> tableMap = new HashMap<>();
        if (CollUtil.isNotEmpty(distributionChannelVisitFlows)) {
            //已存在的昨天的数据
            for (DistributionChannelVisitFlow item : distributionChannelVisitFlows) {
                tableMap.put(item.getChannelId(), Convert.toLong(item.getUniqueVisitor()));
            }
        }
        //每个redis中昨天市场渠道的数量
        Map<Long, Long> redisVisitMap = new HashMap<>();
        for (DistributionChannel item : distributionChannels) {
            String key = channelCore.getVisitKey(item.getDedicatedLinkCode(), DateUtils.parseDateToStr(DateUtils.YYYYMMDD, loadDay));
            Long num = redisService.zCard(key);
            redisVisitMap.put(item.getId(), Optional.ofNullable(num).orElse(0L));
        }

        List<DistributionChannelVisitFlow> distributionChannelVisitFlowList = new ArrayList<>();
        List<Long> deleteChannelIds = new ArrayList<>();
        for (DistributionChannel item : distributionChannels) {
            if (CollUtil.isNotEmpty(tableMap) && ObjectUtil.isNotNull(tableMap.get(item.getId()))) {
                if (tableMap.get(item.getId()).compareTo(redisVisitMap.get(item.getId())) >= 0) {
                    //昨天记录数据 >= refis昨天存储的数据     可能存在缓存被清楚的情况 跳过不更新
                    continue;
                } else {
                    //redis数据 > 数据库存储的数量
                    deleteChannelIds.add(item.getId());
                }
            }
            //获取昨天数据
            DistributionChannelVisitFlow distributionChannelVisitFlow = new DistributionChannelVisitFlow();
            distributionChannelVisitFlow.setChannelId(item.getId());
            distributionChannelVisitFlow.setUniqueVisitor(Convert.toInt(redisVisitMap.get(item.getId())));
            distributionChannelVisitFlow.setPageView(0);
            distributionChannelVisitFlow.setBounceRate(new BigDecimal("0"));
            distributionChannelVisitFlow.setCreateTime(loadDay);
            distributionChannelVisitFlowList.add(distributionChannelVisitFlow);
        }

        if (CollUtil.isNotEmpty(deleteChannelIds)) {
            distributionChannelVisitFlowService.remove(new LambdaQueryWrapper<DistributionChannelVisitFlow>()
                    .eq(DistributionChannelVisitFlow::getCreateTime, loadDay)
                    .in(DistributionChannelVisitFlow::getChannelId, deleteChannelIds)
            );
        }

        distributionChannelVisitFlowService.saveBatch(distributionChannelVisitFlowList);
    }

    private void loadMarketingData(List<MarketingChannel> marketingChannels, Date loadDay) {
        //获取日期流水数据
        List<MarketingChannelVisitFlow> marketingChannelVisitFlows =
                marketingChannelVisitFlowService.list(new LambdaQueryWrapper<MarketingChannelVisitFlow>()
                        .eq(MarketingChannelVisitFlow::getCreateTime, loadDay));
        Map<Long, Long> tableMap = new HashMap<>();
        if (CollUtil.isNotEmpty(marketingChannelVisitFlows)) {
            //已存在的昨天的数据
            for (MarketingChannelVisitFlow item : marketingChannelVisitFlows) {
                tableMap.put(item.getChannelId(), Convert.toLong(item.getUniqueVisitor()));
            }
        }
        //每个redis中昨天市场渠道的数量
        Map<Long, Long> redisVisitMap = new HashMap<>();
        for (MarketingChannel item : marketingChannels) {
            String key = channelCore.getVisitKey(item.getDedicatedLinkCode(), DateUtils.parseDateToStr(DateUtils.YYYYMMDD, loadDay));
            Long num = redisService.zCard(key);
            redisVisitMap.put(item.getId(), Optional.ofNullable(num).orElse(0L));
        }

        List<MarketingChannelVisitFlow> marketingChannelVisitFlowList = new ArrayList<>();
        List<Long> deleteChannelIds = new ArrayList<>();
        for (MarketingChannel item : marketingChannels) {
            if (CollUtil.isNotEmpty(tableMap) && ObjectUtil.isNotNull(tableMap.get(item.getId()))) {
                if (tableMap.get(item.getId()).compareTo(redisVisitMap.get(item.getId())) >= 0) {
                    //昨天记录数据 >= refis昨天存储的数据     可能存在缓存被清楚的情况 跳过不更新
                    continue;
                } else {
                    //redis数据 > 数据库存储的数量
                    deleteChannelIds.add(item.getId());
                }
            }
            //获取昨天数据
            MarketingChannelVisitFlow marketingChannelVisitFlow = new MarketingChannelVisitFlow();
            marketingChannelVisitFlow.setChannelId(item.getId());
            marketingChannelVisitFlow.setUniqueVisitor(Convert.toInt(redisVisitMap.get(item.getId())));
            marketingChannelVisitFlow.setPageView(0);
            marketingChannelVisitFlow.setBounceRate(new BigDecimal("0"));
            marketingChannelVisitFlow.setCreateTime(loadDay);
            marketingChannelVisitFlowList.add(marketingChannelVisitFlow);
        }

        if (CollUtil.isNotEmpty(deleteChannelIds)) {
            marketingChannelVisitFlowService.remove(new LambdaQueryWrapper<MarketingChannelVisitFlow>()
                    .eq(MarketingChannelVisitFlow::getCreateTime, loadDay)
                    .in(MarketingChannelVisitFlow::getChannelId, deleteChannelIds)
            );
        }

        marketingChannelVisitFlowService.saveBatch(marketingChannelVisitFlowList);
    }
}
