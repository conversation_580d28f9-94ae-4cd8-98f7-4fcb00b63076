package com.wnkx.biz.job.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.system.api.domain.entity.biz.amazon.AmazonGoodsPic;
import com.wnkx.biz.amazon.service.AmazonGoodsPicService;
import com.wnkx.biz.amazon.service.AmazonImageService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class AmazonImagerHistoryHandler {
    private final AmazonGoodsPicService amazonGoodsPicService;
    private final AmazonImageService amazonImageService;


    @XxlJob("amazonImageHistoryHandler")
    public void amazonImageHistoryHandler() {
        List<AmazonGoodsPic> list = amazonGoodsPicService.list(new LambdaQueryWrapper<AmazonGoodsPic>()
                .isNull(AmazonGoodsPic::getSpecInfo));
        XxlJobHelper.log("数据总量:" + list.size());
        list.forEach(amazonGoodsPic -> {
                    AmazonGoodsPic result = amazonImageService.getAmazonProductInfo(amazonGoodsPic);
                    if (result != null) {
                        amazonGoodsPicService.update(null, new LambdaUpdateWrapper<AmazonGoodsPic>()
                                .eq(AmazonGoodsPic::getGoodsId, result.getGoodsId())
                                .set(AmazonGoodsPic::getSpecInfo, result.getSpecInfo())
                                .set(AmazonGoodsPic::getProductName, result.getProductName())
                                .set(AmazonGoodsPic::getProductChineseName, result.getProductChineseName())
                        );
                        XxlJobHelper.log("处理数据:" + result.getGoodsId());
                    }
                }
        );

    }

    @XxlJob("verifyAmazonCookieStatus")
    public void verifyAmazonCookieStatus() {
        XxlJobHelper.log("验证cookie有效任务开始");
        amazonImageService.verifyAmazonCookie();
        XxlJobHelper.log("验证cookie有效任务结束");
    }

}
