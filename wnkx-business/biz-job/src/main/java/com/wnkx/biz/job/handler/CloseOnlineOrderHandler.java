package com.wnkx.biz.job.handler;

import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.wnkx.biz.business.service.IBusinessBalancePrepayService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 取消线上钱包支付订单
 * @create :2024-10-12 17:36
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class CloseOnlineOrderHandler {

    private final IBusinessBalancePrepayService businessBalancePrepayService;
    @Value("${order.video.closeOrderHours:720}")
    private Integer closeOrderHours;

    @XxlJob("closeOnlineOrderHandler")
    public void closeOrderHandler() {
        XxlJobHelper.log("任务closeOnlineOrderHandler执行中.....");
        //查询n天前、未支付的订单
        List<BusinessBalancePrepay> closeOnlineOrders = businessBalancePrepayService.getCloseOnlineOrders(DateUtils.addYears(DateUtils.getNowDate(), -1)
                , DateUtils.addHours(DateUtils.getNowDate(), -closeOrderHours));

        if (CollUtil.isEmpty(closeOnlineOrders)) {
            return;
        }
        for (BusinessBalancePrepay item : closeOnlineOrders) {
            try {
                XxlJobHelper.log("订单：" + item.getPrepayNum() + ", 创建时间：" + DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, item.getCreatTime()) + ", 状态：" + item.getAuditStatus() + "，开始取消");
                businessBalancePrepayService.cancelOnlineOrder(item.getId());
            } catch (Exception e) {
                log.error("取消订单(closeOnlineOrderHandler)失败：{}", e);
            }
        }
    }
}
