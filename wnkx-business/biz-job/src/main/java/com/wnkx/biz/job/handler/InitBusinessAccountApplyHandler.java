package com.wnkx.biz.job.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.system.api.domain.entity.BusinessAccountRebindLog;
import com.ruoyi.system.api.domain.entity.biz.business.BizUser;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccount;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountApply;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountRebindLogVo;
import com.wnkx.biz.business.mapper.BusinessAccountApplyMapper;
import com.wnkx.biz.business.service.IBizUserService;
import com.wnkx.biz.business.service.IBusinessAccountApplyService;
import com.wnkx.biz.business.service.IBusinessAccountRebindLogService;
import com.wnkx.biz.business.service.IBusinessAccountService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class InitBusinessAccountApplyHandler {
    private final IBizUserService bizUserService;
    private final IBusinessAccountApplyService accountApplyService;
    private final IBusinessAccountRebindLogService rebindLogService;
    private final IBusinessAccountService accountService;
    private final BusinessAccountApplyMapper accountApplyMapper;

    @XxlJob("applyLogInit")
    @Transactional(rollbackFor = Exception.class)
    public void applyLogInitHandler() {
        XxlJobHelper.log("子账号记录初始化开始...");
        List<BusinessAccountRebindLogVo> list = accountApplyMapper.selectApplyAndRebindLog();

        List<BusinessAccountRebindLog> LogList = rebindLogService.list();
        Map<Long, BusinessAccountRebindLog> rebindLogMap = CollUtil.isNotEmpty(LogList)
                ? LogList.stream().collect(Collectors.toMap(BusinessAccountRebindLog::getApplyId, bar -> bar))
                : new HashMap<>();

        List<BusinessAccountRebindLog> finalRebindLog = new ArrayList<>();
        for (BusinessAccountRebindLogVo item : list) {
            BusinessAccountRebindLog logModel = new BusinessAccountRebindLog();
            BeanUtil.copyProperties(item, logModel);
            //如果已经存在略过
            if (null != rebindLogMap && null == rebindLogMap.get(item.getApplyId())) {
                finalRebindLog.add(logModel);
            }
        }

        if (CollUtil.isNotEmpty(finalRebindLog)) {
            rebindLogService.saveBatch(finalRebindLog);
        }
        XxlJobHelper.log("子账号记录初始化结束...");
    }
}
