package com.wnkx.biz.job.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecord;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecordRelevance;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecordWithdrawal;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderMemberChannel;
import com.wnkx.biz.channel.service.*;
import com.wnkx.biz.remote.RemoteService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-07-10 13:52
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class InitMemberSeedRecordHandler {
    private final MemberSeedRecordService memberSeedRecordService;
    private final MemberSeedRecordRelevanceService memberSeedRecordRelevanceService;
    private final MemberSeedRecordWithdrawalService memberSeedRecordWithdrawalService;
    private final IOrderMemberChannelService orderMemberChannelService;
    private final IDistributionChannelService distributionChannelService;
    private final RemoteService remoteService;

    @XxlJob("initMemberSeedRecord")
    @Transactional(rollbackFor = Exception.class)
    public void initMemberSeedRecord() {
        XxlJobHelper.log("任务initMemberSeedRecord执行中.....");
        List<OrderMemberChannel> orderMemberChannels = orderMemberChannelService.list();
        if (CollUtil.isEmpty(orderMemberChannels)) {
            XxlJobHelper.log("会员渠道记录表获取错误.....");
            return;
        }
        List<String> orderNums = orderMemberChannels.stream().map(OrderMemberChannel::getOrderNum).collect(Collectors.toList());
        List<Order> orderListByOrderNums = remoteService.getOrderListByOrderNums(orderNums);
        if (CollUtil.isEmpty(orderListByOrderNums)) {
            XxlJobHelper.log("订单获取错误.....");
            return;
        }
        Map<String, Order> orderMap = orderListByOrderNums.stream().collect(Collectors.toMap(Order::getOrderNum, Function.identity()));


        List<DistributionChannel> channelList = distributionChannelService.list();
        if (CollUtil.isEmpty(channelList)) {
            XxlJobHelper.log("获取渠道错误.....");
            return;
        }
        Map<Long, DistributionChannel> channelMap = channelList.stream().collect(Collectors.toMap(DistributionChannel::getId, Function.identity()));

        List<MemberSeedRecord> memberSeedRecords = new ArrayList<>();
        for (OrderMemberChannel orderMemberChannel : orderMemberChannels) {
            DistributionChannel distributionChannel = channelMap.get(orderMemberChannel.getChannelId());
            if (ObjectUtil.isNull(distributionChannel) || ChannelTypeEnum.FISSION.getCode().equals(distributionChannel.getChannelType())) {
                XxlJobHelper.log("获取渠道失败：" + orderMemberChannel.getChannelName());
                continue;
            }
            Order order = orderMap.get(orderMemberChannel.getOrderNum());
            if (ObjectUtil.isNull(order)) {
                XxlJobHelper.log("获取订单失败：" + orderMemberChannel.getOrderNum());
                continue;
            }
            BigDecimal amount = orderMemberChannel.getRealPayAmount().subtract(Optional.ofNullable(order.getTaxPointCost()).orElse(BigDecimal.ZERO));
            MemberSeedRecord memberSeedRecord = new MemberSeedRecord();
            memberSeedRecord.setChannelId(orderMemberChannel.getChannelId());
            memberSeedRecord.setChannelType(orderMemberChannel.getChannelType());
            memberSeedRecord.setChannelBizUserId(null);
            memberSeedRecord.setChannelName(orderMemberChannel.getChannelName());
            memberSeedRecord.setChannelPhone(orderMemberChannel.getChannelPhone());
            memberSeedRecord.setChannelSeedId(distributionChannel.getSeedId());
            memberSeedRecord.setSeedCode(orderMemberChannel.getSeedCode());
            memberSeedRecord.setBusinessId(orderMemberChannel.getBusinessId());
            memberSeedRecord.setBusinessName(orderMemberChannel.getBusinessName());
            memberSeedRecord.setBizUserId(orderMemberChannel.getBizUserId());
            memberSeedRecord.setBizUserNickName(orderMemberChannel.getBizUserNickName());
            memberSeedRecord.setBizUserPhone(orderMemberChannel.getBizUserPhone());
            memberSeedRecord.setMemberCode(orderMemberChannel.getMemberCode());
            memberSeedRecord.setMemberPackageType(orderMemberChannel.getMemberPackageType());
            memberSeedRecord.setOrderNum(orderMemberChannel.getOrderNum());
            memberSeedRecord.setRealPayAmount(orderMemberChannel.getRealPayAmount());
            memberSeedRecord.setCurrency(orderMemberChannel.getCurrency());
            memberSeedRecord.setRealPayAmountCurrency(orderMemberChannel.getRealPayAmountCurrency());
            memberSeedRecord.setPayType(orderMemberChannel.getPayType());
            memberSeedRecord.setSettleType(ChannelDiscountTypeEnum.FIXED_RATIO.getCode());
            memberSeedRecord.setSettleRage(orderMemberChannel.getSettleRage());
            memberSeedRecord.setSettleAmount(orderMemberChannel.getSettleAmount());
            memberSeedRecord.setSeedCodeDiscount(amount.subtract(orderMemberChannel.getSettleAmount()));
            memberSeedRecord.setBuyTime(order.getPayTime());
            memberSeedRecord.setCreateTime(orderMemberChannel.getCreateTime());
            if (SettleStatusEnum.WAIT_SETTLED.getCode().equals(orderMemberChannel.getSettleStatus())) {
                memberSeedRecord.setStatus(MemberSeedRecordStatusEnum.PENDING_DEPOSIT.getCode());
            } else if (SettleStatusEnum.UN_SETTLED.getCode().equals(orderMemberChannel.getSettleStatus())) {
                memberSeedRecord.setStatus(MemberSeedRecordStatusEnum.PENDING_WITHDRAWAL.getCode());
                if (orderMemberChannel.getSettleAmount().compareTo(BigDecimal.ZERO) == 0) {
                    memberSeedRecord.setStatus(MemberSeedRecordStatusEnum.UN_NEED_TRANSFERRED.getCode());
                }
            } else if (SettleStatusEnum.UNABLE_SETTLED.getCode().equals(orderMemberChannel.getSettleStatus())) {
                memberSeedRecord.setStatus(MemberSeedRecordStatusEnum.DEPOSIT_FAILED.getCode());
            } else if (SettleStatusEnum.SETTLED.getCode().equals(orderMemberChannel.getSettleStatus())) {
                memberSeedRecord.setStatus(MemberSeedRecordStatusEnum.TRANSFERRED.getCode());
                memberSeedRecordService.save(memberSeedRecord);
                MemberSeedRecordWithdrawal memberSeedRecordWithdrawal = new MemberSeedRecordWithdrawal();
                memberSeedRecordWithdrawal.setWithdrawalNum(memberSeedRecordWithdrawalService.initWithdrawalNum(orderMemberChannel.getId()));
                memberSeedRecordWithdrawal.setChannelId(memberSeedRecord.getChannelId());
                memberSeedRecordWithdrawal.setChannelSeedId(memberSeedRecord.getChannelSeedId());
                memberSeedRecordWithdrawal.setChannelSeedCode(memberSeedRecord.getSeedCode());
                memberSeedRecordWithdrawal.setChannelType(memberSeedRecord.getChannelType());
                memberSeedRecordWithdrawal.setSettleAmount(memberSeedRecord.getSettleAmount());
                memberSeedRecordWithdrawal.setWithdrawalAccountType(0);
                memberSeedRecordWithdrawal.setPayeeName("");
                memberSeedRecordWithdrawal.setPayeePhone("");
                memberSeedRecordWithdrawal.setPayeeIdentityCard("");
                memberSeedRecordWithdrawal.setPayeeAccount("");
                memberSeedRecordWithdrawal.setStatus(MemberSeedRecordStatusEnum.TRANSFERRED.getCode());
                memberSeedRecordWithdrawal.setAuditTime(orderMemberChannel.getOperationTime());
                memberSeedRecordWithdrawal.setAuditRemark(orderMemberChannel.getRemark());
                memberSeedRecordWithdrawal.setAuditUserId(orderMemberChannel.getSettleUserId());
                memberSeedRecordWithdrawal.setAuditUserName(orderMemberChannel.getSettleUserName());
                memberSeedRecordWithdrawal.setWithdrawalTime(orderMemberChannel.getOperationTime());
                memberSeedRecordWithdrawal.setWithdrawalRemark(orderMemberChannel.getRemark());
                memberSeedRecordWithdrawal.setWithdrawalUserId(orderMemberChannel.getSettleUserId());
                memberSeedRecordWithdrawal.setWithdrawalUserName(orderMemberChannel.getSettleUserName());
                memberSeedRecordWithdrawal.setPayoutTime(orderMemberChannel.getSettleTime());
                memberSeedRecordWithdrawal.setPayAccount("");
                memberSeedRecordWithdrawal.setResourceUrl(orderMemberChannel.getSettleResourceUrl());
                memberSeedRecordWithdrawal.setCreateTime(DateUtils.addDays(orderMemberChannel.getCreateTime(), 7));
                memberSeedRecordWithdrawal.setUpdateTime(orderMemberChannel.getOperationTime());
                memberSeedRecordWithdrawalService.save(memberSeedRecordWithdrawal);
                MemberSeedRecordRelevance memberSeedRecordRelevance = new MemberSeedRecordRelevance();
                memberSeedRecordRelevance.setMemberSeedRecordId(memberSeedRecord.getId());
                memberSeedRecordRelevance.setMemberSeedRecordWithdrawalId(memberSeedRecordWithdrawal.getId());
                memberSeedRecordRelevanceService.save(memberSeedRecordRelevance);
                continue;

            } else {
                continue;
            }

            memberSeedRecords.add(memberSeedRecord);
        }
        memberSeedRecordService.saveBatch(memberSeedRecords);

    }
}
