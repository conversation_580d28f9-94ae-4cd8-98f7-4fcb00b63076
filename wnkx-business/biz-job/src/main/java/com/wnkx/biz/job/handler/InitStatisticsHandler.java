package com.wnkx.biz.job.handler;

import com.wnkx.biz.remote.RemoteService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-07-10 13:52
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class InitStatisticsHandler {

    private final RemoteService remoteService;

    @XxlJob("initStatisticsHandler")
    @Transactional(rollbackFor = Exception.class)
    public void initMemberStatistics() {
        XxlJobHelper.log("任务initMemberStatistics执行中.....");



        XxlJobHelper.log("任务initMemberStatistics执行结束.....");
    }
}
