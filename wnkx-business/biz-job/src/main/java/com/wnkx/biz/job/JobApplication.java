package com.wnkx.biz.job;

import com.ruoyi.common.security.annotation.EnableCustomConfig;
import com.ruoyi.common.security.annotation.EnableRyFeignClients;
import com.wnkx.biz.job.handler.InitMemberSeedRecordHandler;
import com.wnkx.biz.job.handler.RecordBusinessStatisticsHandler;
import lombok.RequiredArgsConstructor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/5/16
 */
@EnableDiscoveryClient
@EnableTransactionManagement
@MapperScan(basePackages = {"com.wnkx.biz.**.mapper"})
@SpringBootApplication(scanBasePackages = {"com.wnkx.biz"})
@EnableRyFeignClients
@RestController
@EnableCustomConfig
@RequiredArgsConstructor
public class JobApplication {
    public static void main(String[] args) {
        SpringApplication.run(JobApplication.class, args);
    }

    @GetMapping("/hello")
    public String hello()
    {
        return "hello";
    }
}
