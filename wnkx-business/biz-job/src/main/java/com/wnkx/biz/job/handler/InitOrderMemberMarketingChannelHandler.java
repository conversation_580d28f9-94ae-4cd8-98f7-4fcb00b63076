package com.wnkx.biz.job.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.dto.order.OrderPayDetailDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.OrderMemberMarketingChannel;
import com.ruoyi.system.api.domain.vo.order.finace.OrderPayDetailVO;
import com.wnkx.biz.channel.mapper.OrderMemberMarketingChannelMapper;
import com.wnkx.biz.remote.RemoteService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/6/30
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class InitOrderMemberMarketingChannelHandler {
    private final RemoteService remoteService;
    private final OrderMemberMarketingChannelMapper orderMemberMarketingChannelMapper;

    /**
     * 初始化 会员市场渠道记录表
     */
    @XxlJob("initOrderMemberMarketingChannelHandler")
    @Transactional(rollbackFor = Exception.class)
    public void initOrderMemberMarketingChannelHandler() {
        XxlJobHelper.log("任务InitOrderMemberMarketingChannelHandler执行中.....");
        orderMemberMarketingChannelMapper.delete(new LambdaQueryWrapper<OrderMemberMarketingChannel>().isNotNull(OrderMemberMarketingChannel::getId));
        List<OrderMemberMarketingChannel> initOrderMemberMarketingChannel = orderMemberMarketingChannelMapper.getInitOrderMemberMarketingChannel();

        if (CollUtil.isEmpty(initOrderMemberMarketingChannel)) {
            return;
        }

        List<String> orderNums = initOrderMemberMarketingChannel.stream().map(OrderMemberMarketingChannel::getOrderNum).collect(Collectors.toList());
        List<OrderPayDetailVO> basePayDetailVOS = remoteService.getBasePayDetailVOS(OrderPayDetailDTO.builder().orderNums(orderNums).build());
        if (CollUtil.isEmpty(basePayDetailVOS)) {
            XxlJobHelper.log("basePayDetailVOS为空.....");
            return;
        }
        Map<String, OrderPayDetailVO> orderPayDetailMap = basePayDetailVOS.stream().collect(Collectors.toMap(OrderPayDetailVO::getOrderNum, p -> p));

        for (OrderMemberMarketingChannel orderMemberMarketingChannel : initOrderMemberMarketingChannel) {
            OrderPayDetailVO orderPayDetailVO = orderPayDetailMap.get(orderMemberMarketingChannel.getOrderNum());
            if (ObjectUtil.isNull(orderPayDetailVO)) {
                return;
            }

            orderMemberMarketingChannel.setRealPayAmount(orderPayDetailVO.getRealPayAmount());
        }
        orderMemberMarketingChannelMapper.saveBatch(initOrderMemberMarketingChannel);
    }
}
