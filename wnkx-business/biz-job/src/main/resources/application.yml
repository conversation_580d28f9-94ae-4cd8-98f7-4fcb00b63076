# job 应用配置信息
server:
  port: 7211
  shutdown: graceful
  tomcat:
    threads:
      min-spare: 1 # 正常情况下 job 应用不提供 web 服务，通常仅提供一个用于应用心跳检测的接口，因此不需要太多的服务线程
spring:
  application:
    name: biz-job
  lifecycle:
    timeout-per-shutdown-phase: 30s
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${wnkx.ds.ip}:${wnkx.ds.port}/${wnkx.ds.biz-db:biz-center}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: ${wnkx.ds.username}
    password: ${wnkx.ds.password}
mybatis-plus:
  #  configuration:
  #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.wnkx.biz.**.domain
  global-config:
    db-config:
      id-type: auto
xxl:
  job:
    executor:
      appname: biz-job
      ip:
      address:
      port: 9987
      logpath: logs/xxl-job/jobhandler
      logretentiondays: 3
    accessToken: default_token
