package com.wnkx.biz.casus.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.casus.CasusVideoQueryDTO;
import com.ruoyi.system.api.domain.dto.order.casus.CasusVideoSaveDTO;
import com.ruoyi.system.api.domain.dto.order.casus.CasusVideoUpdateDTO;
import com.ruoyi.system.api.domain.entity.order.casus.CasusVideo;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【casus_video(案例视频表)】的数据库操作Service
* @createDate 2024-07-10 13:35:30
*/
@Validated
public interface ICasusVideoService extends IService<CasusVideo> {

    /**
     * 保存案例视频
     * @param dto
     */
    void save(@Valid CasusVideoSaveDTO dto);


    /**
     * 修改案例视频
     * @param dto
     */
    void update(@Valid CasusVideoUpdateDTO dto);
    /**
     * 获取视频列表
     * @param dto
     * @return
     */
    List<CasusVideo> queryList(CasusVideoQueryDTO dto);

    /**
     * 删除视频
     * @param id
     */
    void delete(Long id);


}
