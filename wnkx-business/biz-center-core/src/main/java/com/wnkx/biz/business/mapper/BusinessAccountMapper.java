package com.wnkx.biz.business.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccount;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【business_account(商家账号表)】的数据库操作Mapper
* @createDate 2024-06-20 10:30:41
* @Entity com.ruoyi.system.api.domain.entity.biz.business.BusinessAccount
*/
@Mapper
public interface BusinessAccountMapper extends SuperMapper<BusinessAccount> {

    /**
     * 获取账号数据列表(商家账号  不管是否绑定登录账号)
     * @param dto
     * @return
     */
    List<BusinessAccountDetailVO> getBusinessAccountDetailVOs(@Param("dto") BusinessAccountDetailDTO dto);

    /**
     * 四个数据都是可以保证唯一的(已绑定登录账号的商家账号)
     * @param dto
     * @return
     */
    BusinessAccountVO queryOne(@Param("dto") BusinessAccountDTO dto);


    /**
     * 查询账号列表(已绑定登录账号的商家账号)
     * @param dto
     * @return
     */
    List<BusinessAccountVO> queryList(@Param("dto") BusinessAccountDTO dto);

    /**
     * 查询账号列表(商家账号列表，不管是否绑定登录账号)
     * @param dto
     * @return
     */
    List<BusinessAccountVO> list(@Param("dto") BusinessAccountDTO dto);

    /**
     * 账号解绑登录账号
     *
     * @param id
     */
    default void unbindLoginUser(Long id) {
        this.update(null, new LambdaUpdateWrapper<BusinessAccount>()
                .set(BusinessAccount::getBizUserId, null)
                .set(BusinessAccount::getName, "")
                .eq(BusinessAccount::getId, id));
    }

    /**
     * 绑定商家账号 - 登录商家账号
     * *
     * @param id
     * @param userId
     */
    default void bindingLoginUser(Long id, Long userId) {
        this.update(null, new LambdaUpdateWrapper<BusinessAccount>()
                .set(BusinessAccount::getBizUserId, userId)
                .eq(BusinessAccount::getId, id));
    }

    /**
     * 根据账号account删除商家账号信息
     * @param account
     */
    default void deleteByAccount(String account){
        this.delete(new LambdaQueryWrapper<BusinessAccount>()
                .eq(BusinessAccount::getAccount, account));

    }

    /**
     * 根据账号account获取商家账号数据*
     * @param account
     * @return
     */
    default BusinessAccount getByAccount(String account){
        return this.selectOne(new LambdaQueryWrapper<BusinessAccount>()
                .eq(BusinessAccount::getAccount, account));

    }
    /**
     * 根据登录账号id获取商家账号数据*
     * @param bizUserId 登录账号id
     * @return
     */
    default BusinessAccount getByBizUserId(Long bizUserId){
        return this.selectOne(new LambdaQueryWrapper<BusinessAccount>()
                .eq(BusinessAccount::getBizUserId, bizUserId));

    }

    String getmemberCodeByAccountId(Long businessAccountId);

    List<String> getBusinessManagerList(Integer type);

    /**
     * 商家回访-填写回访记录-回访账号下拉框
     */
    List<BusinessAccountVO> writeReturnVisitAccount(@Param("businessId") Long businessId);

    /**
     * 通过商家ID查询数据
     */
    default List<BusinessAccount> selectListByBusinessId(Long businessId) {
        return selectList(new LambdaQueryWrapper<BusinessAccount>()
                .eq(BusinessAccount::getBusinessId, businessId)
        );
    }
}
