package com.wnkx.biz.model.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.EventExecuteObjectEnum;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.model.ModelChangeRecordDTO;
import com.ruoyi.system.api.domain.entity.biz.model.ModelChangeRecord;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedOustModelCountInfo;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelChangeRecordVO;
import com.wnkx.biz.model.mapper.ModelChangeRecordMapper;
import com.wnkx.biz.model.service.ModelChangeRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/11 17:10
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ModelChangeRecordServiceImpl extends ServiceImpl<ModelChangeRecordMapper, ModelChangeRecord> implements ModelChangeRecordService {

    /**
     * 获取英文部客服 淘汰模特数
     */
    @Override
    public List<CustomerServiceAddedOustModelCountInfo> getEnglishCustomerServiceOustModelCounts(String date) {
        return baseMapper.getEnglishCustomerServiceOustModelCounts("'%Y-%m'", date);
    }

    /**
     * 获取某时间内 最终修改为 暂停/取消合作 的模特ID
     */
    @Override
    public List<Long> getFinalOustModelIdsByDate(String dateFormat, String date) {
        return baseMapper.getFinalOustModelIdsByDate(dateFormat, date);
    }

    /**
     * 批量新增模特变更记录
     */
    @Override
    public void saveBatchModelChangeRecord(List<ModelChangeRecordDTO> dtoList) {
        List<ModelChangeRecord> modelChangeRecords = BeanUtil.copyToList(dtoList, ModelChangeRecord.class);
        LoginUserInfoVO loginUserInfoVo = SecurityUtils.getLoginUserInfoVo();
        for (ModelChangeRecord modelChangeRecord : modelChangeRecords) {
            modelChangeRecord.setOperateTime(DateUtil.date());
            modelChangeRecord.setOperateObject(loginUserInfoVo.getUserType());
            modelChangeRecord.setOperateUserId(loginUserInfoVo.getUserId());
            modelChangeRecord.setOperateUserName(EventExecuteObjectEnum.SYSTEM.getCode().equals(loginUserInfoVo.getUserType()) ? "系统" : loginUserInfoVo.getName());
        }
        baseMapper.saveBatch(modelChangeRecords);
    }

    /**
     * 查询模特变更记录
     */
    @Override
    public List<ModelChangeRecordVO> listByModelId(Long modelId) {
        List<ModelChangeRecord> list = baseMapper.listByModelId(modelId);
        return BeanUtil.copyToList(list, ModelChangeRecordVO.class);
    }

    /**
     * 新增模特变更记录
     */
    @Override
    public void saveModelChangeRecord(ModelChangeRecordDTO dto) {
        ModelChangeRecord modelChangeRecord = BeanUtil.copyProperties(dto, ModelChangeRecord.class);
        modelChangeRecord.setOperateTime(DateUtil.date());
        LoginUserInfoVO loginUserInfoVo = SecurityUtils.getLoginUserInfoVo();
        modelChangeRecord.setOperateObject(loginUserInfoVo.getUserType());
        modelChangeRecord.setOperateUserId(loginUserInfoVo.getUserId());
        modelChangeRecord.setOperateUserName(EventExecuteObjectEnum.SYSTEM.getCode().equals(loginUserInfoVo.getUserType()) ? "系统" : loginUserInfoVo.getName());
        baseMapper.insert(modelChangeRecord);
    }
}
