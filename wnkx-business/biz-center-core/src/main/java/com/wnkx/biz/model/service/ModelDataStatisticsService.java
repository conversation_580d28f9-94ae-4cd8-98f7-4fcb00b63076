package com.wnkx.biz.model.service;

import com.ruoyi.system.api.domain.vo.biz.datastatistics.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/9 16:01
 */
public interface ModelDataStatisticsService {

    /**
     * 模特数据-模特基础数据
     */
    ModelBasicDataVO getModelBasicData();

    /**
     * 模特数据-模特数量趋势分析
     */
    ModelNumberTrendAnalysisVO getModelNumberTrendAnalysis(Date beginTime, Date endTime);

    /**
     * 模特数据-每月新增模特分析
     */
    ModelCountAnalysisVO getNewModelAnalysis(String date);

    /**
     * 模特数据-每月淘汰模特分析
     */
    ModelCountAnalysisVO getOustModelAnalysis(String date);

    /**
     * 模特数据-模特类型分析
     */
    ModelTypeAnalysisVO getModelTypeAnalysis(Integer status);

    /**
     * 模特数据-模特接单排行榜
     */
    ModelOrderRankingVO getModelOrderRanking(String date);

    /**
     * 模特数据-开发模特合作状态分布
     */
    DataPieChartVO getModelStatusData(Date beginTime, Date endTime, BigDecimal beginScore, BigDecimal endScore);

    /**
     * 模特数据-模特排单情况
     */
    DataPieChartVO getModelOrderScheduledData(Date beginTime, Date endTime, BigDecimal beginScore, BigDecimal endScore);
}
