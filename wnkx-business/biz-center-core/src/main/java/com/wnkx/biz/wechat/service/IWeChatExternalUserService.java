package com.wnkx.biz.wechat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatExternalUser;

/**
 * 企业微信外部联系人信息Service接口
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
public interface IWeChatExternalUserService extends IService<WeChatExternalUser> {

    /**
     * 添加企业微信外部联系人信息 如果存在则设置为启用 并更新信息 （通过externalUserid和unionid判断）
     */
    WeChatExternalUser saveOrUpdateWeChatExternalUser(WeChatExternalUser weChatExternalUser);

    /**
     * 通过externalUserid将WeChatExternalUser设置为禁用
     */
    void disableWeChatExternalUser(String externalUserid);

    /**
     * 通过unionid查询WeChatExternalUser
     */
    WeChatExternalUser getOneByUnionid(String unionid);

    boolean hasRecordUser(String externalUserid);

    void updateContactUserId(String externalUserId, String connectUserId);

    void updateContactUserInfo(String externalUserId, String connectUserId, String contractUserName);
}
