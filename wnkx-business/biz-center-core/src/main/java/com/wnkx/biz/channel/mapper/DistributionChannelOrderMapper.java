package com.wnkx.biz.channel.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.utils.DateUtils;
import com.wnkx.db.mapper.SuperMapper;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelOrder;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【distribution_channel_order(分销渠道订单表)】的数据库操作Mapper
 * @createDate 2024-12-09 16:15:59
 * @Entity com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelOrder
 */
public interface DistributionChannelOrderMapper extends SuperMapper<DistributionChannelOrder> {

    /**
     * 根据获取今日渠道订单数据
     *
     * @return
     */
    default List<DistributionChannelOrder> getTodayDistributionChannelOrder() {
        return this.selectList(new LambdaQueryWrapper<DistributionChannelOrder>()
                .between(DistributionChannelOrder::getPayTime, DateUtils.getStartOfToday(), DateUtils.getEndOfToday()));
    }
}




