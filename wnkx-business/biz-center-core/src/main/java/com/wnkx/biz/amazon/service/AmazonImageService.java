package com.wnkx.biz.amazon.service;

import com.ruoyi.system.api.domain.dto.order.AsyncCrawlTask;
import com.ruoyi.system.api.domain.entity.biz.amazon.AmazonGoodsPic;
import com.ruoyi.system.api.domain.vo.AmazonProductInfo;
import org.springframework.scheduling.annotation.Async;

/**
 * <AUTHOR>
 * @date 2024/8/8
 */
public interface AmazonImageService {
    /**
     * 异步批量抓取amazon产品链接图片并更新视频订单
     */
    @Async
    void asyncUpdateOrderVideoImage(AsyncCrawlTask asyncCrawlTask);

    AmazonProductInfo getAmazonProductInfo(String url);

    void verifyAmazonCookie();

    AmazonGoodsPic getAmazonProductInfo(AmazonGoodsPic amazonGoodsPic);

    AmazonProductInfo getTiktokInfo(String url);
}
