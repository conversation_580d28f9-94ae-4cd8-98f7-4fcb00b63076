package com.wnkx.biz.core;

import cn.hutool.core.util.ObjectUtil;
import com.aliyun.dypnsapi20170525.Client;
import com.aliyun.dypnsapi20170525.models.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.system.api.domain.dto.biz.aliyun.PhoneWithTokenDTO;
import com.wnkx.biz.config.AliyunConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-01-06 15:50
 **/
@Component
@RequiredArgsConstructor
@Slf4j
public class AliyunCore {
    private final Client client;
    private final AliyunConfig aliyunConfig;

    public GetAuthTokenResponseBody.GetAuthTokenResponseBodyTokenInfo getAuthToken() {
        try {
            GetAuthTokenRequest getAuthTokenRequest = new GetAuthTokenRequest().setUrl(aliyunConfig.getUrl())
                    .setOrigin(aliyunConfig.getOrigin())
                    .setSceneCode(aliyunConfig.getSceneCode());
            GetAuthTokenResponse authToken = client.getAuthToken(getAuthTokenRequest);
            if (authToken.statusCode == HttpStatus.OK.value()) {
                return authToken.getBody().getTokenInfo();
            }
        } catch (Exception e) {
            log.error("获取数据失败", e);
        }
        throw new ServiceException("获取数据失败");
    }

    public GetPhoneWithTokenResponseBody.GetPhoneWithTokenResponseBodyData getPhoneWithToken(PhoneWithTokenDTO dto){
        try {
            GetPhoneWithTokenResponse phoneWithToken = client.getPhoneWithToken(new GetPhoneWithTokenRequest().setSpToken(dto.getSpToken()));
            if (phoneWithToken.statusCode == HttpStatus.OK.value()) {
                return phoneWithToken.getBody().getData();
            }
        } catch (Exception e) {
            log.error("获取数据失败", e);
        }
        throw new ServiceException("获取数据失败");

    }
    public Boolean verifyPhoneWithToken(VerifyPhoneWithTokenRequest verifyPhoneWithTokenRequest){
        try {

            VerifyPhoneWithTokenResponse verifyPhoneWithTokenResponse = client.verifyPhoneWithToken(verifyPhoneWithTokenRequest);
            if (verifyPhoneWithTokenResponse.statusCode == HttpStatus.OK.value()) {
                VerifyPhoneWithTokenResponseBody.VerifyPhoneWithTokenResponseBodyGateVerify gateVerify = verifyPhoneWithTokenResponse.getBody().getGateVerify();
                if (ObjectUtil.isNotNull(gateVerify) && gateVerify.verifyResult.equals("PASS")) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("获取数据失败", e);
        }
        return false;

    }
}
