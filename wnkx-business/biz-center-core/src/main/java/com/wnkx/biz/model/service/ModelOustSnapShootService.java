package com.wnkx.biz.model.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.Model;
import com.ruoyi.system.api.domain.entity.biz.model.ModelOustSnapShoot;

/**
 * <AUTHOR> 
 * @Date 2025-05-13 17:34:14 
 */
public interface ModelOustSnapShootService extends IService<ModelOustSnapShoot> {

    /**
     * 新增或更新当月模特快照数据
     */
    void saveOrUpdateMonthModelSnapShoot(Model model);

    /**
     * 删除当月模特快照数据
     */
    void removeMonthModelSnapShoot(Long modelId);
}
