package com.wnkx.biz.wechat.service;

import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatContactUserConfig;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【we_chat_contact_user_config(对外联系人配置表)】的数据库操作Service
* @createDate 2025-03-05 13:46:50
*/
public interface WeChatContactUserConfigService extends IService<WeChatContactUserConfig> {

    void init(String contactUserId, String contactUserName);

    List<WeChatContactUserConfig> getConfigList();

    String getAcquisitionByContactUserId(String contactUserId);
}
