package com.wnkx.biz.casus.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.casus.*;
import com.ruoyi.system.api.domain.entity.order.casus.CasusGroup;
import com.ruoyi.system.api.domain.vo.order.casus.CasusGroupVO;
import com.ruoyi.system.api.domain.vo.order.casus.GroupAddVideoVO;
import com.ruoyi.system.api.domain.vo.order.casus.GroupVideoVO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【casus_group(案例分组表)】的数据库操作Service
* @createDate 2024-07-10 13:35:30
*/
@Validated
public interface ICasusGroupService extends IService<CasusGroup> {

    /**
     * 获取案例分组类表
     * @param dto
     * @return
     */
    List<CasusGroupVO> queryList(CasusGroupDTO dto);

    /**
     * 保存案例分组
     * @param dto
     */
    void save(@Valid CasusGroupSaveDTO dto);

    /**
     * 修改案例分组
     * @param dto
     */
    void update(@Valid CasusGroupUpdateDTO dto);

    /**
     * 删除分组
     * @param id
     */
    void delete(Long id);

    /**
     * 获取案例分组视频列表
     * @param dto
     * @return
     */
    List<GroupVideoVO> queryGroupsVideoList(@Valid GroupsVideoListDTO dto);


    /**
     * 获取分组可添加视频列表
     * @param dto
     * @return
     */
    List<GroupAddVideoVO> queryAddGroupsVideoList(@Valid GroupAddVideoDTO dto);

    /**
     * 添加分组案例
     * @param dto
     */
    void addGroupVideo(@Valid GroupVideoDTO dto);

    /**
     * 清楚分组案例
     * @param dto
     */
    void removeGroupVideo(@Valid GroupVideoDTO dto);

    /**
     * 更新分组视频排序
     * @param dto
     */
    void updateGroupSort(@Valid UpdateGroupSortDTO dto);
}
