package com.wnkx.biz.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/4 10:31
 */
@Component
@ConfigurationProperties(prefix = "channel.poster")
@Data
@RefreshScope
public class ChannelPoserProperties {

    /**
     * 使用哪个模板 1-有模特，2-没模特
     */
    private Integer templateType;

    /**
     * 有模特的模板
     */
    private String templateModelPath;

    /**
     * 有模特的模板 20250407 无海报名称省略X
     */
    private String templateModelPath20250407;

    /**
     * 没模特的模板
     */
    private String templatePath;

    /**
     * 没模特的模板 20250407 无海报名称省略X
     */
    private String templatePath20250407;

    /**
     * 像素点放大倍数
     */
    private Integer pixelMultiple;

    /**
     *  有视频内容|折
     */
    private String templatePath2025052301;
    /**
     * 有视频内容|元
     */
    private String templatePath2025052302;
    /**
     * 无视频内容|折
     */
    private String templatePath2025052311;
    /**
     * 无视频内容|元
     */
    private String templatePath2025052312;

    /**
     * 新分销 无海报名称
     */
    private String templatePath2025060401;
    private String templatePath2025060402;

    /**
     * 新分销 有海报名称
     */
    private String templatePath2025060411;
    private String templatePath2025060412;
}
