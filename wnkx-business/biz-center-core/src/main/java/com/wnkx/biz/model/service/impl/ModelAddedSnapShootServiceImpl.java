package com.wnkx.biz.model.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.Model;
import com.ruoyi.system.api.domain.entity.biz.model.ModelAddedSnapShoot;
import com.wnkx.biz.model.mapper.ModelAddedSnapShootMapper;
import com.wnkx.biz.model.service.ModelAddedSnapShootService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2025-05-13 17:33:47
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ModelAddedSnapShootServiceImpl extends ServiceImpl<ModelAddedSnapShootMapper, ModelAddedSnapShoot> implements ModelAddedSnapShootService {

    /**
     * 新增模特快照数据
     */
    @Override
    public void saveModelAddedSnapShoot(Model model) {
        ModelAddedSnapShoot modelAddedSnapShoot = new ModelAddedSnapShoot();
        modelAddedSnapShoot.setModelId(model.getId());
        modelAddedSnapShoot.setCooperation(model.getCooperation());
        modelAddedSnapShoot.setCommissionUnit(model.getCommissionUnit());
        modelAddedSnapShoot.setCommission(model.getCommission());
        modelAddedSnapShoot.setExtraData(JSONUtil.toJsonStr(model));
        modelAddedSnapShoot.setCreateTime(model.getCreateTime());
        baseMapper.insert(modelAddedSnapShoot);
    }
}
