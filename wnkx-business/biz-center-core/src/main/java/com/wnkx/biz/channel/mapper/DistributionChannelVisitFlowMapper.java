package com.wnkx.biz.channel.mapper;

import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelVisitFlow;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【distribution_channel_visit_flow(分销渠道访问流水数据)】的数据库操作Mapper
* @createDate 2025-03-04 10:59:09
* @Entity com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelVisitFlow
*/
public interface DistributionChannelVisitFlowMapper extends BaseMapper<DistributionChannelVisitFlow> {

    /**
     * 根据渠道ID获取访问流水统计数据
     * @param channelType
     * @return
     */
    Long getSumVisit(@Param("channelType") Integer channelType);
}




