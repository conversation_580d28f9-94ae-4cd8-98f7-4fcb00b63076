package com.wnkx.biz.channel.mapper;

import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecordRelevance;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【member_seed_record_relevance(会员种草提现_种草记录关联表)】的数据库操作Mapper
 * @createDate 2025-05-15 09:15:45
 * @Entity com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecordRelevance
 */
public interface MemberSeedRecordRelevanceMapper extends SuperMapper<MemberSeedRecordRelevance> {

    /**
     * 根据结算Id获取 种草记录Id
     * @param ids
     * @return
     */
    List<Long> getMemberSeedRecordIdsByWithdrawalIds(@Param("ids") List<Long> ids);
}




