package com.wnkx.biz.channel.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.ChannelTypeEnum;
import com.ruoyi.common.core.enums.SettleStatusEnum;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserChannelListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionChancelStatisticsDTO;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelDTO;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelListDTO;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelSettlementDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.order.OrderMemberChannel;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserChannelListVO;
import com.ruoyi.system.api.domain.vo.biz.channel.DistributionChannelVO;
import com.ruoyi.system.api.domain.vo.order.OrderMemberChannelDetailVO;
import com.ruoyi.system.api.domain.vo.order.OrderMemberChannelListVO;
import com.ruoyi.system.api.domain.vo.order.OrderMemberChannelStatisticVO;
import com.ruoyi.system.api.domain.vo.order.OrderMemberFissionListVO;
import com.wnkx.biz.business.mapper.BizUserMapper;
import com.wnkx.biz.channel.mapper.MemberSeedRecordMapper;
import com.wnkx.biz.channel.mapper.OrderMemberChannelMapper;
import com.wnkx.biz.channel.service.IOrderMemberChannelService;
import com.wnkx.biz.core.ChannelCore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【order_member_channel(会员渠道记录表)】的数据库操作Service实现
 * @createDate 2024-09-24 17:04:00
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderMemberChannelServiceImpl extends ServiceImpl<OrderMemberChannelMapper, OrderMemberChannel> implements IOrderMemberChannelService {

    private final ChannelCore channelCore;
    private final BizUserMapper bizUserMapper;
    private final RedisService redisService;
    private final MemberSeedRecordMapper memberSeedRecordMapper;

    /**
     * 分销渠道结算-结算
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void memberChannelSettlement(OrderMemberChannelSettlementDTO dto) {
        redisService.getLocks(CacheConstants.CHANNEL_SETTLEMENT_LOCK_KEY, dto.getIds(), CacheConstants.CHANNEL_SETTLEMENT_LOCK_KEY_SECOND);
        try {
            Assert.isTrue(baseMapper.checkOrderMemberChannelIsUnSettlement(dto.getIds()), "当前结算状态已更新，请刷新页面");
            List<OrderMemberChannel> orderMemberChannels = baseMapper.selectBatchIds(dto.getIds());
            Assert.isTrue(CollUtil.isNotEmpty(orderMemberChannels), "数据不存在，请检查后重试");
            Assert.isTrue(orderMemberChannels.size() == dto.getIds().size(), "数据不存在，请检查后重试");

            DateTime date = DateUtil.date();
            Long userId = SecurityUtils.getUserId();
            String username = SecurityUtils.getUsername();

            for (OrderMemberChannel orderMemberChannel : orderMemberChannels) {
                orderMemberChannel.setSettleTime(dto.getSettleTime());
                orderMemberChannel.setSettleResourceUrl(dto.getSettleResourceUrl());
                orderMemberChannel.setRemark(dto.getRemark());
                orderMemberChannel.setOperationTime(date);
                orderMemberChannel.setSettleStatus(SettleStatusEnum.SETTLED.getCode());
                orderMemberChannel.setSettleUserId(userId);
                orderMemberChannel.setSettleUserName(username);
                orderMemberChannel.setRealSettleAmount(ObjectUtil.isNotNull(dto.getRealSettleAmount()) ? dto.getRealSettleAmount() : orderMemberChannel.getSettleAmount());
            }

            baseMapper.updateBatchById(orderMemberChannels);
        } finally {
            for (Long lockKey : dto.getIds()) {
                redisService.releaseLock(CacheConstants.CHANNEL_SETTLEMENT_LOCK_KEY + lockKey);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void memberChannelUnableSettlement(Long businessId) {
        Assert.notNull(businessId, "businessId不能为空");
        OrderMemberChannel orderMemberChannel = checkUnableSettlement(businessId);
        if (ObjectUtil.isNull(orderMemberChannel)) {
            return;
        }
        OrderMemberChannel update = new OrderMemberChannel();
        update.setId(orderMemberChannel.getId());
        update.setSettleStatus(SettleStatusEnum.UNABLE_SETTLED.getCode());
        baseMapper.updateById(update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void memberChannelUnSettlement(Long businessId) {
        Assert.notNull(businessId, "businessId不能为空");
        OrderMemberChannel orderMemberChannel = baseMapper.getOrderMemberChannelByBusinessId(businessId);
        if (ObjectUtil.isNull(orderMemberChannel)) {
            return;
        }
        if (SettleStatusEnum.WAIT_SETTLED.getCode().equals(orderMemberChannel.getSettleStatus())) {
            OrderMemberChannel update = new OrderMemberChannel();
            update.setId(orderMemberChannel.getId());
            update.setSettleStatus(SettleStatusEnum.UN_SETTLED.getCode());
            baseMapper.updateById(update);
        }
    }

    private OrderMemberChannel checkUnableSettlement(Long businessId) {
        Assert.notNull(businessId, "businessId不能为空");
        OrderMemberChannel orderMemberChannel = baseMapper.getOrderMemberChannelByBusinessId(businessId);
        if (ObjectUtil.isNull(orderMemberChannel)) {
            return null;
        }
        //需要待结算设置无法结算
        Assert.isTrue(SettleStatusEnum.WAIT_SETTLED.getCode().equals(orderMemberChannel.getSettleStatus()), "当前结算状态已更新，请刷新页面~");
        //需要创建时间是7天内
        Assert.isTrue(DateUtils.addDays(orderMemberChannel.getCreateTime(), 7).compareTo(DateUtils.getNowDate()) > 0, "购买会员7天内才能结算~");
        return orderMemberChannel;
    }

    @Override
    public OrderMemberChannel getOrderMemberChannelByBusinessId(Long businessId) {
        return baseMapper.getOrderMemberChannelByBusinessId(businessId);
    }

    /**
     * 分销渠道结算-查看结算记录
     */
    @Override
    public OrderMemberChannelDetailVO memberChannelDetailById(Long id) {
        return BeanUtil.copyProperties(baseMapper.memberChannelDetailById(id), OrderMemberChannelDetailVO.class);
    }

    /**
     * 分销渠道列表
     */
    @Override
    public List<OrderMemberChannelListVO> memberChannelListByCondition(OrderMemberChannelListDTO dto) {

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("msr.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        dto.setChannelType(ChannelTypeEnum.DISTRIBUTION.getCode());
        List<OrderMemberChannelListVO> orderMemberChannelListVOS = memberSeedRecordMapper.queryOrderMemberChannelList(dto);
        if (CollUtil.isEmpty(orderMemberChannelListVOS)) {
            return orderMemberChannelListVOS;
        }

        return orderMemberChannelListVOS;
    }

    @Override
    public List<OrderMemberChannelListVO> oldMemberChannelListByCondition(OrderMemberChannelListDTO dto) {

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("omc.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        dto.setChannelType(ChannelTypeEnum.DISTRIBUTION.getCode());
        List<OrderMemberChannelListVO> orderMemberChannelListVOS = baseMapper.memberChannelListByCondition(dto);

        if (CollUtil.isEmpty(orderMemberChannelListVOS)) {
            return orderMemberChannelListVOS;
        }

        return orderMemberChannelListVOS;
    }

    @Override
    public List<OrderMemberFissionListVO> fissionMemberChannelListByCondition(OrderMemberChannelListDTO dto) {
        dto.setChannelType(ChannelTypeEnum.FISSION.getCode());
        if (StrUtil.isNotBlank(dto.getKeyword())) {
            List<BizUserChannelListVO> bizUserChannelListVOS = bizUserMapper.channelBizUserId(BizUserChannelListDTO.builder().keyword(dto.getKeyword()).build());
            if (CollUtil.isEmpty(bizUserChannelListVOS)) {
                return Collections.emptyList();
            }
            dto.setChannelIds(bizUserChannelListVOS.stream().map(BizUserChannelListVO::getChannelId).collect(Collectors.toList()));
            dto.setKeyword(null);
        }
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("msr.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
//        List<OrderMemberChannelListVO> orderMemberChannelListVOS = baseMapper.memberChannelListByCondition(dto);
        List<OrderMemberChannelListVO> orderMemberChannelListVOS = memberSeedRecordMapper.queryOrderMemberChannelList(dto);
        if (CollUtil.isEmpty(orderMemberChannelListVOS)) {
            return Collections.emptyList();
        }
        List<OrderMemberFissionListVO> orderMemberFissionListVOS = new ArrayList<>();

        List<BizUserChannelListVO> bizUserChannelListVOS = bizUserMapper.channelBizUserId(BizUserChannelListDTO.builder()
                                                                                                               .channelIds(orderMemberChannelListVOS
                                                                                                                                   .stream()
                                                                                                                                   .map(OrderMemberChannelListVO::getChannelId)
                                                                                                                                   .collect(Collectors.toList())).build());
        if (CollUtil.isNotEmpty(bizUserChannelListVOS)) {
            Map<Long, BizUserChannelListVO> bizUserMap = bizUserChannelListVOS.stream().collect(Collectors.toMap(BizUserChannelListVO::getChannelId, Function.identity()));
            for (OrderMemberChannelListVO item : orderMemberChannelListVOS) {
                OrderMemberFissionListVO orderMemberFissionListVO = BeanUtil.copyProperties(item, OrderMemberFissionListVO.class);
                orderMemberFissionListVOS.add(orderMemberFissionListVO);
                BizUserChannelListVO bizUserChannelListVO = bizUserMap.get(item.getChannelId());
                if (ObjectUtil.isNull(bizUserChannelListVO)) {
                    continue;
                }
                orderMemberFissionListVO.setFissionMemberCode(bizUserChannelListVO.getMemberCode());
                orderMemberFissionListVO.setFissionBusinessName(bizUserChannelListVO.getBusinessName());
                orderMemberFissionListVO.setFissionNickname(bizUserChannelListVO.getNickName());
                orderMemberFissionListVO.setFissionAccountName(bizUserChannelListVO.getName());
            }
        }

        return orderMemberFissionListVOS;
    }

    /**
     * 新增会员渠道记录
     *
     */
    @Override
    public void saveOrderMemberChannel(OrderMemberChannelDTO dto) {
        DistributionChannelVO distributionChannelVO = channelCore.getDistributionChannelBySeedCode(dto.getSeedCode());
        if (ObjectUtil.isNull(distributionChannelVO)) {
            return;
        }
        OrderMemberChannel orderMemberChannel = BeanUtil.copyProperties(dto, OrderMemberChannel.class);
        orderMemberChannel.setChannelId(distributionChannelVO.getId());
        orderMemberChannel.setChannelName(distributionChannelVO.getChannelName());
        orderMemberChannel.setChannelPhone(distributionChannelVO.getPhone());
        orderMemberChannel.setChannelType(distributionChannelVO.getChannelType());
        orderMemberChannel.setChannelBizUserId(Optional.ofNullable(distributionChannelVO.getId()).orElse(null));
        orderMemberChannel.setSeedCode(distributionChannelVO.getSeedCode());
        orderMemberChannel.setSettleRage(distributionChannelVO.getBrokeRage());
        orderMemberChannel.setSettleAmount((dto.getRealPayAmount().subtract(dto.getTaxPointCost())).multiply(distributionChannelVO.getBrokeRage().divide(BigDecimal.valueOf(100), 4, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN));
        orderMemberChannel.setCreateTime(DateUtil.date());
        orderMemberChannel.setBizUserId(dto.getBizUserId());
        orderMemberChannel.setBizUserNickName(dto.getBizUserNickName());
        orderMemberChannel.setBizUserPhone(dto.getBizUserPhone());
        baseMapper.insert(orderMemberChannel);
    }


    /**
     * 通过渠道ID 获取 会员成交数、待结算金额、已结算金额
     */
    @Override
    public Map<Long, OrderMemberChannelStatisticVO> getMemberChannelStatistic(List<Long> channelIds) {
        List<OrderMemberChannel> orderMemberChannels = baseMapper.memberChannelListByChannelIds(channelIds);
        if (CollUtil.isEmpty(orderMemberChannels)) {
            return Collections.emptyMap();
        }

        Map<Long, OrderMemberChannelStatisticVO> resultMap = new HashMap<>();

        Map<Long, List<OrderMemberChannel>> orderMemberChannelsMap = orderMemberChannels.stream().collect(Collectors.groupingBy(OrderMemberChannel::getChannelId));
        orderMemberChannelsMap.forEach((key, value) -> {
            BigDecimal unSettleAmount = value.stream()
                    .filter(orderMemberChannel -> SettleStatusEnum.UN_SETTLED.getCode().equals(orderMemberChannel.getSettleStatus()))
                    .map(OrderMemberChannel::getSettleAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal settleAmount = value.stream()
                    .filter(orderMemberChannel -> SettleStatusEnum.SETTLED.getCode().equals(orderMemberChannel.getSettleStatus()))
                    .map(OrderMemberChannel::getRealSettleAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal realPayAmount = value.stream()
                    .map(OrderMemberChannel::getRealPayAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            OrderMemberChannelStatisticVO orderMemberChannelStatisticVO = new OrderMemberChannelStatisticVO();
            orderMemberChannelStatisticVO.setId(key);
            orderMemberChannelStatisticVO.setMemberNum(value.size());
            orderMemberChannelStatisticVO.setUnSettleAmount(unSettleAmount);
            orderMemberChannelStatisticVO.setSettleAmount(settleAmount);
            orderMemberChannelStatisticVO.setRealPayAmount(realPayAmount);
            resultMap.put(key, orderMemberChannelStatisticVO);
        });

        return resultMap;
    }

    @Override
    public OrderMemberChannelStatisticVO getMemberChannelStatisticTotal(DistributionChancelStatisticsDTO dto) {
        Assert.notNull(dto.getChannelType(), "渠道类型不能为空");
        return baseMapper.getMemberChannelStatisticTotal(dto);
    }

    @Override
    public List<OrderMemberChannel> memberChannelListByChannelIds(List<Long> bizUserIds) {
        if (CollUtil.isEmpty(bizUserIds)) {
            return Collections.emptyList();
        }
        //根据bizUserIds获取渠道ID
        List<DistributionChannel> distributionChannels = channelCore.getChannelByIds(bizUserIds);
        if (CollUtil.isEmpty(distributionChannels)) {
            return Collections.emptyList();
        }
        return baseMapper.memberChannelListByChannelIds(distributionChannels.stream().map(DistributionChannel::getId).collect(Collectors.toList()));
    }

    @Override
    public void deleteByBusinessId(Long businessId) {
        if (ObjectUtil.isNull(businessId)) {
            return;
        }
        baseMapper.delete(new LambdaQueryWrapper<OrderMemberChannel>()
                .eq(OrderMemberChannel::getBusinessId, businessId));
    }

    @Override
    public Long getUnSettledCount() {
        return baseMapper.getUnSettledCount();
    }
}




