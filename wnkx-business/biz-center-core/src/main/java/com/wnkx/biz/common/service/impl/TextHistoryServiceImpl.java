package com.wnkx.biz.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.common.TextHistory;
import com.wnkx.biz.common.mapper.TextHistoryMapper;
import com.wnkx.biz.common.service.TextHistoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:29
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TextHistoryServiceImpl extends ServiceImpl<TextHistoryMapper, TextHistory> implements TextHistoryService {

    /**
     * 添加数据
     */
    @Override
    public void insert(TextHistory textHistory) {
        baseMapper.insert(textHistory);
    }
}
