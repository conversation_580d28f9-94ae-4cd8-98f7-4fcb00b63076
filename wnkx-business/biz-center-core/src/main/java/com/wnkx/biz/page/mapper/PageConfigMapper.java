package com.wnkx.biz.page.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.system.api.domain.dto.biz.page.PageConfigQueryListDTO;
import com.ruoyi.system.api.domain.dto.biz.page.PageConfigUpdateDTO;
import com.ruoyi.system.api.domain.entity.biz.page.PageConfig;
import com.ruoyi.system.api.domain.vo.biz.page.PageConfigVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Optional;

/**
* <AUTHOR>
* @description 针对表【page_config(页面配置表)】的数据库操作Mapper
* @createDate 2024-08-19 16:43:20
* @Entity com.ruoyi.system.api.domain.entity.biz.business.PageConfig
*/
@Mapper
public interface PageConfigMapper extends SuperMapper<PageConfig> {

    /**
     * 获取页面列表*
     * @param dto
     * @return
     */
    List<PageConfigVO> queryList(PageConfigQueryListDTO dto);

    /**
     * 修改页面配置*
     * @param dto
     * @return
     */
    default int update(PageConfigUpdateDTO dto){
        return this.update(null, new LambdaUpdateWrapper<PageConfig>()
                        .set(PageConfig::getName, dto.getName())
                        .set(PageConfig::getLink, Optional.ofNullable(dto.getLink()).orElse(""))
                .eq(PageConfig::getId, dto.getId()));
    }
}
