package com.wnkx.biz.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :预付款配置
 * @create :2024-11-05 10:36
 **/

@Component
@ConfigurationProperties(prefix = "biz.prepay")
@Data
@RefreshScope
public class PrepayConfig {
    private String prefix;
    private Long initPrepayNum;
    /**
     * 最小优惠金额
     */
    private Long minRecharge;

    /**
     * 最小充值金额赠送
     */
    private Long minRechargePresented;

    /**
     * 最大充值金额
     */
    private Long maxRecharge;

    /**
     * 最大充值金额赠送
     */
    private Long maxRechargePresented;
}
