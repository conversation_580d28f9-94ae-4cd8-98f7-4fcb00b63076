package com.wnkx.biz.core;

import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableListVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * 模特数据表列表列枚举
 *
 * <AUTHOR>
 * @date 2025/7/8 9:40
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ModelDataTableListColumnEnum {
    COLUMN1(1, "客服", "service_id", false, ModelDataTableListVO::getServiceId),
    COLUMN2(2, "开发人", "developer_id", false, ModelDataTableListVO::getDeveloperId),
    COLUMN3(3, "性别", "", false, null),
    COLUMN4(4, "国家", "", false, null),
    COLUMN5(5, "平台", "", false, null),
    COLUMN6(6, "类型", "type", false, ModelDataTableListVO::getType),
    COLUMN7(7, "年龄层", "age_group", true, ModelDataTableListVO::getAgeGroup),
    COLUMN8(8, "等级", "cooperation_score", false, ModelDataTableListVO::getCooperationScore),
    COLUMN9(9, "佣金", "commissionSort", false, ModelDataTableListVO::getCommissionSort),
    COLUMN10(10, "案例数", "caseCount", false, ModelDataTableListVO::getCaseCount),
    COLUMN11(11, "标签数", "tagCount", false, ModelDataTableListVO::getTagCount),
    COLUMN12(12, "品类数", "categoryCount", false, ModelDataTableListVO::getCategoryCount),
    COLUMN13(13, "蜗牛照", "have_snail_pic", false, ModelDataTableListVO::getHaveSnailPic),
    COLUMN14(14, "家庭", "familyMemberCount", false, ModelDataTableListVO::getFamilyMemberCount),
    COLUMN15(15, "收藏", "collectCount", false, ModelDataTableListVO::getCollectCount),
    COLUMN16(16, "拉黑", "blacklistCount", false, ModelDataTableListVO::getBlacklistCount),
    COLUMN17(17, "意向接单率", "intentionOrderRate", false, ModelDataTableListVO::getIntentionOrderRate),
    COLUMN18(18, "预选接单率", "preSelectOrderRate", false, ModelDataTableListVO::getPreSelectOrderRate),
    COLUMN19(19, "分发接单率", "dispatchOrderRate", false, ModelDataTableListVO::getDispatchOrderRate),
    COLUMN20(20, "自选排单率", "selfSelectOrderRate", false, ModelDataTableListVO::getSelfSelectOrderRate),
    COLUMN21(21, "商家拒绝率", "rejectOrderRate", false, ModelDataTableListVO::getRejectOrderRate),
    COLUMN22(22, "排单数", "orderScheduledCount", false, ModelDataTableListVO::getOrderScheduledCount),
    COLUMN23(23, "待拍数", "waitPictureCount", false, ModelDataTableListVO::getWaitPictureCount),
    COLUMN24(24, "反馈数", "feedbackCount", false, ModelDataTableListVO::getFeedbackCount),
    COLUMN25(25, "超时率", "overtimeRate", false, ModelDataTableListVO::getOvertimeRate),
    COLUMN26(26, "售后率", "afterSaleRate", false, ModelDataTableListVO::getAfterSaleRate),
    COLUMN27(27, "完成数", "completeCount", false, ModelDataTableListVO::getCompleteCount),
    COLUMN28(28, "丢件数", "dropCount", false, ModelDataTableListVO::getDropCount),
    COLUMN29(29, "被取消", "cancelCount", false, ModelDataTableListVO::getCancelCount),
    COLUMN30(30, "被退回", "returnCount", false, ModelDataTableListVO::getReturnCount),
    ;

    private Integer code;
    private String columnName;
    private String columnSortSqlName;
    private Boolean reversal;
    private Function<ModelDataTableListVO, ? extends Comparable<?>> extractor;


    public static String getColumnSortSqlNameByCode(Integer code) {
        for (ModelDataTableListColumnEnum value : ModelDataTableListColumnEnum.values()) {
            if (value.code.equals(code)) {
                return value.columnSortSqlName;
            }
        }
        return "";
    }

    public static ModelDataTableListColumnEnum getByCode(Integer code) {
        for (ModelDataTableListColumnEnum value : ModelDataTableListColumnEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return COLUMN1;
    }

    public static String getColumnSortWayByCode(Integer code, String sortWay) {
        for (ModelDataTableListColumnEnum value : ModelDataTableListColumnEnum.values()) {
            if (value.code.equals(code)) {
                if (value.reversal) {
                    return OrderByDto.DIRECTION.DESC.value().equals(sortWay) ? OrderByDto.DIRECTION.ASC.value() : OrderByDto.DIRECTION.DESC.value();
                }
                return sortWay;
            }
        }
        return OrderByDto.DIRECTION.DESC.value();
    }

    public static List<Integer> getOrderColumns() {
        return List.of(
                COLUMN17.code,
                COLUMN18.code,
                COLUMN19.code,
                COLUMN20.code,
                COLUMN21.code,
                COLUMN22.code,
                COLUMN23.code,
                COLUMN24.code,
                COLUMN25.code,
                COLUMN26.code,
                COLUMN27.code,
                COLUMN28.code,
                COLUMN29.code,
                COLUMN30.code
        );
    }

    public static List<Integer> getAllColumns() {
        ArrayList<Integer> allColumns = new ArrayList<>();

        for (ModelDataTableListColumnEnum value : ModelDataTableListColumnEnum.values()) {
            allColumns.add(value.code);
        }
        return allColumns;
    }
}
