package com.wnkx.biz.model.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.model.ModelTag;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 模特分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Mapper
public interface ModelTagMapper extends SuperMapper<ModelTag>
{
    /**
     * 根据模特id获取模特关联标签
     *
     * @param modelId 模特id
     * @return 模特关联标签
     */
    default List<ModelTag> selectListByModelId(Collection<Long> modelId) {
        return this.selectList(new LambdaQueryWrapper<ModelTag>()
                .in(ModelTag::getModelId, modelId)
                .orderByDesc(ModelTag::getDictId)
        );
    }

    /**
     * 通过模特id删除标签表
     */
    default void removeByModelId(List<Long> modelId) {
        this.delete(new LambdaQueryWrapper<ModelTag>()
                .in(ModelTag::getModelId, modelId)
        );
    }

    /**
     * 获取标签使用次数
     *
     * @param dictId 标签id
     * @return 标签使用次数
     */
    default Long getDictUseCount(Long dictId) {
        return this.selectCount(new LambdaQueryWrapper<ModelTag>()
                .eq(ModelTag::getDictId, dictId)
        );
    }

    /**
     * 通过标签ID删除标签表
     */
    default void removeByTagId(Long tagId) {
        this.delete(new LambdaQueryWrapper<ModelTag>()
                .in(ModelTag::getDictId, tagId)
        );
    }
}
