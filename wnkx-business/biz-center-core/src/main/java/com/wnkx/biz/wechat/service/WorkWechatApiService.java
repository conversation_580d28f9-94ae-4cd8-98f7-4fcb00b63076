package com.wnkx.biz.wechat.service;

import com.ruoyi.common.core.enums.ChannelTypeEnum;
import com.ruoyi.system.api.domain.dto.biz.wechat.CustomerTagEdit;
import com.ruoyi.system.api.domain.dto.biz.wechat.ExternalUserInfoDTO;
import com.ruoyi.system.api.domain.dto.biz.wechat.WeChatGroupTagDTO;
import com.ruoyi.system.api.domain.dto.biz.wechat.WeWelcomeMsg;

import java.util.List;

/**
 * 企业微信api操作
 *
 * <AUTHOR>
 * @date 2024/9/25
 */
public interface WorkWechatApiService {


    /**
     * 根据externalUserId 获取联系人数据
     * @param externalUserId 外部联系人id
     * @return 外部联系人数据
     */
    public ExternalUserInfoDTO getExternalUserInfo(String externalUserId);


    /**
     * 创建渠道联系我二维码(渠道)
     *
     * @param channelType        渠道类型
     * @param dedicatedLinkCode 渠道专属链接code
     * @return 二维码url
     */
    public String contactMeQrcode(ChannelTypeEnum channelType, String dedicatedLinkCode);

    public String contactMeQrcodeByState(String state, String contactUserId);

    /**
     * 创建商家子账号联系我二维码(商家子账号)
     *
     * @param memberCode 商家编码
     * @return 二维码url
     */
    public String contactMeQrcode(String memberCode,String contactUid);

    /**
     * 客户发送欢迎语
     * @param weWelcomeMsg
     */
    public void sendWelcomeMsg(WeWelcomeMsg weWelcomeMsg);
    /**
     * 获取分组id列表
     * @param tagIds    标签id
     * @param groupIds  分组id
     * @return
     */
    List<WeChatGroupTagDTO> tagList(List<String> tagIds, List<String> groupIds);

    /**
     * 添加标签
     * @param channelType    渠道类型
     * @param tagName       标签名称
     * @return  标签id
     */
    public String addTag(ChannelTypeEnum channelType, String tagName);

    /**
     * 修改标签
     * @param channelType    渠道类型
     * @param tagId     标签id
     * @param tagName   标签名称
     */
    public void editTag(ChannelTypeEnum channelType, String tagId, String tagName);

    /**
     * 客户打标签
     * @param customerTagEdit
     */
    public void markTag(CustomerTagEdit customerTagEdit);

    /**
     * 编辑客户备注
     * @param userId   用户id
     * @param externalUserId   外部联系人id
     * @param remark   备注
     */
    public void changeExternalContactRemark(String userId,String externalUserId, String remark);


    String contactSubAccountQrcode(String memberCode, String connectUser);
}
