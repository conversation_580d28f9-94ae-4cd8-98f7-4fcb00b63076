package com.wnkx.biz.model.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.biz.model.ModelTag;
import com.ruoyi.system.api.domain.vo.biz.model.ModelTagVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 模特分类Service接口
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface IModelTagService extends IService<ModelTag> {
    /**
     * 根据模特id获取模特关联标签
     *
     * @param modelId 模特id
     * @return 模特关联标签
     */
    List<ModelTag> selectListByModelId(Collection<Long> modelId);

    /**
     * 通过模特id删除标签表
     */
    void removeByModelId(List<Long> modelId);

    /**
     * 获取标签使用次数
     * @param dictId 标签id
     * @return  标签使用次数
     */
    Long getDictUseCount(Long dictId);

    Map<Long, List<ModelTagVO>> getModelTagVOMap(Collection<Long> modelId);

    /**
     * 通过标签ID删除标签表
     */
    void removeByTagId(Long tagId);
}
