package com.wnkx.biz.utlis;

import lombok.extern.slf4j.Slf4j;

import java.awt.*;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class FontLoader {

    // 存储已加载字体的 Map，以字体文件名为键，Font 为值
    private static final Map<String, Font> fontCache = new HashMap<>();

    /**
     * 初始化时加载指定文件夹中的所有字体
     */
    static {
        // 获取资源文件夹中的字体文件
        // String fontFolderPath = "/fonts"; // fonts文件夹路径
        // try {
        //     File fontFolder = new File(Objects.requireNonNull(FontLoader.class.getResource(fontFolderPath)).toURI());
        //     for (File file : Objects.requireNonNull(fontFolder.listFiles())) {
        //         File[] fontFiles = file.listFiles((dir, name) -> name.endsWith(".ttf") || name.endsWith(".otf")); // 只获取字体文件
        //         if (fontFiles != null) {
        //             for (File fontFile : fontFiles) {
        //                 try {
        //                     Font font = loadFontFromFile(fontFile);
        //                     if (font != null) {
        //                         // 将字体存入缓存，使用文件名作为键
        //                         fontCache.put(fontFile.getName(), font);
        //                     }
        //                 } catch (Exception e) {
        //                     log.error("加载字体失败: " + e);
        //                     throw new RuntimeException("加载字体文件夹失败");
        //                 }
        //             }
        //         }
        //     }
        // } catch (Exception e) {
        //     log.error("加载字体文件夹失败: " + e);
        //     throw new RuntimeException("加载字体文件夹失败");
        // }
        try {
            fontCache.put("MiSans-Light.ttf", loadFontFromFile(FontLoader.class.getResourceAsStream("/fonts/MiSans/MiSans-Light.ttf")));
            fontCache.put("MiSans-Medium.ttf", loadFontFromFile(FontLoader.class.getResourceAsStream("/fonts/MiSans/MiSans-Medium.ttf")));
            fontCache.put("MiSans-Normal.ttf", loadFontFromFile(FontLoader.class.getResourceAsStream("/fonts/MiSans/MiSans-Normal.ttf")));
            fontCache.put("D-DIN-Bold.ttf", loadFontFromFile(FontLoader.class.getResourceAsStream("/fonts/D-Din/D-DIN-Bold.ttf")));
            fontCache.put("D-DINExp-Bold.ttf", loadFontFromFile(FontLoader.class.getResourceAsStream("/fonts/D-Din/D-DINExp-Bold.ttf")));
            fontCache.put("Barlow-SemiBold.ttf", loadFontFromFile(FontLoader.class.getResourceAsStream("/fonts/Barlow/Barlow-SemiBold.ttf")));
            fontCache.put("PingFang-Regular.ttf", loadFontFromFile(FontLoader.class.getResourceAsStream("/fonts/PingFang/PingFang-Regular.ttf")));
        } catch (Exception e) {
                log.error("加载字体文件夹失败: " + e);
                throw new RuntimeException("加载字体文件夹失败");
        }
    }

    /**
     * 根据字体文件名获取字体
     *
     * @param fontFileName 字体文件名
     * @return Font 对象
     */
    public static Font getFont(String fontFileName, Integer fontSize) {
        Font font = fontCache.get(fontFileName);
        if (font == null) {
            log.error("未找到字体: " + fontFileName);
            // 如果没有缓存字体，则使用默认字体
            font = new Font("Serif", Font.PLAIN, fontSize);
        }
        return font.deriveFont(Font.PLAIN, fontSize);
    }

    /**
     * 从文件加载字体
     *
     * @param inputStream 文件流
     * @return 加载的字体
     * @throws IOException         字体文件读取异常
     * @throws FontFormatException 字体格式异常
     */
    private static Font loadFontFromFile(InputStream inputStream) throws IOException, FontFormatException {
        // 读取字体文件
        return Font.createFont(Font.TRUETYPE_FONT, inputStream);
    }
}
