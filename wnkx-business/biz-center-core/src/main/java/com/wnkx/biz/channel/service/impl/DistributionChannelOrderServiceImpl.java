package com.wnkx.biz.channel.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelOrder;
import com.wnkx.biz.channel.service.IDistributionChannelOrderService;
import com.wnkx.biz.channel.mapper.DistributionChannelOrderMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【distribution_channel_order(分销渠道订单表)】的数据库操作Service实现
 * @createDate 2024-12-09 16:15:59
 */
@Service
public class DistributionChannelOrderServiceImpl extends ServiceImpl<DistributionChannelOrderMapper, DistributionChannelOrder>
        implements IDistributionChannelOrderService {

    @Override
    public List<DistributionChannelOrder> getTodayDistributionChannelOrder() {
        return baseMapper.getTodayDistributionChannelOrder();
    }
}




