package com.wnkx.biz.remote;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.RemoteConfigService;
import com.ruoyi.system.api.RemoteDictDataService;
import com.ruoyi.system.api.RemoteOrderService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.dto.OrderVideoStatisticsDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.EditFissionChannelDiscountDTO;
import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableListDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.logistic.LogisticFollowNotifyDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.ModelUpdateAddressDTO;
import com.ruoyi.system.api.domain.dto.order.pay.OrderPayAccountDTO;
import com.ruoyi.system.api.domain.dto.system.EditMemberDiscountDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.SysDictData;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelOrderRankingInfo;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderMember;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccount;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRefund;
import com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivityAmendmentRecord;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsDetailVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.ModelBasicDataVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableListVO;
import com.ruoyi.system.api.domain.vo.order.ModelOrderVO;
import com.ruoyi.system.api.domain.vo.order.OrderModelTimeoutVO;
import com.ruoyi.system.api.domain.vo.order.finace.OrderPayDetailVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/6/20 10:49
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RemoteService {
    /**
     * 用户服务api接口
     */
    private final RemoteUserService remoteUserService;
    /**
     * 订单服务api接口
     */
    private final RemoteOrderService remoteOrderService;

    private final RemoteConfigService remoteConfigService;
    private final RemoteDictDataService remoteDictDataService;


    /**
     * 查询模特数据表列表
     */
    public List<ModelDataTableListVO> selectModelDataTableListByCondition(ModelDataTableListDTO modelDataTableListDTO) {
        List<ModelDataTableListVO> modelDataTableListVOList = remoteOrderService.selectModelDataTableListByCondition(modelDataTableListDTO, SecurityConstants.INNER);
        Assert.notNull(modelDataTableListVOList, "调用远程服务[查询模特数据表列表]失败");
        return modelDataTableListVOList;
    }

    /**
     * 模特数据统计-模特接单排行榜
     */
    public List<ModelOrderRankingInfo> getModelOrderRanking(String date) {
        List<ModelOrderRankingInfo> modelOrderRanking = remoteOrderService.getModelOrderRanking(date, SecurityConstants.INNER);
        Assert.notNull(modelOrderRanking, "调用远程服务[模特数据统计-模特接单排行榜]失败");
        return modelOrderRanking;
    }

    /**
     * 模特数据统计-获取模特基础数据
     */
    public ModelBasicDataVO getModelBasicData() {
        ModelBasicDataVO modelBasicData = remoteOrderService.getModelBasicData(SecurityConstants.INNER);
        Assert.notNull(modelBasicData, "调用远程服务[模特数据统计-获取模特基础数据]失败");
        return modelBasicData;
    }

    /**
     * 通过商家ID查询商家是否有进行中或者交易完成的视频订单
     */
    public Boolean hasValidVideoOrderByBusinessId(Long businessId, String startTime) {
        Boolean result = remoteOrderService.hasValidVideoOrderByBusinessId(businessId, startTime, SecurityConstants.INNER);
        Assert.notNull(result, "调用远程服务[通过商家ID查询商家是否有进行中或者交易完成的视频订单]失败");
        return result;
    }

    /**
     * 获取订单统计相详情
     */
    public List<OrderVideoStatisticsDetailVO> orderVideoStatisticsDetail(OrderVideoStatisticsDTO orderVideoStatisticsDTO) {
        return remoteOrderService.orderVideoStatisticsDetail(orderVideoStatisticsDTO);
    }

    /**
     * 获取已支付的视频订单
     */
    public List<Order> getPayedOrderList() {
        return remoteOrderService.getPayedOrderList(SecurityConstants.INNER);
    }

    /**
     * 取提交凭证 未审核订单
     *
     * @return
     */
    public List<Order> getPayedUnCheckOrderList() {
        return remoteOrderService.getPayedUnCheckOrderList(SecurityConstants.INNER);
    }

    /**
     * 关闭订单
     *
     * @param orderNums
     */
    public void closeAllOrder(Collection<String> orderNums) {
        remoteOrderService.closeAllOrder(orderNums, SecurityConstants.INNER);
    }

    /**
     * 查询订单列表
     *
     * @param orderNums
     */
    public List<Order> getOrderListByOrderNums(Collection<String> orderNums) {
        if (CollUtil.isEmpty(orderNums)) {
            return null;
        }
        return remoteOrderService.getOrderListByOrderNums(orderNums, SecurityConstants.INNER);
    }

    /**
     * 物流跟进数据同步
     *
     * @param dto
     */
    public void logisticFollowNotify(LogisticFollowNotifyDTO dto) {
        log.info("物流回调数据：{}", JSON.toJSONString(dto));
        remoteOrderService.logisticFollowNotify(dto, SecurityConstants.INNER);
    }

    /**
     * 修改模特地址 通知跟进数据失败
     *
     * @param dto
     */
    public void modelUpdateAddress(ModelUpdateAddressDTO dto) {
        Assert.notNull(remoteOrderService.modelUpdateAddress(dto, SecurityConstants.INNER), "修改模特地址，通知跟进数据失败~");
    }

    /**
     * 批量更新视频订单的对接人
     */
    public void updateOrderVideoContact(UpdateOrderContactDTO dto) {
        Assert.isTrue(remoteOrderService.updateOrderVideoContact(dto, SecurityConstants.INNER), "批量更新视频订单的对接人失败！");
    }

    /**
     * 获取用户信息列表
     */
    public List<SysUser> getUserList(SysUserListDTO dto) {
        return remoteUserService.listNoPage(dto, SecurityConstants.INNER);
    }

    /**
     * 获取用户信息map
     *
     * @return 用户信息map
     */
    public Map<Long, UserVO> getUserMap(SysUserListDTO dto) {
        Map<Long, UserVO> map = new HashMap<>();
        //  获取用户列表
        List<SysUser> data = getUserList(dto);
        if (CollUtil.isEmpty(data)) {
            return map;
        }

        for (SysUser sysUser : data) {
            UserVO userVO = new UserVO();
            userVO.setId(sysUser.getUserId());
            userVO.setName(sysUser.getUserName());
            userVO.setPhonenumber(sysUser.getPhonenumber());

            map.put(userVO.getId(), userVO);
        }

        return map;
    }

    /**
     * 查询有逾期未反馈素材和无法接单的模特
     *
     * @param modelId 模特id
     */
    public List<Long> checkModelOverdueVideo(Collection<Long> modelId) {
        List<Long> r = remoteOrderService.queryOrderModel(modelId, SecurityConstants.INNER);
        Assert.notNull(r, "查询有逾期未反馈素材和无法接单的模特失败");
        return r;
    }

    /**
     * 根据参数键名查询参数值
     *
     * @param configKey 参数键名
     * @return 结果
     */
    public String getConfigKey(String configKey) {
        return remoteConfigService.getConfigKey(configKey, SecurityConstants.INNER);
    }
//    /**
//     * 修改裂变渠道折扣
//     * @param dto
//     */
//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    public void editFissionMemberDiscount(EditMemberDiscountDTO dto){
//        remoteConfigService.editFissionMemberDiscount(dto, SecurityConstants.INNER);
//    }

    /**
     * 修改裂变渠道折扣
     *
     * @param dto
     */
    public void editFissionMemberDiscountV1(EditFissionChannelDiscountDTO dto) {
        String s = remoteConfigService.editFissionMemberDiscountV1(dto, SecurityConstants.INNER);
        Assert.notNull(s, "修改裂变渠道折扣失败");
    }

    /**
     * 修改会员渠道折扣
     *
     * @param dto
     */
    public void editMemberDiscount(EditMemberDiscountDTO dto) {
        String s = remoteConfigService.editMemberDiscount(dto, SecurityConstants.INNER);
        Assert.notNull(s, "修改裂变渠道折扣失败");
    }


    /**
     * 获取模特待拍数、已完成订单数、超时订单
     */
    public List<ModelOrderVO> getModelOrderCount(Collection<Long> modelIds) {
        return remoteOrderService.getModelOrderCount(modelIds, SecurityConstants.INNER);
    }

    /**
     * 新增活动修改记录
     *
     * @param promotionActivityAmendmentRecord
     */
    public void savePromotionActivityAmendmentRecord(PromotionActivityAmendmentRecord promotionActivityAmendmentRecord) {
        String s = remoteOrderService.savePromotionActivityAmendmentRecord(promotionActivityAmendmentRecord, SecurityConstants.INNER);
        Assert.notNull(s, "新增活动修改记录失败");
    }

    /**
     * 获取模特超时率、售后率
     */
    public List<OrderModelTimeoutVO> getModelOvertimeRateAndAfterSaleRate() {
        return remoteOrderService.getModelOvertimeRateAndAfterSaleRate(SecurityConstants.INNER);
    }

    /**
     * 获取订单有效数量
     *
     * @param bizUserId
     * @return
     */
    public Long getValidOrderCount(Long bizUserId) {
        return remoteOrderService.getValidOrderCount(bizUserId, SecurityConstants.INNER);
    }

    /**
     * 获取有效订单
     */
    public List<OrderMember> getValidOrderMemberList(Long bizUserId) {
        List<OrderMember> validOrderMemberList = remoteOrderService.getValidOrderMemberList(bizUserId, SecurityConstants.INNER);
        Assert.notNull(validOrderMemberList, "获取有效订单 失败");
        return validOrderMemberList;
    }

    /**
     * 获取未取消订单数量
     * * * @param bizUserId
     *
     * @return
     */
    public Long getUnCancelOrderCount(Long businessId) {
        return remoteOrderService.getUnCancelOrderCount(businessId, SecurityConstants.INNER);
    }

    /**
     * 更新预选模特列表为已淘汰
     */
    public void outPreselectModel(List<OutPreselectModelDTO> dtoList) {
        Assert.isTrue(remoteOrderService.outPreselectModel(dtoList, SecurityConstants.INNER), "系统异常，请联系管理员处理！");
    }

    /**
     * 添加预选模特列表查询匹配单下非淘汰的模特
     */
    public Set<Long> selectNormalPreselectModelByMatchId(Long matchId) {
        return remoteOrderService.selectNormalPreselectModelByMatchId(matchId, SecurityConstants.INNER);
    }

    /**
     * 添加分发模特列表查询匹配单下的模特ID
     */
    public Set<Long> selectPreselectModelIdsByMatchId(Long matchId) {
        Set<Long> preselectModelIds = remoteOrderService.selectPreselectModelIdsByMatchId(matchId, SecurityConstants.INNER);
        Assert.notNull(preselectModelIds, "调用远程服务[添加分发模特列表查询匹配单下的模特ID]失败");
        return preselectModelIds;
    }

    /**
     * 添加预选模特列表查询匹配单下非淘汰的模特
     */
    public Set<Long> selectRejectModelIdByVideoId(Long videoId) {
        return remoteOrderService.selectRejectModelIdByVideoId(videoId, SecurityConstants.INNER);
    }

    /**
     * 获取订单明细基础数据
     * * @param dto
     */
    public List<OrderPayDetailVO> getBasePayDetailVOS(OrderPayDetailDTO dto) {
        return remoteOrderService.getBasePayDetailVOS(dto, SecurityConstants.INNER);
    }

    /**
     * 保存订单流水记录
     * * @param dto
     */
    public void saveOrderPayLog(OrderPayLogDTO dto) {
        String s = remoteOrderService.saveOrderPayLog(dto, SecurityConstants.INNER);
        Assert.notNull(s, "保存订单流水日志失败");
    }

    /**
     * 禁用会员发票
     *
     * @param businessId
     */
    public void banMemberInvoice(Long businessId) {
        String s = remoteOrderService.banMemberInvoice(businessId, SecurityConstants.INNER);
        Assert.notNull(s, "禁用会员发票失败");
    }

    /**
     * 保存订单收款账号
     * * @param dto
     */
    public void saveOrderPayeeAccount(OrderPayAccountDTO dto) {
        String s = remoteOrderService.saveOrderPayeeAccount(dto, SecurityConstants.INNER);
        Assert.notNull(s, "保存订单收款账号失败");
    }

    /**
     * * 获取收款账号列表
     *
     * @param orderNums
     * @return
     */
    public List<OrderPayeeAccount> queryOrderPayeeAccountListByOrderNums(List<String> orderNums) {
        return remoteOrderService.queryOrderPayeeAccountListByOrderNums(orderNums, SecurityConstants.INNER);
    }

    /**
     * 模特变更对接客服更新视频订单的出单人ID
     */
    public void updateIssueId(UpdateIssueIdDTO dto) {
        remoteOrderService.updateIssueId(dto, SecurityConstants.INNER);
    }

    /**
     * 清楚购物车意向模特数据
     *
     * @param dto
     */
    public void clearVideoCartIntentionModelId(ClearVideoCartIntentionModelDTO dto) {
        remoteOrderService.clearVideoCartIntentionModelId(dto, SecurityConstants.INNER);
    }


    /**
     * 提现申请通过后 对发票的处理
     */
    public void withdrawalSuccess(List<WithdrawalSuccessDTO> dtoList) {
        Assert.isTrue(remoteOrderService.withdrawalSuccess(dtoList, SecurityConstants.INNER), "调用 [提现申请通过后 对发票的处理] 接口失败");
    }

    /**
     * 根据字典类型查询字典数据信息
     */
    public List<SysDictData> selectDictDataByType(String dictType) {
        return remoteDictDataService.selectDictDataByType(dictType, SecurityConstants.INNER);
    }

    public List<OrderVideoRefund> getOrderVideoRefundList(List<String> numbers) {
        return remoteOrderService.getOrderVideoRefundList(numbers, SecurityConstants.INNER);
    }

    /**
     * （一次性）初始化模特数据-模特接单排行榜
     */
    public Map<String, List<ModelOrderRankingInfo>> getModelOrderRankings(List<String> collect) {
        return remoteOrderService.getModelOrderRankings(collect, SecurityConstants.INNER);
    }

    /**
     * 模特数据-模特排单情况
     */
    public List<PieChartVO> getModelOrderScheduledData(Collection<Long> modelIds) {
        List<PieChartVO> modelOrderScheduledData = remoteOrderService.getModelOrderScheduledData(modelIds, SecurityConstants.INNER);
        Assert.notNull(modelOrderScheduledData, "调用远程服务[模特数据统计-模特排单情况]失败");
        return modelOrderScheduledData;
    }

    public Date getOrderFirstMatchTime(Long orderId) {
        return remoteOrderService.getOrderFirstMatchTime(orderId, SecurityConstants.INNER);
    }
}

