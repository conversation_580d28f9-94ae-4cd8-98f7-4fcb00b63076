package com.wnkx.biz.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.biz.model.UserModelBlacklist;
import com.ruoyi.system.api.domain.vo.biz.business.user.UserBlackModelVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user_model_blacklist(模特黑名单)】的数据库操作Service
* @createDate 2025-01-09 10:46:49
*/
public interface IUserModelBlacklistService extends IService<UserModelBlacklist> {

    /**
     * 拉黑模特
     * @param modelId
     */
    void blackModel(Long modelId);

    /**
     * 取消拉黑
     * @param modelId
     */
    void cancelBlackModel(Long modelId);

    /**
     * * 获取黑名单列表
     * @param bizUserId
     * @return
     */
    List<UserBlackModelVO> userBlackModelList(Long bizUserId);

    /**
     * 用户所有黑名单列表,不分页
     * @param bizUserId
     * @return
     */
    List<UserBlackModelVO> userAllBlackModelList(Long bizUserId);

    /**
     * 根据模特ID列表获取拉黑数据
     * @param modelIds
     * @return
     */
    List<UserModelBlacklist> userBlackModelListByModelIds(List<Long> modelIds);

    /**
     * 获取当前用户拉黑模特列表
     */
    List<UserModelBlacklist> selectBlackModelListByBizUserId();
}
