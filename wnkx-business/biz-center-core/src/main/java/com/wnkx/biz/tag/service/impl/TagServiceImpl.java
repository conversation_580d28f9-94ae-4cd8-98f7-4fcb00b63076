package com.wnkx.biz.tag.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.tag.TagDTO;
import com.ruoyi.system.api.domain.dto.biz.tag.TagListDTO;
import com.ruoyi.system.api.domain.dto.biz.tag.TagSortDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.biz.tag.Tag;
import com.ruoyi.system.api.domain.vo.TagListVO;
import com.ruoyi.system.api.domain.vo.biz.tag.TagSimpleVO;
import com.wnkx.biz.model.service.IModelTagService;
import com.wnkx.biz.remote.RemoteService;
import com.wnkx.biz.tag.mapper.TagMapper;
import com.wnkx.biz.tag.service.ITagService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 标签Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
@RequiredArgsConstructor
public class TagServiceImpl extends ServiceImpl<TagMapper, Tag> implements ITagService {
    /**
     * 模特关联标签服务
     */
    private final IModelTagService modelTagService;
    private final RemoteService remoteService;

    /**
     * 获取标签下拉框
     */
    @Override
    public List<TagSimpleVO> tagSelect(Long categoryId) {
        List<Tag> tags = baseMapper.tagSelect(categoryId);
        return BeanUtil.copyToList(tags, TagSimpleVO.class);
    }

    /**
     * 获取指定分类的指定级别及以上标签
     */
    @Override
    public List<TagListVO> rankUpslope(Long categoryId, Integer rank) {
        List<TagListVO> list = baseMapper.rankUpslope(categoryId, rank);
        return buildTagTree(list);
    }

    /**
     * 获取当前分类的指定级别标签
     */
    @Override
    public List<TagListVO> rank(Long categoryId, Integer rank) {
        return baseMapper.rank(categoryId, rank);
    }

    /**
     * 根据标签id获取标签信息
     */
    @Override
    public List<TagListVO> queryList(Collection<Long> tagIds) {
        return BeanUtil.copyToList(this.listByIds(tagIds), TagListVO.class);
    }

    /**
     * 获取当前分类的一级二级标签
     */
    @Override
    public List<TagListVO> stair(Long categoryId) {
        List<TagListVO> stair = baseMapper.stair(categoryId);
        if (CollUtil.isEmpty(stair)) {
            return new ArrayList<>();
        }

        return this.buildTagTree(stair);
    }

    /**
     * 查询标签
     *
     * @param id 标签主键
     * @return 标签
     */
    @Override
    public Tag selectTagById(Long id) {
        return this.getById(id);
    }

    /**
     * 查询标签列表
     *
     * @param tagListDTO 标签
     * @return 标签
     */
    @Override
    public List<TagListVO> selectTagList(TagListDTO tagListDTO) {
        List<Tag> tags = baseMapper.selectListByCondition(tagListDTO);

        if (CollUtil.isEmpty(tags)) {
            return new ArrayList<>();
        }
        Set<Tag> tagsSet = new HashSet<>(tags);

        //  通过查询到的标签的path，按/切割，获取上级标签的id
        Set<Long> parentIds = collectParentId(tags);

        if (CollUtil.isNotEmpty(parentIds)) {
            List<Tag> parentTags = baseMapper.selectByIds(parentIds);
            tagsSet.addAll(parentTags);
        }

        List<TagListVO> tagListVOS = BeanUtil.copyToList(tagsSet, TagListVO.class);

        List<Long> createUserIds = tagListVOS.stream().map(TagListVO::getCreateBy).collect(Collectors.toList());
        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(createUserIds);
        Map<Long, UserVO> userMap = remoteService.getUserMap(dto);
        for (TagListVO tagListVO : tagListVOS) {
            tagListVO.setCreateUser(userMap.getOrDefault(tagListVO.getCreateBy(), null));
        }

        return this.buildTagTree(tagListVOS);
    }

    /**
     * 新增标签
     *
     * @param tagDTO 标签
     * @return 结果
     */
    @Override
    public void insertTag(TagDTO tagDTO) {
        Tag tag = BeanUtil.copyProperties(tagDTO, Tag.class);

        String path = StrUtil.SLASH + tag.getCategoryId();
        if (tag.getParentId() != 0) {
            Tag parent = this.getById(tag.getParentId());
            Assert.notNull(parent, "父级标签不存在：" + tag.getParentId());
            path = parent.getPath() + StrPool.SLASH + tag.getParentId();
        }

        tag.setPath(path);

        tag.setCreateBy(SecurityUtils.getUserId());
        tag.setUpdateBy(SecurityUtils.getUserId());
        tag.setSort(Optional.ofNullable(baseMapper.getMaxGroupSort(tagDTO.getParentId())).orElse(Tag.builder().sort(1L).build()).getSort() + 1);
        this.save(tag);
    }

    /**
     * 修改标签
     *
     * @param tagDTO 标签
     * @return 结果
     */
    @Override
    public void updateTag(TagDTO tagDTO) {
        //获取当前标签数据
        Tag tag = BeanUtil.copyProperties(tagDTO, Tag.class);

        Tag tableTag = baseMapper.selectById(tagDTO.getId());
        Assert.notNull(tableTag, "标签数据不能为空");
        if (StatusEnum.ENABLED.getCode().equals(tableTag.getStatus()) && StatusEnum.UN_ENABLED.getCode().equals(tagDTO.getStatus())){
            //修改状态为禁用
            //  检查是否有下级有效标签
            //  a.有   不允许禁用
            //  b.没有  检查是否有被使用
            Long leafCount = baseMapper.getUnderLayerValidTagCount(tableTag);
            Assert.isFalse(leafCount > 0, () -> new ServiceException("存在下级有效标签，无法变更状态！"));

            Long useCount = modelTagService.getDictUseCount(tableTag.getId());
            Assert.isFalse(useCount > 0, () -> new ServiceException("标签已被关联使用，无法变更状态！"));
        }

        String path = StrUtil.SLASH + tag.getCategoryId();
        if (tag.getParentId() != 0) {
            Tag parent = this.getById(tag.getParentId());
            Assert.notNull(parent, "父级标签不存在：" + tag.getParentId());
            path = parent.getPath() + StrPool.SLASH + tag.getParentId();
        }

        tag.setPath(path);

        tag.setUpdateBy(SecurityUtils.getUserId());
        baseMapper.updateById(tag);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sort(TagSortDTO tagSort) {
        List<Tag> tags = baseMapper.selectByIds(Arrays.asList(tagSort.getOriginTagId(), tagSort.getResultTagId()));
        Assert.isTrue(CollUtil.isNotEmpty(tags) && tags.size() == 2, "标签Id不存在");
        Assert.isTrue(tags.get(0).getParentId().equals(tags.get(1).getParentId()), "只能同级别排序");
        Assert.isTrue(tags.get(0).getCategoryId().equals(tags.get(1).getCategoryId()), "只能同分类排序");
        Tag originTag = tags.get(0);
        Tag resultTag = tags.get(1);
        Long maxSort = originTag.getSort().compareTo(resultTag.getSort()) > 0 ? originTag.getSort() : resultTag.getSort();
        Long minSort = originTag.getSort().compareTo(resultTag.getSort()) > 0 ? resultTag.getSort() : originTag.getSort();

        List<Tag> tagGroup = baseMapper.tagSelectByParentId(tags.get(0).getParentId(), tags.get(0).getCategoryId(), minSort, maxSort);
        Map<Long, Tag> tagMap = tagGroup.stream().collect(Collectors.toMap(Tag::getId, item -> item));
        int type = 1;
        if (tagMap.get(tagSort.getResultTagId()).getSort() > tagMap.get(tagSort.getOriginTagId()).getSort()) {
            /**
             *   0 1 2 3
             *    后移
             *   3 0 1 2
             *  (x + tagGroup - 1)% sise
             */
            type = tagGroup.size() -1;
        } else if (tagMap.get(tagSort.getResultTagId()).getSort() < tagMap.get(tagSort.getOriginTagId()).getSort()) {
            /**
             * 0 1 2 3
             *  前移
             * 1 2 3 0
             * (x + 1)% sise*
             */
            type = 1;
        } else {
            return;
        }
        List<Tag> updateSort = new ArrayList<>();
        for (int i = 0; i < tagGroup.size(); i++) {
            Tag tag = new Tag();
            tag.setId(tagGroup.get(i).getId());
            tag.setSort(tagGroup.get((i + type) % tagGroup.size()).getSort());
            tag.setParentId(null);
            updateSort.add(tag);
        }
        baseMapper.updateBatchById(updateSort);
    }

    /**
     * 删除标签信息
     *
     * @param id 标签主键
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTagById(Long id) {
        Tag tag = this.getById(id);

        //  检查是否有下级标签
        //  a.有   不允许删除
        //  b.没有  检查是否有被使用
        Long leafCount = baseMapper.getUnderLayerTagCount(tag);
        Assert.isFalse(leafCount > 0, () -> new ServiceException("存在下级标签，无法删除！"));

        modelTagService.removeByTagId(id);
        removeById(id);
    }


    private List<TagListVO> buildTagTree(List<TagListVO> tags) {
        tags = tags.stream().sorted(Comparator.comparingLong(TagListVO::getSort).thenComparing(TagListVO::getCreateTime, Comparator.reverseOrder())).collect(Collectors.toList());

        Map<Long, TagListVO> tagMap = new HashMap<>();
        for (TagListVO tag : tags) {
            tagMap.put(tag.getId(), tag);
        }

        List<TagListVO> roots = new ArrayList<>();
        for (TagListVO tag : tags) {
            if (tag.getParentId() == 0) {
                roots.add(tag);
            } else {
                TagListVO parent = tagMap.get(tag.getParentId());
                if (parent != null) {
                    parent.getChildren().add(tag);
                }
            }
        }
        return roots;
    }

    /**
     * 获取上级标签的id
     */
    private Set<Long> collectParentId(List<Tag> tags) {
        List<String> paths = tags.stream()
                .filter(tag -> tag.getParentId() != null && tag.getParentId() != 0)
                .map(Tag::getPath)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        Set<Long> parentIds = new HashSet<>();
        List<Long> tagIds = tags.stream().map(Tag::getId).collect(Collectors.toList());
        for (String path : paths) {
            List<Long> split = StringUtils.splitToLong(path, StrUtil.SLASH);
            for (int i = 1; i < split.size(); i++) {
                Long parentId = split.get(i);
                if (!tagIds.contains(parentId)) {
                    parentIds.add(parentId);
                }
            }
        }
        return parentIds;
    }
}
