package com.wnkx.biz.config;

import com.ruoyi.system.api.domain.config.Region;
import lombok.extern.slf4j.Slf4j;
import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class IpRegionConfig {

    private static final String IP_REGION_XDB = "ip2region.xdb";

    private static Searcher SEARCHER;

    private static Map<String, String> ipRegionMap = new HashMap<>();

    @PostConstruct
    public void initSearcher() {
        long startTime = System.nanoTime();
        try (InputStream inputStream = IpRegionConfig.class.getClassLoader().getResourceAsStream(IP_REGION_XDB)) {
            if (inputStream == null) {
                throw new IOException(String.format("%s loading failed.", IP_REGION_XDB));
            }
            byte[] buffer = new byte[8192];
            int bytesRead;
            try (ByteArrayOutputStream output = new ByteArrayOutputStream()) {
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    output.write(buffer, 0, bytesRead);
                }
                SEARCHER = Searcher.newWithBuffer(output.toByteArray());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        long cost1 = TimeUnit.NANOSECONDS.toMicros(System.nanoTime() - startTime);
        log.info("{} loading completed, took: {} μs", IP_REGION_XDB, cost1);
        initMap();
    }

    public static Region getRegion(String ip) {
        try {
            String regionStr = SEARCHER.search(ip.replace("/", "").replace("\\", ""));
            return convert(regionStr);
        } catch (Exception e) {
            log.error("ip2region error: {}", e.getMessage());
            return new Region();
        }
    }

    private static Region convert(String regionStr) {
        if (regionStr == null || regionStr.length() == 0) {
            return null;
        }
        String[] regionSplit = regionStr.split("\\|");
        if (regionSplit.length != 5) {
            return null;
        }
        Region region = new Region();
        region.setCountry("0".equals(regionSplit[0]) ? null : regionSplit[0]);
        region.setRegion("0".equals(regionSplit[1]) ? null : regionSplit[1]);
        region.setProvince("0".equals(regionSplit[2]) ? null : regionSplit[2].replace("省",""));
        region.setCity("0".equals(regionSplit[3]) ? null : regionSplit[3].replace("市",""));
        region.setIsp("0".equals(regionSplit[4]) ? null : regionSplit[4]);
        region.setCode(ipRegionMap.getOrDefault(region.getProvince() + "-" + region.getCity(), ""));
        return region;
    }

    private void initMap() {
        ipRegionMap.put("北京-北京", "etPyXlDQAAaElMUKdlEmDkQ56C7sxhwQ");
        ipRegionMap.put("天津-天津", "etPyXlDQAAcowOVcPE3gGw4RrclS28BA");
        ipRegionMap.put("河北-石家庄", "etPyXlDQAAZM2ETRfPvtkQIgYEipJI4Q");
        ipRegionMap.put("河北-唐山", "etPyXlDQAAlk89Df0jOiZqo7LtE0y6sA");
        ipRegionMap.put("河北-秦皇岛", "etPyXlDQAAybpiIMl2zt-hFkAds57qSQ");
        ipRegionMap.put("河北-邯郸", "etPyXlDQAA8IRTo65TOS7VgYc4drVm5A");
        ipRegionMap.put("河北-邢台", "etPyXlDQAAGkJ3NaEWbiGYY39Dyx1GTg");
        ipRegionMap.put("河北-保定", "etPyXlDQAAzzSGeLuV7351yxHlXQYQ9g");
        ipRegionMap.put("河北-张家口", "etPyXlDQAAaxolVMfjNI-A9NadB2A_pQ");
        ipRegionMap.put("河北-承德", "etPyXlDQAA9ufrU2jmoZeEQTN4bFYUvw");
        ipRegionMap.put("河北-沧州", "etPyXlDQAAeLBpKByzzeFs8bV1g08ujA");
        ipRegionMap.put("河北-廊坊", "etPyXlDQAApihNfAeOKQsQiNk9LNc4wA");
        ipRegionMap.put("河北-衡水", "etPyXlDQAA4jAPGAUbOMv4PJL9q4qYcQ");
        ipRegionMap.put("山西-太原", "etPyXlDQAA8vjxo3VEs2ZSmZXfzpyIRg");
        ipRegionMap.put("山西-大同", "etPyXlDQAAREVWAwYGWtOnqqRlRLxNZg");
        ipRegionMap.put("山西-阳泉", "etPyXlDQAAQ8ylCsdVhqitdzYXkSuSiw");
        ipRegionMap.put("山西-长治", "etPyXlDQAAr2hIrir2BvOkPVHUhcPNpg");
        ipRegionMap.put("山西-晋城", "etPyXlDQAAlPtQm68MHCLzmAQKjPcdsQ");
        ipRegionMap.put("山西-朔州", "etPyXlDQAA7h_2bCYVOmW3W7wtwrQwew");
        ipRegionMap.put("山西-晋中", "etPyXlDQAA0lUZ4lUuz3IVu_07Z4rSxw");
        ipRegionMap.put("山西-运城", "etPyXlDQAAOijaTYFW4Ljk8332-12vSQ");
        ipRegionMap.put("山西-忻州", "etPyXlDQAA3FMc7YMP8ddJG1uRQHiAqg");
        ipRegionMap.put("山西-临汾", "etPyXlDQAA-mTif_hnLp3giDeaXYtuew");
        ipRegionMap.put("山西-吕梁", "etPyXlDQAAJ04t6UGLdRah4IDmFuA1Wg");
        ipRegionMap.put("辽宁-沈阳", "etPyXlDQAAUuSW126jKCUSYySQmfB-BQ");
        ipRegionMap.put("辽宁-大连", "etPyXlDQAAzYEBo3R1LPtJdoflI8xkpA");
        ipRegionMap.put("辽宁-鞍山", "etPyXlDQAAOmNvpQUfHAFLz9tW7GjVNA");
        ipRegionMap.put("辽宁-抚顺", "etPyXlDQAA4KP-uUDuOf2jpmKQIqmtdw");
        ipRegionMap.put("辽宁-本溪", "etPyXlDQAACUlfp035Wcp1s93ua-pmng");
        ipRegionMap.put("辽宁-丹东", "etPyXlDQAAW_PXiPbTNpU3wPO5LC_5Mw");
        ipRegionMap.put("辽宁-锦州", "etPyXlDQAArwtCY51O--K7un7T5oluzg");
        ipRegionMap.put("辽宁-营口", "etPyXlDQAAokjurtc_zFL9JDAuJWrOsg");
        ipRegionMap.put("辽宁-阜新", "etPyXlDQAAdHIwYaJz-xVFFqaZHHHf8A");
        ipRegionMap.put("辽宁-辽阳", "etPyXlDQAAXFIwLU-pIFs3LK6y3tspwg");
        ipRegionMap.put("辽宁-盘锦", "etPyXlDQAAkw1w9PX9hv-UZQ41Dd4U1g");
        ipRegionMap.put("辽宁-铁岭", "etPyXlDQAAQv8ZcFAqISLojnPHdXztVw");
        ipRegionMap.put("辽宁-朝阳", "etPyXlDQAApfUnwPNy7kUmg57IhTa6cg");
        ipRegionMap.put("辽宁-葫芦岛", "etPyXlDQAANRNrHlaDho7HrMrw8DZsCw");
        ipRegionMap.put("吉林-长春", "etPyXlDQAATmq0LQnYLMvbKxPj0WqUNQ");
        ipRegionMap.put("吉林-吉林", "etPyXlDQAAsVHld1HQ2S8mQi-bDZ3EcA");
        ipRegionMap.put("吉林-四平", "etPyXlDQAA13ZAmBzYGuoev1IfoVOOjA");
        ipRegionMap.put("吉林-辽源", "etPyXlDQAABTFQ74NIdN_9VHwewpM0nA");
        ipRegionMap.put("吉林-通化", "etPyXlDQAANa_JW5A8uS55ApxTl5sDWQ");
        ipRegionMap.put("吉林-白山", "etPyXlDQAAQLplnP-9PGAe8tjSfnEDAQ");
        ipRegionMap.put("吉林-松原", "etPyXlDQAAQvp5siJF1rtYti-D5fU4Ig");
        ipRegionMap.put("吉林-白城", "etPyXlDQAA71p0zCU3JnKVtnZ3a9EGKQ");
        ipRegionMap.put("吉林-延边朝鲜族自治州", "etPyXlDQAAhMqdk787c0EbdHKYN0lK1g");
        ipRegionMap.put("上海-上海", "etPyXlDQAACQIZsSkIgzol3kZrJqxdlw");
        ipRegionMap.put("江苏-南京", "etPyXlDQAAAGSF3nWM_AlanCNKp02bTQ");
        ipRegionMap.put("江苏-无锡", "etPyXlDQAAH_SVXI-2SZCSLIWPWyAfPA");
        ipRegionMap.put("江苏-徐州", "etPyXlDQAAI5ePq9ZUHiWBGweXqt3uFw");
        ipRegionMap.put("江苏-常州", "etPyXlDQAAfmRw8NGbAPPDheICAJLA5A");
        ipRegionMap.put("江苏-苏州", "etPyXlDQAAm3eSDv4uJa9zLIiF2K0hFg");
        ipRegionMap.put("江苏-南通", "etPyXlDQAAYr_zoCfbEDGTDmIqUhbFuA");
        ipRegionMap.put("江苏-连云港", "etPyXlDQAATW-_E1GoWX33gIqMr2EO8w");
        ipRegionMap.put("江苏-淮安", "etPyXlDQAASewa4KyxNL_o_bwsGmHiQg");
        ipRegionMap.put("江苏-盐城", "etPyXlDQAAasXmM2YojoMTntIpWvwbrw");
        ipRegionMap.put("江苏-扬州", "etPyXlDQAAVz1jrFLfnV_4Y4LEPi9d5w");
        ipRegionMap.put("江苏-镇江", "etPyXlDQAAt6jZg2qw8vHbLO0Td-pwMw");
        ipRegionMap.put("江苏-泰州", "etPyXlDQAA9vsEl-r8TdFSZNCpKyl1Tw");
        ipRegionMap.put("江苏-宿迁", "etPyXlDQAAt7WNdLC1k161xQpNUF-u5w");
        ipRegionMap.put("浙江-杭州", "etPyXlDQAAMmmkQM8n1sWgDwX15GW79Q");
        ipRegionMap.put("浙江-宁波", "etPyXlDQAAB6NJwbuw5bfkRQdle4Td5g");
        ipRegionMap.put("浙江-温州", "etPyXlDQAARhYdj3KpupLsBtkigSKXkA");
        ipRegionMap.put("浙江-嘉兴", "etPyXlDQAA5yU_kimyKtktcAyZgbuUUA");
        ipRegionMap.put("浙江-湖州", "etPyXlDQAARMrquStW-wt6npr18wV5_w");
        ipRegionMap.put("浙江-绍兴", "etPyXlDQAA2F243m1JkjPnLu-vq_G37Q");
        ipRegionMap.put("浙江-金华", "etPyXlDQAApdGP8-QM8q6wcO8FrUVmZg");
        ipRegionMap.put("浙江-衢州", "etPyXlDQAArRYjFR9aJXxc63ZJV3VBlQ");
        ipRegionMap.put("浙江-舟山", "etPyXlDQAAXH2meC2GYbbnh_bVeqcSgg");
        ipRegionMap.put("浙江-台州", "etPyXlDQAAL3aY9zv924q8445J3QNNgQ");
        ipRegionMap.put("浙江-丽水", "etPyXlDQAAYnRC8biuHRjsmjlVhAB_Gw");
        ipRegionMap.put("安徽-合肥", "etPyXlDQAAX1DhibbdKyuamUXySzOKDQ");
        ipRegionMap.put("安徽-芜湖", "etPyXlDQAAMci631MaBvT3K76nWogRgw");
        ipRegionMap.put("安徽-蚌埠", "etPyXlDQAA3Ef1wOPCzlJWNISfE8coPg");
        ipRegionMap.put("安徽-淮南", "etPyXlDQAA1aPoZOoB9MgzfNXYMdt5Ow");
        ipRegionMap.put("安徽-马鞍山", "etPyXlDQAAwqQmTBTUNh8RI52tAEyYaw");
        ipRegionMap.put("安徽-淮北", "etPyXlDQAA1xsErUajDy4kSTEyHW_lrw");
        ipRegionMap.put("安徽-铜陵", "etPyXlDQAANaJ0ww7L87lCcb761Oz7Rw");
        ipRegionMap.put("安徽-安庆", "etPyXlDQAALdLSqRaP5KXn_9ggfx68Kg");
        ipRegionMap.put("安徽-黄山", "etPyXlDQAAT2F3azGsTs6B8CJJ_IW-Aw");
        ipRegionMap.put("安徽-滁州", "etPyXlDQAA-1itlxeePSj3nCpTVjlFWA");
        ipRegionMap.put("安徽-阜阳", "etPyXlDQAAR3F-afVExY6bIBHyL-7D4w");
        ipRegionMap.put("安徽-宿州", "etPyXlDQAAM4m5GSAvA2O0I38Coz7cZA");
        ipRegionMap.put("安徽-六安", "etPyXlDQAAn8wIEmHyIscmty6Qt88hAA");
        ipRegionMap.put("安徽-亳州", "etPyXlDQAAu02mFBYg6BlPUUggnp4afA");
        ipRegionMap.put("安徽-池州", "etPyXlDQAAY8clPX5isgI2azSmI2jv8A");
        ipRegionMap.put("安徽-宣城", "etPyXlDQAAY5XbQS5B9YoVefsvibxKlA");
        ipRegionMap.put("福建-福州", "etPyXlDQAAnJs9qAYLJe3l73pZy5o-Ww");
        ipRegionMap.put("福建-厦门", "etPyXlDQAAdR_SK_uKuWrGIQ-4Iu7rpw");
        ipRegionMap.put("福建-莆田", "etPyXlDQAAipuk7yjKddamaLGVh6lcxQ");
        ipRegionMap.put("福建-三明", "etPyXlDQAAJLqf_sUdKA1EO66kwReTeg");
        ipRegionMap.put("福建-泉州", "etPyXlDQAAR4dx-msOOp3zFt_UiOn-Dw");
        ipRegionMap.put("福建-漳州", "etPyXlDQAA4K6_LuzElxLg9-aeQvu5Tg");
        ipRegionMap.put("福建-南平", "etPyXlDQAA6NMhqvoD5X4e6Z3TCA9r5Q");
        ipRegionMap.put("福建-龙岩", "etPyXlDQAAFjCH_g7p11LtNzM7wuutgA");
        ipRegionMap.put("福建-宁德", "etPyXlDQAAZWRMr6wSfXo7zoMgKtd59Q");
        ipRegionMap.put("江西-南昌", "etPyXlDQAAQVqRNlcuciQo6uHhz1l03g");
        ipRegionMap.put("江西-景德镇", "etPyXlDQAAyua13eebAe9F8CzxIWV4Pg");
        ipRegionMap.put("江西-萍乡", "etPyXlDQAAv9zY_6kuXpWnsIsERZVqGg");
        ipRegionMap.put("江西-九江", "etPyXlDQAA30YnneffuF93LlS5oM4z7Q");
        ipRegionMap.put("江西-新余", "etPyXlDQAATdrp-wUicZwjehLUihpnag");
        ipRegionMap.put("江西-鹰潭", "etPyXlDQAAO-MiMHVv9ilDC_2pi0Vilg");
        ipRegionMap.put("江西-赣州", "etPyXlDQAAlGZiTAjx7bL5BgazkvLgBQ");
        ipRegionMap.put("江西-吉安", "etPyXlDQAATniWopYe-fRN98nocxl1-w");
        ipRegionMap.put("江西-宜春", "etPyXlDQAAQQA7Ce5A0DXocxKbDu9Xig");
        ipRegionMap.put("江西-抚州", "etPyXlDQAAQDtvFTRHgJP7Q-YEON6uzw");
        ipRegionMap.put("江西-上饶", "etPyXlDQAAqK_PQJ6YT1KViZv7y-ogGA");
        ipRegionMap.put("山东-济南", "etPyXlDQAA3wuvzBQvWUxwycaM7fz7gA");
        ipRegionMap.put("山东-青岛", "etPyXlDQAAJAgPEwsKtAXbRBrlQMsuNA");
        ipRegionMap.put("山东-淄博", "etPyXlDQAAeLc_Wqsl7G8QIhtGMwRn-A");
        ipRegionMap.put("山东-枣庄", "etPyXlDQAAryv8thlSCVxKTPhNmTianw");
        ipRegionMap.put("山东-东营", "etPyXlDQAAxRzHUqJNVeNG69z05zquYw");
        ipRegionMap.put("山东-烟台", "etPyXlDQAAa3bPRICR_EiKbD5jMC9oAw");
        ipRegionMap.put("山东-潍坊", "etPyXlDQAAFyf7T9vp2ilpzEPHmGfNHg");
        ipRegionMap.put("山东-济宁", "etPyXlDQAA6q35J5JmVcWKNX5tNkii6Q");
        ipRegionMap.put("山东-泰安", "etPyXlDQAA4O1c9S5Ya9132Yu8s0uPug");
        ipRegionMap.put("山东-威海", "etPyXlDQAAO4xsyRSYz3230D5CyJj_Bw");
        ipRegionMap.put("山东-日照", "etPyXlDQAAnD_2sEOP-mQHVPS6ntvnMQ");
        ipRegionMap.put("山东-临沂", "etPyXlDQAAuioRVyzNeMwVx4WYI3T0eQ");
        ipRegionMap.put("山东-德州", "etPyXlDQAA4TisxAye1vmWq6X43BErwQ");
        ipRegionMap.put("山东-聊城", "etPyXlDQAAR3no83IX9kFljwUbksVkdA");
        ipRegionMap.put("山东-滨州", "etPyXlDQAAYEgGBzhCA7vqd-I1F8L3Rw");
        ipRegionMap.put("山东-菏泽", "etPyXlDQAA7_YR6nuI5wbjC0yPE6pvhg");
        ipRegionMap.put("河南-郑州", "etPyXlDQAAtUAgnvVW5M-BdE8LDpUbJw");
        ipRegionMap.put("河南-开封", "etPyXlDQAAmMAdWYtEG7w5dSxUl5DK0A");
        ipRegionMap.put("河南-洛阳", "etPyXlDQAADxg5qfZGt72J8QiUncxlUg");
        ipRegionMap.put("河南-平顶山", "etPyXlDQAA6EmiVu3WfTcTRmAjS9WJkg");
        ipRegionMap.put("河南-安阳", "etPyXlDQAA9X6cIStpnUxXtMVgoU0PMg");
        ipRegionMap.put("河南-鹤壁", "etPyXlDQAAR_TtqsIU5D-vAwkKJnyxVQ");
        ipRegionMap.put("河南-新乡", "etPyXlDQAAVwhA58f2ht12z0FuCMdewA");
        ipRegionMap.put("河南-焦作", "etPyXlDQAA9Mb3zOcop5Q7DELmi_Ufqg");
        ipRegionMap.put("河南-濮阳", "etPyXlDQAAqbdcta3Z0KJ_NgYlFxKlmA");
        ipRegionMap.put("河南-许昌", "etPyXlDQAAN2XBOjPgvI3UcF0a7vh51w");
        ipRegionMap.put("河南-漯河", "etPyXlDQAAFj44z-ohRmutO8ABLIwJ8Q");
        ipRegionMap.put("河南-三门峡", "etPyXlDQAAbT-Duhbaf9F2be8D6_7BHQ");
        ipRegionMap.put("河南-南阳", "etPyXlDQAAT6vTS5Ph5mBU9wGbQrJjjA");
        ipRegionMap.put("河南-商丘", "etPyXlDQAAqZTDkcfCx_dJedVSQBWW2Q");
        ipRegionMap.put("河南-信阳", "etPyXlDQAAUnpz7I6FGv0aiVEA65crow");
        ipRegionMap.put("河南-周口", "etPyXlDQAAsHYhTnBh6K6fFSR-dQ67FQ");
        ipRegionMap.put("河南-驻马店", "etPyXlDQAArnKuO-9_gsEJucOPV-V_6A");
        ipRegionMap.put("河南-济源", "etPyXlDQAA8BXyBt_JfLy7dKIOd3tBww");
        ipRegionMap.put("湖北-武汉", "etPyXlDQAAY3QTa31k5jvWo_UOrsIT5g");
        ipRegionMap.put("湖北-黄石", "etPyXlDQAAvqjMIgc72g7kN6GOCzfrUA");
        ipRegionMap.put("湖北-十堰", "etPyXlDQAAFdC696fm7vMdUZV-gGT1bA");
        ipRegionMap.put("湖北-宜昌", "etPyXlDQAAi2pJkNkYHVFOe6Dz3vLLMA");
        ipRegionMap.put("湖北-襄阳", "etPyXlDQAAMFkr85VfyJh2NK9Tld0lbg");
        ipRegionMap.put("湖北-鄂州", "etPyXlDQAAaRGBlG3dQljeO-HvD9DGSA");
        ipRegionMap.put("湖北-荆门", "etPyXlDQAAh6zd3vBRbL6PEZxCgh-j7Q");
        ipRegionMap.put("湖北-孝感", "etPyXlDQAAoo8QEiS8WC2MEM9w_9En4Q");
        ipRegionMap.put("湖北-荆州", "etPyXlDQAAKXs8Wq9McGG46rAf6xe1fg");
        ipRegionMap.put("湖北-黄冈", "etPyXlDQAAwMLdbAI58oI9s1Vp9MUqUg");
        ipRegionMap.put("湖北-咸宁", "etPyXlDQAA5EtINsTEE-Xncdb6LWDc_g");
        ipRegionMap.put("湖北-随州", "etPyXlDQAA0Gf9WcPJEl2dGghMy5Vytg");
        ipRegionMap.put("湖北-恩施土家族苗族自治州", "etPyXlDQAAaWwujVNsdkBqeMneptR3Ww");
        ipRegionMap.put("湖北-仙桃", "etPyXlDQAAjQbNL3n7A6yurThYHvia1g");
        ipRegionMap.put("湖北-潜江", "etPyXlDQAAG7nlDUQ8SCtyoc1k1-FkOw");
        ipRegionMap.put("湖北-天门", "etPyXlDQAACiJWaah3SNeLDKrolsONzw");
        ipRegionMap.put("湖北-神农架林区", "etPyXlDQAAExGvWWhxgsdUwiUs1zCdmQ");
        ipRegionMap.put("湖南-长沙", "etPyXlDQAA_4l1GWAosqvzISzTwzx51w");
        ipRegionMap.put("湖南-株洲", "etPyXlDQAAOszknZpUR7VRbHvVFXYr_Q");
        ipRegionMap.put("湖南-湘潭", "etPyXlDQAAGgIRuUc_WbUbPgfTSQB3lw");
        ipRegionMap.put("湖南-衡阳", "etPyXlDQAAL6WgN29IHGRyXltJQhwRCQ");
        ipRegionMap.put("湖南-邵阳", "etPyXlDQAA14jCFn0gWUESHuWRTMQvUQ");
        ipRegionMap.put("湖南-岳阳", "etPyXlDQAAKQEaKLkAQuuu99n7yeL2Pw");
        ipRegionMap.put("湖南-常德", "etPyXlDQAAxO4lVCYoHobqgVsSgcdpYA");
        ipRegionMap.put("湖南-张家界", "etPyXlDQAAMRkUa_Hr48bLdkCJt5K4hA");
        ipRegionMap.put("湖南-益阳", "etPyXlDQAACgsiTRdeWvYA23I78tTppA");
        ipRegionMap.put("湖南-郴州", "etPyXlDQAA43TCSnRBYDaV5iUCCMgb9g");
        ipRegionMap.put("湖南-永州", "etPyXlDQAA9BbHy_RBFTpwcESA9cSmNg");
        ipRegionMap.put("湖南-怀化", "etPyXlDQAAtAxtoVCRozsI5_Q9m2ICAA");
        ipRegionMap.put("湖南-娄底", "etPyXlDQAAUdn9JHIhpyDCTzyFDifOrQ");
        ipRegionMap.put("湖南-湘西土家族苗族自治州", "etPyXlDQAADI0nUhlqB-tcOr9nGJI30g");
        ipRegionMap.put("广东-广州", "etPyXlDQAAL8kNCktAQzzafu8cRJJn9Q");
        ipRegionMap.put("广东-韶关", "etPyXlDQAAsHcDPEMKQZtRiTxNyZHP0g");
        ipRegionMap.put("广东-深圳", "etPyXlDQAAEVUYBWo6N7K5h7FG1KmtPw");
        ipRegionMap.put("广东-珠海", "etPyXlDQAAWpCF2hvFJqQiqigJF-oKsg");
        ipRegionMap.put("广东-汕头", "etPyXlDQAAKb5kM6J9eguVhJhN7-h8JQ");
        ipRegionMap.put("广东-佛山", "etPyXlDQAAB1eUIddE_Fiw26zLLx_E7w");
        ipRegionMap.put("广东-江门", "etPyXlDQAA3NF8YX1AAVDiq1TCEdFgOw");
        ipRegionMap.put("广东-湛江", "etPyXlDQAAQULehVOSHTBdUeTMaekyAA");
        ipRegionMap.put("广东-茂名", "etPyXlDQAArLt69yeCpgZo3U6GboNoxQ");
        ipRegionMap.put("广东-肇庆", "etPyXlDQAAC5hQPajfQavj8mC2UYe7vQ");
        ipRegionMap.put("广东-惠州", "etPyXlDQAAbKUe0T96vcEB384whXKNeQ");
        ipRegionMap.put("广东-梅州", "etPyXlDQAApu2oyx01wRqV66trc5Lukg");
        ipRegionMap.put("广东-汕尾", "etPyXlDQAA0E0jT5oEHkbmY3SQVB5msw");
        ipRegionMap.put("广东-河源", "etPyXlDQAAcTeRdi93YyRrC-u9WqfHPA");
        ipRegionMap.put("广东-阳江", "etPyXlDQAA95BgWCkag63dehui8vaLlg");
        ipRegionMap.put("广东-清远", "etPyXlDQAAShzGwfpbLUKzYnP_Kq010Q");
        ipRegionMap.put("广东-东莞", "etPyXlDQAAbCIa-RW-cv5txxeMEclRcA");
        ipRegionMap.put("广东-中山", "etPyXlDQAAT3RlSC_76M5P2JARKwHaew");
        ipRegionMap.put("广东-潮州", "etPyXlDQAAl8NQfdDMYcNONdeP0Y2wGA");
        ipRegionMap.put("广东-揭阳", "etPyXlDQAAHHaKCVHQ5jn0g7t5bj7jsw");
        ipRegionMap.put("广东-云浮", "etPyXlDQAArfvmGqFGN6DnO5pzB0icqw");
        ipRegionMap.put("广西-南宁", "etPyXlDQAAQcFPQ7C4BatD7-wwd7uVBQ");
        ipRegionMap.put("广西-柳州", "etPyXlDQAAaFkVVziaoMQ_RFrIJDcEDg");
        ipRegionMap.put("广西-桂林", "etPyXlDQAAv54-V_mPTl4wk58tLe6wqg");
        ipRegionMap.put("广西-梧州", "etPyXlDQAAyWh_oBCdVI0ycD1SQS-JBQ");
        ipRegionMap.put("广西-北海", "etPyXlDQAALbWD60iatfb6McDoqkCmvQ");
        ipRegionMap.put("广西-防城港", "etPyXlDQAAMGu1fnxNzsk9TN7Gag7kZw");
        ipRegionMap.put("广西-钦州", "etPyXlDQAA47o8lUAOCImUdy5TUrQpng");
        ipRegionMap.put("广西-贵港", "etPyXlDQAAWlMhOLfLJKa1NUPupDEcNQ");
        ipRegionMap.put("广西-玉林", "etPyXlDQAA5rgjw4elu8ZFC9a1WfJVIg");
        ipRegionMap.put("广西-百色", "etPyXlDQAAgB9xLxYKJGJ0G9HfdBaAPA");
        ipRegionMap.put("广西-贺州", "etPyXlDQAAF1OmHiQxbihFbjCOqq965w");
        ipRegionMap.put("广西-河池", "etPyXlDQAAGy40zrcHo4wZV7vNqiHyRQ");
        ipRegionMap.put("广西-来宾", "etPyXlDQAA2xXQdIauVT4-HT8xtuK7bg");
        ipRegionMap.put("广西-崇左", "etPyXlDQAA9a5F07TsSKidqur3ygDJFQ");
        ipRegionMap.put("海南-海口", "etPyXlDQAA1G4hXQ4PHr2vySqEiEYeaA");
        ipRegionMap.put("海南-三亚", "etPyXlDQAA9T5Fm3kvBYPrxi8eBlDaag");
        ipRegionMap.put("海南-三沙", "etPyXlDQAA_1-KGoDkP-GySjcMX690qg");
        ipRegionMap.put("海南-儋州", "etPyXlDQAAg9xmSeLBiFu-D1EJmsEzxA");
        ipRegionMap.put("海南-五指山", "etPyXlDQAAR50jAmwCz48iCm0LpSHIjA");
        ipRegionMap.put("海南-琼海", "etPyXlDQAAmy-q_Z93OkDrpZwC3QvXdQ");
        ipRegionMap.put("海南-文昌", "etPyXlDQAAPBYFvETWDkS7B3icyz00gg");
        ipRegionMap.put("海南-万宁", "etPyXlDQAAFZIwbZHbAIZZ0dDowsamUg");
        ipRegionMap.put("海南-东方", "etPyXlDQAAuLE0hA5Iwh3vJbNVvNhcpw");
        ipRegionMap.put("海南-定安县", "etPyXlDQAAYVD0kiUW2Xw2iJ_6KEV72Q");
        ipRegionMap.put("海南-屯昌县", "etPyXlDQAAtF04TNf9ZdB1XPGq9Lo_ug");
        ipRegionMap.put("海南-澄迈县", "etPyXlDQAAvazX6aw8rJxOw2ERJsRlzg");
        ipRegionMap.put("海南-临高县", "etPyXlDQAAaWYJwWFkvSHaXrhklT1PfA");
        ipRegionMap.put("海南-白沙黎族自治县", "etPyXlDQAAqRH3JIZlgQ5MeKtrusOFOg");
        ipRegionMap.put("海南-昌江黎族自治县", "etPyXlDQAAqvIgK0nM55stdO2E58GBpw");
        ipRegionMap.put("海南-乐东黎族自治县", "etPyXlDQAAUJ4vzk2GyWT52X7XuUI6_A");
        ipRegionMap.put("海南-陵水黎族自治县", "etPyXlDQAA_VadlPCT0gaYUV9NYeLVmw");
        ipRegionMap.put("海南-保亭黎族苗族自治县", "etPyXlDQAAX-_rXdlXaL8v5WC-Ne6nkw");
        ipRegionMap.put("海南-琼中黎族苗族自治县", "etPyXlDQAAX3YaDyOKWf_dZqV5fuAURA");
        ipRegionMap.put("重庆-重庆", "etPyXlDQAAGTrvmGi8WvKILDYj7tGrfg");
        ipRegionMap.put("四川-成都", "etPyXlDQAAYlWOJ0f31gWpupjcuqHb-g");
        ipRegionMap.put("四川-自贡", "etPyXlDQAAh_H04L82G7mX7q_h0_dxGA");
        ipRegionMap.put("四川-攀枝花", "etPyXlDQAA0a6NpiSF0904dP3Fk-MWzw");
        ipRegionMap.put("四川-泸州", "etPyXlDQAA3MtCFjq9OR5hbMTUribWLA");
        ipRegionMap.put("四川-德阳", "etPyXlDQAAevUS3YXXhoom6EnahE72pg");
        ipRegionMap.put("四川-绵阳", "etPyXlDQAALmC-thV_CIhuo1_1VpuOMw");
        ipRegionMap.put("四川-广元", "etPyXlDQAAzQ-E1djEFIZ9SnwohWSabw");
        ipRegionMap.put("四川-遂宁", "etPyXlDQAALxwksM74g2FLMzKr7AjAaA");
        ipRegionMap.put("四川-内江", "etPyXlDQAAIhdiy6y1kBaL4tZ2KcuK_g");
        ipRegionMap.put("四川-乐山", "etPyXlDQAAs29w2r9_3bF3pjLGPwW3xw");
        ipRegionMap.put("四川-南充", "etPyXlDQAAHXxl1lWV0aVIPqn0C41sPg");
        ipRegionMap.put("四川-眉山", "etPyXlDQAAcU_nHobfZcwD8J9YKrRLdw");
        ipRegionMap.put("四川-宜宾", "etPyXlDQAAp205841HMx4GiGa3OmAxKw");
        ipRegionMap.put("四川-广安", "etPyXlDQAAFkhP2UhseRihO0JzbUtBgw");
        ipRegionMap.put("四川-达州", "etPyXlDQAA3vKsiwShTtQ8vUIzwIu8Ew");
        ipRegionMap.put("四川-雅安", "etPyXlDQAAxBabGXfAMjwXLhUbSyW7Qg");
        ipRegionMap.put("四川-巴中", "etPyXlDQAAaWfBi1WqGsznK7kL76MU8g");
        ipRegionMap.put("四川-资阳", "etPyXlDQAA_BNIq7RPwCdC3fFrRo6K9w");
        ipRegionMap.put("四川-阿坝藏族羌族自治州", "etPyXlDQAAngqvNOcVHTk8ZyPOjq2rJw");
        ipRegionMap.put("四川-甘孜藏族自治州", "etPyXlDQAArV9dIKw-PD06AhK5OovOcw");
        ipRegionMap.put("四川-凉山彝族自治州", "etPyXlDQAAFHWmaB0H82S_tIjk0Npw_w");
        ipRegionMap.put("贵州-贵阳", "etPyXlDQAAmH69Zvu1IRQI617RQJDsrg");
        ipRegionMap.put("贵州-六盘水", "etPyXlDQAANQLSHzjFphbLRMTvn4NDcQ");
        ipRegionMap.put("贵州-遵义", "etPyXlDQAAwX-uQ66awIuNhaR3fNs7eA");
        ipRegionMap.put("贵州-安顺", "etPyXlDQAALItiTJh9OOGB8XHF2xgQCA");
        ipRegionMap.put("贵州-毕节", "etPyXlDQAAF7yjKseMcFlGtVClz2yTog");
        ipRegionMap.put("贵州-铜仁", "etPyXlDQAAxfPnNHgQNYigvz6GiarIyg");
        ipRegionMap.put("贵州-黔西南布依族苗族自治州", "etPyXlDQAA87mTMP2Stn1_H74aMxFN9Q");
        ipRegionMap.put("贵州-黔东南苗族侗族自治州", "etPyXlDQAAuOp8SI9TQ2jpOdSFABbyqA");
        ipRegionMap.put("贵州-黔南布依族苗族自治州", "etPyXlDQAAO-o7PSrWfbheuO_a43Y_bw");
        ipRegionMap.put("云南-昆明", "etPyXlDQAAm1WwprA5_jTkAEdnfp_8CA");
        ipRegionMap.put("云南-曲靖", "etPyXlDQAASHX9Q3eadCpKHq880wBURQ");
        ipRegionMap.put("云南-玉溪", "etPyXlDQAAuqHpVSy-yD8zqlHr6ka3Uw");
        ipRegionMap.put("云南-保山", "etPyXlDQAAcmsD_tcU1COwCHGl4u4odw");
        ipRegionMap.put("云南-昭通", "etPyXlDQAAn1ikCqg0VqID3_MR8UFupA");
        ipRegionMap.put("云南-丽江", "etPyXlDQAAKv7ZK3B9lC2UbMnVmpcRKg");
        ipRegionMap.put("云南-普洱", "etPyXlDQAAPepPdvwfnXIq7L3V1hB5Gg");
        ipRegionMap.put("云南-临沧", "etPyXlDQAAcPAfTOkJ5_g1xN73A5ieYA");
        ipRegionMap.put("云南-楚雄彝族自治州", "etPyXlDQAA2bWkGkmXqSSGDMfEDXGM1w");
        ipRegionMap.put("云南-红河哈尼族彝族自治州", "etPyXlDQAAlQoopGVxwwnHgLlgsas3bw");
        ipRegionMap.put("云南-文山壮族苗族自治州", "etPyXlDQAAGAY-aGKsTkMy1Ze8uucbMg");
        ipRegionMap.put("云南-西双版纳傣族自治州", "etPyXlDQAAPoh9Yyf_Di06InB88sIvVA");
        ipRegionMap.put("云南-大理白族自治州", "etPyXlDQAA4OIzmi24V1PHQCDgh3_aRg");
        ipRegionMap.put("云南-德宏傣族景颇族自治州", "etPyXlDQAA4b_r6MYUPGAy9xIHzS-LMA");
        ipRegionMap.put("云南-怒江傈僳族自治州", "etPyXlDQAAJZ_R7CcIKE98obhBX6yrnA");
        ipRegionMap.put("云南-迪庆藏族自治州", "etPyXlDQAAvuk4b7VZtqA7rh-tjhD3Yw");
        ipRegionMap.put("西藏-拉萨", "etPyXlDQAAXEE13SM3XYG0vtc2bs-BTA");
        ipRegionMap.put("西藏-日喀则", "etPyXlDQAANlcurb2KMdtzVcAVXd84rw");
        ipRegionMap.put("西藏-昌都", "etPyXlDQAAE9phl_Mzs9VeOek9DZqGOw");
        ipRegionMap.put("西藏-林芝", "etPyXlDQAAtXjQTxNyrw402rUibwg_OQ");
        ipRegionMap.put("西藏-山南", "etPyXlDQAAad44uDVO-oQwM202HrAciQ");
        ipRegionMap.put("西藏-那曲", "etPyXlDQAAMQN2rTssNCbzC9Lz6QFejg");
        ipRegionMap.put("西藏-阿里地区", "etPyXlDQAAi-SS-i4oqMwM4UmKcVUb8w");
        ipRegionMap.put("陕西-西安", "etPyXlDQAATxarxaPqQLr5jSYQFZdsQA");
        ipRegionMap.put("陕西-铜川", "etPyXlDQAASgDofI-tVnJ5kSiw8-h8Eg");
        ipRegionMap.put("陕西-宝鸡", "etPyXlDQAAiK80GzoPF28MOSYg9ciD5g");
        ipRegionMap.put("陕西-咸阳", "etPyXlDQAAh7t4pZ0eO48QqU6zNvUJ_g");
        ipRegionMap.put("陕西-渭南", "etPyXlDQAA1Atjy886-lEeISDGWHdnug");
        ipRegionMap.put("陕西-延安", "etPyXlDQAAINHc_K4icDcrU5_hAsJsRg");
        ipRegionMap.put("陕西-汉中", "etPyXlDQAAareVmalAF0AJ5TDQlmbpvg");
        ipRegionMap.put("陕西-榆林", "etPyXlDQAAWRdCiSxtUIFc2wtWX18Ieg");
        ipRegionMap.put("陕西-安康", "etPyXlDQAAF_9P2MlZotcDhtqxUDSkjA");
        ipRegionMap.put("陕西-商洛", "etPyXlDQAAq3sEha7dJmJiB5VZYTdSgA");
        ipRegionMap.put("甘肃-兰州", "etPyXlDQAAER6u6wf5Hn1PXWo4PdeeQw");
        ipRegionMap.put("甘肃-嘉峪关", "etPyXlDQAAnV03ap4BzN9zf0UbHNigxg");
        ipRegionMap.put("甘肃-金昌", "etPyXlDQAAXif_-5BAPTk4zedP-3CSSQ");
        ipRegionMap.put("甘肃-白银", "etPyXlDQAAZCrGDR-OX6EssfFQXeBVqg");
        ipRegionMap.put("甘肃-天水", "etPyXlDQAArNsjEvPAKMKCgv4bApYIVQ");
        ipRegionMap.put("甘肃-武威", "etPyXlDQAABJgdfX1jX4mnMIVMtRAWAA");
        ipRegionMap.put("甘肃-张掖", "etPyXlDQAASnmB1t3hlUgdbqCxWDbN5g");
        ipRegionMap.put("甘肃-平凉", "etPyXlDQAAfcJ9cXbhwh6v0NuWiHvO0A");
        ipRegionMap.put("甘肃-酒泉", "etPyXlDQAA7Kcu_SyNRV9_dPPsVZJbKg");
        ipRegionMap.put("甘肃-庆阳", "etPyXlDQAACRU-TeyTgowIyIWbkFLpXw");
        ipRegionMap.put("甘肃-定西", "etPyXlDQAAxSub1jrRJK1dSGdtrdsfug");
        ipRegionMap.put("甘肃-陇南", "etPyXlDQAApuqBpKrdtdCevpfq39WRRg");
        ipRegionMap.put("甘肃-临夏回族自治州", "etPyXlDQAA_fe-3zu33C8wdrQ-yAexwQ");
        ipRegionMap.put("甘肃-甘南藏族自治州", "etPyXlDQAA1RtQ4kQS1l4eRcwhbEI-GA");
        ipRegionMap.put("青海-西宁", "etPyXlDQAAp1pHDKinqUL9-MbR72aEdA");
        ipRegionMap.put("青海-海东", "etPyXlDQAAq5EZbvGcAnDu9ITHaqiZVQ");
        ipRegionMap.put("青海-海北藏族自治州", "etPyXlDQAAuXvfTuYlU0AeTRbh8OcMuw");
        ipRegionMap.put("青海-黄南藏族自治州", "etPyXlDQAAkgluwrSWqcNxvEZwVqQNVQ");
        ipRegionMap.put("青海-海南藏族自治州", "etPyXlDQAABsFw1NFfQCkjTwXs-j2AUw");
        ipRegionMap.put("青海-果洛藏族自治州", "etPyXlDQAAbdOn-hsZSAzvWJkp0yZjcQ");
        ipRegionMap.put("青海-玉树藏族自治州", "etPyXlDQAA33VCPpAUykxYumNy2Z_VXQ");
        ipRegionMap.put("青海-海西蒙古族藏族自治州", "etPyXlDQAAem7EkZWOqMen0CkPYliaBA");
        ipRegionMap.put("宁夏-银川", "etPyXlDQAA5xtgCXQm5ssn1qd-_RhB1Q");
        ipRegionMap.put("宁夏-石嘴山", "etPyXlDQAAFkGD7CNXtzeKzyJ-cFeCnA");
        ipRegionMap.put("宁夏-吴忠", "etPyXlDQAAoOaUGhb7uaZTifr_NQD-gA");
        ipRegionMap.put("宁夏-固原", "etPyXlDQAAPBwGAQiRh7FESJ33-gwy_g");
        ipRegionMap.put("宁夏-中卫", "etPyXlDQAA5P8hiN7VqeFrgHILaZl9xQ");
        ipRegionMap.put("新疆-乌鲁木齐", "etPyXlDQAAw_LMWMY0rjBfL4rQJ9E3tQ");
        ipRegionMap.put("新疆-克拉玛依", "etPyXlDQAAQiQ0t1r9ZiVEY7sFTG1bXQ");
        ipRegionMap.put("新疆-吐鲁番", "etPyXlDQAAebjOFsNJIWGj-ElfDhTrMQ");
        ipRegionMap.put("新疆-哈密", "etPyXlDQAA_V-G1AifkPp65sC1-3ihag");
        ipRegionMap.put("新疆-昌吉回族自治州", "etPyXlDQAA0Gtz3qml5ifC4l0r7ipFRQ");
        ipRegionMap.put("新疆-博尔塔拉蒙古自治州", "etPyXlDQAAB0iY3W1IyRQHNJMKkTTcZQ");
        ipRegionMap.put("新疆-巴音郭楞蒙古自治州", "etPyXlDQAA5pr7SD0q-inDm-vqsgYGHQ");
        ipRegionMap.put("新疆-阿克苏地区", "etPyXlDQAA1HCPstEkH9ONafWktY6pxg");
        ipRegionMap.put("新疆-克孜勒苏柯尔克孜自治州", "etPyXlDQAAkSJxpEnhhxIFcvvLF2wsZA");
        ipRegionMap.put("新疆-喀什地区", "etPyXlDQAAdTMvDQMKTKYjEQ2Ct7QGYA");
        ipRegionMap.put("新疆-和田地区", "etPyXlDQAAkSsjq54YSNH1TBe8RxpFLg");
        ipRegionMap.put("新疆-伊犁哈萨克自治州", "etPyXlDQAATKVV54resJIgr5WJPi0cZw");
        ipRegionMap.put("新疆-塔城地区", "etPyXlDQAAdEK643qt7s8rpoDxSrv7lQ");
        ipRegionMap.put("新疆-阿勒泰地区", "etPyXlDQAAlRPDktSSgyJlfzhIGgvyFQ");
        ipRegionMap.put("新疆-石河子", "etPyXlDQAAQJl2kf2MOn0mVgEU3uYrDg");
        ipRegionMap.put("新疆-阿拉尔", "etPyXlDQAAnGxy5wI2A_Tt45lpLBs-PA");
        ipRegionMap.put("新疆-图木舒克", "etPyXlDQAAO9HerCzD9l4dxlOfbD1IIg");
        ipRegionMap.put("新疆-五家渠", "etPyXlDQAAC2Pnh_HlnD4bntVO0SKzkg");
        ipRegionMap.put("新疆-北屯", "etPyXlDQAAOe3YgwkH_83DCVvFp2BDsQ");
        ipRegionMap.put("新疆-铁门关", "etPyXlDQAAbu5resh4DR_WZO6EAUXTjw");
        ipRegionMap.put("新疆-双河", "etPyXlDQAAHMfY33Eb_P9RhxyB6setPQ");
        ipRegionMap.put("新疆-可克达拉", "etPyXlDQAAALfEZ_JuVMyd6phl70_RHQ");
        ipRegionMap.put("新疆-昆玉", "etPyXlDQAAzPF0b1JxiW9t27nFQfe31w");
        ipRegionMap.put("新疆-胡杨河", "etPyXlDQAAdVngkEmQivrMZ5LW3oxNZg");
        ipRegionMap.put("台湾-台湾", "etPyXlDQAAz_zpDX6vYcqrVh36wrx9RA");
        ipRegionMap.put("香港-香港", "etPyXlDQAAOnbFpH0hDn1Y6rsx2HS1Mw");
        ipRegionMap.put("澳门-澳门", "etPyXlDQAAE39pTXxZQIjOugRZFPZkFQ");
        ipRegionMap.put("北京", "etPyXlDQAAbH0c0Kuw694fupD1dXJBog");
        ipRegionMap.put("天津", "etPyXlDQAA81xCfget_Fg3umNRwqWXHQ");
        ipRegionMap.put("河北", "etPyXlDQAAa_YhTaWG604EszLBN7wB9Q");
        ipRegionMap.put("山西", "etPyXlDQAA-QXEG9ab4K_9nqYnONu3iw");
        ipRegionMap.put("内蒙", "etPyXlDQAAI6nFL6kIFvDq3HyniTa4VA");
        ipRegionMap.put("辽宁", "etPyXlDQAAcVKT43G0lh7BnNQeBE7V4Q");
        ipRegionMap.put("吉林", "etPyXlDQAA1JsfxX_7xTnL5U_W_5cPbA");
        ipRegionMap.put("黑龙", "etPyXlDQAAgSpdFaCZOBlI_QsSrH4nJA");
        ipRegionMap.put("上海", "etPyXlDQAAiDIglMC49yWaZh1NND1ByA");
        ipRegionMap.put("江苏", "etPyXlDQAAg6CJ5hnQA1ThX6_5hj43yg");
        ipRegionMap.put("浙江", "etPyXlDQAAh7R-C5HJMHTQjfkmpjDeTg");
        ipRegionMap.put("安徽", "etPyXlDQAAS73Tg3kSTxXPepKowwaAHQ");
        ipRegionMap.put("福建", "etPyXlDQAAFD5dCUUHmZdVQu7eWW2SuA");
        ipRegionMap.put("江西", "etPyXlDQAAtyJ8LU-xKXqnyi6Mi97P_Q");
        ipRegionMap.put("山东", "etPyXlDQAADOcI10p5vFnZmF-K12L2fA");
        ipRegionMap.put("河南", "etPyXlDQAAs0AmP4kFZIGXDclH2yj7bw");
        ipRegionMap.put("湖北", "etPyXlDQAAXEvi9P4NMUf-8M8sjCxR-g");
        ipRegionMap.put("湖南", "etPyXlDQAAWsua-D20moLzpsxDR8s6Mw");
        ipRegionMap.put("广东", "etPyXlDQAAFCi0IdZQH9z-Z4t6bElCTQ");
        ipRegionMap.put("广西", "etPyXlDQAATquGeICKteGPOT9KyVKSyw");
        ipRegionMap.put("海南", "etPyXlDQAA7iNj0Od5PlTcMWl1GueDBA");
        ipRegionMap.put("重庆", "etPyXlDQAAaPQFMLee1Aa-FXB7iJEnJw");
        ipRegionMap.put("四川", "etPyXlDQAAFp8s-k_0v3LmYzzmo2d0cw");
        ipRegionMap.put("贵州", "etPyXlDQAAHK_0TrNPCxVFjCteZVTcNA");
        ipRegionMap.put("云南", "etPyXlDQAAmwFVa6kDxxUxW2yDVWF7OQ");
        ipRegionMap.put("西藏", "etPyXlDQAAJGpiqKlQK5PqFvSjT8UlCg");
        ipRegionMap.put("陕西", "etPyXlDQAAlK5VOqNc5sfKG4A-ZiOhvg");
        ipRegionMap.put("甘肃", "etPyXlDQAAF-bDAsXycdng9HPKqGubcw");
        ipRegionMap.put("青海", "etPyXlDQAAnh30LRrESwTkJgct3qXnVQ");
        ipRegionMap.put("宁夏", "etPyXlDQAAkbr-NzshNW3UJuXf8977AQ");
        ipRegionMap.put("新疆", "etPyXlDQAAAt16bgPMLQNhtgUcMUqU2g");
        ipRegionMap.put("台湾", "etPyXlDQAAbg6ZetyKMyt6PQdBaQFFaQ");
        ipRegionMap.put("香港", "etPyXlDQAAoiPQTa8CmK2i_XB4tHWWow");
        ipRegionMap.put("澳门", "etPyXlDQAAIWgTHSKkEttJ2km2x_8Ljw");
        ipRegionMap.put("黑龙江-哈尔滨", "etPyXlDQAAA3LNsH8BlYo18A6P0mFc7w");
        ipRegionMap.put("黑龙江-齐齐哈尔", "etPyXlDQAAQvycYkLjl5_sqRI2b82qCg");
        ipRegionMap.put("黑龙江-鸡西", "etPyXlDQAAsxAqnP-CYFIeL22ukdA7ug");
        ipRegionMap.put("黑龙江-鹤岗", "etPyXlDQAAsJjJiye0x2eDEV5Cpj6cUA");
        ipRegionMap.put("黑龙江-双鸭山", "etPyXlDQAAfRYuWyyuvd-eyL5KJbdtaw");
        ipRegionMap.put("黑龙江-大庆", "etPyXlDQAA3Zx8nmAy4pmXfw9LX5vG1w");
        ipRegionMap.put("黑龙江-伊春", "etPyXlDQAAl_lgM44gPjdfYBTUNXtadA");
        ipRegionMap.put("黑龙江-佳木斯", "etPyXlDQAAOM7gf2fUEs6SIngtg_ALSQ");
        ipRegionMap.put("黑龙江-七台河", "etPyXlDQAAOWnlX18cwEgwrgqX2LM7ng");
        ipRegionMap.put("黑龙江-牡丹江", "etPyXlDQAALUN5jrnbIvsm1F6pSyUMeQ");
        ipRegionMap.put("黑龙江-黑河", "etPyXlDQAAykxeIRgd2iRo7HugLpfl7A");
        ipRegionMap.put("黑龙江-绥化", "etPyXlDQAAFFTvbk7EcxXDkt8Ts_JRcA");
        ipRegionMap.put("黑龙江-大兴安岭地区", "etPyXlDQAAiDH1bKxfRlfiP6ryB1l9fQ");
        ipRegionMap.put("内蒙古-呼和浩特", "etPyXlDQAA92vM28sPHQEGH3L0sPc8Sw");
        ipRegionMap.put("内蒙古-包头", "etPyXlDQAA4ACMwq5XHkyYj_zcJE7obA");
        ipRegionMap.put("内蒙古-乌海", "etPyXlDQAA19-yENSA3jgeCBfoouyWhA");
        ipRegionMap.put("内蒙古-赤峰", "etPyXlDQAAQTL_wEj0MC8fnqZrI3LmUA");
        ipRegionMap.put("内蒙古-通辽", "etPyXlDQAATUrc-OBbp_Jjmkuw1PfCuA");
        ipRegionMap.put("内蒙古-鄂尔多斯", "etPyXlDQAAcfYH8hPVBl7XkGM0NnX_9A");
        ipRegionMap.put("内蒙古-呼伦贝尔", "etPyXlDQAAvF9hgODglHnbKxrPYPwbUA");
        ipRegionMap.put("内蒙古-巴彦淖尔", "etPyXlDQAABgu7fEwWXCblcjMkn5Hbuw");
        ipRegionMap.put("内蒙古-乌兰察布", "etPyXlDQAAqemq6sK53LOpum5RSlvbdQ");
        ipRegionMap.put("内蒙古-兴安盟", "etPyXlDQAAYNM1SNejpmfQY7wOCbepvg");
        ipRegionMap.put("内蒙古-锡林郭勒盟", "etPyXlDQAAAXY0SMpBMflnrUn87LnFlg");
        ipRegionMap.put("内蒙古-阿拉善盟", "etPyXlDQAAp2hvKRCJ_pjxRLkbuSuuJg");
    }

}
