package com.wnkx.biz.channel.service.impl;
import java.util.Date;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.ConfigConstants;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.OrderVideoStatisticsDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserChannelListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserChannelStatisticsDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserDetailListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.*;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.EditFissionChannelDiscountDTO;
import com.ruoyi.system.api.domain.dto.order.PosterDTO;
import com.ruoyi.system.api.domain.dto.system.EditMemberDiscountDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BizUser;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.entity.biz.channel.BizUserChannel;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelOrder;
import com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivityAmendmentRecord;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsDetailVO;
import com.ruoyi.system.api.domain.vo.SysUserVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserChannelListVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserListVO;
import com.ruoyi.system.api.domain.vo.biz.channel.*;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelBrokeRageVO;
import com.ruoyi.system.api.domain.vo.order.OrderMemberChannelStatisticVO;
import com.wnkx.biz.business.mapper.BizUserMapper;
import com.wnkx.biz.business.service.IBusinessService;
import com.wnkx.biz.channel.mapper.DistributionChannelMapper;
import com.wnkx.biz.channel.mapper.DistributionChannelVisitFlowMapper;
import com.wnkx.biz.channel.service.*;
import com.wnkx.biz.config.ChannelDistributionProperties;
import com.wnkx.biz.config.ChannelMarketingProperties;
import com.wnkx.biz.core.ChannelCore;
import com.wnkx.biz.remote.RemoteService;
import com.wnkx.biz.utlis.TarGzImageProcessor;
import com.wnkx.biz.wechat.service.WorkWechatApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【distribution_channel(分销渠道信息表)】的数据库操作Service实现
 * @createDate 2024-09-24 17:02:50
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DistributionChannelServiceImpl extends ServiceImpl<DistributionChannelMapper, DistributionChannel>
        implements IDistributionChannelService {
    private static final Long reentrancyCount = 5L;
    private static final Long expireTime = 10L;
    private final WorkWechatApiService workWechatApiService;
    private final RedisService redisService;
    private final ChannelMarketingProperties channelMarketingProperties;
    private final IBizUserChannelService bizUserChannelService;
    private final IOrderMemberChannelService orderMemberChannelService;
    private final ChannelCore channelCore;
    private final ChannelDistributionProperties channelDistributionProperties;
    private final TarGzImageProcessor tarGzImageProcessor;
    private final IDistributionChannelOrderService distributionChannelOrderService;
    private final IBusinessService businessService;
    private final DistributionChannelActivityService distributionChannelActivityService;
    private final BizUserMapper bizUserMapper;
    private final RemoteService remoteService;
    private final DistributionChannelVisitFlowMapper distributionChannelVisitFlowMapper;
    private final DistributionChannelDiscountLogService distributionChannelDiscountLogService;
    /**
     * 预览分销渠道海报
     */
    @Override
    public void previewPoster(Long id, HttpServletResponse response) {
        DistributionChannel distributionChannel = baseMapper.selectById(id);
        Assert.notNull(distributionChannel, "分销渠道不存在，请刷新后重试~");
//        Assert.isTrue(StatusEnum.ENABLED.getCode().equals(distributionChannel.getStatus()), "分销渠道已禁用，请刷新后重试~");
        String configKey = getDiscount(distributionChannel);
        ChannelBrokeRageVO channelBrokeRageVO = JSON.parseObject(configKey, ChannelBrokeRageVO.class);
        Assert.notNull(channelBrokeRageVO, "海报异常，请联系客服~");
        String discount = "";
        Integer memberDiscountType = channelBrokeRageVO.getMemberDiscountType();
        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(memberDiscountType)){
            discount = channelBrokeRageVO.getMemberDiscount().stripTrailingZeros().toPlainString();
        }else {
            discount = channelBrokeRageVO.getMemberDiscount().divide(BigDecimal.TEN, 2, RoundingMode.DOWN).stripTrailingZeros().toPlainString();
        }

        tarGzImageProcessor.generatePosterPreview(PosterDTO.builder().posterName(distributionChannel.getPosterName())
                .channelName(distributionChannel.getChannelName())
                .discount(discount)
                .discountType(memberDiscountType)
                .activityTimeLimit("限时活动")
                .channelType(distributionChannel.getChannelType())
                .seedCode(distributionChannel.getSeedCode())
                .weChatUrl(distributionChannel.getWeChatUrl()).build(), response);
    }

    @Override
    public void previewFissionPoster(Long id, HttpServletResponse response) {
        DistributionChannel distributionChannel = baseMapper.selectById(id);
        Assert.notNull(distributionChannel, "裂变渠道不存在，请刷新后重试~");
        String configKey = getDiscount(distributionChannel);
        ChannelBrokeRageVO channelBrokeRageVO = JSON.parseObject(configKey, ChannelBrokeRageVO.class);
        Assert.notNull(channelBrokeRageVO, "海报异常，请联系客服~");
        String discount = "";
        Integer memberDiscountType = channelBrokeRageVO.getMemberDiscountType();
        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(memberDiscountType)){
            discount = channelBrokeRageVO.getMemberDiscount().stripTrailingZeros().toPlainString();
        }else {
            discount = channelBrokeRageVO.getMemberDiscount().divide(BigDecimal.TEN, 2, RoundingMode.DOWN).stripTrailingZeros().toPlainString();
        }
        //获取商家ID
        Map<Long, Integer> userBusinessVideoNumMap = getUserBusinessVideoNum(Arrays.asList(distributionChannel.getBizUserId()));
        tarGzImageProcessor.generateFissionPosterPreview(PosterDTO.builder().posterName(distributionChannel.getPosterName())
                .channelName(distributionChannel.getChannelName())
                .discount(discount)
                .discountType(memberDiscountType)
                .videoNum(StatusTypeEnum.YES.getCode().equals(channelDistributionProperties.getVideoNumRemark())? Convert.toInt(distributionChannel.getRemark()):userBusinessVideoNumMap.getOrDefault(distributionChannel.getBizUserId(), 0))
                .channelType(distributionChannel.getChannelType())
                .seedCode(distributionChannel.getSeedCode()).weChatUrl(distributionChannel.getWeChatUrl()).build(), response);
    }

    private String getDiscount(DistributionChannel distributionChannel) {
        String configKey = "";
        if (ChannelTypeEnum.FISSION.getCode().equals(distributionChannel.getChannelType())) {
            configKey = redisService.getCacheObject(Constants.SYS_CONFIG_KEY + ConfigConstants.SYS_CHANNEL_FISSION_MEMBER_DISCOUNT_V1).toString();
        } else if (ChannelTypeEnum.DISTRIBUTION.getCode().equals(distributionChannel.getChannelType())) {
            configKey = redisService.getCacheObject(Constants.SYS_CONFIG_KEY + ConfigConstants.SYS_CHANNEL_MEMBER_DISCOUNT_V1).toString();
        } else {
            throw new ServiceException("渠道类型错误");
        }
        return configKey;
    }

    /**
     * 下载全部分销渠道海报
     */
    @Override
    public void downloadAllPoster(HttpServletResponse response) {
        List<DistributionChannel> distributionChannels = baseMapper.selectNormalDistributionChannelList();
        String configKey = redisService.getCacheObject(Constants.SYS_CONFIG_KEY + ConfigConstants.SYS_CHANNEL_MEMBER_DISCOUNT_V1).toString();
        downloadPoster(response, distributionChannels, configKey);
    }

//    @Override
//    public void downloadAllPosterByIds(List<Long> channelIds, HttpServletResponse response) {
//        Assert.isTrue(CollUtil.isNotEmpty(channelIds), "请选择要下载的渠道");
//        Assert.isTrue(channelIds.size() <= 20, "渠道数量不能大于20");
//        List<DistributionChannel> distributionChannels = baseMapper.selectBatchIds(channelIds);
//        String configKey = redisService.getCacheObject(Constants.SYS_CONFIG_KEY + ConfigConstants.SYS_CHANNEL_FISSION_MEMBER_DISCOUNT).toString();
//        downloadPoster(response, distributionChannels, configKey);
//    }

    @Override
    public void downloadAllFissionPosterByIds(List<Long> channelIds, HttpServletResponse response) {
        Assert.isTrue(CollUtil.isNotEmpty(channelIds), "请选择要下载的渠道");
        Assert.isTrue(channelIds.size() <= 20, "渠道数量不能大于20");
        List<DistributionChannel> distributionChannels = baseMapper.selectBatchIds(channelIds);
        Assert.isTrue(CollUtil.isNotEmpty(distributionChannels), "请选择要下载的渠道");
        String configKey = getDiscount(distributionChannels.get(0));


        List<Long> bizUserIds = distributionChannels.stream().map(DistributionChannel::getBizUserId).collect(Collectors.toList());
        //获取商家ID
        Map<Long, Integer> userBusinessVideoNumMap = getUserBusinessVideoNum(bizUserIds);
        downloadFissionPoster(response, distributionChannels, configKey, userBusinessVideoNumMap);
    }

    private void downloadPoster(HttpServletResponse response, List<DistributionChannel> distributionChannels, String configKey) {
        if (CollUtil.isEmpty(distributionChannels)) {
            throw new ServiceException("渠道状态都已关闭，请先开启渠道状态~");
        }
        ChannelBrokeRageVO channelBrokeRageVO = JSON.parseObject(configKey, ChannelBrokeRageVO.class);
        Assert.notNull(channelBrokeRageVO, "海报异常，请联系客服~");
        String discount = "";
        Integer memberDiscountType = channelBrokeRageVO.getMemberDiscountType();
        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(memberDiscountType)){
            discount = channelBrokeRageVO.getMemberDiscount().stripTrailingZeros().toPlainString();
        }else {
            discount = channelBrokeRageVO.getMemberDiscount().divide(BigDecimal.TEN, 2, RoundingMode.DOWN).stripTrailingZeros().toPlainString();
        }
        List<PosterDTO> posterDTOS = new ArrayList<>();
        for (DistributionChannel item :distributionChannels){
            PosterDTO posterDTO = new PosterDTO();
            posterDTO.setPosterName(item.getPosterName());
            posterDTO.setChannelName(item.getChannelName());
            posterDTO.setDiscount(discount);
            posterDTO.setDiscountType(memberDiscountType);
            posterDTO.setChannelType(item.getChannelType());
            posterDTO.setActivityTimeLimit("限时活动");
            posterDTO.setSeedCode(item.getSeedCode());
            posterDTO.setWeChatUrl(item.getWeChatUrl());
            posterDTOS.add(posterDTO);
        }
        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(memberDiscountType)){
            tarGzImageProcessor.generatePosterToTarGz("会员立减"+ discount +"元", posterDTOS, response);
        }else {
            tarGzImageProcessor.generatePosterToTarGz("会员" + discount + "折", posterDTOS, response);

        }
    }

    private void downloadFissionPoster(HttpServletResponse response, List<DistributionChannel> distributionChannels, String configKey,  Map<Long, Integer> userBusinessVideoNumMap) {
        if (CollUtil.isEmpty(distributionChannels)) {
            throw new ServiceException("渠道状态都已关闭，请先开启渠道状态~");
        }
        ChannelBrokeRageVO channelBrokeRageVO = JSON.parseObject(configKey, ChannelBrokeRageVO.class);
        Assert.notNull(channelBrokeRageVO, "海报异常，请联系客服~");
        String discount = "";
        Integer memberDiscountType = channelBrokeRageVO.getMemberDiscountType();
        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(memberDiscountType)){
            discount = channelBrokeRageVO.getMemberDiscount().stripTrailingZeros().toPlainString();
        }else {
            discount = channelBrokeRageVO.getMemberDiscount().divide(BigDecimal.TEN, 2, RoundingMode.DOWN).stripTrailingZeros().toPlainString();
        }
        List<PosterDTO> posterDTOS = new ArrayList<>();
        for (DistributionChannel item :distributionChannels){
            PosterDTO posterDTO = new PosterDTO();
            posterDTO.setPosterName(item.getPosterName());
            posterDTO.setChannelName(item.getChannelName());
            posterDTO.setVideoNum(StatusTypeEnum.YES.getCode().equals(channelDistributionProperties.getVideoNumRemark())? Convert.toInt(item.getRemark()):userBusinessVideoNumMap.getOrDefault(item.getBizUserId(), 0));
            posterDTO.setDiscount(discount);
            posterDTO.setDiscountType(memberDiscountType);
            posterDTO.setChannelType(item.getChannelType());
            posterDTO.setActivityTimeLimit("限时活动");
            posterDTO.setSeedCode(item.getSeedCode());
            posterDTO.setWeChatUrl(item.getWeChatUrl());
            posterDTOS.add(posterDTO);
        }
        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(memberDiscountType)){
            tarGzImageProcessor.generateFissionPosterToTarGz("会员立减"+ discount +"元", posterDTOS, response);
        }else {
            tarGzImageProcessor.generateFissionPosterToTarGz("会员" + discount + "折", posterDTOS, response);

        }

    }

    @Override
    public List<SysUserVO> createUserList(String name) {
        return baseMapper.createUserList(name);
    }

    @Override
    public List<DistributionChannel> queryListBySeedCodes(Collection<String> seedCodes) {
        return baseMapper.queryListBySeedCodes(seedCodes);
    }

//    @Override
//    public FissionMemberDiscountVO getFissionMemberDiscount() {
//        return FissionMemberDiscountVO.builder()
//                .brokeRage(baseMapper.getFissionBrokeRage().intValue())
//                .discount(getFissionDiscount())
//                .build();
//    }

//    @Override
//    public void editFissionMemberDiscount(EditFissionMemberDiscountDTO dto) {
//        FissionMemberDiscountVO fissionMemberDiscount = getFissionMemberDiscount();
//        if (!dto.getBrokeRage().equals(fissionMemberDiscount.getBrokeRage())) {
//            baseMapper.updateFissionBrokeRage(new BigDecimal(dto.getBrokeRage()));
//        }
//        if (!dto.getDiscount().equals(fissionMemberDiscount.getDiscount())) {
//            remoteService.editFissionMemberDiscount(EditMemberDiscountDTO.builder().discount(dto.getDiscount()).build());
//        }
//    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editMemberDiscount(EditMemberDiscountDTO dto) {
        ChannelBrokeRageVO channelBrokeRageVO = channelCore.getChannelMemberDiscountV1();

        if (ObjectUtil.notEqual(dto.getMemberDiscountType(), channelBrokeRageVO.getMemberDiscountType())
                || ObjectUtil.notEqual(dto.getMemberDiscount(), channelBrokeRageVO.getMemberDiscount())
        ) {
            PromotionActivityAmendmentRecord promotionActivityAmendmentRecord = new PromotionActivityAmendmentRecord();
            promotionActivityAmendmentRecord.setActivityId(Convert.toLong(PromotionActivityTypeEnum.SEED_CODE_DISTRIBUTION_DISCOUNT.getCode()));
            promotionActivityAmendmentRecord.setType(dto.getMemberDiscountType());
            promotionActivityAmendmentRecord.setAmount(dto.getMemberDiscount());
            if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(dto.getMemberDiscountType())){
                promotionActivityAmendmentRecord.setCurrency("CNY");
            }
            promotionActivityAmendmentRecord.setSettleDiscountType(null);
            promotionActivityAmendmentRecord.setSettleDiscount(null);
            promotionActivityAmendmentRecord.setStartTime(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, "2024-01-01 00:00:00"));
            promotionActivityAmendmentRecord.setEndTime(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, "3000-01-01 00:00:00"));

            remoteService.savePromotionActivityAmendmentRecord(promotionActivityAmendmentRecord);
            remoteService.editMemberDiscount(dto);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editFissionMemberDiscountV1(EditFissionChannelDiscountDTO dto) {
        //获取裂变折扣
        ChannelBrokeRageVO channelBrokeRageVO = getFissionDiscountV1();
        if (ObjectUtil.notEqual(dto.getSettleDiscountType(), channelBrokeRageVO.getSettleDiscountType())
                || ObjectUtil.notEqual(dto.getSettleDiscount(), channelBrokeRageVO.getSettleDiscount())
        ) {
            baseMapper.updateFissionSettle(dto.getSettleDiscount(), dto.getSettleDiscountType());
        }

        if (ObjectUtil.notEqual(dto.getSettleDiscountType(), channelBrokeRageVO.getSettleDiscountType())
                || ObjectUtil.notEqual(dto.getSettleDiscount(), channelBrokeRageVO.getSettleDiscount())
                || ObjectUtil.notEqual(dto.getMemberDiscountType(), channelBrokeRageVO.getMemberDiscountType())
                || ObjectUtil.notEqual(dto.getMemberDiscount(), channelBrokeRageVO.getMemberDiscount())
                || ObjectUtil.notEqual(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, dto.getStartTime()), channelBrokeRageVO.getStartTime())
                || ObjectUtil.notEqual(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, dto.getEndTime()), channelBrokeRageVO.getEndTime())
        ) {
            PromotionActivityAmendmentRecord promotionActivityAmendmentRecord = new PromotionActivityAmendmentRecord();
            promotionActivityAmendmentRecord.setActivityId(Convert.toLong(PromotionActivityTypeEnum.SEED_CODE_FISSION_DISCOUNT.getCode()));
            promotionActivityAmendmentRecord.setType(dto.getMemberDiscountType());
            promotionActivityAmendmentRecord.setAmount(dto.getMemberDiscount());
            if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(dto.getMemberDiscountType())){
                promotionActivityAmendmentRecord.setCurrency("CNY");
            }
            promotionActivityAmendmentRecord.setSettleDiscountType(dto.getSettleDiscountType());
            promotionActivityAmendmentRecord.setSettleDiscount(dto.getSettleDiscount());
            promotionActivityAmendmentRecord.setStartTime(dto.getStartTime());
            promotionActivityAmendmentRecord.setEndTime(dto.getEndTime());

            remoteService.savePromotionActivityAmendmentRecord(promotionActivityAmendmentRecord);
            remoteService.editFissionMemberDiscountV1(dto);
        }
    }

//
//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    public Integer getFissionDiscount() {
//        Integer discount = Convert.toInt(redisService.getCacheObject(getFissionDiscountKey()));
//        if (ObjectUtil.isNull(discount)) {
//            String configKey = remoteService.getConfigKey(getFissionDiscountKey());
//            return Convert.toInt(configKey);
//        }
//
//        return discount;
//    }

    /**
     * 获取裂变折扣
     * @return
     */
    @Override
    public ChannelBrokeRageVO getFissionDiscountV1(){
        return channelCore.getFissionDiscountV1();
    }

//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    private String getFissionDiscountKey() {
//        return Constants.SYS_CONFIG_KEY + ConfigConstants.SYS_CHANNEL_FISSION_MEMBER_DISCOUNT;
//    }
//    private String getFissionDiscountKeyV1() {
//        return Constants.SYS_CONFIG_KEY + ConfigConstants.SYS_CHANNEL_FISSION_MEMBER_DISCOUNT_V1;
//    }

    /**
     * 下载分销渠道海报
     */
    @Override
    public void downloadPoster(Long id, HttpServletResponse response) {
        DistributionChannel distributionChannel = baseMapper.selectById(id);
        Assert.notNull(distributionChannel, "分销渠道不存在，请刷新后重试~");
        Assert.isTrue(StatusEnum.ENABLED.getCode().equals(distributionChannel.getStatus()), "分销渠道已禁用，请刷新后重试~");
        if (ChannelTypeEnum.FISSION.getCode().equals(distributionChannel.getChannelType())) {
            //设置 裂变拉新激活时间及状态
            if (StatusTypeEnum.NO.getCode().equals(distributionChannel.getActivationStatus())) {
                DistributionChannel updateDistributionChannel = new DistributionChannel();
                updateDistributionChannel.setId(id);
                updateDistributionChannel.setActivationTime(new Date());
                updateDistributionChannel.setActivationStatus(StatusTypeEnum.YES.getCode());
                baseMapper.updateById(updateDistributionChannel);
            }
        }


        String configKey = getDiscount(distributionChannel);
        ChannelBrokeRageVO channelBrokeRageVO = JSON.parseObject(configKey, ChannelBrokeRageVO.class);
        Assert.notNull(channelBrokeRageVO, "海报异常，请联系客服~");
        String discount = "";
        Integer memberDiscountType = channelBrokeRageVO.getMemberDiscountType();
        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(memberDiscountType)){
            discount = channelBrokeRageVO.getMemberDiscount().stripTrailingZeros().toPlainString();
        }else {
            discount = channelBrokeRageVO.getMemberDiscount().divide(BigDecimal.TEN, 2, RoundingMode.DOWN).stripTrailingZeros().toPlainString();
        }
        if (ChannelTypeEnum.FISSION.getCode().equals(distributionChannel.getChannelType())) {
            //获取商家ID
            Map<Long, Integer> userBusinessVideoNumMap = getUserBusinessVideoNum(Arrays.asList(distributionChannel.getBizUserId()));
            tarGzImageProcessor.generateFissionPosterToPic(PosterDTO.builder()
                    .posterName(distributionChannel.getPosterName())
                    .channelName(distributionChannel.getChannelName())
                    .discount(discount)
                    .discountType(memberDiscountType)
                    .videoNum(StatusTypeEnum.YES.getCode().equals(channelDistributionProperties.getVideoNumRemark())? Convert.toInt(distributionChannel.getRemark()):userBusinessVideoNumMap.getOrDefault(distributionChannel.getBizUserId(), 0))
                    .channelType(distributionChannel.getChannelType())
                    .seedCode(distributionChannel.getSeedCode())
                    .weChatUrl(distributionChannel.getWeChatUrl()).build(), response);
            return;
        }
        //渠道当前（生效）有多个活动时；且活动折扣相同，显示最新活动的海报图
        ChannelActivityDTO channelActivityDTO = distributionChannelActivityService.getLatestChannelDiscount(distributionChannel.getId());
        if (channelActivityDTO != null
                && (
                        ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(memberDiscountType) ||
                (ChannelDiscountTypeEnum.FIXED_RATIO.getCode().equals(memberDiscountType) && channelActivityDTO.getDiscount().compareTo(channelBrokeRageVO.getMemberDiscount()) <= 0 )
        )
        ) {
            //如果是固定金额 直接使用活动折扣（折扣优先） 如果是固定折扣 则取折扣小的
            long betweenDay = DateUtil.betweenDay(channelActivityDTO.getStartTime(), channelActivityDTO.getEndTime(), true) + 1;
            tarGzImageProcessor.generatePosterToPic(
                    PosterDTO.builder()
                            .posterName(distributionChannel.getPosterName())
                            .channelName(distributionChannel.getChannelName())
                            .discount(channelActivityDTO.getDiscount().divide(BigDecimal.TEN, 2, RoundingMode.DOWN).stripTrailingZeros().toPlainString())
                            .activityTimeLimit("活动仅限" + betweenDay + "天")
                            .activityTime(DateUtil.format(channelActivityDTO.getStartTime(), DatePattern.NORM_DATE_PATTERN) + " - " + DateUtil.format(channelActivityDTO.getEndTime(), DatePattern.NORM_DATE_PATTERN))
                            .seedCode(distributionChannel.getSeedCode())
                            .weChatUrl(distributionChannel.getWeChatUrl())
                            .build(),
                    response);
        } else {

            tarGzImageProcessor.generatePosterPreview(PosterDTO.builder().posterName(distributionChannel.getPosterName())
                    .channelName(distributionChannel.getChannelName())
                    .discount(discount)
                    .discountType(memberDiscountType)
                    .activityTimeLimit("限时活动")
                    .channelType(distributionChannel.getChannelType())
                    .seedCode(distributionChannel.getSeedCode())
                    .weChatUrl(distributionChannel.getWeChatUrl()).build(), response);
        }
    }

    @Override
    public void downloadManagerPoster(Long id, HttpServletResponse response) {
        DistributionChannel distributionChannel = baseMapper.selectById(id);
        Assert.notNull(distributionChannel, "分销渠道不存在，请刷新后重试~");
        Assert.isTrue(StatusEnum.ENABLED.getCode().equals(distributionChannel.getStatus()), "分销渠道已禁用，请刷新后重试~");
        String configKey = getDiscount(distributionChannel);
        ChannelBrokeRageVO channelBrokeRageVO = JSON.parseObject(configKey, ChannelBrokeRageVO.class);
        Assert.notNull(channelBrokeRageVO, "海报异常，请联系客服~");
        String discount = "";
        Integer memberDiscountType = channelBrokeRageVO.getMemberDiscountType();
        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(memberDiscountType)) {
            discount = channelBrokeRageVO.getMemberDiscount().stripTrailingZeros().toPlainString();
        } else {
            discount = channelBrokeRageVO.getMemberDiscount().divide(BigDecimal.TEN, 2, RoundingMode.DOWN).stripTrailingZeros().toPlainString();
        }

        tarGzImageProcessor.generatePosterToPic(PosterDTO.builder().posterName(distributionChannel.getPosterName())
                .channelName(distributionChannel.getChannelName())
                .discount(discount)
                .discountType(memberDiscountType)
                .activityTimeLimit("限时活动")
                .channelType(distributionChannel.getChannelType())
                .seedCode(distributionChannel.getSeedCode())
                .weChatUrl(distributionChannel.getWeChatUrl()).build(), response);
    }

    @Override
    public void downloadFissionManagerPoster(Long id, HttpServletResponse response) {
        DistributionChannel distributionChannel = baseMapper.selectById(id);
        Assert.notNull(distributionChannel, "裂变渠道不存在，请刷新后重试~");
        Assert.isTrue(ChannelTypeEnum.FISSION.getCode().equals(distributionChannel.getChannelType()), "裂变渠道不存在，请刷新后重试~");

        String configKey = getDiscount(distributionChannel);
        ChannelBrokeRageVO channelBrokeRageVO = JSON.parseObject(configKey, ChannelBrokeRageVO.class);
        Assert.notNull(channelBrokeRageVO, "海报异常，请联系客服~");
        String discount = "";
        Integer memberDiscountType = channelBrokeRageVO.getMemberDiscountType();
        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(memberDiscountType)){
            discount = channelBrokeRageVO.getMemberDiscount().stripTrailingZeros().toPlainString();
        }else {
            discount = channelBrokeRageVO.getMemberDiscount().divide(BigDecimal.TEN, 2, RoundingMode.DOWN).stripTrailingZeros().toPlainString();
        }
        //获取商家ID
        Map<Long, Integer> userBusinessVideoNumMap = getUserBusinessVideoNum(Arrays.asList(distributionChannel.getBizUserId()));


        tarGzImageProcessor.generateFissionPosterToPic(PosterDTO.builder()
                .posterName(distributionChannel.getPosterName())
                .channelName(distributionChannel.getChannelName())
                .discount(discount)
                .discountType(memberDiscountType)
                .videoNum(StatusTypeEnum.YES.getCode().equals(channelDistributionProperties.getVideoNumRemark())? Convert.toInt(distributionChannel.getRemark()):userBusinessVideoNumMap.getOrDefault(distributionChannel.getBizUserId(), 0))
                .channelType(distributionChannel.getChannelType())
                .seedCode(distributionChannel.getSeedCode())
                .weChatUrl(distributionChannel.getWeChatUrl()).build(), response);

    }

    /**
     * 获取用户对应商家 视频数量
     * @param bizUserId
     * @return map<biz_user_id, videoNum>
     */
    private Map<Long, Integer> getUserBusinessVideoNum(List<Long> bizUserId) {
        BizUserDetailListDTO bizUserListDTO = new BizUserDetailListDTO();
        bizUserListDTO.setIds(bizUserId);
        List<BizUserDetailVO> bizUserDetailList = bizUserMapper.bizUserDetailList(bizUserListDTO);
        if (CollUtil.isEmpty(bizUserDetailList)){
            return Collections.emptyMap();
        }
        Map<Long, Integer> result = new HashMap<>();

        List<Long> businessIds = new ArrayList<>();
        for (BizUserDetailVO item : bizUserDetailList){
            if (ObjectUtil.isNotNull(item.getBusinessId())){
                businessIds.add(item.getBusinessId());
            }
        }
        if (CollUtil.isEmpty(businessIds)){
            return Collections.emptyMap();
        }
        //获取商家数据
        OrderVideoStatisticsDTO orderVideoStatisticsDTO = new OrderVideoStatisticsDTO();
        orderVideoStatisticsDTO.setBusinessIds(businessIds);
        //获取商家订单统计数据
        List<OrderVideoStatisticsDetailVO> data = remoteService.orderVideoStatisticsDetail(orderVideoStatisticsDTO);

        if (CollUtil.isEmpty(data)){
            return Collections.emptyMap();
        }
        Map<Long, Integer> orderMap = data.stream().collect(Collectors.toMap(OrderVideoStatisticsDetailVO::getMerchantId, OrderVideoStatisticsDetailVO::getOrderVideoTotal));

        for (BizUserDetailVO item : bizUserDetailList) {
            if (ObjectUtil.isNotNull(item.getBusinessId())) {
                Integer orderVideoTotal = orderMap.getOrDefault(item.getBusinessId(), 0);
                result.put(item.getId(), orderVideoTotal);
            }
        }

        return result;
    }

    @Override
    public DistributionChannelDetailVO getByDistributionChannelId(Long distributionChannelId) {
        DistributionChannelDetailVO distributionChannelDetailVO = baseMapper.getByDistributionChannelId(distributionChannelId);
        if (ObjectUtil.isNotNull(distributionChannelDetailVO)) {
            distributionChannelDetailVO.setDedicatedLink(channelMarketingProperties.getOfficialWebsitePrefixURL() + distributionChannelDetailVO.getDedicatedLink());
        }
        return distributionChannelDetailVO;
    }

    @Override
    public FissionChannelVO getFissionChannelVOById(Long distributionChannelId) {
        DistributionChannelDetailVO distributionChannelDetailVO = getByDistributionChannelId(distributionChannelId);
        if (ObjectUtil.isNull(distributionChannelDetailVO)) {
            return new FissionChannelVO();
        }
        List<FissionChannelVO> fissionChannelVOS = new ArrayList<>();
        fissionChannelVOS.add(BeanUtil.copyProperties(distributionChannelDetailVO, FissionChannelVO.class));
        fillFissionChannelVO(fissionChannelVOS);
        return fissionChannelVOS.get(0);
    }

    @Override
    public List<DistributionChannelVO> queryList(DistributionChannelListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        if (ObjectUtil.isNotNull(dto.getOrderByType()) && ObjectUtil.isNotNull(ChannelOrderByTypeEnum.getValue(dto.getOrderByType()))) {
            orderByDto.setField(ChannelOrderByTypeEnum.getValue(dto.getOrderByType()), StatusTypeEnum.YES.getCode().equals(dto.getIsAsc()) ? OrderByDto.DIRECTION.ASC : OrderByDto.DIRECTION.DESC);
        }
        orderByDto.setField("create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        dto.setChannelType(ChannelTypeEnum.DISTRIBUTION.getCode());
        if (StrUtil.isNotBlank(dto.getKeyword())){
            dto.setKeyword(dto.getKeyword().toUpperCase());
        }
        List<DistributionChannelVO> distributionChannelVOS = baseMapper.queryList(dto);

        if (CollUtil.isNotEmpty(distributionChannelVOS)) {
            for (DistributionChannelVO item : distributionChannelVOS) {
                item.setMemberNum(Optional.ofNullable(item.getMemberNum()).orElse(0));
                item.setSettleAmount(Optional.ofNullable(item.getSettleAmount()).orElse(BigDecimal.ZERO));
                item.setRealSettleAmount(Optional.ofNullable(item.getRealSettleAmount()).orElse(BigDecimal.ZERO));
                item.setUnSettleAmount(Optional.ofNullable(item.getUnSettleAmount()).orElse(BigDecimal.ZERO));
                item.setRealPayAmount(Optional.ofNullable(item.getRealPayAmount()).orElse(BigDecimal.ZERO));
                item.setUniqueVisitor(Optional.ofNullable(item.getUniqueVisitor()).orElse(0L));
                item.setMemberOrderAmount(Optional.ofNullable(item.getMemberOrderAmount()).orElse(BigDecimal.ZERO));

                item.setAddWeChatNum(Optional.ofNullable(item.getAddWeChatNum()).orElse(0L));
                item.setRegisterNum(Optional.ofNullable(item.getRegisterNum()).orElse(0L));
            }
        }

        return distributionChannelVOS;
    }

    @Override
    public List<FissionChannelVO> queryFissionChannelList(DistributionChannelListDTO dto) {
        if (!loadDto(dto)){
           return List.of();
        }
        //查询登录账号数据
        OrderByDto orderByDto = new OrderByDto();
        if (ObjectUtil.isNotNull(dto.getOrderByType()) && ObjectUtil.isNotNull(ChannelOrderByTypeEnum.getValue(dto.getOrderByType()))) {
            orderByDto.setField(ChannelOrderByTypeEnum.getValue(dto.getOrderByType()), StatusTypeEnum.YES.getCode().equals(dto.getIsAsc()) ? OrderByDto.DIRECTION.ASC : OrderByDto.DIRECTION.DESC);
        }
        orderByDto.setField("create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<FissionChannelVO> fissionChannelVOS = baseMapper.queryFissionChannelList(dto);
        if (CollUtil.isEmpty(fissionChannelVOS)) {
            return List.of();
        }
        fillFissionChannelVO(fissionChannelVOS);
        return fissionChannelVOS;
    }

    @Override
    public void fillFissionChannelVO(List<FissionChannelVO> fissionChannelVOS) {
        //填充商家数据
        List<BizUserChannelListVO> bizUserChannelListVOS = bizUserMapper.channelBizUserId(BizUserChannelListDTO.builder().bizUserIds(fissionChannelVOS.stream().map(FissionChannelVO::getBizUserId).collect(Collectors.toList())).build());
        if (CollUtil.isNotEmpty(bizUserChannelListVOS)) {
            Map<Long, BizUserChannelListVO> bizUserMap = bizUserChannelListVOS.stream().collect(Collectors.toMap(BizUserChannelListVO::getId, Function.identity()));
            for (FissionChannelVO item : fissionChannelVOS) {
                item.setMemberNum(Optional.ofNullable(item.getMemberNum()).orElse(0));
                item.setSettleAmount(Optional.ofNullable(item.getSettleAmount()).orElse(BigDecimal.ZERO));
                item.setRealSettleAmount(Optional.ofNullable(item.getRealSettleAmount()).orElse(BigDecimal.ZERO));
                item.setUnSettleAmount(Optional.ofNullable(item.getUnSettleAmount()).orElse(BigDecimal.ZERO));
                item.setRealPayAmount(Optional.ofNullable(item.getRealPayAmount()).orElse(BigDecimal.ZERO));

                item.setAddWeChatNum(Optional.ofNullable(item.getAddWeChatNum()).orElse(0L));
                item.setRegisterNum(Optional.ofNullable(item.getRegisterNum()).orElse(0L));
                BizUserChannelListVO bizUserChannelListVO = bizUserMap.get(item.getBizUserId());
                if (ObjectUtil.isNull(bizUserChannelListVO)) {
                    item.setMemberStatus(MemberTypeEnum.NO_RECHARGE.getCode());
                    continue;
                }
                item.setBusinessName(bizUserChannelListVO.getBusinessName());
                item.setAccountType(bizUserChannelListVO.getAccountType());
                item.setNickName(bizUserChannelListVO.getNickName());
                item.setAccountName(bizUserChannelListVO.getName());
                item.setMemberCode(bizUserChannelListVO.getMemberCode());
                item.setMemberStatus(Optional.ofNullable(bizUserChannelListVO.getMemberStatus()).orElse(MemberTypeEnum.NO_RECHARGE.getCode()));
                if (ObjectUtil.isNull(bizUserChannelListVO.getMemberValidity())) {
                    item.setSeedCodeStatus(StatusEnum.UN_ENABLED.getCode());
                    item.setFailureTime(bizUserChannelListVO.getUnbindTime());
                } else if (DateUtils.getEndOfToday().compareTo(DateUtils.getEndOfDay(bizUserChannelListVO.getMemberValidity())) <= 0) {
                    item.setSeedCodeStatus(StatusEnum.ENABLED.getCode());
                } else {
                    item.setSeedCodeStatus(StatusEnum.UN_ENABLED.getCode());
                    item.setFailureTime(DateUtils.getEndOfDay(bizUserChannelListVO.getMemberValidity()));
                }

            }
        }
    }

    private Boolean loadDto(DistributionChannelListDTO dto) {
        dto.setChannelType(ChannelTypeEnum.FISSION.getCode());
        if (StrUtil.isNotBlank(dto.getKeyword())) {
            List<BizUserChannelListVO> bizUserChannelListVOS = bizUserMapper.channelBizUserId(BeanUtil.copyProperties(dto, BizUserChannelListDTO.class));
            if (CollUtil.isNotEmpty(bizUserChannelListVOS)) {
                dto.setChannelIds(bizUserChannelListVOS.stream().map(BizUserChannelListVO::getChannelId).collect(Collectors.toList()));
            }
            return Boolean.TRUE;
        }
        if (ObjectUtil.isNotNull(dto.getMemberStatus())){
            List<Long> bizUserIds = bizUserMapper.getBizUserIdByMemberStatus(dto.getMemberStatus());
            if (CollUtil.isEmpty(bizUserIds)){
                return Boolean.FALSE;
            }
            dto.setBizUserIds(bizUserIds.stream().distinct().filter(Objects::nonNull).collect(Collectors.toList()));
        }
        return Boolean.TRUE;
    }

    @Override
    public DistributionChannelStatisticsVO statistics(DistributionChannelListDTO dto) {
        if (StrUtil.isNotBlank(dto.getKeyword())){
            dto.setKeyword(dto.getKeyword().toUpperCase());
        }
        DistributionChannelStatisticsVO distributionChannelStatisticsVO = baseMapper.distributionChannelStatistics(dto);
        if (ObjectUtil.isNull(distributionChannelStatisticsVO)) {
            return DistributionChannelStatisticsVO.builder().channelNum(0L).addWeChatNum(0L).unSettleAmount(BigDecimal.ZERO).memberNum(0L).registerNum(0L)
                    .memberAmount(BigDecimal.ZERO)
                    .uniqueVisitor(0L).build();
        }
        distributionChannelStatisticsVO.setMemberNum(Optional.ofNullable(distributionChannelStatisticsVO.getMemberNum()).orElse(0L));
        distributionChannelStatisticsVO.setMemberAmount(Optional.ofNullable(distributionChannelStatisticsVO.getMemberAmount()).orElse(BigDecimal.ZERO));
        distributionChannelStatisticsVO.setRegisterNum(Optional.ofNullable(distributionChannelStatisticsVO.getRegisterNum()).orElse(0L));
        distributionChannelStatisticsVO.setAddWeChatNum(Optional.ofNullable(distributionChannelStatisticsVO.getAddWeChatNum()).orElse(0L));
        distributionChannelStatisticsVO.setUniqueVisitor(Optional.ofNullable(distributionChannelStatisticsVO.getUniqueVisitor()).orElse(0L));
        return distributionChannelStatisticsVO;
    }

    @Override
    public DistributionChannelStatisticsVO fissionStatistics(DistributionChannelListDTO dto) {
        if (!loadDto(dto)){
            return DistributionChannelStatisticsVO.builder().channelNum(0L).addWeChatNum(0L).unSettleAmount(BigDecimal.ZERO).memberNum(0L).registerNum(0L).build();
        }
        DistributionChannelStatisticsVO distributionChannelStatisticsVO = baseMapper.fissionChannelStatistics(dto);
        if (ObjectUtil.isNull(distributionChannelStatisticsVO)) {
            return DistributionChannelStatisticsVO.builder().channelNum(0L).addWeChatNum(0L).unSettleAmount(BigDecimal.ZERO).memberNum(0L).registerNum(0L).build();
        }
        distributionChannelStatisticsVO.setMemberNum(Optional.ofNullable(distributionChannelStatisticsVO.getMemberNum()).orElse(0L));
        distributionChannelStatisticsVO.setRegisterNum(Optional.ofNullable(distributionChannelStatisticsVO.getRegisterNum()).orElse(0L));
        distributionChannelStatisticsVO.setAddWeChatNum(Optional.ofNullable(distributionChannelStatisticsVO.getAddWeChatNum()).orElse(0L));
        return distributionChannelStatisticsVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaveDistributionChannelVO saveDistribution(DistributionChancelSaveDTO dto) {
        dto.setChannelName(dto.getChannelName().trim());
        Assert.isNull(baseMapper.getByChannelName(dto.getChannelName()), "渠道名称不能重复");
//        现有流程 标签需要添加 -种草码 种草码唯一 不需要校验渠道名称唯一
//        channelCore.checkChannelName(ChannelTypeEnum.DISTRIBUTION, dto.getChannelName());
        channelCore.saveDistributionChannel(dto.getChannelName(), BeanUtil.copyProperties(dto, DistributionChannel.class), ChannelTypeEnum.DISTRIBUTION);

        return SaveDistributionChannelVO.builder().loginStatus(WxChatLoginStatusEnum.LOGIN_SUCCESS).build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editDistribution(DistributionChancelEditDTO dto) {
        dto.setChannelName(dto.getChannelName().trim());
        DistributionChannel distributionChannel = baseMapper.selectById(dto.getId());
        Assert.notNull(distributionChannel, "分销渠道不存在，请刷新页面");
        if (!dto.getChannelName().equals(distributionChannel.getChannelName())) {
            Assert.isNull(baseMapper.getByChannelName(dto.getChannelName()), "渠道名称不能重复");
//            channelCore.checkChannelName(ChannelTypeEnum.DISTRIBUTION, dto.getChannelName());
            channelCore.editTag(ChannelTypeEnum.DISTRIBUTION, distributionChannel.getTagId(), getWeChatTag(dto.getChannelName(), distributionChannel.getSeedCode()));
        }
        DistributionChannel updateDistributionChannel = BeanUtil.copyProperties(dto, DistributionChannel.class);
        updateDistributionChannel.setUpdateBy(SecurityUtils.getUsername());
        updateDistributionChannel.setUpdateId(SecurityUtils.getUserId());
        baseMapper.updateFillNullById(updateDistributionChannel);

        if (ObjectUtil.notEqual(dto.getSettleDiscountType(), distributionChannel.getSettleDiscountType())
                || ObjectUtil.notEqual(dto.getBrokeRage(), distributionChannel.getBrokeRage())
        ) {
            EditFissionChannelDiscountDTO editFissionChannelDiscountDTO = new EditFissionChannelDiscountDTO();
            editFissionChannelDiscountDTO.setChannelId(dto.getId());
            editFissionChannelDiscountDTO.setChannelType(ChannelTypeEnum.DISTRIBUTION.getCode());
            editFissionChannelDiscountDTO.setSettleDiscountType(dto.getSettleDiscountType());
            editFissionChannelDiscountDTO.setSettleDiscount(dto.getBrokeRage());
            editFissionChannelDiscountDTO.setStartTime(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, "2024-01-01 00:00:00"));
            editFissionChannelDiscountDTO.setEndTime(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, "3000-01-01 00:00:00"));
            distributionChannelDiscountLogService.saveDistributionChannelDiscountLog(editFissionChannelDiscountDTO);
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(DistributionStatusDTO dto) {
        DistributionChannel distributionChannel = BeanUtil.copyProperties(dto, DistributionChannel.class);
        distributionChannel.setDisableTime(DateUtils.getNowDate());
        baseMapper.updateById(distributionChannel);
    }

    @Override
    public DistributionChannelStatisticsVO statistics(DistributionChancelStatisticsDTO dto) {
        Assert.notNull(dto.getChannelType(), "渠道类型不能为空");
        UserChannelStatisticsVO totalStatisticsVO = bizUserChannelService.getTotalStatisticsVO(BizUserChannelStatisticsDTO.builder()
                .channelType(dto.getChannelType())
                .registerStartTime(dto.getStartTime())
                .registerEndTime(dto.getEndTime()).build());
        Long channelNum = baseMapper.distributionChannelNum(dto);
        Long visit = distributionChannelVisitFlowMapper.getSumVisit(dto.getChannelType());
        OrderMemberChannelStatisticVO memberChannelStatisticTotal =Optional.ofNullable(baseMapper.getMemberSeedRecordStatisticTotal(dto))
                .orElse(OrderMemberChannelStatisticVO.builder().memberNum(0).unSettleAmount(BigDecimal.ZERO).build());
        return DistributionChannelStatisticsVO.builder()
                .channelNum(channelNum)
                .registerNum(Optional.ofNullable(totalStatisticsVO).orElse(UserChannelStatisticsVO.builder().registerNum(0L).build()).getRegisterNum())
                .memberNum(Convert.toLong(memberChannelStatisticTotal.getMemberNum()))
                .uniqueVisitor(visit)
                .unSettleAmount(Optional.ofNullable(memberChannelStatisticTotal.getUnSettleAmount()).orElse(BigDecimal.ZERO)).build();
    }

    @Override
    public DistributionChannelVO getDistributionChannelBySeedCode(String seedCode) {
        return baseMapper.getDistributionChannelBySeedCode(seedCode);
    }

    @Override
    public DistributionChannel getDistributionChannelEntityBySeedCode(String seedCode) {
        return baseMapper.getBySeedCode(seedCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDistributionChannelOrder(DistributionChannelOrderDTO dto) {
        Business business = businessService.getById(dto.getBusinessId());
        if (ObjectUtil.isNull(business) || StrUtil.isBlank(business.getSeedCode())) {
            return;
        }
        DistributionChannelOrder distributionChannelOrder = BeanUtil.copyProperties(dto, DistributionChannelOrder.class);
        distributionChannelOrderService.save(distributionChannelOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchDistributionChannelOrder(DistributionChannelOrderBatchDTO dto) {
        List<DistributionChannelOrderDTO> list = dto.getList();
        if (StatusTypeEnum.YES.getCode().equals(dto.getIsAll())) {
            //删除所有
            distributionChannelOrderService.remove(new LambdaQueryWrapper<DistributionChannelOrder>().isNotNull(DistributionChannelOrder::getId));
            //重新加入
            List<DistributionChannelOrder> distributionChannelOrderList = BeanUtil.copyToList(list, DistributionChannelOrder.class);
            List<DistributionChannelOrder> result = loadSeedCode(distributionChannelOrderList);
            if (CollUtil.isNotEmpty(result)) {
                distributionChannelOrderService.saveBatch(result);
            }

        } else {
            //保存今天的数据
            List<DistributionChannelOrder> distributionChannelOrders = distributionChannelOrderService.getTodayDistributionChannelOrder();
            if (CollUtil.isEmpty(distributionChannelOrders)) {
                //全保存
                List<DistributionChannelOrder> distributionChannelOrderList = BeanUtil.copyToList(list, DistributionChannelOrder.class);
                List<DistributionChannelOrder> result = loadSeedCode(distributionChannelOrderList);
                if (CollUtil.isNotEmpty(result)) {
                    distributionChannelOrderService.saveBatch(result);
                }
            } else {
                //部分保存
                List<String> orderNums = distributionChannelOrders.stream().map(DistributionChannelOrder::getOrderNum).collect(Collectors.toList());
                List<DistributionChannelOrder> distributionChannelOrderList = new ArrayList<>();
                for (DistributionChannelOrderDTO item : list) {
                    if (orderNums.contains(item.getOrderNum())) {
                        continue;
                    }
                    DistributionChannelOrder distributionChannelOrder = BeanUtil.copyProperties(item, DistributionChannelOrder.class);
                    distributionChannelOrderList.add(distributionChannelOrder);
                }
                if (CollUtil.isNotEmpty(distributionChannelOrderList)) {
                    loadSeedCode(distributionChannelOrderList);
                    List<DistributionChannelOrder> result = loadSeedCode(distributionChannelOrderList);
                    if (CollUtil.isNotEmpty(result)) {
                        distributionChannelOrderService.saveBatch(result);
                    }
                }
            }
        }
    }

    private List<DistributionChannelOrder> loadSeedCode(List<DistributionChannelOrder> distributionChannelOrderList) {
        List<Business> businesses = businessService.listByIds(distributionChannelOrderList.stream().map(DistributionChannelOrder::getBusinessId).collect(Collectors.toList()));
        Map<Long, Business> businessMap = new HashMap<>();
        List<DistributionChannelOrder> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(businesses)) {
            Map<Long, Business> collect = businesses.stream().collect(Collectors.toMap(Business::getId, item -> item));
            businessMap.putAll(collect);
        }
        for (DistributionChannelOrder item : distributionChannelOrderList) {
            Business business = businessMap.get(item.getBusinessId());
            if (ObjectUtil.isNull(business) || StrUtil.isBlank(business.getSeedCode())) {
                continue;
            }
            item.setSeedCode(business.getSeedCode());
            result.add(item);
        }

        if (CollUtil.isEmpty(result)) {
            return Collections.emptyList();
        }

        return result;
    }

    @Override
    public String getWeChatUrlByLinkCode(String linkCode) {
        DistributionChannel distributionChannel = baseMapper.getByLinkCode(linkCode);
        if (ObjectUtil.isNull(distributionChannel)) {
            return "";
        }
        return distributionChannel.getWeChatUrl();
    }

    @Override
    public DistributionChannel getByLinkCode(String linkCode) {
        return baseMapper.getByLinkCode(linkCode);
    }

    @Override
    public List<ChannelInviteVO> inviteList(InviteListDTO dto) {
        PageUtils.startPage();
        List<ChannelInviteVO> channelInviteVOS = bizUserChannelService.inviteList(dto);
        if (CollUtil.isNotEmpty(channelInviteVOS)) {
            for (ChannelInviteVO item : channelInviteVOS) {
                if (ObjectUtil.isNotNull(item.getMemberType()) && ObjectUtil.isNotNull(item.getMemberChannelId()) && item.getMemberChannelId().compareTo(dto.getChannelId()) == 0) {
                    item.setIsChannelMember(StatusTypeEnum.YES.getCode());
                } else {
                    item.setIsChannelMember(StatusTypeEnum.NO.getCode());
                }

                /**
                 * sql中使用 order_member_channel为主表查询到的数据 需要做处理 标记为非该渠道邀请
                 */
                if (ObjectUtil.isNotNull(item.getRegisterChannelId()) && item.getRegisterChannelId().compareTo(dto.getChannelId()) == 0) {
                    item.setIsRegisterChannel(StatusTypeEnum.YES.getCode());
                } else {
                    item.setIsRegisterChannel(StatusTypeEnum.NO.getCode());
                }
                if (ObjectUtil.isNotNull(item.getWechatChannelId()) && item.getWechatChannelId().compareTo(dto.getChannelId()) == 0) {
                    item.setIsWechatChannel(StatusTypeEnum.YES.getCode());
                } else {
                    item.setIsWechatChannel(StatusTypeEnum.NO.getCode());
                }
            }
        }

        return channelInviteVOS;
    }

    @Override
    public ChannelPrivacyInfoVO privacyInfo(Long id) {
        DistributionChannel distributionChannel = baseMapper.selectById(id);
        Assert.notNull(distributionChannel, "渠道数据不存在，请刷新页面~");
        ChannelPrivacyInfoVO channelPrivacyInfoVO = BeanUtil.copyProperties(distributionChannel, ChannelPrivacyInfoVO.class);
        channelPrivacyInfoVO.setChannelUrl(channelDistributionProperties.getChannelUrl());
        return channelPrivacyInfoVO;
    }

    @Override
    public List<ChannelInviteVO> channelRegisterList() {
        PageUtils.startPage();
        Long userId = SecurityUtils.getUserId();
        if (ObjectUtil.isNull(userId)) {
            return Collections.emptyList();
        }
        return baseMapper.getChannelRegisterList(userId);
    }

    @Override
    public DistributionChannelInfoVO channelInfo() {
        DistributionChannel distributionChannel = baseMapper.selectById(SecurityUtils.getUserId());
        if (ObjectUtil.isNull(distributionChannel)) {
            throw new ServiceException("获取渠道数据失败");
        }
        //获取收益
        Map<Long, OrderMemberChannelStatisticVO> memberChannelStatistic = orderMemberChannelService.getMemberChannelStatistic(Arrays.asList(distributionChannel.getId()));
        OrderMemberChannelStatisticVO orderMemberChannelStatisticVO = Optional.ofNullable(memberChannelStatistic.get(distributionChannel.getId()))
                .orElse(OrderMemberChannelStatisticVO.builder().unSettleAmount(BigDecimal.ZERO).settleAmount(BigDecimal.ZERO).build());
        DistributionChannelInfoVO distributionChannelInfoVO = BeanUtil.copyProperties(distributionChannel, DistributionChannelInfoVO.class);
        distributionChannelInfoVO.setSettleAmount(orderMemberChannelStatisticVO.getSettleAmount());
        distributionChannelInfoVO.setUnSettleAmount(orderMemberChannelStatisticVO.getUnSettleAmount());
        distributionChannelInfoVO.setTotalAmount(orderMemberChannelStatisticVO.getSettleAmount().add(orderMemberChannelStatisticVO.getUnSettleAmount()));
        distributionChannelInfoVO.setDedicatedLink(channelMarketingProperties.getBusinessWebsitePrefixURL() + distributionChannel.getDedicatedLinkCode());

        if (ChannelTypeEnum.DISTRIBUTION.getCode().equals(distributionChannel.getChannelType())) {
            distributionChannelInfoVO.setChannelAccountType(ChanelAccountTypeEnum.NORMAL.getCode());
        } else {
            List<BizUserChannelListVO> bizUserChannelListVOS = bizUserMapper.channelBizUserId(BizUserChannelListDTO.builder().bizUserIds(Arrays.asList(distributionChannel.getBizUserId())).build());
            if (CollUtil.isEmpty(bizUserChannelListVOS)) {
                distributionChannelInfoVO.setChannelAccountType(ChanelAccountTypeEnum.UNBIND.getCode());
            } else if (ObjectUtil.isNull(bizUserChannelListVOS.get(0).getMemberValidity())) {
                distributionChannelInfoVO.setChannelAccountType(ChanelAccountTypeEnum.UNBIND.getCode());
            } else if (DateUtils.getEndOfToday().compareTo(DateUtils.getEndOfDay(bizUserChannelListVOS.get(0).getMemberValidity())) > 0) {
                distributionChannelInfoVO.setChannelAccountType(ChanelAccountTypeEnum.EXPIRED.getCode());
            } else {
                distributionChannelInfoVO.setChannelAccountType(ChanelAccountTypeEnum.NORMAL.getCode());
            }
            if (CollUtil.isNotEmpty(bizUserChannelListVOS)){
                distributionChannelInfoVO.setMemberStatus(bizUserChannelListVOS.get(0).getMemberStatus());
                distributionChannelInfoVO.setNickName(bizUserChannelListVOS.get(0).getNickName());
            }
        }
        return distributionChannelInfoVO;
    }


    /**
     * 获取企业微信标签
     *
     * @param channelName 渠道名称
     * @param seedCode    种草码
     * @return
     */
    private String getWeChatTag(String channelName, String seedCode) {
        Assert.notNull(channelName, "渠道名称不能为空");
        Assert.notNull(seedCode, "种草码不能为空");
        return channelName + "-" + seedCode;
    }

    @Override
    public List<BizUserListVO> inviteRegisterList() {
        PageUtils.startPage();
        // 直接查询用户信息，保持分页完整性
        return bizUserChannelService.inviteRegisterListWithUserInfo(SecurityUtils.getUserId());
    }

}




