package com.wnkx.biz.model.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.ModelPerson;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedOustModelCountInfo;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 模特对接人员Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@Mapper
public interface ModelPersonMapper extends SuperMapper<ModelPerson>
{
    /**
     * 通过模特id获取模特关联运营
     */
    default List<ModelPerson> selectListByModelId(Collection<Long> modelId) {
        return this.selectList(new LambdaQueryWrapper<ModelPerson>()
                .in(ModelPerson::getModelId, modelId));
    }

    /**
     * 获取模特对接运营id和模特开发人ID
     *
     * @return 运营id
     */
    Set<Long> getUserId();

    /**
     * 通过模特id删除关联人员表
     */
    default void removeByModelId(List<Long> modelId) {
        this.delete(new LambdaQueryWrapper<ModelPerson>()
                .in(ModelPerson::getModelId, modelId)
        );
    }

    /**
     * 通过运营id获取模特
     */
    default List<ModelPerson> selectListByUserIds(Collection<Long> userIds) {
        return this.selectList(new LambdaQueryWrapper<ModelPerson>()
                .in(ModelPerson::getUserId, userIds)
        );
    }

    /**
     * 获取英文部客服 新增模特数
     */
    List<CustomerServiceAddedOustModelCountInfo> getEnglishCustomerServiceAddedModelCounts(@Param("date") String date);
}
