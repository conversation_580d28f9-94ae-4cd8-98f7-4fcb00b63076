package com.wnkx.biz.wechat.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.config.WorkWeChatConfig;
import com.ruoyi.system.api.domain.dto.biz.wechat.ExternalUserInfoDTO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelVO;
import com.wnkx.biz.business.service.IBusinessAccountService;
import com.wnkx.biz.config.ChannelMarketingProperties;
import com.wnkx.biz.wechat.service.WorkWechatApiService;
import com.wnkx.biz.wechat.service.WorkWechatRemarkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 微信客户备注服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WorkWechatRemarkServiceImpl implements WorkWechatRemarkService {
    private final ChannelMarketingProperties channelMarketingProperties;
    private final WorkWeChatConfig workWeChatConfig;
    private final WorkWechatApiService workWechatApiService;
    private final IBusinessAccountService businessAccountService;

    /**
     * 备注逻辑变更
     * 1. 用户初始注册时 - > 尝试更新所有客服下标签  （渠道信息)
     * 2. 用户已注册，未成为会员，再次加好友 - > 尝试更新单个客服下标签 （渠道信息）
     * 3. 用户已注册，成为会员，再次加好友 - > 尝试更新单个客服下标签  （会员标签）
     * 4. 用户已注册，会员过期/子账号，再次加好友 - > 尝试更新单个客服下标签  （会员标签）
     */
    @Override
    public void changeCustomerRemark(ExternalUserInfoDTO externalUserInfo, ChannelVO channelByWeChatState, String connectUserName) {
        if (channelMarketingProperties.getRemarkEnable().equals(Boolean.FALSE.toString())) {
            return;
        }
        try {
            if (CollUtil.isEmpty(externalUserInfo.getFollow_user())) {
                return;
            }
            //
            BusinessAccountVO businessAccountVO = businessAccountService.getBusinessAccount(externalUserInfo.getExternalContact().getUnionid());
            if (businessAccountVO == null || StatusTypeEnum.YES.getCode().equals(businessAccountVO.getIsMock())) {
//            用户未加入商家情况下需要走新用户打标流程，否则走商家编码打标流程
//            注意，配置时需要区分环境+对接人 不然会出现乱发的情况
                for (ExternalUserInfoDTO.FollowUser item : externalUserInfo.getFollow_user()) {
                    if (StrUtil.isNotBlank(item.getState()) && workWeChatConfig.getContactUser().contains(item.getUserid())) {
//                    渠道注册/官网/子账号
                        if (channelByWeChatState.getChannelType() == ChannelTypeEnum.BUSINESS) {
//                        子账号
                            log.info("子账号注册：{}", item);
                            workWechatApiService.changeExternalContactRemark(item.getUserid(),
                                    externalUserInfo.getExternalContact().getExternalUserid(),
                                    channelByWeChatState.getChannelName());

                        }
                        if (channelByWeChatState.getChannelType() == ChannelTypeEnum.WEBSITE) {
//                        官网
                            log.info("自然流注册：{}", item);
                            if (channelMarketingProperties.getDebugger().equals(Boolean.TRUE.toString())) {
                                log.info("官网备注{}", channelByWeChatState.getChannelName());
                                return;
                            }
                            workWechatApiService.changeExternalContactRemark(item.getUserid(),
                                    externalUserInfo.getExternalContact().getExternalUserid(),
                                    channelByWeChatState.getChannelName());
                        }
                        if (channelByWeChatState.getChannelType() == ChannelTypeEnum.DISTRIBUTION ||
                                channelByWeChatState.getChannelType() == ChannelTypeEnum.MARKETING ||
                                channelByWeChatState.getChannelType() == ChannelTypeEnum.FISSION ||
                                channelByWeChatState.getChannelType() == ChannelTypeEnum.VIP
                        ) {
//                        渠道
                            workWechatApiService.changeExternalContactRemark(item.getUserid(),
                                    externalUserInfo.getExternalContact().getExternalUserid(),
                                    channelByWeChatState.getChannelName());
                        }
                    }
                }

            } else {
                log.info("已注册过用户再次添加客服{}", businessAccountVO);
                if (StatusTypeEnum.NO.getCode().equals(businessAccountVO.getIsMock())) {
                    String memberCode = businessAccountVO.getBusinessVO().getMemberCode();
                    UpdateWechatRemarkType updateWechatRemarkType =
                            StatusTypeEnum.YES.getCode().equals(businessAccountVO.getBusinessVO().getMemberType()) ? UpdateWechatRemarkType.VIP :
                                    UpdateWechatRemarkType.EXPIRE;
                    BizUserAccountTypeEnum bizUserAccountTypeEnum =
                            StatusTypeEnum.YES.getCode().equals(businessAccountVO.getIsOwnerAccount()) ? BizUserAccountTypeEnum.OWNER_ACCOUNT :
                                    BizUserAccountTypeEnum.ACCOUNT;
                    updateAccountRemark(externalUserInfo.getExternalContact().getExternalUserid(), memberCode, updateWechatRemarkType, bizUserAccountTypeEnum, connectUserName);
                }

            }
        } catch (Exception e) {
            log.error("更改备注失败：{},{}", e, externalUserInfo.toString());
        }
    }

    @Override
    public void updateAccountRemark(String accountExternalUserId, String memberCode, UpdateWechatRemarkType type, BizUserAccountTypeEnum accountType) {
        updateAccountRemark(accountExternalUserId, memberCode, type, accountType, null);
    }


    private void updateAccountRemark(String accountExternalUserId, String memberCode, UpdateWechatRemarkType type, BizUserAccountTypeEnum accountType, String connectUserName) {
        if (channelMarketingProperties.getRemarkEnable().equals(Boolean.FALSE.toString())) {
            return;
        }
        if (type == UpdateWechatRemarkType.EXPIRE) {
            String expireRemark = String.format("【已到期】%s-%s", memberCode, accountType.getLabel());
            if (StringUtils.isBlank(connectUserName)) {
                batchUpdateRemark(accountExternalUserId, expireRemark);
            } else {
                updateExternalContactRemark(connectUserName, accountExternalUserId, expireRemark);
            }
        } else if (type == UpdateWechatRemarkType.VIP) {
            batchUpdateRemark(accountExternalUserId, String.format("%s-%s", memberCode, accountType.getLabel()));
        } else if (type == UpdateWechatRemarkType.UNBIND_BUSINESS) {
            String unbindBusinessRemark = "";
            if (StringUtils.isBlank(connectUserName)) {
                batchUpdateRemark(accountExternalUserId, unbindBusinessRemark);
            } else {
                updateExternalContactRemark(connectUserName, accountExternalUserId, unbindBusinessRemark);
            }
        } else if (type == UpdateWechatRemarkType.BIND_BUSINESS) {
            String bindBusinessRemark = String.format("%s-%s", memberCode, accountType.getLabel());
            if (StringUtils.isBlank(connectUserName)) {
                batchUpdateRemark(accountExternalUserId, bindBusinessRemark);
            } else {
                updateExternalContactRemark(connectUserName, accountExternalUserId, bindBusinessRemark);
            }
        }
    }

    @Override
    public void batchUpdateRemark(String accountExternalUserId, String remark) {
        for (String connectUserName : workWeChatConfig.getContactUser()) {
            updateExternalContactRemark(connectUserName, accountExternalUserId, remark);
        }
    }

    private void updateExternalContactRemark(String connectUserName, String accountExternalUserId, String remark) {
        try {
            workWechatApiService.changeExternalContactRemark(connectUserName, accountExternalUserId, remark);
        } catch (Exception e) {
            if (e.getMessage().contains(WorkWechatErrorCodeEnum.NOT_EXTERNAL_CONTACT.getCode())) {
                log.info("账号状态到期更新备注失败{},外部联系人Id{},用户Id{}", e.getMessage(), accountExternalUserId, connectUserName);
            }
            log.error("账号状态到期更新备注失败{}", e.getMessage());
        }
    }
}
