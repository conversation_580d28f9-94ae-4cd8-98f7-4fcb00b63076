package com.wnkx.biz.wechat.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatContactUserConfigInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【we_chat_contact_user_config_info(对外联系人详细配置表)】的数据库操作Mapper
* @createDate 2025-03-05 13:46:50
* @Entity com.ruoyi.system.api.domain.entity.biz.wechat.WeChatContactUserConfigInfo
*/
public interface WeChatContactUserConfigInfoMapper extends BaseMapper<WeChatContactUserConfigInfo> {

    default List<WeChatContactUserConfigInfo> getConfigUrlByContactUserId(String contactUserId){
        return selectList(new LambdaQueryWrapper<WeChatContactUserConfigInfo>()
                .eq(WeChatContactUserConfigInfo::getConfigId, contactUserId)
        );
    }
}




