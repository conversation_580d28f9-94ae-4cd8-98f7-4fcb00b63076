package com.wnkx.biz.model.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.biz.model.ModelAccount;

/**
* <AUTHOR>
* @description 针对表【model_account(模特账号表)】的数据库操作Service
* @createDate 2024-07-05 16:17:18
*/
public interface IModelAccountService extends IService<ModelAccount> {

    /**
     * 根据模特Id创建模特账号
     * @param modelId
     */
    void createModelAccount(Long modelId);

    /**
     * 更新最新登录时间
     *
     * @param account
     */
    void updateLoginTime(String account);

    /**
     * 通过模特id获取模特账号
     */
    ModelAccount getOneByModelId(Long modelId);
}
