package com.wnkx.biz.channel.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.RandomCodeUtil;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.config.WechatConfig;
import com.ruoyi.system.api.domain.dto.biz.channel.InviteListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.MarketingChannelDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.MarketingChannelListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.VisitMarketingChannelDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.MarketingChannel;
import com.ruoyi.system.api.domain.vo.SysUserVO;
import com.ruoyi.system.api.domain.vo.biz.channel.*;
import com.wnkx.biz.channel.mapper.DistributionChannelMapper;
import com.wnkx.biz.channel.mapper.MarketingChannelMapper;
import com.wnkx.biz.channel.service.IMarketingChannelService;
import com.wnkx.biz.config.ChannelMarketingProperties;
import com.wnkx.biz.core.ChannelCore;
import com.wnkx.biz.wechat.service.WorkWechatApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【marketing_channel(市场渠道信息表)】的数据库操作Service实现
 * @createDate 2024-09-24 17:03:37
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MarketingChannelServiceImpl extends ServiceImpl<MarketingChannelMapper, MarketingChannel> implements IMarketingChannelService {

    private static final String DEDICATED_LINK_CODE_PREFIX = "SC";
    private final WorkWechatApiService workWechatApiService;
    private final ChannelMarketingProperties channelMarketingProperties;
    private final ChannelCore channelCore;
    private final WechatConfig wechatConfig;
    private final RedisService redisService;
    private final DistributionChannelMapper distributionChannelMapper;


    /**
     * 导出市场渠道列表
     */
    @Override
    public void exportMarketingChannelList(MarketingChannelListDTO listDTO, HttpServletResponse response) {
        final List<MarketingChannelListVO> marketingChannelListVOS = baseMapper.marketingChannelListByCondition(listDTO);

        List<MarketingChannelListExportVO> collect = marketingChannelListVOS.stream().map(item -> {
            MarketingChannelListExportVO marketingChannelListExportVO = BeanUtil.copyProperties(item, MarketingChannelListExportVO.class);
            if (LandingFormEnum.ENTERPRISE_MICRO_CUSTOMER_SERVICE.getCode().equals(marketingChannelListExportVO.getLandingForm())) {
                marketingChannelListExportVO.setDedicatedLinkCode(channelMarketingProperties.getEnterpriseMicroCustomerServicePrefixURL() + marketingChannelListExportVO.getDedicatedLinkCode());
            } else {
                marketingChannelListExportVO.setDedicatedLinkCode(channelMarketingProperties.getOfficialWebsitePrefixURL() + marketingChannelListExportVO.getDedicatedLinkCode());
            }
            return marketingChannelListExportVO;
        }).collect(Collectors.toList());

        ExcelUtil<MarketingChannelListExportVO> util = new ExcelUtil<>(MarketingChannelListExportVO.class);

        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "市场渠道列表导出");

        util.exportExcel(response, collect, "市场渠道列表导出");
    }

    @Override
    public MarketingChannelStatisticsVO marketingChannelStatistics(MarketingChannelListDTO listDTO) {
        MarketingChannelStatisticsVO marketingChannelStatisticsVO = baseMapper.marketingChannelStatistics(listDTO);
        if (ObjectUtil.isNull(marketingChannelStatisticsVO)) {
            return MarketingChannelStatisticsVO.builder().memberNum(0L).realPayAmount(BigDecimal.ZERO).uniqueVisitor(0).newRegistrationCount(0).wechatCount(0).build();
        }
        marketingChannelStatisticsVO.setMemberNum(Optional.ofNullable(marketingChannelStatisticsVO.getMemberNum()).orElse(0L));
        marketingChannelStatisticsVO.setWechatCount(Optional.ofNullable(marketingChannelStatisticsVO.getWechatCount()).orElse(0));
        marketingChannelStatisticsVO.setNewRegistrationCount(Optional.ofNullable(marketingChannelStatisticsVO.getNewRegistrationCount()).orElse(0));
        marketingChannelStatisticsVO.setUniqueVisitor(Optional.ofNullable(marketingChannelStatisticsVO.getUniqueVisitor()).orElse(0));
        marketingChannelStatisticsVO.setRealPayAmount(Optional.ofNullable(marketingChannelStatisticsVO.getRealPayAmount()).orElse(BigDecimal.ZERO));
        return marketingChannelStatisticsVO;
    }

    @Override
    public List<SysUserVO> createUserList(String name) {
        return baseMapper.createUserList(name);
    }

    @Override
    public List<ChannelInviteVO> getChannelInviteList(InviteListDTO dto) {
        PageUtils.startPage();
        List<ChannelInviteVO> channelInviteList = baseMapper.getChannelInviteList(dto);
        if (CollUtil.isNotEmpty(channelInviteList)) {
            for (ChannelInviteVO item : channelInviteList) {
                if (ObjectUtil.isNotNull(item.getMemberType()) && ObjectUtil.isNotNull(item.getMemberChannelId()) && item.getMemberChannelId().compareTo(dto.getChannelId()) == 0) {
                    item.setIsChannelMember(StatusTypeEnum.YES.getCode());
                } else {
                    item.setIsChannelMember(StatusTypeEnum.NO.getCode());
                }

                /**
                 * sql中使用 order_member_channel为主表查询到的数据 需要做处理 标记为非该渠道邀请
                 */
                if (ObjectUtil.isNotNull(item.getRegisterChannelId()) && item.getRegisterChannelId().compareTo(dto.getChannelId()) == 0) {
                    item.setIsRegisterChannel(StatusTypeEnum.YES.getCode());
                } else {
                    item.setIsRegisterChannel(StatusTypeEnum.NO.getCode());
                }
            }
        }
        return channelInviteList;
    }

    @Override
    public ChannelInviteStatisticsVO getChannelInviteStatistics(InviteListDTO dto) {
        ChannelInviteStatisticsVO channelInviteStatistics = Optional.ofNullable(baseMapper.getChannelInviteStatistics(dto)).orElse(new ChannelInviteStatisticsVO());
        channelInviteStatistics.setMemberNum(Optional.ofNullable(channelInviteStatistics.getMemberNum()).orElse(0));
        channelInviteStatistics.setRegisterNum(Optional.ofNullable(channelInviteStatistics.getRegisterNum()).orElse(0));
        channelInviteStatistics.setRealPayAmount(Optional.ofNullable(channelInviteStatistics.getRealPayAmount()).orElse(BigDecimal.ZERO));
        return channelInviteStatistics;
    }

    @Override
    public List<ChannelInviteExportVO> exportChannelInviteList(InviteListDTO dto) {
        List<ChannelInviteVO> channelInviteList = baseMapper.getChannelInviteList(dto);
        if (CollUtil.isEmpty(channelInviteList)) {
            return Collections.emptyList();
        }
        List<ChannelInviteExportVO> channelInviteExportVOS = new ArrayList<>();
        for (ChannelInviteVO item : channelInviteList) {
            ChannelInviteExportVO vo = BeanUtil.copyProperties(item, ChannelInviteExportVO.class);
            if (BizUserAccountTypeEnum.COMMON.getCode().equals(item.getAccountType())) {
                vo.setMemberType(null);
                vo.setCreateTime(null);
                vo.setRealPayAmount(null);
            } else if (!(ObjectUtil.isNotNull(item.getMemberType()) && ObjectUtil.isNotNull(item.getMemberChannelId()) && item.getMemberChannelId().compareTo(dto.getChannelId()) == 0)) {
                vo.setMemberType(3);
                vo.setCreateTime(null);
                vo.setRealPayAmount(null);
            }
            channelInviteExportVOS.add(vo);
        }

        return channelInviteExportVOS;
    }

    @Override
    public void visit(VisitMarketingChannelDTO dto) {
        if (StrUtil.isBlank(dto.getFingerprint()) || StrUtil.isBlank(dto.getDedicatedLinkCode())) {
            return;
        }
        redisService.setZSet(channelCore.getVisitKey(dto.getDedicatedLinkCode(), DateUtils.parseDateToStr(DateUtils.YYYYMMDD, new Date())), dto.getFingerprint(), System.currentTimeMillis());
        //设置48小时过期
        redisService.expire(channelCore.getVisitKey(dto.getDedicatedLinkCode(), DateUtils.parseDateToStr(DateUtils.YYYYMMDD, new Date())), 48, TimeUnit.HOURS);
    }

    @Override
    public void refreshUniqueVisitor() {
        baseMapper.refreshUniqueVisitor();
    }

    /**
     * 通过专属链接code获取专属企微二维码
     */
    @Override
    public QrcodeByDedicatedLinkCodeVO getQrcodeByDedicatedLinkCode(String dedicatedLinkCode) {
        if (StringUtils.isBlank(dedicatedLinkCode)) {
            return getSysDefaultQrcode(WechatContactUserTagEnum.SYS_WEBSITE_1.getShortTag());
        }
        if (dedicatedLinkCode.length() < 4) {
            return getSysDefaultQrcode(dedicatedLinkCode);
        }
        if (dedicatedLinkCode.startsWith(ChannelTypeEnum.DISTRIBUTION.getTagLabel()) || dedicatedLinkCode.startsWith(ChannelTypeEnum.FISSION.getTagLabel())) {
            String qrcodeByDedicatedLinkCode = distributionChannelMapper.getQrcodeByDedicatedLinkCode(dedicatedLinkCode);
            if (StringUtils.isNotBlank(qrcodeByDedicatedLinkCode)) {
                return QrcodeByDedicatedLinkCodeVO.builder().type(2).url(qrcodeByDedicatedLinkCode).build();
            }
            return QrcodeByDedicatedLinkCodeVO.builder().type(wechatConfig.getType()).url(wechatConfig.getUrl()).build();
        }
        MarketingChannel marketingChannel = baseMapper.getQrcodeByDedicatedLinkCode(dedicatedLinkCode);
        if (ObjectUtil.isNull(marketingChannel)) {
            String qrcodeByDedicatedLinkCode = distributionChannelMapper.getQrcodeByDedicatedLinkCode(dedicatedLinkCode);
            if (StringUtils.isNotBlank(qrcodeByDedicatedLinkCode)) {
                return QrcodeByDedicatedLinkCodeVO.builder().type(2).url(qrcodeByDedicatedLinkCode).build();
            }
            return QrcodeByDedicatedLinkCodeVO.builder().type(wechatConfig.getType()).url(wechatConfig.getUrl()).build();
        }
        return QrcodeByDedicatedLinkCodeVO.builder().type(2).url(marketingChannel.getWeChatUrl()).build();
    }

    /**
     * 下载物料
     */
    @Override
    public DownloadMaterialVO downloadMaterial(Long id) {
        MarketingChannel marketingChannel = baseMapper.selectById(id);
        Assert.notNull(marketingChannel, "渠道信息不存在，请刷新后重试~");

        DownloadMaterialVO downloadMaterialVO = BeanUtil.copyProperties(marketingChannel, DownloadMaterialVO.class);

        if (LandingFormEnum.OFFICIAL_WEBSITE.getCode().equals(downloadMaterialVO.getLandingForm())) {
            downloadMaterialVO.setDedicatedLinkCode(channelMarketingProperties.getOfficialWebsitePrefixURL() + downloadMaterialVO.getDedicatedLinkCode());
            downloadMaterialVO.setWechatCode(marketingChannel.getWeChatUrl());
        } else {
            downloadMaterialVO.setDedicatedLinkCode(marketingChannel.getWeChatUrl());
        }

        return downloadMaterialVO;
    }

    /**
     * 编辑市场渠道
     */
    @Override
    public void editMarketingChannel(MarketingChannelDTO dto) {
        dto.setMarketingChannelName(dto.getMarketingChannelName().trim());
        Assert.isFalse(baseMapper.checkMarketingChannelNameExist(dto.getId(), dto.getMarketingChannelName()), "投流名称：{} 已存在，请修改后重试！", dto.getMarketingChannelName());
        MarketingChannel originChannel = baseMapper.selectById(dto.getId());
        if (!dto.getMarketingChannelName().equals(originChannel.getMarketingChannelName())) {
            channelCore.checkChannelName(ChannelTypeEnum.MARKETING, dto.getMarketingChannelName());
            channelCore.editTag(ChannelTypeEnum.MARKETING, originChannel.getTagId(), dto.getMarketingChannelName());
        }
        MarketingChannel marketingChannel = BeanUtil.copyProperties(dto, MarketingChannel.class);
        marketingChannel.setUpdateId(SecurityUtils.getUserId());
        marketingChannel.setUpdateBy(SecurityUtils.getUsername());
        baseMapper.updateById(marketingChannel);
    }

    /**
     * 查询市场渠道详情
     */
    @Override
    public MarketingChannelDetailVO getMarketingChannelDetail(Long id) {
        MarketingChannel marketingChannel = baseMapper.selectById(id);
        if (ObjectUtil.isNull(marketingChannel)) {
            return new MarketingChannelDetailVO();
        }
        if (LandingFormEnum.OFFICIAL_WEBSITE.getCode().equals(marketingChannel.getLandingForm())) {
            marketingChannel.setDedicatedLinkCode(channelMarketingProperties.getOfficialWebsitePrefixURL() + marketingChannel.getDedicatedLinkCode());
        } else {
            marketingChannel.setDedicatedLinkCode(marketingChannel.getWeChatUrl());
        }
        return BeanUtil.copyProperties(marketingChannel, MarketingChannelDetailVO.class);
    }

    /**
     * 新增市场渠道
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMarketingChannel(MarketingChannelDTO dto) {
        dto.setMarketingChannelName(dto.getMarketingChannelName().trim());
        Assert.isFalse(baseMapper.checkMarketingChannelNameExist(null, dto.getMarketingChannelName()), "投流名称：{} 已存在，请修改后重试！", dto.getMarketingChannelName());
        channelCore.checkChannelName(ChannelTypeEnum.MARKETING, dto.getMarketingChannelName());
        MarketingChannel marketingChannel = BeanUtil.copyProperties(dto, MarketingChannel.class);

        String dedicatedLinkCode = DEDICATED_LINK_CODE_PREFIX + RandomCodeUtil.generateRandomCode(8);
        marketingChannel.setDedicatedLinkCode(dedicatedLinkCode);
        String weChatUrl = workWechatApiService.contactMeQrcode(ChannelTypeEnum.MARKETING, dedicatedLinkCode);
        marketingChannel.setWeChatUrl(weChatUrl);
        String tagId = workWechatApiService.addTag(ChannelTypeEnum.MARKETING, dto.getMarketingChannelName());
        marketingChannel.setTagId(tagId);

        marketingChannel.setCreateId(SecurityUtils.getUserId());
        marketingChannel.setCreateBy(SecurityUtils.getUsername());
        marketingChannel.setUpdateId(SecurityUtils.getUserId());
        marketingChannel.setUpdateBy(SecurityUtils.getUsername());
        try {
            baseMapper.insert(marketingChannel);
        } catch (DuplicateKeyException e) {
            log.warn("插入MarketingChannel重复条目错误", e);
            throw new ServiceException("系统繁忙！请稍后重试~");
        }
    }

    /**
     * 查询市场渠道列表
     */
    @Override
    public List<MarketingChannelListVO> marketingChannelListByCondition(MarketingChannelListDTO listDTO) {
        OrderByDto orderByDto = new OrderByDto();
        if (ObjectUtil.isNotNull(listDTO.getOrderByType()) && ObjectUtil.isNotNull(ChannelOrderByTypeEnum.getValue(listDTO.getOrderByType()))) {
            orderByDto.setField(MarketingChannelOrderByTypeEnum.getValue(listDTO.getOrderByType()), StatusTypeEnum.YES.getCode().equals(listDTO.getIsAsc()) ? OrderByDto.DIRECTION.ASC : OrderByDto.DIRECTION.DESC);
        }
        orderByDto.setField("mc.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<MarketingChannelListVO> marketingChannelListVOS = baseMapper.marketingChannelListByCondition(listDTO);
        if (CollUtil.isEmpty(marketingChannelListVOS)) {
            return Collections.emptyList();
        }
        marketingChannelListVOS.forEach(item -> {
            item.setMemberNum(Optional.ofNullable(item.getMemberNum()).orElse(0L));
            item.setWechatCount(Optional.ofNullable(item.getWechatCount()).orElse(0));
            item.setNewRegistrationCount(Optional.ofNullable(item.getNewRegistrationCount()).orElse(0));
            item.setUniqueVisitor(Optional.ofNullable(item.getUniqueVisitor()).orElse(0));
            item.setRealPayAmount(Optional.ofNullable(item.getRealPayAmount()).orElse(BigDecimal.ZERO));
        });
        return marketingChannelListVOS;
    }

    private QrcodeByDedicatedLinkCodeVO getSysDefaultQrcode(String dedicatedLinkCode) {
        String url = redisService.getCacheObject(CacheConstants.CURRENT_ACTIVE_URL_PREFIX + dedicatedLinkCode);
        if (StringUtils.isNotBlank(url)){
            return QrcodeByDedicatedLinkCodeVO.builder()
                    .type(2)
                    .url(url)
                    .build();
        }
        return QrcodeByDedicatedLinkCodeVO.builder().type(wechatConfig.getType()).url(wechatConfig.getUrl()).build();

    }
}




