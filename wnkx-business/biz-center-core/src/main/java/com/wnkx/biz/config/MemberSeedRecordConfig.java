package com.wnkx.biz.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会员种草结算记录配置
 * @create :2025-05-20 14:44
 **/

@Component
@ConfigurationProperties(prefix = "channel.member-seed-record")
@Data
@RefreshScope
public class MemberSeedRecordConfig {
    List<String> payAccount;
}
