package com.wnkx.biz.channel.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.channel.MarketingChannelVisitFlow;
import com.wnkx.biz.channel.service.IMarketingChannelVisitFlowService;
import com.wnkx.biz.channel.mapper.MarketingChannelVisitFlowMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【marketing_channel_visit_flow(市场渠道访问流水数据)】的数据库操作Service实现
 * @createDate 2024-12-10 10:17:51
 */
@Service
public class MarketingChannelVisitFlowServiceImpl extends ServiceImpl<MarketingChannelVisitFlowMapper, MarketingChannelVisitFlow>
        implements IMarketingChannelVisitFlowService {

}




