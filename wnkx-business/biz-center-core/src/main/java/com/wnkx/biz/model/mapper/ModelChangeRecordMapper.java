package com.wnkx.biz.model.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.model.ModelChangeRecord;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedOustModelCountInfo;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/11 17:09
 */
public interface ModelChangeRecordMapper extends SuperMapper<ModelChangeRecord> {

    /**
     * 查询模特变更记录
     */
    default List<ModelChangeRecord> listByModelId(Long modelId) {
        return selectList(new LambdaQueryWrapper<ModelChangeRecord>()
                .eq(ModelChangeRecord::getModelId, modelId)
                .orderByDesc(ModelChangeRecord::getOperateTime)
        );
    }

    /**
     * 获取某时间内 最终修改为 暂停/取消合作 的模特ID
     */
    List<Long> getFinalOustModelIdsByDate(@Param("dateFormat") String dateFormat, @Param("date") String date);

    /**
     * 获取英文部客服 淘汰模特数
     */
    List<CustomerServiceAddedOustModelCountInfo> getEnglishCustomerServiceOustModelCounts(@Param("dateFormat") String dateFormat, @Param("date") String date);
}
