package com.wnkx.biz.channel.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.WithdrawalListDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecordWithdrawal;
import com.ruoyi.system.api.domain.vo.biz.channel.fission.FissionCountStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.channel.fission.FissionSettleRecordVO;
import com.ruoyi.system.api.domain.vo.biz.channel.fission.MemberSeedRecordWithdrawalVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【member_seed_record_withdrawal(会员种草提现)】的数据库操作Mapper
 * @createDate 2025-05-15 09:15:45
 * @Entity com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecordWithdrawal
 */
public interface MemberSeedRecordWithdrawalMapper extends SuperMapper<MemberSeedRecordWithdrawal> {

    /**
     * 获取种草提现记录
     * @param channelId
     * @return
     */
    List<MemberSeedRecordWithdrawalVO> queryMemberSeedRecordWithdrawalListByChannelId(@Param("channelId") Long channelId);


    /**
     * 根据提现记录ID获取 种草提现记录详情
     * @param id
     * @return
     */
    MemberSeedRecordWithdrawalVO getMemberSeedRecordWithdrawalDetail(@Param("id") Long id);

    /**
     *  获取裂变拉新结算列表
     * @param dto
     * @return
     */
    List<MemberSeedRecordWithdrawalVO> queryMemberSeedRecordWithdrawalList(@Param("dto") WithdrawalListDTO dto);

    /**
     * 获取裂变拉新结算列表导出数据
     * @param dto
     * @return
     */
    List<MemberSeedRecordWithdrawalVO> queryMemberSeedRecordWithdrawalListExport(@Param("dto") WithdrawalListDTO dto);


    /**
     * 获取裂变结算记录
     * @param dto
     * @return
     */
    List<FissionSettleRecordVO> queryFissionSettleRecordList(@Param("dto") WithdrawalListDTO dto);

    /**
     * 获取裂变拉新结算统计
     * @return
     */
    FissionCountStatisticsVO getFissionCountStatisticsVO();

    /**
     * 根据收款账号类型获取上次历史数据
     * @param accountType
     * @param channelId
     * @return
     */
    default MemberSeedRecordWithdrawal getLastWithdrawalDetailByAccountType(Integer accountType, Long channelId){
        return selectOne(new LambdaQueryWrapper<MemberSeedRecordWithdrawal>()
                .eq(MemberSeedRecordWithdrawal::getChannelId, channelId)
                .eq(ObjectUtil.isNotNull(accountType), MemberSeedRecordWithdrawal::getWithdrawalAccountType, accountType)
                .orderByDesc(MemberSeedRecordWithdrawal::getCreateTime)
                .last("limit 1")
        );
    }

}




