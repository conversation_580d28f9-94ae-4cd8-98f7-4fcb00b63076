package com.wnkx.biz.channel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionChancelStatisticsDTO;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelDTO;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelListDTO;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelSettlementDTO;
import com.ruoyi.system.api.domain.entity.order.OrderMemberChannel;
import com.ruoyi.system.api.domain.vo.order.OrderMemberChannelDetailVO;
import com.ruoyi.system.api.domain.vo.order.OrderMemberChannelListVO;
import com.ruoyi.system.api.domain.vo.order.OrderMemberChannelStatisticVO;
import com.ruoyi.system.api.domain.vo.order.OrderMemberFissionListVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【order_member_channel(会员渠道记录表)】的数据库操作Service
 * @createDate 2024-09-24 17:04:00
 */
public interface IOrderMemberChannelService extends IService<OrderMemberChannel> {

    /**
     * 新增会员渠道记录
     */
    void saveOrderMemberChannel(OrderMemberChannelDTO dto);

    /**
     * 分销渠道列表
     */
    List<OrderMemberChannelListVO> memberChannelListByCondition(OrderMemberChannelListDTO dto);

    /**
     * 分销渠道结算-分销渠道列表
     * @param dto
     * @return
     */
    List<OrderMemberChannelListVO> oldMemberChannelListByCondition(OrderMemberChannelListDTO dto);

    /**
     * 裂变渠道列表
     * @param dto
     * @return
     */
    List<OrderMemberFissionListVO> fissionMemberChannelListByCondition(OrderMemberChannelListDTO dto);

    /**
     * 分销渠道结算-查看结算记录
     */
    OrderMemberChannelDetailVO memberChannelDetailById(Long id);

    /**
     * 分销渠道结算-结算
     */
    void memberChannelSettlement(OrderMemberChannelSettlementDTO dto);

    /**
     * 不可结算分销记录
     *
     * @param businessId
     */
    void memberChannelUnableSettlement(Long businessId);

    /**
     * 更新未结算
     * @param businessId
     */
    void memberChannelUnSettlement(Long businessId);

    /**
     * 根据商家Id获取分销记录
     *
     * @param businessId
     * @return
     */
    OrderMemberChannel getOrderMemberChannelByBusinessId(Long businessId);

    /**
     * 通过渠道ID 获取 会员成交数、待结算金额、已结算金额
     */
    Map<Long, OrderMemberChannelStatisticVO> getMemberChannelStatistic(List<Long> channelIds);


    /**
     * 获取分销渠道统计总数据： 会员成交数、待结算金额、已结算金额
     *
     * @param dto
     * @return
     */
    OrderMemberChannelStatisticVO getMemberChannelStatisticTotal(DistributionChancelStatisticsDTO dto);

    /**
     * 获取会员渠道记录列表数据*
     *
     * @param bizUserIds
     * @return
     */
    List<OrderMemberChannel> memberChannelListByChannelIds(List<Long> bizUserIds);

    /**
     * 根据商家ID删除 会员渠道记录表
     *
     * @param businessId
     */
    void deleteByBusinessId(Long businessId);

    /**
     * 获取待结算数量
     * @return
     */
    Long getUnSettledCount();
}
