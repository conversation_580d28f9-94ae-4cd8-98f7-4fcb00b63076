package com.wnkx.biz.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/8/28
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "sms")
public class SmsConfig {
    String url;
    String appCode;
    String templateId;
    String content;
}
