package com.wnkx.biz.channel.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityInfoDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelActivityInfo;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【distribution_channel_activity_info(渠道活动关联表)】的数据库操作Mapper
* @createDate 2024-12-02 14:27:22
* @Entity com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelActivityInfo
*/
public interface DistributionChannelActivityInfoMapper extends SuperMapper<DistributionChannelActivityInfo> {

    default void deleteByActivity(Long activityId){
        delete(new LambdaQueryWrapper<DistributionChannelActivityInfo>()
                .eq(DistributionChannelActivityInfo::getActivityId, activityId)
        );
    }

    List<ChannelActivityInfoDTO> getActivityChannelInfo(@Param(value = "channelName") String channelName,
                                                        @Param(value = "activityId") Long activityId,
                                                        @Param(value= "showDisableChannel") boolean showDisableChannel
    );

}






