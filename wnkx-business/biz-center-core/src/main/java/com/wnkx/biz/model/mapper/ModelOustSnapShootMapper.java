package com.wnkx.biz.model.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.model.ModelOustSnapShoot;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR> 
 * @Date 2025-05-13 17:34:08 
 */
@Mapper
public interface ModelOustSnapShootMapper extends SuperMapper<ModelOustSnapShoot> {

    /**
     * 删除当月模特快照数据
     */
    default void removeMonthModelSnapShoot(Long modelId) {
        delete(new LambdaQueryWrapper<ModelOustSnapShoot>()
                .eq(ModelOustSnapShoot::getModelId, modelId)
                .last("AND DATE_FORMAT(create_time,'%Y-%m')=DATE_FORMAT(NOW(),'%Y-%m')")
        );
    }
}
