package com.wnkx.biz.amazon.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.amazon.AmazonGoodsPic;
import com.wnkx.biz.amazon.mapper.AmazonGoodsPicMapper;
import com.wnkx.biz.amazon.service.AmazonGoodsPicService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/8 11:50
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AmazonGoodsPicServiceImpl extends ServiceImpl<AmazonGoodsPicMapper, AmazonGoodsPic> implements AmazonGoodsPicService {


    /**
     * 查询数据
     */
    @Override
    public List<AmazonGoodsPic> selectByCondition(List<String> goodsIds) {
        return baseMapper.selectByCondition(goodsIds);
    }

    /**
     * 添加数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addAmazonGoodsPic(List<AmazonGoodsPic> amazonGoodsPics) {
        for (AmazonGoodsPic amazonGoodsPic : amazonGoodsPics) {
            baseMapper.insertAmazonGoodsPic(amazonGoodsPic);
        }
    }
}
