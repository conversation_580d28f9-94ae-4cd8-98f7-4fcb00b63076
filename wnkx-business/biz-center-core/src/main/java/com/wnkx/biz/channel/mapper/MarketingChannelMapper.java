package com.wnkx.biz.channel.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.system.api.domain.dto.biz.channel.InviteListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.MarketingChannelListDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.MarketingChannel;
import com.ruoyi.system.api.domain.vo.SysUserVO;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelInviteStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelInviteVO;
import com.ruoyi.system.api.domain.vo.biz.channel.MarketingChannelListVO;
import com.ruoyi.system.api.domain.vo.biz.channel.MarketingChannelStatisticsVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【marketing_channel(市场渠道信息表)】的数据库操作Mapper
 * @createDate 2024-09-24 17:03:37
 * @Entity com.ruoyi.system.api.domain.entity.biz.channel.MarketingChannel
 */
public interface MarketingChannelMapper extends SuperMapper<MarketingChannel> {

    /**
     * 查询市场渠道列表
     */
    List<MarketingChannelListVO> marketingChannelListByCondition(@Param("dto") MarketingChannelListDTO listDTO);

    /**
     * 市场渠道统计
     *
     * @param listDTO
     * @return
     */
    MarketingChannelStatisticsVO marketingChannelStatistics(@Param("dto") MarketingChannelListDTO listDTO);

    /**
     * 获取创建人列表
     *
     * @param name
     * @return
     */
    List<SysUserVO> createUserList(String name);

    /**
     * 根据市场渠道id获取市场渠道数据
     *
     * @param bizUserId
     * @return
     */
    MarketingChannel getMarketingChannelByBizUserId(Long bizUserId);

    /**
     * 获取邀请记录
     *
     * @param dto
     * @return
     */
    List<ChannelInviteVO> getChannelInviteList(InviteListDTO dto);

    /**
     * 邀请记录统计
     *
     * @param dto
     * @return
     */
    ChannelInviteStatisticsVO getChannelInviteStatistics(InviteListDTO dto);

    /**
     * 刷新独立访客数
     */
    void refreshUniqueVisitor();

    /**
     * 查询市场渠道名称是否存在
     */
    default Boolean checkMarketingChannelNameExist(Long id, String marketingChannelName) {
        return exists(new LambdaQueryWrapper<MarketingChannel>()
                .ne(ObjectUtil.isNotNull(id), MarketingChannel::getId, id)
                .eq(MarketingChannel::getMarketingChannelName, marketingChannelName)
        );
    }

    /**
     * 通过专属链接code获取专属企微二维码
     */
    default MarketingChannel getQrcodeByDedicatedLinkCode(String dedicatedLinkCode) {
        return selectOne(new LambdaQueryWrapper<MarketingChannel>()
                .eq(MarketingChannel::getDedicatedLinkCode, dedicatedLinkCode)
        );
    }

    default List<MarketingChannel> getMarketList(List<Long> ids) {
         return this.selectList(Wrappers.lambdaQuery(MarketingChannel.class).in(MarketingChannel::getId, ids));
    }
}




