package com.wnkx.biz.model.mapper;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.model.ModelAccount;
import com.wnkx.db.mapper.SuperMapper;

/**
* <AUTHOR>
* @description 针对表【model_account(模特账号表)】的数据库操作Mapper
* @createDate 2024-07-05 16:17:18
* @Entity com.ruoyi.system.api.domain.entity.biz.business.ModelAccount
*/
public interface ModelAccountMapper extends SuperMapper<ModelAccount> {


    /**
     * 通过模特id获取模特账号
     */
    default ModelAccount getOneByModelId(Long modelId) {
        return this.selectOne(new LambdaQueryWrapper<ModelAccount>()
                .eq(ModelAccount::getModelId, modelId)
                .last("limit 1")
        );
    }
}
