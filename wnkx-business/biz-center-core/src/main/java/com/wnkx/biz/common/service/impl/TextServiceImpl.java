package com.wnkx.biz.common.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.system.api.domain.dto.biz.common.*;
import com.ruoyi.system.api.domain.entity.biz.common.Text;
import com.ruoyi.system.api.domain.entity.biz.common.TextHistory;
import com.ruoyi.system.api.domain.vo.biz.common.*;
import com.wnkx.biz.common.mapper.TextMapper;
import com.wnkx.biz.common.service.TextHistoryService;
import com.wnkx.biz.common.service.TextService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:29
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TextServiceImpl extends ServiceImpl<TextMapper, Text> implements TextService {

    private final TextHistoryService textHistoryService;

    private final String[] ignoreFields = {"id", "createTime", "updateTime"};


    /**
     * 查看数据详情
     */
    @Override
    public TextVO detail(Long id) {
        return BeanUtil.copyProperties(baseMapper.selectById(id), TextVO.class);
    }

    @Override
    public TextHelpVO helpDetail(Long id) {
        return BeanUtil.copyProperties(baseMapper.selectById(id), TextHelpVO.class);
    }

    /**
     * 删除数据
     */
    @Override
    public void delete(Long id) {
        Text text = baseMapper.selectById(id);
        if (ObjectUtil.isNull(text)) {
            return;
        }
        Assert.isTrue(StatusEnum.ENABLED.getCode().equals(text.getCanDelete()), "不允许删除该数据！");

        baseMapper.deleteById(id);
    }

    /**
     * 编辑数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(TextDTO dto) {
        Text text = baseMapper.selectById(dto.getId());
        Assert.notNull(text, "数据不存在！");
        BeanUtil.copyProperties(dto, text);
        text.setVersion(text.getVersion() + 1);
        text.setUpdateTime(null);
        baseMapper.updateById(text);

        addTextHistory(text);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editHelp(TextHelpDTO dto) {
        Text text = baseMapper.selectById(dto.getId());
        Assert.notNull(text, "数据不存在！");
        BeanUtil.copyProperties(dto, text);
        text.setVersion(text.getVersion() + 1);
        text.setUpdateTime(null);
        baseMapper.updateById(text);

        addTextHistory(text);
    }

    @Override
    public void updateStatus(TextHelpStatusDTO dto) {
        Text text = baseMapper.selectById(dto.getId());
        Assert.notNull(text, "数据不存在！");
        BeanUtil.copyProperties(dto, text);
        baseMapper.updateById(text);
    }

    /**
     * 文本列表
     */
    @Override
    public List<TextListVO> selectListByCondition(TextListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("tt.create_time", OrderByDto.DIRECTION.ASC);
        orderByDto.setField("tt.id", OrderByDto.DIRECTION.ASC);
        PageUtils.startPage(orderByDto);
        return baseMapper.selectListByCondition(dto);
    }

    @Override
    public List<TextHelpListVO> selectHelpListByCondition(TextHelpListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("tt.type", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("tt.sort", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("tt.create_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("tt.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        return baseMapper.selectHelpListByCondition(dto);
    }

    @Override
    public List<UserTextHelpListVO> selectUserHelpListByCondition() {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("tt.type", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("tt.sort", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("tt.create_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("tt.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<TextHelpVO> textHelpVOS = baseMapper.selectUserHelpListByCondition(TextHelpListDTO.builder().status(StatusEnum.ENABLED.getCode()).build());
        if (CollUtil.isEmpty(textHelpVOS)) {
            return Collections.emptyList();
        }
        Map<Integer, List<TextHelpVO>> map = textHelpVOS.stream().collect(Collectors.groupingBy(TextHelpVO::getType));
        List<UserTextHelpListVO> userTextHelpListVOS = new ArrayList<>();
        for (Integer type : map.keySet()) {
            userTextHelpListVOS.add(UserTextHelpListVO.builder()
                    .type(type)
                    .list(map.get(type))
                    .build());
        }

        return userTextHelpListVOS;
    }

    /**
     * 添加数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(TextDTO dto) {
        Text text = BeanUtil.copyProperties(dto, Text.class);
        baseMapper.insert(text);

        addTextHistory(text);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addHelp(TextHelpDTO dto) {
        Text text = BeanUtil.copyProperties(dto, Text.class);
        baseMapper.insert(text);
        addTextHistory(text);
    }


    private void addTextHistory(Text text) {
        TextHistory textHistory = BeanUtil.copyProperties(text, TextHistory.class, ignoreFields);
        textHistory.setTextId(text.getId());
        textHistoryService.insert(textHistory);
    }
}
