package com.wnkx.biz.wechat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatContactUserConfigInfo;
import com.wnkx.biz.wechat.service.WeChatContactUserConfigInfoService;
import com.wnkx.biz.wechat.mapper.WeChatContactUserConfigInfoMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【we_chat_contact_user_config_info(对外联系人详细配置表)】的数据库操作Service实现
* @createDate 2025-03-05 13:46:50
*/
@Service
public class WeChatContactUserConfigInfoServiceImpl extends ServiceImpl<WeChatContactUserConfigInfoMapper, WeChatContactUserConfigInfo>
    implements WeChatContactUserConfigInfoService{

}




