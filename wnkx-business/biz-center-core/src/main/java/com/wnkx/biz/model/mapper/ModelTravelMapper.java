package com.wnkx.biz.model.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.model.ModelTravel;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 模特行程Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@Mapper
public interface ModelTravelMapper extends SuperMapper<ModelTravel>
{
    /**
     * 通过模特id获取模特行程
     */
    default List<ModelTravel> selectModelTravelByModelIds(Collection<Long> modelId) {
        return this.selectList(new LambdaQueryWrapper<ModelTravel>()
                .in(ModelTravel::getModelId, modelId)
        );
    }

    /**
     * 通过模特id删除行程时间表
     */
    default void removeByModelIds(List<Long> modelIds) {
        delete(new LambdaQueryWrapper<ModelTravel>()
                .in(ModelTravel::getModelId, modelIds)
        );
    }
}
