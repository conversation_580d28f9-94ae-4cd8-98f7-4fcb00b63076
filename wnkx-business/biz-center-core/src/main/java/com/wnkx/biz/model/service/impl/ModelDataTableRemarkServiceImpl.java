package com.wnkx.biz.model.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableAddRemarkDTO;
import com.ruoyi.system.api.domain.entity.biz.model.ModelDataTableRemark;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableRemarkVO;
import com.wnkx.biz.model.mapper.ModelDataTableRemarkMapper;
import com.wnkx.biz.model.service.ModelDataTableRemarkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-07-08 20:35:48
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ModelDataTableRemarkServiceImpl extends ServiceImpl<ModelDataTableRemarkMapper, ModelDataTableRemark> implements ModelDataTableRemarkService {


    /**
     * 查询备注
     */
    @Override
    public List<ModelDataTableRemarkVO> selectModelDataTableRemarkListByModelIds(List<Long> modelIds) {
        List<ModelDataTableRemark> modelDataTableRemarks = baseMapper.selectModelDataTableRemarkListByModelIds(modelIds);
        return BeanUtil.copyToList(modelDataTableRemarks, ModelDataTableRemarkVO.class);
    }

    /**
     * 查询备注
     */
    @Override
    public List<ModelDataTableRemarkVO> selectModelDataTableRemarkList(Long modelId) {
        PageUtils.startPage();
        List<ModelDataTableRemark> modelDataTableRemarks = baseMapper.selectModelDataTableRemarkListByModelId(modelId);
        return BeanUtil.copyToList(modelDataTableRemarks, ModelDataTableRemarkVO.class);
    }

    /**
     * 添加备注
     */
    @Override
    public void saveModelDataTableRemark(ModelDataTableAddRemarkDTO dto) {
        ModelDataTableRemark modelDataTableRemark = new ModelDataTableRemark();
        modelDataTableRemark.setModelId(dto.getModelId());
        modelDataTableRemark.setRemark(dto.getRemark());

        String username = SecurityUtils.getUsername();
        Long userId = SecurityUtils.getUserId();
        DateTime dateTime = DateUtil.date();

        modelDataTableRemark.setCreateBy(username);
        modelDataTableRemark.setCreateById(userId);
        modelDataTableRemark.setCreateTime(dateTime);
        modelDataTableRemark.setUpdateBy(username);
        modelDataTableRemark.setUpdateById(userId);
        modelDataTableRemark.setUpdateTime(dateTime);
        baseMapper.insert(modelDataTableRemark);
    }
}
