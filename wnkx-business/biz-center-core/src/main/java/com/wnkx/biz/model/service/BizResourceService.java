package com.wnkx.biz.model.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.biz.common.BizResource;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/4 14:54
 */
public interface BizResourceService extends IService<BizResource> {

    /**
     * 批量新增数据
     */
    List<Long> saveBatchBizResourceReturnIds(Collection<String> objectKeys);

    /**
     * 批量新增图片资源
     *
     * @param bizResources 图片资源
     */
    List<BizResource> saveBatchBizResource(List<BizResource> bizResources);

    /**
     * 通过资源id查询数据
     */
    List<BizResource> selectListByIds(Collection<Long> resourceIds);

    /**
     * 查询资源列表
     * @param resourceIds
     * @return
     */
    List<String> selectObjectKeyListByIds(Collection<Long> resourceIds);

    /**
     * 通过资源id查询数据(map)
     */
    Map<Long, BizResource> getResourceMapByIds(Collection<Long> resourceIds);
}
