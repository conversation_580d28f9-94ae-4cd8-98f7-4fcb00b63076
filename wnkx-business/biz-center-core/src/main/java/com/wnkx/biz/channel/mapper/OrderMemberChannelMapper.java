package com.wnkx.biz.channel.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.ChannelTypeEnum;
import com.ruoyi.common.core.enums.SettleStatusEnum;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionChancelStatisticsDTO;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderMemberChannel;
import com.ruoyi.system.api.domain.vo.order.OrderMemberChannelListVO;
import com.ruoyi.system.api.domain.vo.order.OrderMemberChannelStatisticVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_member_channel(会员渠道记录表)】的数据库操作Mapper
 * @createDate 2024-09-24 17:04:00
 * @Entity com.ruoyi.system.api.domain.entity.order.OrderMemberChannel
 */
public interface OrderMemberChannelMapper extends SuperMapper<OrderMemberChannel> {

    /**
     * 分销渠道列表
     */
    List<OrderMemberChannelListVO> memberChannelListByCondition(@Param("dto") OrderMemberChannelListDTO dto);

    /**
     * 通过渠道ID 查询列表
     */
    default List<OrderMemberChannel> memberChannelListByChannelIds(List<Long> channelIds) {
        return selectList(new LambdaQueryWrapper<OrderMemberChannel>()
                .in(OrderMemberChannel::getChannelId, channelIds)
        );
    }

    /**
     * 检查订单是否结算
     *
     * @param ids 订单Id
     * @return
     */
    default Boolean checkOrderMemberChannelIsUnSettlement(List<Long> ids) {
        return !exists(new LambdaQueryWrapper<OrderMemberChannel>()
                .ne(OrderMemberChannel::getSettleStatus, SettleStatusEnum.UN_SETTLED.getCode())
                .in(OrderMemberChannel::getId, ids)
        );
    }

    /**
     * 根据商家Id获取结算记录
     *
     * @param businessId
     * @return
     */
    default OrderMemberChannel getOrderMemberChannelByBusinessId(Long businessId) {
        return selectOne(new LambdaQueryWrapper<OrderMemberChannel>()
                .eq(OrderMemberChannel::getBusinessId, businessId)
        );
    }

    /**
     * 获取分销渠道会员统计数据
     *
     * @param dto
     * @return
     */
    OrderMemberChannelStatisticVO getMemberChannelStatisticTotal(DistributionChancelStatisticsDTO dto);

    /**
     * 根据登录账号ID 查询列表
     *
     * @param bizUserIds
     * @return
     */
    default List<OrderMemberChannel> memberChannelListByChannelBizUserIds(List<Long> bizUserIds) {
        return selectList(new LambdaQueryWrapper<OrderMemberChannel>()
                .in(OrderMemberChannel::getBizUserId, bizUserIds)
        );

    }

    OrderMemberChannel memberChannelDetailById(Long id);

    /**
     * 获取待结算数量
     * @return
     */
    default Long getUnSettledCount(){
        return selectCount(new LambdaQueryWrapper<OrderMemberChannel>()
                .eq(OrderMemberChannel::getSettleStatus, SettleStatusEnum.UN_SETTLED.getCode())
                .eq(OrderMemberChannel::getChannelType, ChannelTypeEnum.DISTRIBUTION.getCode())
                .gt(OrderMemberChannel::getSettleAmount, 0)
        );
    }
}




