package com.wnkx.biz.core;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.FlowMemberSeedRecordDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecordWithdrawal;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.wnkx.biz.channel.mapper.MemberSeedRecordMapper;
import com.wnkx.biz.channel.mapper.MemberSeedRecordRelevanceMapper;
import com.wnkx.biz.channel.service.MemberSeedRecordWithdrawalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会员种草提现记录
 * @create :2025-05-20 13:38
 **/

@Component
@RequiredArgsConstructor
@Slf4j
public class MemberSeedRecordWithdrawalCore {
    private final MemberSeedRecordMapper memberSeedRecordMapper;
    private final MemberSeedRecordWithdrawalService memberSeedRecordWithdrawalService;
    private final MemberSeedRecordRelevanceMapper memberSeedRecordRelevanceMapper;
    private final RedisService redisService;

    /**
     * 审核通过、审核不通过 完成打款 打款异常
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void flowMemberSeedRecord(FlowMemberSeedRecordDTO dto) {
        //先查询提现记录
        List<MemberSeedRecordWithdrawal> memberSeedRecordWithdrawals = memberSeedRecordWithdrawalService.listByIds(dto.getIds());
        Assert.isTrue(CollUtil.isNotEmpty(memberSeedRecordWithdrawals), "提现记录不存在");
        //  加锁
        List<Long> lockVideoIds = new ArrayList<>();
        List<Long> channelIds = new ArrayList<>();
        try {
            for (MemberSeedRecordWithdrawal item : memberSeedRecordWithdrawals) {
                if (channelIds.contains(item.getChannelId())){
                    continue;
                }
                channelIds.add(item.getChannelId());
                Assert.isTrue(redisService.getLock(CacheConstants.MEMBER_SEED_RECORD_WITHDRAWAL_INIT_KEY + item.getChannelId(), 60L), "订单流转中，请稍后重试！");
                lockVideoIds.add(item.getChannelId());
            }
        } catch (Exception e) {
            //  异常 把加的锁释放掉
            for (Long lockVideoId : lockVideoIds) {
                redisService.releaseLock(CacheConstants.MEMBER_SEED_RECORD_WITHDRAWAL_INIT_KEY + lockVideoId);
            }
            throw new ServiceException("数据正在处理中，请稍后重试！");
        }


        try {
            if (MemberSeedRecordStatusEnum.PENDING_TRANSFER.getCode().equals(dto.getStatus())) {
                //审核通过
                Assert.isTrue(StrUtil.isBlank(dto.getRemark()) || dto.getRemark().length() <= 150 , "备注不能超过150字~");
                checkWithdrawalStatus(memberSeedRecordWithdrawals, MemberSeedRecordStatusEnum.UNDER_REVIEW);
            }else if (MemberSeedRecordStatusEnum.REVIEW_REJECTED.getCode().equals(dto.getStatus())){
                //审核不通过
                Assert.notNull(dto.getRemark(), "拒绝理由不能为空~");
                Assert.isTrue(StrUtil.isBlank(dto.getRemark()) || dto.getRemark().length() <= 150 , "拒绝理由不能超过150字~");
                checkWithdrawalStatus(memberSeedRecordWithdrawals, MemberSeedRecordStatusEnum.UNDER_REVIEW);
            }else if (MemberSeedRecordStatusEnum.TRANSFERRED.getCode().equals(dto.getStatus())){
                //完成打款
                Assert.isTrue(StrUtil.isBlank(dto.getRemark()) || dto.getRemark().length() <= 150 , "备注不能超过150字~");
                Assert.isTrue(StrUtil.isNotBlank(dto.getPayAccount()), "打款账号不能为空~");
                Assert.isTrue(CollUtil.isEmpty(dto.getResourceUrlList()) || dto.getResourceUrlList().size() <= 5, "打款凭证数量不能大于5~");
                Assert.notNull(dto.getPayoutTime(), "打款时间不能为空~");
                checkWithdrawalStatus(memberSeedRecordWithdrawals, MemberSeedRecordStatusEnum.PENDING_TRANSFER);
            }else if (MemberSeedRecordStatusEnum.TRANSFER_EXCEPTION.getCode().equals(dto.getStatus())){
                //打款异常
                Assert.notNull(dto.getRemark(), "异常原因不能为空~");
                Assert.isTrue(StrUtil.isBlank(dto.getRemark()) || dto.getRemark().length() <= 150 , "异常原因不能超过150字~");
                checkWithdrawalStatus(memberSeedRecordWithdrawals, MemberSeedRecordStatusEnum.PENDING_TRANSFER);
            }else {
                throw new ServiceException("状态不正确");
            }

            //计算提现总额|校验状态是否正确
            Date date = new Date();
            LoginUserInfoVO loginUserInfoVo = dto.getLoginUserInfoVo();
            if (ObjectUtil.isNull(loginUserInfoVo)){
                loginUserInfoVo = SecurityUtils.getLoginUserInfoVo();
            }
            List<MemberSeedRecordWithdrawal> updateList = new ArrayList<>();
            for (MemberSeedRecordWithdrawal memberSeedRecordWithdrawal : memberSeedRecordWithdrawals) {
                MemberSeedRecordWithdrawal update = new MemberSeedRecordWithdrawal();
                update.setId(memberSeedRecordWithdrawal.getId());
                update.setStatus(dto.getStatus());
                if (List.of(MemberSeedRecordStatusEnum.PENDING_TRANSFER.getCode(), MemberSeedRecordStatusEnum.REVIEW_REJECTED.getCode()).contains(dto.getStatus())){
                    Assert.isTrue(MemberSeedRecordStatusEnum.UNDER_REVIEW.getCode().equals(memberSeedRecordWithdrawal.getStatus()), "数据已更新，请刷新页面~");
                    update.setAuditRemark(dto.getRemark());
                    update.setAuditTime(date);
                    update.setAuditUserId(loginUserInfoVo.getUserId());
                    update.setAuditUserName(loginUserInfoVo.getName());
                }else if (MemberSeedRecordStatusEnum.TRANSFERRED.getCode().equals(dto.getStatus())){
                    Assert.isTrue(MemberSeedRecordStatusEnum.PENDING_TRANSFER.getCode().equals(memberSeedRecordWithdrawal.getStatus()), "数据已更新，请刷新页面~");
                    update.setWithdrawalRemark(dto.getRemark());
                    update.setPayAccount(dto.getPayAccount());
                    update.setWithdrawalTime(date);
                    update.setPayoutTime(dto.getPayoutTime());
                    update.setWithdrawalUserId(loginUserInfoVo.getUserId());
                    update.setWithdrawalUserName(loginUserInfoVo.getName());
                    if (CollUtil.isNotEmpty(dto.getResourceUrlList())) {
                        update.setResourceUrl(StrUtil.join(StrUtil.COMMA, dto.getResourceUrlList()));
                    }
                }else if (MemberSeedRecordStatusEnum.TRANSFER_EXCEPTION.getCode().equals(dto.getStatus())){
                    Assert.isTrue(MemberSeedRecordStatusEnum.PENDING_TRANSFER.getCode().equals(memberSeedRecordWithdrawal.getStatus()), "数据已更新，请刷新页面~");
                    update.setWithdrawalRemark(dto.getRemark());
                    update.setWithdrawalTime(date);
                    update.setWithdrawalUserId(loginUserInfoVo.getUserId());
                    update.setWithdrawalUserName(loginUserInfoVo.getName());
                }
                updateList.add(update);
            }

            memberSeedRecordWithdrawalService.updateBatchById(updateList);

            //查询出所有
            List<Long> memberSeedRecordIds = memberSeedRecordRelevanceMapper.getMemberSeedRecordIdsByWithdrawalIds(dto.getIds());
            //填充种草记录数据
            memberSeedRecordMapper.setWithdrawalInfo(memberSeedRecordIds, dto.getStatus());
        } finally {
            for (Long lockVideoId : lockVideoIds) {
                redisService.releaseLock(CacheConstants.MEMBER_SEED_RECORD_WITHDRAWAL_INIT_KEY + lockVideoId);
            }
        }
    }


    /**
     * 检查会员种草提现数据
     *
     * @param memberSeedRecordWithdrawal 会员种草提现数据
     * @param statusEnum  应该是什么状态
     */
    public void checkWithdrawalStatus(List<MemberSeedRecordWithdrawal> memberSeedRecordWithdrawal, MemberSeedRecordStatusEnum... statusEnum){
        List<Integer> status = Arrays.stream(statusEnum).map(MemberSeedRecordStatusEnum::getCode).collect(Collectors.toList());
        for (MemberSeedRecordWithdrawal item : memberSeedRecordWithdrawal) {
            if (!status.contains(item.getStatus())) {
                log.error(StrUtil.format("会员种草提现订单状态异常，需要是：{}，当前是：[{}]，请刷新页面",
                        Arrays.stream(statusEnum).map(MemberSeedRecordStatusEnum::getLabel).collect(Collectors.toList()),
                        OrderStatusEnum.getLabel(item.getStatus())));
                throw new ServiceException("会员种草提现订单状态发生变化，请刷新页面重试~");
            }
        }
    }
}
