package com.wnkx.biz.page.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.page.PageConfigDTO;
import com.ruoyi.system.api.domain.dto.biz.page.PageConfigQueryListDTO;
import com.ruoyi.system.api.domain.dto.biz.page.PageConfigUpdateDTO;
import com.ruoyi.system.api.domain.entity.biz.page.HomePage;
import com.ruoyi.system.api.domain.entity.biz.page.PageConfig;
import com.ruoyi.system.api.domain.entity.biz.page.PageConfigInfo;
import com.ruoyi.system.api.domain.vo.biz.page.PageConfigVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【page_config(页面配置表)】的数据库操作Service
* @createDate 2024-08-19 16:43:20
*/
public interface IPageConfigService extends IService<PageConfig> {

    /**
     * 查询页面分页列表
     * @param dto
     * @return
     */
    List<PageConfigVO> queryList(PageConfigQueryListDTO dto);

    /**
     * 添加自定义页面*
     * @param dto
     * @return
     */
    PageConfig save(PageConfigDTO dto);

    /**
     * 修改页面配置
     * @param dto
     * @return
     */
    int update(PageConfigUpdateDTO dto);

    /**
     * 删除页面配置
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 保存精选案例页面配置详情
     * @param dto
     * @return
     */
    PageConfig saveChooseCase(PageConfigInfo dto);

    /**
     * 保存首页配置
     * @param dto
     * @return
     */
    PageConfig saveHomePage(HomePage dto);

    /**
     * 获取页面配置详情
     * @param id
     * @return
     */
    PageConfigInfo getInfo(Long id);

    /**
     * 获取首页配置详情
     * @param id
     * @return
     */
    HomePage getHomePage(Long id);
}
