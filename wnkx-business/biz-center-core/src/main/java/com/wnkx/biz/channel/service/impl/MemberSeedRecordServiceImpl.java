package com.wnkx.biz.channel.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserChannelListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.MemberSeedRecordWithdrawalDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.WithdrawalDTO;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecord;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecordRelevance;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecordWithdrawal;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserChannelListVO;
import com.ruoyi.system.api.domain.vo.biz.channel.DistributionChannelVO;
import com.ruoyi.system.api.domain.vo.biz.channel.fission.FissionAmountStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.channel.fission.FissionCountStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.channel.fission.MemberSeedRecordVO;
import com.ruoyi.system.api.domain.vo.biz.channel.fission.WithdrawalVO;
import com.wnkx.biz.business.mapper.BizUserMapper;
import com.wnkx.biz.channel.mapper.MemberSeedRecordRelevanceMapper;
import com.wnkx.biz.channel.service.MemberSeedRecordWithdrawalService;
import com.wnkx.biz.core.ChannelCore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wnkx.biz.channel.service.MemberSeedRecordService;
import com.wnkx.biz.channel.mapper.MemberSeedRecordMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
* <AUTHOR>
* @description 针对表【member_seed_record(会员种草记录)】的数据库操作Service实现
* @createDate 2025-05-15 09:15:45
*/
@Service
@RequiredArgsConstructor
@Slf4j
public class MemberSeedRecordServiceImpl extends ServiceImpl<MemberSeedRecordMapper, MemberSeedRecord>
    implements MemberSeedRecordService{

    private final ChannelCore channelCore;
    private final BizUserMapper bizUserMapper;
    private final MemberSeedRecordWithdrawalService memberSeedRecordWithdrawalService;
    private final RedisService redisService;
    private final MemberSeedRecordRelevanceMapper memberSeedRecordRelevanceMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMemberSeedRecord(OrderMemberChannelDTO dto) {
        DistributionChannelVO distributionChannelVO = channelCore.getDistributionChannelBySeedCode(dto.getSeedCode());
        if (ObjectUtil.isNull(distributionChannelVO)) {
            return;
        }
        MemberSeedRecord memberSeedRecord = BeanUtil.copyProperties(dto, MemberSeedRecord.class);
        memberSeedRecord.setChannelId(distributionChannelVO.getId());
        memberSeedRecord.setChannelName(distributionChannelVO.getChannelName());
        memberSeedRecord.setChannelPhone(distributionChannelVO.getPhone());
        memberSeedRecord.setChannelType(distributionChannelVO.getChannelType());
        memberSeedRecord.setChannelBizUserId(Optional.ofNullable(distributionChannelVO.getBizUserId()).orElse(null));
        memberSeedRecord.setSeedCode(distributionChannelVO.getSeedCode());
        memberSeedRecord.setChannelSeedId(distributionChannelVO.getSeedId());
        memberSeedRecord.setBuyTime(dto.getBuyTime());

        memberSeedRecord.setSettleType(distributionChannelVO.getSettleDiscountType());
        memberSeedRecord.setSettleRage(distributionChannelVO.getBrokeRage());
        BigDecimal amount = dto.getRealPayAmount().subtract(dto.getTaxPointCost());
        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(distributionChannelVO.getSettleDiscountType())) {
            memberSeedRecord.setSeedCodeDiscount(distributionChannelVO.getBrokeRage());
            memberSeedRecord.setSettleAmount(distributionChannelVO.getBrokeRage());
        } else {
            memberSeedRecord.setSettleAmount(amount.multiply(distributionChannelVO.getBrokeRage().divide(BigDecimal.valueOf(100), 4, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN));
            memberSeedRecord.setSeedCodeDiscount(amount.subtract(memberSeedRecord.getSettleAmount()));
        }
        memberSeedRecord.setCreateTime(DateUtil.date());
        memberSeedRecord.setBizUserId(dto.getBizUserId());
        memberSeedRecord.setBizUserNickName(dto.getBizUserNickName());
        memberSeedRecord.setBizUserPhone(dto.getBizUserPhone());
        baseMapper.insert(memberSeedRecord);


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void memberChannelUnableSettlement(Long businessId) {
        Assert.notNull(businessId, "businessId不能为空");
        MemberSeedRecord memberSeedRecord = checkUnableSettlement(businessId);
        if (ObjectUtil.isNull(memberSeedRecord)) {
            return;
        }
        MemberSeedRecord update = new MemberSeedRecord();
        update.setId(memberSeedRecord.getId());
        update.setStatus(MemberSeedRecordStatusEnum.DEPOSIT_FAILED.getCode());
        baseMapper.updateById(update);
    }

    @Override
    public FissionAmountStatisticsVO getFissionStatisticsVO(Long channelId) {
        FissionAmountStatisticsVO fissionAmountStatisticsVO = baseMapper.getFissionStatisticsVO(channelId);

        if (ObjectUtil.isNull(fissionAmountStatisticsVO)){
            fissionAmountStatisticsVO = new FissionAmountStatisticsVO();
            fissionAmountStatisticsVO.setPendingDepositAmount(new BigDecimal("0"));
            fissionAmountStatisticsVO.setCanWithdrawAmount(new BigDecimal("0"));
            fissionAmountStatisticsVO.setUnderReviewAmount(new BigDecimal("0"));
            fissionAmountStatisticsVO.setPendingTransferAmount(new BigDecimal("0"));
            fissionAmountStatisticsVO.setWithdrawSuccessAmount(new BigDecimal("0"));

            fissionAmountStatisticsVO.setWithdrawAuditAmount(new BigDecimal("0"));
            fissionAmountStatisticsVO.setTotalAmount(new BigDecimal("0"));
        }else {
            fissionAmountStatisticsVO.setPendingDepositAmount(Optional.ofNullable(fissionAmountStatisticsVO.getPendingDepositAmount()).orElse(new BigDecimal("0")));
            fissionAmountStatisticsVO.setCanWithdrawAmount(Optional.ofNullable(fissionAmountStatisticsVO.getCanWithdrawAmount()).orElse(new BigDecimal("0")));
            fissionAmountStatisticsVO.setUnderReviewAmount(Optional.ofNullable(fissionAmountStatisticsVO.getUnderReviewAmount()).orElse(new BigDecimal("0")));
            fissionAmountStatisticsVO.setPendingTransferAmount(Optional.ofNullable(fissionAmountStatisticsVO.getPendingTransferAmount()).orElse(new BigDecimal("0")));
            fissionAmountStatisticsVO.setWithdrawSuccessAmount(Optional.ofNullable(fissionAmountStatisticsVO.getWithdrawSuccessAmount()).orElse(new BigDecimal("0")));

            fissionAmountStatisticsVO.setWithdrawAuditAmount(fissionAmountStatisticsVO.getPendingTransferAmount().add(fissionAmountStatisticsVO.getUnderReviewAmount()));
            fissionAmountStatisticsVO.setTotalAmount(fissionAmountStatisticsVO.getPendingDepositAmount()
                    .add(fissionAmountStatisticsVO.getCanWithdrawAmount())
                    .add(fissionAmountStatisticsVO.getWithdrawAuditAmount())
                    .add(fissionAmountStatisticsVO.getWithdrawSuccessAmount()));
        }
        return fissionAmountStatisticsVO;
    }

    @Override
    public List<MemberSeedRecordVO> memberSeedRecordList(Long channelId) {
        Assert.notNull(channelId, "渠道Id不能为空");
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("a.buy_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("a.withdrawal_create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<MemberSeedRecordVO> memberSeedRecordVOS = baseMapper.memberSeedRecordList(channelId);
        if (CollUtil.isEmpty(memberSeedRecordVOS)) {
            return memberSeedRecordVOS;
        }
        if (!checkWithdrawal(channelId)){
            //如果是不可提现 则所有待提现数据 设置为 暂不可提现
            for (MemberSeedRecordVO memberSeedRecordVO : memberSeedRecordVOS) {
                if (MemberSeedRecordStatusEnum.PENDING_WITHDRAWAL.getCode().equals(memberSeedRecordVO.getStatus())){
                    memberSeedRecordVO.setStatus(MemberSeedRecordStatusEnum.CANNOT_WITHDRAWAL_TEMPORARY.getCode());
                }
            }
        }
        return memberSeedRecordVOS;
    }

    private FissionCountStatisticsVO getFissionCountStatisticsVO(Long channelId) {
        FissionCountStatisticsVO fissionCountStatisticsVO = baseMapper.getFissionCountStatisticsVO(channelId);
        if (ObjectUtil.isNull(fissionCountStatisticsVO)) {
            fissionCountStatisticsVO = new FissionCountStatisticsVO();
            fissionCountStatisticsVO.setPendingDepositCount(0);
            fissionCountStatisticsVO.setPendingWithdrawalCount(0);
            fissionCountStatisticsVO.setUnderReviewCount(0);
            fissionCountStatisticsVO.setPendingTransferCount(0);
            fissionCountStatisticsVO.setWithdrawSuccessCount(0);
            fissionCountStatisticsVO.setReviewRejectedCount(0);
            fissionCountStatisticsVO.setTransferExceptionCount(0);
        } else {
            fissionCountStatisticsVO.setPendingDepositCount(Optional.ofNullable(fissionCountStatisticsVO.getPendingDepositCount()).orElse(0));
            fissionCountStatisticsVO.setPendingWithdrawalCount(Optional.ofNullable(fissionCountStatisticsVO.getPendingWithdrawalCount()).orElse(0));
            fissionCountStatisticsVO.setUnderReviewCount(Optional.ofNullable(fissionCountStatisticsVO.getUnderReviewCount()).orElse(0));
            fissionCountStatisticsVO.setPendingTransferCount(Optional.ofNullable(fissionCountStatisticsVO.getPendingTransferCount()).orElse(0));
            fissionCountStatisticsVO.setWithdrawSuccessCount(Optional.ofNullable(fissionCountStatisticsVO.getWithdrawSuccessCount()).orElse(0));
            fissionCountStatisticsVO.setReviewRejectedCount(Optional.ofNullable(fissionCountStatisticsVO.getReviewRejectedCount()).orElse(0));
            fissionCountStatisticsVO.setTransferExceptionCount(Optional.ofNullable(fissionCountStatisticsVO.getTransferExceptionCount()).orElse(0));
        }

        Integer withdrawCount = fissionCountStatisticsVO.getUnderReviewCount()
                + fissionCountStatisticsVO.getPendingTransferCount()
                + fissionCountStatisticsVO.getWithdrawSuccessCount()
                + fissionCountStatisticsVO.getReviewRejectedCount()
                + fissionCountStatisticsVO.getTransferExceptionCount();
        fissionCountStatisticsVO.setWithdrawCount(withdrawCount);
        return fissionCountStatisticsVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WithdrawalVO withdrawal(WithdrawalDTO dto) {
        Assert.isTrue(redisService.getLock(CacheConstants.MEMBER_SEED_RECORD_WITHDRAWAL_INIT_KEY + dto.getChannelId(), 60L), "订单流程正在进行，无法再次处理！");
        try {
            //提现类型不能有误
            Assert.isTrue(List.of(WithdrawTypeEnum.ALIPAY.getCode(), WithdrawTypeEnum.PUBLIC.getCode(), WithdrawTypeEnum.BANK.getCode()).contains(dto.getWithdrawalAccountType()), "提现账号类型有误");

            if (WithdrawTypeEnum.PUBLIC.getCode().equals(dto.getWithdrawalAccountType())){
                Assert.isTrue(StrUtil.isNotBlank(dto.getBankName()), "[开户行名称]不能为空~");
                Assert.isTrue(dto.getPayeeName().length() <= 30, "[收款公司名称]不能超过30位~");
            }else {
                Assert.isTrue(StrUtil.isNotBlank(dto.getPayeeIdentityCard()), "[收款方身份证号]不能为空~");
                Assert.isTrue(dto.getPayeeName().length() <= 16, "[收款方姓名]不能超过16位~");
            }

            //获取可退金额列表
            List<MemberSeedRecord> memberSeedRecords = baseMapper.canWithdrawAmountList(dto.getChannelId());
            Assert.isTrue(CollUtil.isNotEmpty(memberSeedRecords), "当前没有可提现金额~");
            // 可退金额
            BigDecimal canWithdrawAmountTotal = memberSeedRecords.stream().map(MemberSeedRecord::getSettleAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            Assert.isTrue(canWithdrawAmountTotal.compareTo(dto.getSettleAmount()) == 0, "数据已刷新，请刷新页面~");

            //判断当前渠道是不是会员
            if (memberSeedRecords.size() == 1) {
                Assert.isTrue(checkWithdrawal(dto.getChannelId()),"首次提现需成功邀请2次~");
            }
            MemberSeedRecordWithdrawal memberSeedRecordWithdrawal = memberSeedRecordWithdrawalService.initEntity(MemberSeedRecordWithdrawalDTO.builder()
                    .withdrawalDTO(dto)
                    .memberSeedRecordList(memberSeedRecords)
                    .build());
            Assert.notNull(memberSeedRecordWithdrawal, "提现失败~");
            WithdrawalVO withdrawalVO = new WithdrawalVO();
            withdrawalVO.setSettleAmount(dto.getSettleAmount());
            withdrawalVO.setWithdrawalAccountType(dto.getWithdrawalAccountType());
            withdrawalVO.setWithdrawalNum(memberSeedRecordWithdrawal.getWithdrawalNum());

            return withdrawalVO;
        } finally {
            redisService.releaseLock(CacheConstants.MEMBER_SEED_RECORD_WITHDRAWAL_INIT_KEY + dto.getChannelId());
        }
    }

    @Override
    public Boolean checkWithdrawal(Long channelId) {
        //获取已提现数据
        FissionCountStatisticsVO fissionCountStatisticsVO = getFissionCountStatisticsVO(channelId);
        //待提现、审核失败、打款异常都为空 则不可提现
        if (fissionCountStatisticsVO.getPendingWithdrawalCount().compareTo(0) == 0
                && fissionCountStatisticsVO.getReviewRejectedCount().compareTo(0) == 0
                && fissionCountStatisticsVO.getTransferExceptionCount().compareTo(0) == 0
        ) {
            return Boolean.FALSE;
        }
        List<DistributionChannel> distributionChannels = channelCore.getChannelByIds(Arrays.asList(channelId));
        if (CollUtil.isEmpty(distributionChannels)){
            return Boolean.FALSE;
        }
        if (ChannelTypeEnum.DISTRIBUTION.getCode().equals(distributionChannels.get(0).getChannelType())) {
            //分销不需要 首单不可提现
            return Boolean.TRUE;
        }

        if (fissionCountStatisticsVO.getPendingWithdrawalCount().compareTo(1) == 0
                && fissionCountStatisticsVO.getWithdrawCount().compareTo(0) == 0
        ) {
            //只有一条待提现数据 无提现数据
            List<BizUserChannelListVO> bizUserChannelListVOS = bizUserMapper.channelBizUserId(BizUserChannelListDTO
                    .builder()
                    .channelIds(Arrays.asList(channelId))
                    .build());
            if (CollUtil.isEmpty(bizUserChannelListVOS)
                    || ObjectUtil.isNull(bizUserChannelListVOS.get(0).getMemberStatus())
                    || List.of(MemberTypeEnum.EXPIRE.getCode(), MemberTypeEnum.NO_RECHARGE.getCode())
                    .contains(bizUserChannelListVOS.get(0).getMemberStatus())) {
                //非会员 不可提现
                return Boolean.FALSE;
            }
        }

        return Boolean.TRUE;
    }

    private MemberSeedRecord checkUnableSettlement(Long businessId){
        Assert.notNull(businessId, "businessId不能为空");
        MemberSeedRecord memberSeedRecord = baseMapper.getMemberSeedRecordByBusinessId(businessId);
        if (ObjectUtil.isNull(memberSeedRecord)) {
            return null;
        }
        //需要是待入账状态
        Assert.isTrue(MemberSeedRecordStatusEnum.PENDING_DEPOSIT.getCode().equals(memberSeedRecord.getStatus()), "当前结算状态已更新，请刷新页面~");
        //需要创建时间是7天内
        Assert.isTrue(DateUtils.addDays(memberSeedRecord.getCreateTime(), 7).compareTo(DateUtils.getNowDate()) > 0, "购买会员7天内才能结算~");
        return memberSeedRecord;
    }
}




