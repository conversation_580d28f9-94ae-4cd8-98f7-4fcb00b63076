package com.wnkx.biz.model.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.model.ModelChangeRecordDTO;
import com.ruoyi.system.api.domain.entity.biz.model.ModelChangeRecord;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedOustModelCountInfo;
import com.ruoyi.system.api.domain.vo.biz.model.ModelChangeRecordVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/11 17:09
 */
public interface ModelChangeRecordService extends IService<ModelChangeRecord> {

    /**
     * 新增模特变更记录
     */
    void saveModelChangeRecord(ModelChangeRecordDTO dto);

    /**
     * 查询模特变更记录
     */
    List<ModelChangeRecordVO> listByModelId(Long modelId);

    /**
     * 批量新增模特变更记录
     */
    void saveBatchModelChangeRecord(List<ModelChangeRecordDTO> dtoList);

    /**
     * 获取某时间内 最终修改为 暂停/取消合作 的模特ID
     */
    List<Long> getFinalOustModelIdsByDate(String dateFormat, String date);

    /**
     * 获取英文部客服 淘汰模特数
     */
    List<CustomerServiceAddedOustModelCountInfo> getEnglishCustomerServiceOustModelCounts(String date);
}
