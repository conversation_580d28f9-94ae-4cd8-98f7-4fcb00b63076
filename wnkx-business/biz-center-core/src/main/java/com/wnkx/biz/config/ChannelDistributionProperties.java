package com.wnkx.biz.config;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/25 16:25
 */
@Component
@ConfigurationProperties(prefix = "channel.distribution")
@Data
@RefreshScope
public class ChannelDistributionProperties {
    /**
     * 分销渠道地址
     */
    private String channelUrl = StrUtil.EMPTY;
    private Integer videoNumRemark = 0;

}
