package com.wnkx.biz.config;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/25 16:25
 */
@Component
@ConfigurationProperties(prefix = "channel.marketing")
@Data
@RefreshScope
public class ChannelMarketingProperties {
    /**
     * 调试模式
     */
    private String debugger = StrUtil.EMPTY;
    /**
     * 下载物料-官方网站前缀URL
     */
    private String officialWebsitePrefixURL = StrUtil.EMPTY;
    /**
     * 下载物料-企微前缀URL
     */
    private String enterpriseMicroCustomerServicePrefixURL = StrUtil.EMPTY;
    /**
     * 专属链接 - 系统商家端首页
     */
    private String businessWebsitePrefixURL = StrUtil.EMPTY;

    /**
     * 欢迎语格式
     */
    private String welcomeSms = StrUtil.EMPTY;

    /**
     * 欢迎语图片
     */
    private String welcomeImage = StrUtil.EMPTY;

    /**
     * 子账号欢迎语
     */
    private String subAccountWelcomeSms = StrUtil.EMPTY;

    /**
     * 老用户欢迎语
     */
    private String oldBusinessWelcomeSms = StrUtil.EMPTY;

    /**
     * 备注功能启用
     */
    private String remarkEnable = StrUtil.EMPTY;

}
