package com.wnkx.biz.channel.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelDiscountLog;
import com.wnkx.db.mapper.SuperMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【distribution_channel_discount_log(渠道折扣日志)】的数据库操作Mapper
 * @createDate 2025-05-15 09:15:45
 * @Entity com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelDiscountLog
 */
public interface DistributionChannelDiscountLogMapper extends SuperMapper<DistributionChannelDiscountLog> {

    /**
     * 根据渠道ID获取 渠道折扣日志
     * @param channelId
     * @return
     */
    default List<DistributionChannelDiscountLog> queryListByChannelId(Long channelId) {
        return this.selectList(new LambdaQueryWrapper<DistributionChannelDiscountLog>().eq(DistributionChannelDiscountLog::getChannelId, channelId));
    }
}




