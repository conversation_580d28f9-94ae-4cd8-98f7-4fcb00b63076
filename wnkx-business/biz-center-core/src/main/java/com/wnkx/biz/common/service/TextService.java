package com.wnkx.biz.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.common.*;
import com.ruoyi.system.api.domain.entity.biz.common.Text;
import com.ruoyi.system.api.domain.vo.biz.common.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:29
 */
public interface TextService extends IService<Text> {

    /**
     * 文本列表
     */
    List<TextListVO> selectListByCondition(TextListDTO dto);

    /**
     * 帮助中心列表
     *
     * @param dto
     * @return
     */
    List<TextHelpListVO> selectHelpListByCondition(TextHelpListDTO dto);


    /**
     * 获取商家端 帮助中心数据
     * * @return
     */
    List<UserTextHelpListVO> selectUserHelpListByCondition();

    /**
     * 添加数据
     */
    void add(TextDTO dto);

    /**
     * 添加帮助中心文本
     *
     * @param dto
     */
    void addHelp(TextHelpDTO dto);

    /**
     * 编辑数据
     */
    void edit(TextDTO dto);

    /**
     * 编辑帮助中心文本
     *
     * @param dto
     */
    void editHelp(TextHelpDTO dto);

    /**
     * 修改状态
     * @param dto
     */
    void updateStatus(TextHelpStatusDTO dto);

    /**
     * 删除数据
     */
    void delete(Long id);

    /**
     * 查看数据详情
     */
    TextVO detail(Long id);

    /**
     * 查看帮助详情
     *
     * @param id
     * @return
     */
    TextHelpVO helpDetail(Long id);
}
