package com.wnkx.biz.channel.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.ChannelTypeEnum;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserChannelDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserChannelStatisticsDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserDetailListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.InviteListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.UserChannelStatisticsDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.BizUserChannel;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.biz.channel.MarketingChannel;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserListVO;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelInviteVO;
import com.ruoyi.system.api.domain.vo.biz.channel.UserChannelStatisticsVO;
import com.wnkx.biz.channel.mapper.BizUserChannelMapper;
import com.wnkx.biz.channel.mapper.BusinessChannelMapper;
import com.wnkx.biz.channel.mapper.DistributionChannelMapper;
import com.wnkx.biz.channel.mapper.MarketingChannelMapper;
import com.wnkx.biz.channel.service.IBizUserChannelService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【biz_user_channel(用户渠道信息表)】的数据库操作Service实现
 * @createDate 2024-09-24 17:02:16
 */
@Service
@RequiredArgsConstructor
public class BizUserChannelServiceImpl extends ServiceImpl<BizUserChannelMapper, BizUserChannel> implements IBizUserChannelService {
    private final DistributionChannelMapper channelMapper;
    private final MarketingChannelMapper marketingChannelMapper;
    private final BusinessChannelMapper businessChannelMapper;

    @Override
    public BizUserChannel initBizUserChannel(BizUserChannelDTO dto) {
        BizUserChannel bizUserChannel = BeanUtil.copyProperties(dto, BizUserChannel.class);
        baseMapper.insert(bizUserChannel);
        return bizUserChannel;
    }

    @Override
    public void updateBizUserChannel(BizUserChannelDTO dto) {
        baseMapper.updateByBizUserId(dto);
    }

    @Override
    public Map<Long, UserChannelStatisticsVO> getUserChannelStatisticsVO(UserChannelStatisticsDTO dto) {
        //获取渠道注册数量
        List<UserChannelStatisticsVO> registerList = baseMapper.getRegisterUserChannelStatistics(dto);
        List<UserChannelStatisticsVO> weChatList = baseMapper.getWeChatUserChannelStatistics(dto);
        Set<Long> channelIds = new HashSet<>();
        Map<Long, UserChannelStatisticsVO> resultMap = new HashMap<>();

        Map<Long, Long> registerMap = new HashMap<>();
        Map<Long, Long> weChatMap = new HashMap<>();
        Map<Long, Long> activateMap = new HashMap<>();
        if (CollUtil.isNotEmpty(registerList)) {
            channelIds.addAll(registerList.stream().filter(item -> ObjectUtil.isNotNull(item.getChannelId())).map(UserChannelStatisticsVO::getChannelId).collect(Collectors.toList()));
            registerMap.putAll(registerList.stream().collect(Collectors.toMap(UserChannelStatisticsVO::getChannelId, UserChannelStatisticsVO::getRegisterNum)));
        }
        if (CollUtil.isNotEmpty(weChatList)) {
            channelIds.addAll(weChatList.stream().filter(item -> ObjectUtil.isNotNull(item.getChannelId())).map(UserChannelStatisticsVO::getChannelId).collect(Collectors.toList()));
            weChatMap.putAll(weChatList.stream().filter(item -> ObjectUtil.isNotNull(item.getChannelId())).collect(Collectors.toMap(UserChannelStatisticsVO::getChannelId, UserChannelStatisticsVO::getWechatNum)));
            activateMap.putAll(weChatList.stream().filter(item -> ObjectUtil.isNotNull(item.getChannelId())).collect(Collectors.toMap(UserChannelStatisticsVO::getChannelId, UserChannelStatisticsVO::getActivateNum)));
        }
        if (CollUtil.isEmpty(weChatList)) {
            return resultMap;
        }
        for (Long channelId : channelIds) {
            Long registerNum = registerMap.get(channelId);
            Long weChatNum = weChatMap.get(channelId);
            resultMap.put(channelId, UserChannelStatisticsVO.builder()
                    .channelType(dto.getChannelType())
                    .registerNum(Optional.ofNullable(registerNum).orElse(0L))
                    .wechatNum(Optional.ofNullable(weChatNum).orElse(0L))
                    .build());
        }
        return resultMap;
    }

    @Override
    public UserChannelStatisticsVO getTotalStatisticsVO(BizUserChannelStatisticsDTO dto) {
        UserChannelStatisticsVO totalStatisticsVO = baseMapper.getTotalStatisticsVO(dto);
        if (ObjectUtil.isNotNull(totalStatisticsVO)){
            totalStatisticsVO.setChannelType(dto.getChannelType());
        }
        return totalStatisticsVO;
    }

    @Override
    public List<ChannelInviteVO> inviteList(InviteListDTO dto) {
        return baseMapper.inviteList(dto);
    }

    @Override
    public void activate(Long bizUserId) {
        baseMapper.activate(bizUserId);
    }

    @Override
    public List<BizUserDetailVO> getUserChannel(BizUserDetailListDTO dto) {
        //修改查询，根据商家id查询
        List<BizUserDetailVO> list = businessChannelMapper.selectBusinessChannelByBusinessId(dto);
        List<BizUserDetailVO> bizUserChannelList = baseMapper.selectUserChannelByUserId(dto);
        if (CollUtil.isNotEmpty(bizUserChannelList)){
            list.addAll(bizUserChannelList);
        }
        //市场
        List<Long> marketIds = list.stream().filter(li -> li.getWechatChannelType() != null && li.getWechatChannelType().equals(ChannelTypeEnum.MARKETING.getCode()))
                .map(BizUserDetailVO::getWechatChannelId).collect(Collectors.toList());

        //分销及裂变
        List<Long> distributionAndFissionIds = list.stream().filter(li -> li.getWechatChannelType() != null && (li.getWechatChannelType().equals(ChannelTypeEnum.DISTRIBUTION.getCode())
                || li.getWechatChannelType().equals(ChannelTypeEnum.FISSION.getCode())))
                .map(BizUserDetailVO::getWechatChannelId).collect(Collectors.toList());

        Map<Long, MarketingChannel> marketingMap = new HashMap<>();
        Map<Long, DistributionChannel> distributionChannelMap = new HashMap<>();
        if (CollUtil.isNotEmpty(marketIds)){
            List<MarketingChannel> marketList = marketingChannelMapper.getMarketList(marketIds);
            marketingMap = marketList.stream().collect(Collectors.toMap(MarketingChannel::getId, marketingChannel -> marketingChannel));
        }
        if (CollUtil.isNotEmpty(distributionAndFissionIds)){
            List<DistributionChannel> distributionList = channelMapper.getDistributionList(distributionAndFissionIds);
            distributionChannelMap = distributionList.stream().collect(Collectors.toMap(DistributionChannel::getId, distributionChannel -> distributionChannel));
        }



        //根据wechatChannelType判断类型
        for (BizUserDetailVO item : list) {
            Integer channelType = item.getWechatChannelType();
            Long channelId = item.getWechatChannelId();

            if (ChannelTypeEnum.MARKETING.getCode().equals(channelType)) {
                // 市场渠道
                Optional.ofNullable(marketingMap.get(channelId))
                        .map(MarketingChannel::getMarketingChannelName)
                        .ifPresent(item::setChannelName);
            } else if (ChannelTypeEnum.DISTRIBUTION.getCode().equals(channelType) || ChannelTypeEnum.FISSION.getCode().equals(channelType)) {
                // 分销渠道和裂变渠道
                Optional.ofNullable(distributionChannelMap.get(channelId))
                        .map(DistributionChannel::getChannelName)
                        .ifPresent(item::setChannelName);
            }
        }
        return list;
    }

    @Override
    public List<BizUserChannel> inviteRegisterList(Long distributionChannelId) {
        return baseMapper.inviteRegisterList(distributionChannelId);
    }

    @Override
    public List<BizUserListVO> inviteRegisterListWithUserInfo(Long distributionChannelId) {
        return baseMapper.inviteRegisterListWithUserInfo(distributionChannelId);
    }
}




