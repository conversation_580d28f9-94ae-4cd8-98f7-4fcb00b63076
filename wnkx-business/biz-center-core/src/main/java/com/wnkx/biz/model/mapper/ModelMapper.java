package com.wnkx.biz.model.mapper;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountCollectModelDTO;
import com.ruoyi.system.api.domain.dto.biz.model.*;
import com.ruoyi.system.api.domain.entity.Model;
import com.ruoyi.system.api.domain.vo.biz.business.user.ModelBlackListUserVO;
import com.ruoyi.system.api.domain.vo.biz.model.*;
import com.ruoyi.system.api.domain.vo.order.datastatistics.EnglishCustomerServiceDataVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 模特信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Mapper
public interface ModelMapper extends SuperMapper<Model>
{
    /**
     * 查询模特信息列表
     *
     * @param modelListDTO 模特列表入参
     * @return 模特信息集合
     */
    List<ModelVO> selectModelListByCondition(@Param("dto") ModelListDTO modelListDTO);

    /**
     * 查询模特家庭信息列表
     * @param modelListDTO
     * @return
     */
    List<ModelVO> selectModelFamilyListByCondition(@Param("dto") ModelListDTO modelListDTO);

    /**
     * 获取家庭成员数量
     * @return
     */
    List<ModelFamilyCountVO> getFamilyCount();

    /**
     * 根据家庭ID获取模卡信息
     * @param familyId
     * @return
     */
    List<ModelFamilyVO> selectModelFamilyByFamilyId(@Param("familyId") Long familyId);
    /**
     * 查询模特信息和关联信息
     *
     * @param id 模特信息主键
     * @return 模特信息
     */
    ModelVO selectModelAndRelevanceById(@Param("id") Long id);

    /**
     * 置顶模特
     */
    void top(Long id);

    /**
     * 查询不可接单的模特（内部请求）
     */
    List<Model> queryCannotAcceptList(@Param("modelId") Collection<Long> modelId);

    /**
     * * 获取拉黑模特数据
     * @param dto
     * @return
     */
    List<Model> queryBlacklistModel(@Param("dto") CannotAcceptModelDTO dto);

    /**
     * 获取模特被拉黑登录账号集合
     * @param modelId
     * @return
     */
    List<ModelBlackListUserVO> queryModelBlackListUserVO(Long modelId);

    /**
     * 添加预选模特列表
     */
    List<AddPreselectModelListVO> addPreselectModelList(@Param("dto") AddPreselectModelListDTO addPreselectModelListDTO);

    /**
     * 获取置顶数量
     */
    default Long getTopCount(Long id) {
        return this.selectCount(new LambdaQueryWrapper<Model>()
                .ne(ObjectUtil.isNotNull(id), Model::getId, id)
                .isNotNull(Model::getTopTime)
        );
    }

    /**
     * 模糊查询模特信息列表（模特名称、模特账号）
     */
    List<ModelVO> queryLikeModelList(@Param("dto") ModelListDTO modelListDTO);

    /**
     * 更新模特状态
     */
    default void updateModelStatus(ModelUpdateStatusDTO dto) {
        this.update(null, new LambdaUpdateWrapper<Model>()
                .eq(Model::getId, dto.getId())
                .set(Model::getStatus, dto.getStatus())
                .set(Model::getStatusTime, DateUtil.date())
                .set(Model::getStatusExplain, dto.getStatusExplain())
                .set(ObjectUtil.isNotNull(dto.getBottom()) && dto.getBottom(), Model::getTopTime, null)
                .set(Model::getCancelCooperationType, dto.getCancelCooperationType())
                .set(Model::getCancelCooperationSubType, dto.getCancelCooperationSubType())
                .set(Model::getSyncMsg, null)
        );
    }

    /**
     * 模特行程时间开始更新状态为行程中
     */
    List<Model> modelStartTravelList(@Param("beginOfDay")String beginOfDay);

    /**
     * 模特行程时间结束更新状态为正常
     */
    List<Model> modelEndTravelList(@Param("beginOfDay") String beginOfDay);

    /**
     * 运营端-编辑订单-更换模特列表
     */
    List<ModelVO> editOrderChangeModelList(@Param("dto") EditOrderChangeModelListDTO dto);

    /**
     * 查询模特信息列表（无需登录）
     */
    List<ModelListSimpleVO> referenceList(@Param("dto") BusinessAccountCollectModelDTO dto);

    /**
     * 根据家庭关系获取模特ID列表
     * @param modelFamilyRelationships
     * @return
     */
    List<Long> getModelIdByRelationships(@Param("modelFamilyRelationships") List<Integer> modelFamilyRelationships);

    /**
     * 校验模特名称是否存在
     */
    default boolean checkModelNameExist(Long id, String name) {
        return exists(new LambdaQueryWrapper<Model>()
                .ne(ObjectUtil.isNotNull(id), Model::getId, id)
                .eq(Model::getName, name)
        );
    }

    /**
     * 校验模特家庭是否存在
     * @param familyId
     * @return
     */
    default boolean checkModelFamilyExist(Long familyId) {
        return exists(new LambdaQueryWrapper<Model>()
                .eq(Model::getFamilyId, familyId)
        );
    }

    /**
     * 获取模特家庭成员数量
     * @param familyId
     * @return
     */
    default Long getModelFamilyMemberCount(Long familyId) {
        return this.selectCount(new LambdaQueryWrapper<Model>()
                .eq(Model::getFamilyId, familyId)
        );
    }

    /**
     * 修改排序
     */
    default void updateSort(ModelSortDTO modelSortDTO) {
        update(null, new LambdaUpdateWrapper<Model>()
                .eq(Model::getId, modelSortDTO.getId())
                .set(Model::getUpdateTime, new Date())
                .set(Model::getSort, modelSortDTO.getSort())
        );
    }

    /**
     * 根据模特ID清空家庭成员
     * @param modelIds
     */
    default void clearFamilyMember(List<Long> modelIds){
        update(null, new LambdaUpdateWrapper<Model>()
                .set(Model::getIsFamilyModel, StatusTypeEnum.NO.getCode())
                .set(Model::getIsInitiator, StatusTypeEnum.NO.getCode())
                .set(Model::getFamilyId, null)
                .set(Model::getJoinFamilyTime, null)
                .set(Model::getModelFamilyRelationship, null)
                .in(Model::getId, modelIds));
    }

    default List<Model> selectListByFamilyId(Long familyId) {
        return selectList(Wrappers.lambdaQuery(Model.class).eq(Model::getFamilyId, familyId));
    }


    /**
     * 更新模特 若字段为null 更新为null
     * PS:请注意字段值
     */
    void updateModelInfoFieldNullToNull(@Param("model") Model model);

    /**
     * 通过模特状态查询模特
     */
    default List<Model> selectListByStatus(Integer status) {
        return selectList(new LambdaQueryWrapper<Model>()
                .eq(Model::getStatus, status)
        );
    }

    /**
     * 客服数据-获取英文部关联模特数据
     */
    List<EnglishCustomerServiceDataVO> selectEnglishCustomerServiceModelData();
}
