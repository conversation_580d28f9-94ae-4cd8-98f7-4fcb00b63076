package com.wnkx.biz.page.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.dto.biz.page.PageConfigDTO;
import com.ruoyi.system.api.domain.dto.biz.page.PageConfigQueryListDTO;
import com.ruoyi.system.api.domain.dto.biz.page.PageConfigUpdateDTO;
import com.ruoyi.system.api.domain.entity.biz.page.HomePage;
import com.ruoyi.system.api.domain.entity.biz.page.PageConfig;
import com.ruoyi.system.api.domain.entity.biz.page.PageConfigInfo;
import com.ruoyi.system.api.domain.entity.biz.tag.Tag;
import com.ruoyi.system.api.domain.entity.order.casus.CasusGroup;
import com.ruoyi.system.api.domain.vo.biz.page.HomePageTagVO;
import com.ruoyi.system.api.domain.vo.biz.page.PageChooseCaseDetailVO;
import com.ruoyi.system.api.domain.vo.biz.page.PageChooseCaseVO;
import com.ruoyi.system.api.domain.vo.biz.page.PageConfigVO;
import com.wnkx.biz.casus.service.ICasusGroupService;
import com.wnkx.biz.page.mapper.PageConfigMapper;
import com.wnkx.biz.page.service.IPageConfigService;
import com.wnkx.biz.tag.service.ITagService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ruoyi.common.core.constant.CacheConstants.*;
import static com.ruoyi.common.core.constant.CommonConstant.QUICK_LINE_LESS_GROUP_NUM;

/**
 * <AUTHOR>
 * @description 针对表【page_config(页面配置表)】的数据库操作Service实现
 * @createDate 2024-08-19 16:43:20
 */
@Service
@RequiredArgsConstructor
public class PageConfigServiceImpl extends ServiceImpl<PageConfigMapper, PageConfig>
        implements IPageConfigService {
    private final RedisService redisService;

    private final ICasusGroupService casusGroupService;

    private final ITagService tagService;
    private String THE_DATA_DOES_NOT_EXIST = "不存在该数据~";

    @Override
    public List<PageConfigVO> queryList(PageConfigQueryListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("type", OrderByDto.DIRECTION.ASC);
        orderByDto.setField("create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        return baseMapper.queryList(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PageConfig save(PageConfigDTO dto) {
        PageConfig pageConfig = BeanUtil.copyProperties(dto, PageConfig.class);
        pageConfig.setContent(JSON.toJSONString(PageConfigInfo.builder().build()));
        baseMapper.insert(pageConfig);
        return pageConfig;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(PageConfigUpdateDTO dto) {
        PageConfig pageConfig = this.getById(dto.getId());
        Assert.notNull(pageConfig, THE_DATA_DOES_NOT_EXIST);
        //在配置类型为 首页类型、精选案例类型  nameA ！= nameB
        Assert.isFalse(PageConfigTypeEnum.HOME_PAGE.getCode().equals(pageConfig.getType()) && !dto.getName().equals(pageConfig.getName()), "页面类型为'首页'，不支持修改名称~");
        Assert.isFalse(PageConfigTypeEnum.CHOOSE_CASE.getCode().equals(pageConfig.getType()) && !dto.getName().equals(pageConfig.getName()), "页面类型为'精选案例'，不支持修改名称~");
        return baseMapper.update(dto);
    }

    @Override
    public int delete(Long id) {
        PageConfig pageConfig = this.getById(id);
        Assert.notNull(pageConfig, THE_DATA_DOES_NOT_EXIST);
        Assert.isTrue(PageConfigTypeEnum.OTHER.getCode().equals(pageConfig.getType()), "页面配置只能删除自定义配置页面~");
        return baseMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PageConfig saveChooseCase(PageConfigInfo dto) {
        PageConfig pageConfig = this.getById(dto.getId());
        Assert.notNull(pageConfig, THE_DATA_DOES_NOT_EXIST);
        dto.setPlatform(pageConfig.getPlatform());
        List<Long> groupIds = new ArrayList<>();
        this.getGroupIds(dto, groupIds);
        this.loadGroupName(dto, groupIds);

        String content = JSON.toJSONString(dto);
        PageConfig updatePageConfig = new PageConfig();
        updatePageConfig.setId(dto.getId());
        updatePageConfig.setContent(content);
        this.updateById(updatePageConfig);
        redisService.setCacheObject(getChooseCaseCacheKey(dto.getId()), dto, PAGE_CONFIG_TIMEOUT, TimeUnit.HOURS);
        return updatePageConfig;
    }

    @Override
    public PageConfig saveHomePage(HomePage dto) {
        PageConfig pageConfig = this.getById(dto.getId());
        Assert.notNull(pageConfig, THE_DATA_DOES_NOT_EXIST);

        List<Long> tagIds = new ArrayList<>();
        for (HomePageTagVO homePageTagVO : dto.getHomePageTagList()){
            if (StatusTypeEnum.YES.getCode().equals(homePageTagVO.getIsAll())){
                continue;
            }
            Assert.notNull(homePageTagVO.getTagId(), "标签Id不能为空");
            tagIds.add(homePageTagVO.getTagId());
        }
        if (CollUtil.isNotEmpty(tagIds)){
            List<Tag> tags = tagService.listByIds(tagIds);
            Assert.notEmpty(tags, "不存在对应标签数据~");
            Map<Long, Tag> tagMap = tags.stream().collect(Collectors.toMap(Tag::getId, p -> p));
            for (HomePageTagVO item : dto.getHomePageTagList()){
                if (StatusTypeEnum.YES.getCode().equals(item.getIsAll())){
                    continue;
                }
                Tag tag = tagMap.get(item.getTagId());
                Assert.notNull(tag, "不存在对应标签数据！");
                Assert.isTrue(StatusEnum.ENABLED.getCode().equals(tag.getStatus()), "标签[" + tag.getName() + "]状态无效~");
                Assert.isTrue(ModelTagEnum.CATEGORY.getCode().equals(tag.getCategoryId()), "标签[" + tag.getName() + "]非[" +ModelTagEnum.CATEGORY.getLabel() + "]类型");
                Assert.isTrue(tag.getParentId().compareTo(0L) == 0, "标签[" + tag.getName() + "]非一级标签");
                item.setTagName(tag.getName());
            }
        }

        String content = JSON.toJSONString(dto);
        PageConfig updatePageConfig = new PageConfig();
        updatePageConfig.setId(dto.getId());
        updatePageConfig.setContent(content);
        this.updateById(updatePageConfig);
        redisService.setCacheObject(getHomePageCacheKey(dto.getId()), dto, PAGE_CONFIG_TIMEOUT, TimeUnit.HOURS);
        return updatePageConfig;
    }

    public void loadGroupName(PageConfigInfo dto, List<Long> groupIds) {
        if (CollUtil.isNotEmpty(groupIds)){
            List<CasusGroup> casusGroups = casusGroupService.listByIds(groupIds);
            Assert.notEmpty(casusGroups, "不存在对应分组");
            Map<Long, CasusGroup> casusGroupMap = casusGroups.stream().collect(Collectors.toMap(CasusGroup::getId, p -> p));
            if (CollUtil.isNotEmpty(dto.getPageChooseCaseList())){
                for (PageChooseCaseVO item : dto.getPageChooseCaseList()){
                    for (PageChooseCaseDetailVO itemChild :item.getPageChooseCaseDetailList()){
                        if (SkipTypeEnum.GROUP.getCode().equals(itemChild.getSkipType())) {
                            CasusGroup casusGroup = casusGroupMap.get(Convert.toLong(itemChild.getSkipUri()));
                            Assert.notNull(casusGroup, "不存在对应分组~");
                            Assert.isTrue(casusGroup.getPlatform().equals(dto.getPlatform()), "分组平台不相同，无法添加数据，情选择正确分组~");
                            itemChild.setGroupName(casusGroup.getName());
                        }
                    }
                }
            }

            if (null != dto.getVideoShopWindow() && SkipTypeEnum.GROUP.getCode().equals(dto.getVideoShopWindow().getSkipType())) {
                CasusGroup casusGroup = casusGroupMap.get(Convert.toLong(dto.getVideoShopWindow().getSkipUri()));
                Assert.notNull(casusGroup, "不存在对应分组~");
                Assert.isTrue(casusGroup.getPlatform().equals(dto.getPlatform()), "分组平台不相同，无法添加数据，请选择正确分组~");
                dto.getVideoShopWindow().setGroupName(casusGroup.getName());
            }
        }
    }

    private void getGroupIds(PageConfigInfo dto, List<Long> groupIds) {

        if (CollUtil.isEmpty(dto.getPageChooseCaseList())){
            return;
        }
        for (PageChooseCaseVO item : dto.getPageChooseCaseList()){
            switch (PageModuleTypeEnum.getPageModuleTypeEnum(item.getModuleType())){
                case BANNER:
                    //海报类型
                    break;
                case QUICK_LINK_LESS:
                    Assert.isFalse(CollUtil.isNotEmpty(item.getPageChooseCaseDetailList()) && item.getPageChooseCaseDetailList().size() % QUICK_LINE_LESS_GROUP_NUM != 0, "金刚区需要时4个一组~");
                    break;
                case QUICK_LINK_MORE:
                    Assert.notNull(item.getLayoutType(), "瓷片区[布局类型]不能为空~");
                    Integer layoutNumber = LayoutTypeEnum.getQuickLinkMoreLayout(item.getLayoutType());
                    Assert.isTrue(CollUtil.isNotEmpty(item.getPageChooseCaseDetailList()), "瓷片区数据不能为空~");
                    Assert.isTrue(item.getPageChooseCaseDetailList().size() == layoutNumber, "瓷片区数量不对，请根据不同布局，配置瓷片区正确数量~");
                    break;
                default:
                    throw new ServiceException("选择类型错误 选择类型参数: " + item.getModuleType());
            }

            if (CollUtil.isNotEmpty(item.getPageChooseCaseDetailList())){
                for (PageChooseCaseDetailVO childItem :item.getPageChooseCaseDetailList()){
                    if (SkipTypeEnum.GROUP.getCode().equals(childItem.getSkipType())) {
                        groupIds.add(Convert.toLong(childItem.getSkipUri()));
                    }
                }
            }
        }
        if (null != dto.getVideoShopWindow() && SkipTypeEnum.GROUP.getCode().equals(dto.getVideoShopWindow().getSkipType())) {
            groupIds.add(Convert.toLong(dto.getVideoShopWindow().getSkipUri()));
        }
    }

    @Override
    public PageConfigInfo getInfo(Long id) {
        PageConfigInfo redisPageConfig = redisService.getCacheObject(getChooseCaseCacheKey(id));
        if (redisPageConfig != null) {
            return redisPageConfig;
        }
        PageConfig pageConfig = this.getById(id);
        Assert.notNull(pageConfig, "不存在对应页面配置信息~");
        Assert.notNull(PageConfigTypeEnum.CHOOSE_CASE.getCode().equals(pageConfig.getType()), "所查询数据非精选案例类型~");
        if (StringUtils.isNotBlank(pageConfig.getContent())) {
            PageConfigInfo pageConfigInfo = JSON.parseObject(pageConfig.getContent(), PageConfigInfo.class);
            pageConfigInfo.setId(id);
            pageConfigInfo.setPlatform(pageConfig.getPlatform());
            pageConfigInfo.setUserPerms(pageConfig.getUserPerms());
            redisService.setCacheObject(getChooseCaseCacheKey(pageConfig.getId()), pageConfigInfo, PAGE_CONFIG_TIMEOUT, TimeUnit.HOURS);
            return pageConfigInfo;
        }

        return PageConfigInfo.builder()
                .id(id)
                .platform(pageConfig.getPlatform())
                .userPerms(pageConfig.getUserPerms())
                .build();
    }

    @Override
    public HomePage getHomePage(Long id) {
        HomePage redisHomePage = redisService.getCacheObject(getHomePageCacheKey(id));
        if (redisHomePage != null){
            return redisHomePage;
        }
        PageConfig pageConfig = this.getById(id);
        Assert.notNull(pageConfig, "不存在对应页面配置信息~");
        Assert.notNull(PageConfigTypeEnum.HOME_PAGE.getCode().equals(pageConfig.getType()), "所查询数据非首页类型~");
        if (StringUtils.isNotBlank(pageConfig.getContent())){
            HomePage homePage = JSON.parseObject(pageConfig.getContent(), HomePage.class);
            homePage.setId(id);
            redisService.setCacheObject(getHomePageCacheKey(pageConfig.getId()), homePage, PAGE_CONFIG_TIMEOUT, TimeUnit.HOURS);

            return homePage;
        }
        return HomePage.builder()
                .id(id)
                .showSearch(StatusTypeEnum.NO.getCode())
                .build();
    }

    private String getChooseCaseCacheKey(Long id) {
        return PAGE_CHOOSE_CASE_CONFIG_KEY + id;
    }

    private String getHomePageCacheKey(Long id) {
        return PAGE_HOME_PAGE_CONFIG_KEY + id;
    }

}
