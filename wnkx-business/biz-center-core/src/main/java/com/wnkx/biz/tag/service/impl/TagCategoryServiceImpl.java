package com.wnkx.biz.tag.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.tag.TagCategory;
import com.wnkx.biz.tag.mapper.TagCategoryMapper;
import com.wnkx.biz.tag.service.ITagCategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 标签分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
@RequiredArgsConstructor
public class TagCategoryServiceImpl extends ServiceImpl<TagCategoryMapper, TagCategory> implements ITagCategoryService {
}

