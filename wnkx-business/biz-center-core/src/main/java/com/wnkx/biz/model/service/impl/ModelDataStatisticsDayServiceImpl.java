package com.wnkx.biz.model.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelDataStatisticsDay;
import com.wnkx.biz.model.mapper.ModelDataStatisticsDayMapper;
import com.wnkx.biz.model.service.ModelDataStatisticsDayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-05-08 09:55:16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ModelDataStatisticsDayServiceImpl extends ServiceImpl<ModelDataStatisticsDayMapper, ModelDataStatisticsDay> implements ModelDataStatisticsDayService {

    /**
     * 通过开始时间 结束时间 获取模特数据统计_每日记录表
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 模特数据统计_每日记录表
     */
    @Override
    public List<ModelDataStatisticsDay> getByWriteTimeBetween(Date beginTime, Date endTime) {
        return baseMapper.getByWriteTimeBetween(beginTime, endTime);
    }
}
