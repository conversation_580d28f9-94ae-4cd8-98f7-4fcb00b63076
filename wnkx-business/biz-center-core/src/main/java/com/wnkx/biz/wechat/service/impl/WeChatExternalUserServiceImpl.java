package com.wnkx.biz.wechat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatExternalUser;
import com.wnkx.biz.wechat.mapper.WeChatExternalUserMapper;
import com.wnkx.biz.wechat.service.IWeChatExternalUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 企业微信外部联系人信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@Service
@RequiredArgsConstructor
public class WeChatExternalUserServiceImpl extends ServiceImpl<WeChatExternalUserMapper, WeChatExternalUser> implements IWeChatExternalUserService {


    /**
     * 通过unionid查询WeChatExternalUser
     */
    @Override
    public WeChatExternalUser getOneByUnionid(String unionid) {
        return baseMapper.getOneByUnionid(unionid);
    }

    @Override
    public boolean hasRecordUser(String externalUserid) {
        return baseMapper.hasRecordUser(externalUserid);
    }

    @Override
    public void updateContactUserId(String externalUserId, String connectUserId) {
        baseMapper.updateContactUserId(externalUserId, connectUserId);
    }

    @Override
    public void updateContactUserInfo(String externalUserId, String connectUserId, String contractUserName) {
        baseMapper.updateContactUserInfo(externalUserId,connectUserId,contractUserName);
    }

    /**
     * 通过externalUserid将WeChatExternalUser设置为禁用
     */
    @Override
    public void disableWeChatExternalUser(String externalUserid) {
        baseMapper.disableWeChatExternalUser(externalUserid);
    }

    /**
     * 添加企业微信外部联系人信息 如果存在则设置为启用 并更新信息 （通过externalUserid和unionid判断）
     */
    @Override
    public WeChatExternalUser saveOrUpdateWeChatExternalUser(WeChatExternalUser weChatExternalUser) {
        //  判断是否存在
        WeChatExternalUser dbData = baseMapper.getOneByExternalUseridAndUnionid(weChatExternalUser.getExternalUserid(), weChatExternalUser.getUnionid());
        // 不存在则插入
        if (dbData == null) {
            baseMapper.insert(weChatExternalUser);
            return weChatExternalUser;
        }
        // 存在则更新基础信息并设置为启用
        dbData.setName(weChatExternalUser.getName());
        dbData.setPosition(weChatExternalUser.getPosition());
        dbData.setAvatar(weChatExternalUser.getAvatar());
        dbData.setCorpName(weChatExternalUser.getCorpName());
        dbData.setCorpFullName(weChatExternalUser.getCorpFullName());
        dbData.setType(weChatExternalUser.getType());
        dbData.setGender(weChatExternalUser.getGender());
        dbData.setStatus(StatusEnum.ENABLED.getCode());
        dbData.setUpdateTime(new Date());
        baseMapper.updateById(dbData);
        return dbData;
    }
}
