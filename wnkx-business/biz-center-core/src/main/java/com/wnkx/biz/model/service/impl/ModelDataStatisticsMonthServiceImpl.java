package com.wnkx.biz.model.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelDataStatisticsMonth;
import com.wnkx.biz.model.mapper.ModelDataStatisticsMonthMapper;
import com.wnkx.biz.model.service.ModelDataStatisticsMonthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 
 * @Date 2025-05-09 16:00:01 
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ModelDataStatisticsMonthServiceImpl extends ServiceImpl<ModelDataStatisticsMonthMapper, ModelDataStatisticsMonth> implements ModelDataStatisticsMonthService {


    /**
     * 通过记录时间 获取模特数据统计_每月记录表
     */
    @Override
    public ModelDataStatisticsMonth getByWriteTimeMonth(String date) {
        return baseMapper.getByWriteTimeMonth(date);
    }

    /**
     * 通过开始时间 结束时间 获取模特数据统计_每月记录表
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 模特数据统计_每月记录表
     */
    @Override
    public List<ModelDataStatisticsMonth> getByWriteTimeBetween(Date beginTime, Date endTime) {
        return baseMapper.getByWriteTimeBetween(beginTime, endTime);
    }
}
