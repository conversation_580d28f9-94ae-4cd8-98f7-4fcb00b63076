package com.wnkx.biz.model.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.Model;
import com.ruoyi.system.api.domain.entity.biz.model.ModelOustSnapShoot;
import com.wnkx.biz.model.mapper.ModelOustSnapShootMapper;
import com.wnkx.biz.model.service.ModelOustSnapShootService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2025-05-13 17:34:22
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ModelOustSnapShootServiceImpl extends ServiceImpl<ModelOustSnapShootMapper, ModelOustSnapShoot> implements ModelOustSnapShootService {


    /**
     * 删除当月模特快照数据
     */
    @Override
    public void removeMonthModelSnapShoot(Long modelId) {
        baseMapper.removeMonthModelSnapShoot(modelId);
    }

    /**
     * 新增或更新当月模特快照数据
     */
    @Override
    public void saveOrUpdateMonthModelSnapShoot(Model model) {
        ModelOustSnapShoot modelOustSnapShoot = new ModelOustSnapShoot();
        modelOustSnapShoot.setModelId(model.getId());
        modelOustSnapShoot.setCooperation(model.getCooperation());
        modelOustSnapShoot.setCommissionUnit(model.getCommissionUnit());
        modelOustSnapShoot.setCommission(model.getCommission());
        modelOustSnapShoot.setExtraData(JSONUtil.toJsonStr(model));
        saveOrUpdate(modelOustSnapShoot, new LambdaUpdateWrapper<ModelOustSnapShoot>().eq(ModelOustSnapShoot::getModelId, model.getId()).last("AND DATE_FORMAT(create_time,'%Y-%m')=DATE_FORMAT(NOW(),'%Y-%m')"));
    }
}
