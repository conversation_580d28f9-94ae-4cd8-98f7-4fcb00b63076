package com.wnkx.biz.wechat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatContactConfig;
import com.wnkx.biz.wechat.mapper.WeChatContactConfigMapper;
import com.wnkx.biz.wechat.service.WeChatContactConfigService;
import org.springframework.stereotype.Service;

/**
 * 企业微信通讯录配置Service业务层处理
 *  <AUTHOR>
 */
@Service
public class WeChatContactConfigServiceImpl extends ServiceImpl<WeChatContactConfigMapper, WeChatContactConfig>
    implements WeChatContactConfigService{

}




