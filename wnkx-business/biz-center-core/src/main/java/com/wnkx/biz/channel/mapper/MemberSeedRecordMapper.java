package com.wnkx.biz.channel.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelListDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecord;
import com.ruoyi.system.api.domain.vo.biz.channel.fission.FissionAmountStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.channel.fission.FissionCountStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.channel.fission.MemberSeedRecordVO;
import com.ruoyi.system.api.domain.vo.order.OrderMemberChannelListVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【member_seed_record(会员种草记录)】的数据库操作Mapper
 * @createDate 2025-05-15 09:15:45
 * @Entity com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecord
 */
public interface MemberSeedRecordMapper extends SuperMapper<MemberSeedRecord> {

    /**
     * 裂变渠道统计数据
     * @param channelId
     * @return
     */
    FissionAmountStatisticsVO getFissionStatisticsVO(@Param("channelId") Long channelId);

    /**
     * 裂变渠道数量统计数据
     * @param channelId
     * @return
     */
    FissionCountStatisticsVO getFissionCountStatisticsVO(@Param("channelId") Long channelId);

    /**
     * 会员种草记录列表
     * @param channelId
     * @return
     */
    List<MemberSeedRecordVO> memberSeedRecordList(@Param("channelId") Long channelId);

    /**
     * 可退金额
     * @param channelId
     * @return
     */
    BigDecimal canWithdrawAmount(@Param("channelId") Long channelId);

    /**
     * 渠道会员开通订单记录表
     * @param dto
     * @return
     */
    List<OrderMemberChannelListVO> queryOrderMemberChannelList(@Param("dto") OrderMemberChannelListDTO dto);


    /**
     * 获取可退种草记录
     * @param channelId
     * @return
     */
    default List<MemberSeedRecord> canWithdrawAmountList(Long channelId){
        return selectList(new LambdaQueryWrapper<MemberSeedRecord>()
                .eq(MemberSeedRecord::getChannelId, channelId)
                .in(MemberSeedRecord::getStatus, List.of(MemberSeedRecordStatusEnum.PENDING_WITHDRAWAL.getCode()
                        , MemberSeedRecordStatusEnum.REVIEW_REJECTED.getCode()
                        , MemberSeedRecordStatusEnum.TRANSFER_EXCEPTION.getCode()
                        ))
        );
    }

    /**
     * 根据商家ID获取种草记录
     * @param businessId
     * @return
     */
    default MemberSeedRecord getMemberSeedRecordByBusinessId(Long businessId) {
        return selectOne(new LambdaQueryWrapper<MemberSeedRecord>()
                .eq(MemberSeedRecord::getBusinessId, businessId)
        );
    }

    /**
     * 填充数据
     *
     * @param ids
     * @param status
     */
    default void setWithdrawalInfo(List<Long> ids, Integer status) {
        this.update(null, new LambdaUpdateWrapper<MemberSeedRecord>()
                .set(MemberSeedRecord::getStatus, status)
                .in(MemberSeedRecord::getId, ids));
    }

    /**
     * 获取待提现种草记录数量
     * @param channelId
     * @return
     */
    default Long getPendingWithdrawalCount(Long channelId){
        return selectCount(new LambdaQueryWrapper<MemberSeedRecord>()
                .eq(MemberSeedRecord::getChannelId, channelId)
                .eq(MemberSeedRecord::getStatus, MemberSeedRecordStatusEnum.PENDING_WITHDRAWAL.getCode())
        );
    }
}




