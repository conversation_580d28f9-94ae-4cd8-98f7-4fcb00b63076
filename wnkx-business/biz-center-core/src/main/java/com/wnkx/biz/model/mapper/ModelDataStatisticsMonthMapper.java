package com.wnkx.biz.model.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelDataStatisticsMonth;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-05-09 15:59:47
 */
@Mapper
public interface ModelDataStatisticsMonthMapper extends SuperMapper<ModelDataStatisticsMonth> {


    /**
     * 通过记录时间 获取模特数据统计_每月记录表
     */
    default ModelDataStatisticsMonth getByWriteTimeMonth(String date) {
        return selectOne(new LambdaQueryWrapper<ModelDataStatisticsMonth>()
                .last("WHERE DATE_FORMAT(write_time_begin,'%Y-%m') = '" + date + "'")
        );
    }

    /**
     * 通过开始时间 结束时间 获取模特数据统计_每月记录表
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 模特数据统计_每月记录表
     */
    default List<ModelDataStatisticsMonth> getByWriteTimeBetween(Date beginTime, Date endTime) {
        return selectList(new LambdaQueryWrapper<ModelDataStatisticsMonth>()
                .ge(ModelDataStatisticsMonth::getWriteTimeBegin, beginTime)
                .le(ModelDataStatisticsMonth::getWriteTimeEnd, endTime)
        );
    }
}
