package com.wnkx.biz.model.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelDataStatisticsMonth;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 
 * @Date 2025-05-09 15:59:53 
 */
public interface ModelDataStatisticsMonthService extends IService<ModelDataStatisticsMonth> {

    /**
     * 通过开始时间 结束时间 获取模特数据统计_每月记录表
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 模特数据统计_每月记录表
     */
    List<ModelDataStatisticsMonth> getByWriteTimeBetween(Date beginTime, Date endTime);

    /**
     * 通过记录时间 年月格式 获取模特数据统计_每月记录表
     */
    ModelDataStatisticsMonth getByWriteTimeMonth(String date);
}
