package com.wnkx.biz.model.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.model.ModelVideoResource;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 模特案例资源Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Mapper
public interface ModelVideoResourceMapper extends SuperMapper<ModelVideoResource> {

    /**
     * 通过模特ID删除数据
     */
    default void removeByModelId(Long modelId) {
        delete(new LambdaQueryWrapper<ModelVideoResource>()
                .eq(ModelVideoResource::getModelId, modelId)
        );
    }

    /**
     * 通过模特ID获取关联案例视频
     */
    default List<ModelVideoResource> selectListByModelIds(Collection<Long> modelIds) {
        return selectList(new LambdaQueryWrapper<ModelVideoResource>()
                .in(ModelVideoResource::getModelId, modelIds)
        );
    }
}
