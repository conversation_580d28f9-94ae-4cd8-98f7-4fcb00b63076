package com.wnkx.biz.model.service;

import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableListDTO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableDetailVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableListVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableServiceUserVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/4 18:03
 */
public interface ModelDataTableService {

    /**
     * 查询模特数据表列表
     */
    List<ModelDataTableListVO> selectModelDataTableListByCondition(ModelDataTableListDTO modelDataTableListDTO);

    /**
     * 从Redis缓存查询模特数据表列表
     */
    List<ModelDataTableListVO> selectModelDataTableListByConditionFromRedis(ModelDataTableListDTO modelDataTableListDTO);

    /**
     * 模特数据表列表-客服下拉框
     */
    List<ModelDataTableServiceUserVO> getServiceSelect();

    /**
     * 模特数据表列表-开发人下拉框
     */
    List<ModelDataTableServiceUserVO> getDeveloperSelect();

    /**
     * 模特详情
     */
    ModelDataTableDetailVO getModelDataTableDetailVO(Long modelId);

    /**
     * 获取模特数据表最后更新时间
     */
    String getModelDataTableLastUpdateTime(ModelDataTableListDTO modelDataTableListDTO);
}
