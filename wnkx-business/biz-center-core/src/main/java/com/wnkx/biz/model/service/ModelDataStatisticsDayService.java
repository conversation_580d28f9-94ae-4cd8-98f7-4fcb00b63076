package com.wnkx.biz.model.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelDataStatisticsDay;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-05-08 09:55:09
 */
public interface ModelDataStatisticsDayService extends IService<ModelDataStatisticsDay> {

    /**
     * 通过开始时间 结束时间 获取模特数据统计_每日记录表
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 模特数据统计_每日记录表
     */
    List<ModelDataStatisticsDay> getByWriteTimeBetween(Date beginTime, Date endTime);
}
