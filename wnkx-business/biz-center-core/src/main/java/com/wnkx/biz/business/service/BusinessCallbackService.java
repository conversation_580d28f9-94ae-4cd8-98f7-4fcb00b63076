package com.wnkx.biz.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessCallbackListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.ReturnVisitRecordDTO;
import com.ruoyi.system.api.domain.dto.biz.business.WriteReturnVisitDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessCallback;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessCallbackRecord;
import com.ruoyi.system.api.domain.vo.biz.business.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:28
 */
public interface BusinessCallbackService extends IService<BusinessCallback> {

    /**
     * 商家回访-回访列表
     */
    List<BusinessCallbackListVO> selectCallbackListByCondition(BusinessCallbackListDTO dto);

    /**
     * 获取商家回访记录
     */
    List<BusinessCallbackListVO> selectBusinessCallbackList(Long businessId);

    /**
     * 新增回访记录
     */
    void addBusinessCallback(BusinessCallback businessCallback);

    /**
     * 商家回访-标记回访
     */
    void markReturnVisit(Long id);

    /**
     * 商家回访-填写回访记录
     */
    void writeReturnVisit(WriteReturnVisitDTO dto);

    /**
     * 商家回访-填写回访记录-回访账号下拉框
     */
    List<BusinessAccountVO> writeReturnVisitAccount(Long businessId);

    /**
     * 商家回访-回访记录-回访账号下拉框
     */
    List<BusinessCallbackRecord> returnVisitAccount(Long businessId);

    /**
     * 商家回访-回访详情
     */
    BusinessCallbackVO getCallbackDetailById(Long id);

    /**
     * 商家回访-回访记录
     */
    List<BusinessCallbackRecordListVO> returnVisitRecord(ReturnVisitRecordDTO dto);

    /**
     * 商家回访-回访状态数统计
     */
    BusinessCallbackStatusCountVO returnVisitStatusCount();
}
