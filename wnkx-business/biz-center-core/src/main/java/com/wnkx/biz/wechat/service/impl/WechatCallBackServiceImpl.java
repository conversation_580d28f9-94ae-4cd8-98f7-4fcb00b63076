package com.wnkx.biz.wechat.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.TokenConstants;
import com.ruoyi.common.core.constant.WxConstant;
import com.ruoyi.common.core.enums.ChannelTypeEnum;
import com.ruoyi.common.core.enums.WxCallBackEventEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.wechat.AesException;
import com.ruoyi.common.core.utils.wechat.WXBizMsgCrypt;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.config.WorkWeChatConfig;
import com.ruoyi.system.api.domain.dto.biz.wechat.CustomerTagEdit;
import com.ruoyi.system.api.domain.dto.biz.wechat.ExternalUserInfoDTO;
import com.ruoyi.system.api.domain.dto.biz.wechat.WeWelcomeMsg;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatExternalUser;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelVO;
import com.wnkx.biz.config.ChannelMarketingProperties;
import com.wnkx.biz.core.ChannelCore;
import com.wnkx.biz.wechat.service.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * 企业微信回调处理
 *
 * <AUTHOR>
 * @date 2024/6/18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WechatCallBackServiceImpl implements WechatCallBackService {

    private final WechatService wechatService;
    private final WXBizMsgCrypt wxBizMsgCrypt;
    private final RedisService redisService;
    private final IWeChatExternalUserService weChatExternalUserService;
    private final WorkWechatApiService workWechatApiService;
    private final WorkWeChatConfig workWeChatConfig;
    private final WorkWechatRemarkService workWechatRemarkService;
    private final ChannelCore channelCore;
    private final ChannelMarketingProperties channelMarketingProperties;


    @SneakyThrows
    @Override
    public String verify(String sVerifyMsgSig, String sVerifyTimeStamp, String sVerifyNonce, String sVerifyEchoStr) {
        return wxBizMsgCrypt.verifyURL(sVerifyMsgSig, sVerifyTimeStamp, sVerifyNonce, sVerifyEchoStr);
    }

    @Override
    public void callBack(String sVerifyMsgSig, String sVerifyTimeStamp, String sVerifyNonce, String postData) {
        log.debug("企微回调原始密文{}", postData);
        final String decryptMsg;
        try {
            decryptMsg = wxBizMsgCrypt.DecryptMsg(sVerifyMsgSig, sVerifyTimeStamp, sVerifyNonce, postData);
        } catch (AesException e) {
            log.info(e.getMessage());
            throw new ServiceException("企微回调解密失败");
        }
        log.info("企微回调解密后{}", decryptMsg);
        final Map<String, Object> dataMap = XmlUtil.xmlToMap(decryptMsg);
        doProcess(dataMap);
    }

    private void doProcess(Map<String, Object> dataMap) {
        final String event = String.valueOf(dataMap.getOrDefault(WxConstant.EVENT, ""));
        final String eventType = String.valueOf(dataMap.getOrDefault(WxConstant.CHANGE_TYPE, ""));
        final String externalUserId = String.valueOf(dataMap.getOrDefault(WxConstant.EXTERNAL_USERID, ""));
        final String welcomeCode = String.valueOf(dataMap.getOrDefault(WxConstant.WELCOMECODE, ""));
//        被添加人Id
        final String userId = String.valueOf(dataMap.getOrDefault(WxConstant.UserID, ""));
        if (event.equals(WxCallBackEventEnum.CHANGE_EXTERNAL_CONTACT.getValue())) {
            if (eventType.equals(WxCallBackEventEnum.ADD_HALF_EXTERNAL_CONTACT.getValue())
                    || eventType.equals(WxCallBackEventEnum.ADD_EXTERNAL_CONTACT.getValue())) {
                final ExternalUserInfoDTO externalUserInfo = workWechatApiService.getExternalUserInfo(externalUserId);
                if (!isSpecUserEvent(externalUserInfo)) {
                    log.info("非指定用户事件{}", externalUserInfo);
                    return;
                }

                // 客服给客户打标签
                ChannelVO channelVO = markTag(externalUserInfo, userId);
                log.info("channelVo:{}", channelVO);
                // 更改备注
                workWechatRemarkService.changeCustomerRemark(externalUserInfo, channelVO, userId);
                sendWelComeMsg(welcomeCode, channelVO);

                //  存放we_chat_external_user表 判断是不是有重复数据  更新 除了unionid 和 externalUserid; 以外的所有数据
                log.info("添加外部联系人事件{}", externalUserInfo);
                WeChatExternalUser weChatExternalUser = weChatExternalUserService.saveOrUpdateWeChatExternalUser(BeanUtil.copyProperties(externalUserInfo.getExternalContact(), WeChatExternalUser.class));
                wechatService.updateAccount(externalUserId, externalUserInfo.getExternalContact());

                if (isJoinBusiness(externalUserInfo.getExternalContact().getUnionid())) {
                    log.info("添加子账号事件{}", externalUserInfo);
//                    更新添加人信息
                    weChatExternalUser.setConnectUserId(externalUserInfo.getExternalContact().getConnectUserId());
                    weChatExternalUser.setConnectUserName(externalUserInfo.getExternalContact().getConnectUserName());

                    //如果存在union - 员工信息代表>> 子账号添加
                    wechatService.addBusinessAccountApply(weChatExternalUser, null);
                    // 2.更新外部联系人表数据，更新添加人uid为当前uid
                    weChatExternalUserService.updateContactUserInfo(externalUserId,
                            externalUserInfo.getExternalContact().getConnectUserId(),
                            externalUserInfo.getExternalContact().getConnectUserName()
                    );
                    //删除redis
                    deleteJoinBusiness(externalUserInfo.getExternalContact().getUnionid());
                } else if (isJoinBusinessVerify(externalUserInfo.getExternalContact().getUnionid())) {
                    log.info("已有用户直接验证{}", externalUserInfo);
                    // 页面直接操作添加商家子账号,更新为登录成功，且更新一下添加人即可
                    weChatExternalUser.setConnectUserId(externalUserInfo.getExternalContact().getConnectUserId());
                    weChatExternalUser.setConnectUserName(externalUserInfo.getExternalContact().getConnectUserName());
                    wechatService.subAccountVerifySuccess(externalUserInfo.getExternalContact());
                } else if (isNoTicketCallBack(externalUserInfo.getExternalContact().getUnionid())) {
                    //回调非绑定数据 只处理数据库中存在的商家（不属于系统流程 自主添加企业微信）不做处理
                } else if (isBindCallBack(externalUserInfo.getExternalContact().getUnionid())) {
                    // 不更新用户信息 存储key到redis(换绑添加回调)
                    wechatService.workWechatRebind(externalUserInfo.getExternalContact());
                } else {
                    log.info("用户注册");
                    //注册的商家回调
                    externalUserInfo.getExternalContact().setConnectUserName(weChatExternalUser.getConnectUserName());
                    wechatService.addAccount(externalUserId, externalUserInfo.getExternalContact());
                    wechatService.workWechatLogin(externalUserInfo.getExternalContact().getUnionid());
                }
            }
        }
    }

    /**
     * 对于非指定用户，数据不落库
     *
     * @param externalUserInfo 外部联系人信息
     * @return 是否是指定用户
     */
    private boolean isSpecUserEvent(ExternalUserInfoDTO externalUserInfo) {
        if (ObjectUtil.isNull(externalUserInfo) || CollUtil.isEmpty(externalUserInfo.getFollow_user())) {
            return false;
        }
//        单用户判断
        if (externalUserInfo.getFollow_user().size() == 1) {
            int indexId = workWeChatConfig.getContactUser().indexOf(externalUserInfo.getFollow_user().get(0).getUserid());
            if (indexId > -1) {
                externalUserInfo.getExternalContact().setConnectUserId(externalUserInfo.getFollow_user().get(0).getUserid());
                externalUserInfo.getExternalContact().setConnectUserName(workWeChatConfig.getContactUserName().get(indexId));
                return true;
            }
            return false;
        }


//        多用户判断,1.最新加的是不是要处理
        ExternalUserInfoDTO.FollowUser followUser = getLatestFollowUser(externalUserInfo.getFollow_user());
        int indexId = workWeChatConfig.getContactUser().indexOf(followUser.getUserid());
        if (indexId > -1) {
            externalUserInfo.getExternalContact().setConnectUserId(followUser.getUserid());
            externalUserInfo.getExternalContact().setConnectUserName(workWeChatConfig.getContactUserName().get(indexId));
            return true;
        }

//        2.历史加的用户在/不在表里？ 初始化
        if (weChatExternalUserService.hasRecordUser(externalUserInfo.getExternalContact().getExternalUserid())) {
            return false;
        }
        for (ExternalUserInfoDTO.FollowUser item : externalUserInfo.getFollow_user()) {
            int indexOfUser = workWeChatConfig.getContactUser().indexOf(item.getUserid());
            if (indexOfUser > -1) {
                externalUserInfo.getExternalContact().setConnectUserId(item.getUserid());
                externalUserInfo.getExternalContact().setConnectUserName(workWeChatConfig.getContactUserName().get(indexOfUser));
                return true;
            }
        }
        return false;
    }

    public ExternalUserInfoDTO.FollowUser getLatestFollowUser(List<ExternalUserInfoDTO.FollowUser> followUser) {
        followUser.sort(Comparator.comparing(ExternalUserInfoDTO.FollowUser::getCreatetime).reversed());
        return followUser.get(0);
    }

    private void sendWelComeMsg(String welcomeCode, ChannelVO channelVO) {
        try {
            if (ObjectUtil.isNotNull(channelVO) && StrUtil.isNotBlank(channelVO.getDedicatedLinkCode()) && StrUtil.isNotBlank(welcomeCode)) {
                if (channelVO.getChannelName().equals("老客户")) {
                    workWechatApiService.sendWelcomeMsg(WeWelcomeMsg.builder()
                            .welcome_code(welcomeCode)
                            .text(WeWelcomeMsg.Text.builder().content(channelMarketingProperties.getOldBusinessWelcomeSms()).build())
                            .image(WeWelcomeMsg.Image.builder().pic_url(channelMarketingProperties.getWelcomeImage()).build())
                            .build());
                    return;
                }
//                分销市场渠道
                if (channelVO.getChannelType().equals(ChannelTypeEnum.MARKETING)) {
                    workWechatApiService.sendWelcomeMsg(WeWelcomeMsg.builder()
                            .welcome_code(welcomeCode)
                            .text(WeWelcomeMsg.Text.builder().content(String.format(channelMarketingProperties.getWelcomeSms(), channelMarketingProperties.getBusinessWebsitePrefixURL(), channelVO.getDedicatedLinkCode())).build())
                            .image(WeWelcomeMsg.Image.builder().pic_url(channelMarketingProperties.getWelcomeImage()).build())
                            .build());
                }
                if (channelVO.getChannelType().equals(ChannelTypeEnum.DISTRIBUTION) || channelVO.getChannelType().equals(ChannelTypeEnum.FISSION)) {
                    workWechatApiService.sendWelcomeMsg(WeWelcomeMsg.builder()
                            .welcome_code(welcomeCode)
                            .text(WeWelcomeMsg.Text.builder().content(String.format(channelMarketingProperties.getWelcomeSms(), channelMarketingProperties.getOfficialWebsitePrefixURL(), channelVO.getDedicatedLinkCode())).build())
                            .image(WeWelcomeMsg.Image.builder().pic_url(channelMarketingProperties.getWelcomeImage()).build())
                            .build());
                }
            }
            if (ObjectUtil.isNotNull(channelVO.getChannelType()) && channelVO.getChannelType().equals(ChannelTypeEnum.VIP)) {
                workWechatApiService.sendWelcomeMsg(WeWelcomeMsg.builder()
                        .welcome_code(welcomeCode)
                        .text(WeWelcomeMsg.Text.builder().content(String.format(channelMarketingProperties.getWelcomeSms(), channelMarketingProperties.getBusinessWebsitePrefixURL(), "v")).build())
                        .image(WeWelcomeMsg.Image.builder().pic_url(channelMarketingProperties.getWelcomeImage()).build())
                        .build());
                return;
            }
//            注意，配置时需要区分环境+对接人 不然会出现乱发的情况
            if (ObjectUtil.isNotNull(channelVO.getChannelType()) && channelVO.getChannelType().equals(ChannelTypeEnum.WEBSITE)) {
                if (channelMarketingProperties.getDebugger().equals(Boolean.TRUE.toString())) {
                    log.info("官网欢迎语{}", WeWelcomeMsg.Text.builder().content(String.format(channelMarketingProperties.getWelcomeSms(), channelMarketingProperties.getBusinessWebsitePrefixURL(), "w")).build());
                    return;
                }
                workWechatApiService.sendWelcomeMsg(WeWelcomeMsg.builder()
                        .welcome_code(welcomeCode)
                        .text(WeWelcomeMsg.Text.builder().content(String.format(channelMarketingProperties.getWelcomeSms(), channelMarketingProperties.getBusinessWebsitePrefixURL(), "w")).build())
                        .image(WeWelcomeMsg.Image.builder().pic_url(channelMarketingProperties.getWelcomeImage()).build())
                        .build());
            }
            if (ObjectUtil.isNotNull(channelVO.getChannelType()) && channelVO.getChannelType().equals(ChannelTypeEnum.BUSINESS)) {
                if (channelMarketingProperties.getDebugger().equals(Boolean.TRUE.toString())) {
                    log.info("子账号欢迎语{}", WeWelcomeMsg.Text.builder().content(String.format(channelMarketingProperties.getSubAccountWelcomeSms(), channelMarketingProperties.getBusinessWebsitePrefixURL(), "b")).build());
                    return;
                }
                workWechatApiService.sendWelcomeMsg(WeWelcomeMsg.builder()
                        .welcome_code(welcomeCode)
                        .text(WeWelcomeMsg.Text.builder().content(String.format(channelMarketingProperties.getSubAccountWelcomeSms(), channelMarketingProperties.getBusinessWebsitePrefixURL(), "b")).build())
                        .image(WeWelcomeMsg.Image.builder().pic_url(channelMarketingProperties.getWelcomeImage()).build())
                        .build());
            }
        } catch (Exception e) {
            log.error("发送欢迎语失败：{}", channelVO.toString(), e);
        }
    }

    private ChannelVO markTag(ExternalUserInfoDTO externalUserInfo, String userId) {
        if (CollUtil.isEmpty(externalUserInfo.getFollow_user())) {
            return null;
        }
        ChannelVO channelVo = new ChannelVO();

        for (ExternalUserInfoDTO.FollowUser item : externalUserInfo.getFollow_user()) {
            if (item.getUserid().equals(userId)) {
                if (StrUtil.isNotBlank(item.getState()) && workWeChatConfig.getContactUser().contains(item.getUserid())) {
                    channelVo = channelCore.getChannelByWeChatState(item.getState());
                    String tagId = channelVo.getTagId();
                    String channel = channelVo.getChannelType().equals(ChannelTypeEnum.BUSINESS) ?
                            item.getState() :
                            ChannelTypeEnum.PREFIX + channelVo.getChannelType().getLabel() + "-" + channelVo.getId();
                    externalUserInfo.getExternalContact().setChannel(channel);
                    externalUserInfo.getExternalContact().setChannelType(channelVo.getChannelType().getCode());
                    externalUserInfo.getExternalContact().setChannelId(channelVo.getId());
                    //打标签
                    if (StrUtil.isBlank(tagId) || ObjectUtil.isNull(channelVo.getChannelType())
                            || channelVo.getChannelType().equals(ChannelTypeEnum.NORMAL)
                            || channelVo.getChannelType().equals(ChannelTypeEnum.SEO)
                    ) {
                        continue;
                    }
                    List<String> userTags = new ArrayList<>();
                    userTags.add(tagId);
                    if (redisService.hasKey(CacheConstants.WECHAT_LOGIN_UNION_ID_IP_PREFIX + externalUserInfo.getExternalContact().getUnionid())) {
                        String regionTagId = redisService.getCacheObject(CacheConstants.WECHAT_LOGIN_UNION_ID_IP_PREFIX + externalUserInfo.getExternalContact().getUnionid());
                        if (StringUtils.isNotBlank(regionTagId)) {
                            userTags.add(regionTagId);
                        }
                    }
                    try {
                        workWechatApiService.markTag(CustomerTagEdit.builder()
                                .external_userid(externalUserInfo.getExternalContact().getExternalUserid())
                                .userid(item.getUserid())
                                .add_tag(userTags.toArray(new String[0]))
                                .build());

                    } catch (Exception e) {
                        log.error("打标签失败：", e);
                    }
                    break;
                }
            }
        }


        return channelVo;
    }


    private boolean isJoinBusinessVerify(String unionId) {
        if (redisService.hasKey(CacheConstants.WORK_WECHAT_LOGIN_STATUS_PREFIX + unionId)) {
            String ticket = redisService.getCacheObject(CacheConstants.WORK_WECHAT_LOGIN_STATUS_PREFIX + unionId);
            return ticket.contains(TokenConstants.SUB_ACCOUNT_VER) && ticket.startsWith(TokenConstants.VER);
        }
        return false;
    }

    /**
     * 判断是不是换绑请求
     *
     * @param unionId unionId
     */
    private boolean isBindCallBack(String unionId) {
        final String ticket = redisService.getCacheObject(CacheConstants.WORK_WECHAT_LOGIN_STATUS_PREFIX + unionId);
        return ticket.startsWith(TokenConstants.REB);
    }

    /**
     * 是否不存在ticket
     *
     * @param unionId
     * @return
     */
    private boolean isNoTicketCallBack(String unionId) {
        final String ticket = redisService.getCacheObject(CacheConstants.WORK_WECHAT_LOGIN_STATUS_PREFIX + unionId);
        return null == ticket;
    }

    /**
     * 是不是存在添加子账号如商家数据
     *
     * @param unionId
     * @return
     */
    private boolean isJoinBusiness(String unionId) {
        Object cacheObject = redisService.getCacheObject(CacheConstants.WECHAT_JOIN_BUSINESS_UNIONI_DTO_PREFIX + unionId);
        return null != cacheObject;
    }

    /**
     * 是不是存在添加子账号如商家数据
     *
     * @param unionId
     * @return
     */
    private void deleteJoinBusiness(String unionId) {
        redisService.deleteObject(CacheConstants.WECHAT_JOIN_BUSINESS_UNIONI_DTO_PREFIX + unionId);
    }
}

