package com.wnkx.biz.config;

import com.aliyun.dypnsapi20170525.Client;
import com.aliyun.teaopenapi.models.Config;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-01-06 15:32
 **/
@Configuration
@Slf4j
@Data
public class AliyunConfig {
    @Value("${aliyun.accessKeyID}")
    private String accessKeyID;
    @Value("${aliyun.accessKeySecret}")
    private String accessKeySecret;
    @Value("${aliyun.url}")
    private String url;
    @Value("${aliyun.origin}")
    private String origin;
    @Value("${aliyun.sceneCode}")
    private String sceneCode;
    @Value("${aliyun.endpoint}")
    private String endpoint;

    @Bean
    @SneakyThrows
    public Client client(){
        Config config = new Config().setAccessKeyId(accessKeyID).setAccessKeySecret(accessKeySecret);
        config.setEndpoint(endpoint);
        return new Client(config);
    }
}
