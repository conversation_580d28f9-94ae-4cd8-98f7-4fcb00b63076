package com.wnkx.biz.channel.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityEditDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelActivity;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【distribution_channel_activity(渠道活动表)】的数据库操作Mapper
 * @createDate 2024-12-02 14:27:22
 * @Entity com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelActivity
 */
public interface DistributionChannelActivityMapper extends SuperMapper<DistributionChannelActivity> {

    List<ChannelActivityDTO> channelList(@Param(value = "dto") ChannelActivityDTO dto);

    default Long saveChannelActivity(ChannelActivityEditDTO dto) {
        DistributionChannelActivity entity = new DistributionChannelActivity();
        entity.setActivityName(dto.getActivityName());
        entity.setDiscount(dto.getDiscount());
        entity.setStatus(dto.getStatus());
        entity.setStartTime(dto.getStartTime());
        entity.setEndTime(dto.getEndTime());
        entity.setCreateBy(SecurityUtils.getUsername());
        entity.setCreateById(SecurityUtils.getUserId());
        entity.setUpdateBy(SecurityUtils.getUsername());
        entity.setUpdateById(SecurityUtils.getUserId());
        entity.setType(dto.getType());
        insert(entity);
        return entity.getId();
    }

    default void editChannelActivity(ChannelActivityEditDTO dto) {
        DistributionChannelActivity entity = new DistributionChannelActivity();
        entity.setId(dto.getId());
        entity.setActivityName(dto.getActivityName());
        entity.setDiscount(dto.getDiscount());
        if (dto.getStatus().equals(1)||dto.getStatus().equals(3)){
            entity.setStatus(dto.getStatus());
        }
        entity.setStartTime(dto.getStartTime());
        entity.setEndTime(dto.getEndTime());
        entity.setUpdateBy(SecurityUtils.getUsername());
        entity.setUpdateById(SecurityUtils.getUserId());
        entity.setType(dto.getType());
        updateById(entity);
    }

    ChannelActivityEditDTO getChannelActivityBaseInfo(@Param(value = "ids") List<Long> ids);

    List<ChannelActivityDTO> getChannelActivityList(@Param(value = "channelId") Long channelId,
                                                    @Param(value = "startTime") Date startTime,
                                                    @Param(value = "endTime") Date endTime,
                                                    @Param(value = "activityId") Long activityId);

    BigDecimal getMinDiscount(@Param("id") Long id);

    default boolean checkNameStatus(String activityName) {
        return selectCount(new LambdaQueryWrapper<DistributionChannelActivity>()
                .eq(DistributionChannelActivity::getActivityName, activityName)) <= 0;
    }

    default boolean checkNameStatus(Long activityId, String activityName) {
        return selectCount(new LambdaQueryWrapper<DistributionChannelActivity>()
                .eq(DistributionChannelActivity::getActivityName, activityName)
                .notIn(DistributionChannelActivity::getId, activityId)
        ) <= 0;
    }

    Long getOtherChannelCount(Long id);

    Long getPartChannelCount(Long id);

    ChannelActivityDTO getLatestChannelDiscount(Long channelId);
}




