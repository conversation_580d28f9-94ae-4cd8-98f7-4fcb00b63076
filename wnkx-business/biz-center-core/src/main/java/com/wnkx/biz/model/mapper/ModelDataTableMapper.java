package com.wnkx.biz.model.mapper;

import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableListDTO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableDetailVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableListVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableServiceUserVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/4 18:04
 */
@Mapper
public interface ModelDataTableMapper {

    /**
     * 查询模特数据表列表
     */
    List<ModelDataTableListVO> selectModelDataTableListByCondition(@Param("dto") ModelDataTableListDTO modelDataTableListDTO);

    /**
     * 模特数据表列表-客服下拉框
     */
    List<ModelDataTableServiceUserVO> getServiceSelect();

    /**
     * 模特数据表列表-开发人下拉框
     */
    List<ModelDataTableServiceUserVO> getDeveloperSelect();

    /**
     * 模特详情
     */
    ModelDataTableDetailVO getModelDataTableDetailVO(@Param("modelId") Long modelId);
}
