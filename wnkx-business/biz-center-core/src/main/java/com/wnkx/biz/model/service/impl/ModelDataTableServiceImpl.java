package com.wnkx.biz.model.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.ModelDataTableOrderScheduledCountEnum;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.dto.SectionDTO;
import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableListDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.Model;
import com.ruoyi.system.api.domain.vo.biz.model.*;
import com.wnkx.biz.core.ModelDataTableListColumnEnum;
import com.wnkx.biz.model.mapper.ModelDataTableMapper;
import com.wnkx.biz.model.service.IModelService;
import com.wnkx.biz.model.service.ModelDataTableRemarkService;
import com.wnkx.biz.model.service.ModelDataTableService;
import com.wnkx.biz.remote.RemoteService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/4 18:04
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class ModelDataTableServiceImpl implements ModelDataTableService {

    private final ModelDataTableMapper baseMapper;
    private final RemoteService remoteService;
    private final IModelService modelService;
    private final ModelDataTableRemarkService modelDataTableRemarkService;
    private final RedisService redisService;
    private final ThreadPoolTaskExecutor asyncPoolTaskExecutor;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class CacheWrapper<T> {
        private T data;
        private long lastUpdateTime;
    }


    /**
     * 获取模特数据表最后更新时间
     */
    @Override
    public String getModelDataTableLastUpdateTime(ModelDataTableListDTO modelDataTableListDTO) {
        String cacheKey = getModelDataCacheKey(modelDataTableListDTO);
        CacheWrapper<List<ModelDataTableListVO>> cacheWrapper = redisService.getCacheObject(cacheKey);
        if (ObjectUtil.isNull(cacheWrapper)) {
            return DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN);
        }
        return DateUtil.format(DateUtil.date(cacheWrapper.getLastUpdateTime()), DatePattern.NORM_DATETIME_PATTERN);
    }

    /**
     * 模特详情
     */
    @Override
    public ModelDataTableDetailVO getModelDataTableDetailVO(Long modelId) {
        ModelDataTableDetailVO modelDataTableDetailVO = baseMapper.getModelDataTableDetailVO(modelId);

        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(List.of(modelDataTableDetailVO.getServiceId())).build());
        modelDataTableDetailVO.setServiceName(userMap.getOrDefault(modelDataTableDetailVO.getServiceId(), new UserVO()).getName());

        if (CharSequenceUtil.isNotBlank(modelDataTableDetailVO.getFamilyMemberIds())) {
            List<Model> familyMember = modelService.listByIds(CharSequenceUtil.split(modelDataTableDetailVO.getFamilyMemberIds(), StrPool.COMMA))
                    .stream().limit(9).collect(Collectors.toList());
            modelDataTableDetailVO.setFamilyMember(BeanUtil.copyToList(familyMember, ModelBaseVO.class));
        }

        return modelDataTableDetailVO;
    }

    /**
     * 模特数据表列表-开发人下拉框
     */
    @Override
    public List<ModelDataTableServiceUserVO> getDeveloperSelect() {
        List<ModelDataTableServiceUserVO> list = baseMapper.getDeveloperSelect();

        Set<Long> serviceIds = list.stream().map(ModelDataTableServiceUserVO::getServiceId).collect(Collectors.toSet());
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(serviceIds).build());

        for (ModelDataTableServiceUserVO modelDataTableServiceUserVO : list) {
            modelDataTableServiceUserVO.setServiceName(userMap.getOrDefault(modelDataTableServiceUserVO.getServiceId(), new UserVO()).getName());
        }
        return list;
    }

    /**
     * 模特数据表列表-客服下拉框
     */
    @Override
    public List<ModelDataTableServiceUserVO> getServiceSelect() {
        List<ModelDataTableServiceUserVO> list = baseMapper.getServiceSelect();

        Set<Long> serviceIds = list.stream().map(ModelDataTableServiceUserVO::getServiceId).collect(Collectors.toSet());
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(serviceIds).build());

        for (ModelDataTableServiceUserVO modelDataTableServiceUserVO : list) {
            modelDataTableServiceUserVO.setServiceName(userMap.getOrDefault(modelDataTableServiceUserVO.getServiceId(), new UserVO()).getName());
        }
        return list;
    }

    /**
     * 从Redis缓存查询模特数据表列表
     */
    @Override
    public List<ModelDataTableListVO> selectModelDataTableListByConditionFromRedis(ModelDataTableListDTO dto) {
        String cacheKey = getModelDataCacheKey(dto);
        CacheWrapper<List<ModelDataTableListVO>> redisCacheWrapper = redisService.getCacheObject(cacheKey);
        CacheWrapper<List<ModelDataTableListVO>> cacheWrapper;
        // 缓存不存在，同步加载并写入缓存
        cacheWrapper = Objects.requireNonNullElseGet(redisCacheWrapper, () -> refreshAndCacheModelDataTable(dto));

        long elapsedTime = System.currentTimeMillis() - cacheWrapper.getLastUpdateTime();
        if (elapsedTime > TimeUnit.MINUTES.toMillis(10)) {
            log.info("开始刷新模特数据表数据,旧数据最后更新时间为:{}", cacheWrapper.getLastUpdateTime());
            // 缓存过期，异步更新
            asyncPoolTaskExecutor.execute(() -> {
                try {
                    refreshAndCacheModelDataTable(dto);
                } catch (Exception e) {
                    log.error("异步刷新模特数据表缓存失败", e);
                }
            });
        }

        // 返回现有（已经超过有效时间的）的数据
        List<ModelDataTableListVO> modelDataTableListVOList = cacheWrapper.getData();

        selectModelDataTableListCommon(dto);

        List<Predicate<ModelDataTableListVO>> predicates = new ArrayList<>();

        if (CharSequenceUtil.isNotBlank(dto.getKeyword())) {
            predicates.add(vo -> dto.getKeyword().equals(vo.getName())
                    || dto.getKeyword().equals(vo.getAccount()));
        }

        if (ObjectUtil.isNotNull(dto.getCreateTimeBegin()) && ObjectUtil.isNotNull(dto.getCreateTimeEnd())) {
            predicates.add(vo -> vo.getCreateTime().compareTo(dto.getCreateTimeBegin()) >= 0
                    && vo.getCreateTime().compareTo(dto.getCreateTimeEnd()) <= 0);
        }

        if (CollUtil.isNotEmpty(dto.getOrderScheduledCountSections())) {
            predicates.add(vo -> {
                Long count = vo.getOrderScheduledCount();
                if (count == null) {
                    return false;
                }
                // 只要找到了一个区间满足 begin <= count <= end，就返回 true
                return dto.getOrderScheduledCountSections().stream().anyMatch(sec ->
                        BigDecimal.valueOf(count).compareTo(sec.getBegin()) >= 0
                                && BigDecimal.valueOf(count).compareTo(sec.getEnd()) <= 0
                );
            });
        }

        if (CollUtil.isNotEmpty(dto.getCooperations())) {
            predicates.add(vo -> dto.getCooperations().contains(vo.getCooperation()));
        }

        if (CollUtil.isNotEmpty(dto.getServiceIds())) {
            predicates.add(vo -> dto.getServiceIds().contains(vo.getServiceId()));
        }

        if (CollUtil.isNotEmpty(dto.getDeveloperIds())) {
            predicates.add(vo -> dto.getDeveloperIds().contains(vo.getDeveloperId()));
        }

        if (CollUtil.isNotEmpty(dto.getStatuses())) {
            predicates.add(vo -> dto.getStatuses().contains(vo.getStatus()));
        }

        if (CollUtil.isNotEmpty(dto.getPlatforms())) {
            predicates.add(vo -> CollUtil.containsAny(dto.getPlatforms(), CharSequenceUtil.split(vo.getPlatform(), ',', -1, true, Integer::valueOf)));
        }

        if (CollUtil.isNotEmpty(dto.getTypes())) {
            predicates.add(vo -> dto.getTypes().contains(vo.getType()));
        }

        if (CollUtil.isNotEmpty(dto.getNations())) {
            predicates.add(vo -> dto.getNations().contains(vo.getNation()));
        }

        if (CollUtil.isNotEmpty(dto.getSexes())) {
            predicates.add(vo -> dto.getSexes().contains(vo.getSex()));
        }

        if (CollUtil.isNotEmpty(dto.getAgeGroups())) {
            predicates.add(vo -> dto.getAgeGroups().contains(vo.getAgeGroup()));
        }

        if (ObjectUtil.isNotNull(dto.getIsWarningModel())) {
            if (1 == dto.getIsWarningModel()) {
                predicates.add(vo ->
                        1 == vo.getCaseCountEarlyWarn()
                                || 1 == vo.getTagCountEarlyWarn()
                                || 1 == vo.getCategoryCountEarlyWarn()
                                || 1 == vo.getIntentionOrderRateEarlyWarn()
                                || 1 == vo.getPreSelectOrderRateEarlyWarn()
                                || 1 == vo.getSelfSelectOrderRateEarlyWarn()
                                || 1 == vo.getRejectOrderRateEarlyWarn()
                                || 1 == vo.getOrderScheduledCountEarlyWarn()
                                || 1 == vo.getWaitPictureCountEarlyWarn()
                                || 1 == vo.getOvertimeRateEarlyWarn()
                                || 1 == vo.getAfterSaleRateEarlyWarn()
                                || 1 == vo.getDropCountEarlyWarn()
                                || 1 == vo.getCancelCountEarlyWarn()
                                || 1 == vo.getReturnCountEarlyWarn()
                );
            } else {
                predicates.add(vo ->
                        0 == vo.getCaseCountEarlyWarn()
                                && 0 == vo.getTagCountEarlyWarn()
                                && 0 == vo.getCategoryCountEarlyWarn()
                                && 0 == vo.getIntentionOrderRateEarlyWarn()
                                && 0 == vo.getPreSelectOrderRateEarlyWarn()
                                && 0 == vo.getSelfSelectOrderRateEarlyWarn()
                                && 0 == vo.getRejectOrderRateEarlyWarn()
                                && 0 == vo.getOrderScheduledCountEarlyWarn()
                                && 0 == vo.getWaitPictureCountEarlyWarn()
                                && 0 == vo.getOvertimeRateEarlyWarn()
                                && 0 == vo.getAfterSaleRateEarlyWarn()
                                && 0 == vo.getDropCountEarlyWarn()
                                && 0 == vo.getCancelCountEarlyWarn()
                                && 0 == vo.getReturnCountEarlyWarn()
                );
            }
        }

        Predicate<ModelDataTableListVO> composite = predicates.stream()
                .reduce(x -> true, Predicate::and);

        if (ObjectUtil.isNotNull(dto.getSortColumn()) && CharSequenceUtil.isNotBlank(dto.getSortWay())) {
            ModelDataTableListColumnEnum modelDataTableListColumnEnum = ModelDataTableListColumnEnum.getByCode(dto.getSortColumn());
            Comparator<ModelDataTableListVO> comparator = Comparator.comparing((Function) modelDataTableListColumnEnum.getExtractor(), Comparator.nullsFirst(Comparator.naturalOrder()));

            if (dto.getSortWay().equals(OrderByDto.DIRECTION.DESC.value())) {
                comparator = comparator.reversed();
            }
            return modelDataTableListVOList.stream()
                    .filter(composite)
                    .sorted(comparator)
                    .collect(Collectors.toList());
        }

        return modelDataTableListVOList.stream()
                .filter(composite)
                .collect(Collectors.toList());
    }

    private CacheWrapper<List<ModelDataTableListVO>> refreshAndCacheModelDataTable(ModelDataTableListDTO dto) {
        String lockKey = CacheConstants.MODEL_DATA_TABLE_CACHE_LOCK_KEY;
        if (ObjectUtil.isNotNull(dto.getDataScopeTimeBegin()) && ObjectUtil.isNotNull(dto.getDataScopeTimeEnd())) {
            lockKey = lockKey + "_" + DateUtil.format(dto.getDataScopeTimeBegin(), DatePattern.PURE_DATE_PATTERN) + "_" + DateUtil.format(dto.getDataScopeTimeEnd(), DatePattern.PURE_DATE_PATTERN);
        }
        boolean lockAcquired = false;
        try {
            lockAcquired = redisService.getLock(lockKey, CacheConstants.MODEL_DATA_TABLE_CACHE_LOCK_KEY_EXPIRE);
            Assert.isTrue(lockAcquired, "数据更新中，请稍后重试~");

            ModelDataTableListDTO cacheQueryDto = new ModelDataTableListDTO();
            cacheQueryDto.setCustomColumns(ModelDataTableListColumnEnum.getAllColumns());
            cacheQueryDto.setDataScopeTimeBegin(dto.getDataScopeTimeBegin());
            cacheQueryDto.setDataScopeTimeEnd(dto.getDataScopeTimeEnd());
            List<ModelDataTableListVO> modelDataTableListVOList = selectModelDataTableListByCondition(cacheQueryDto);

            String cacheKey = getModelDataCacheKey(dto);
            CacheWrapper<List<ModelDataTableListVO>> newCacheWrapper = new CacheWrapper<>(modelDataTableListVOList, System.currentTimeMillis());
            redisService.setCacheObject(cacheKey, newCacheWrapper);
            return newCacheWrapper;
        } finally {
            if (lockAcquired) {
                redisService.releaseLock(lockKey);
            }
        }
    }

    /**
     * 查询模特数据表列表
     */
    @Override
    public List<ModelDataTableListVO> selectModelDataTableListByCondition(ModelDataTableListDTO dto) {
        if (ObjectUtil.isNotNull(dto.getCustomColumns()) && CollUtil.containsAny(dto.getCustomColumns(), ModelDataTableListColumnEnum.getOrderColumns())) {
            List<ModelDataTableListVO> remoteList = remoteService.selectModelDataTableListByCondition(dto);
            dto.setRemoteListJson(JSONUtil.toJsonStr(remoteList));
        }

        selectModelDataTableListCommon(dto);

        List<ModelDataTableListVO> modelDataTableListVOList = baseMapper.selectModelDataTableListByCondition(dto);
        if (CollUtil.isEmpty(modelDataTableListVOList)) {
            return modelDataTableListVOList;
        }

        Set<Long> serviceIds = modelDataTableListVOList.stream().map(ModelDataTableListVO::getServiceId).collect(Collectors.toSet());
        Set<Long> developerIds = modelDataTableListVOList.stream().map(ModelDataTableListVO::getDeveloperId).collect(Collectors.toSet());
        serviceIds.addAll(developerIds);
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(serviceIds).build());

        List<ModelDataTableRemarkVO> modelDataTableRemarkVOS = modelDataTableRemarkService.selectModelDataTableRemarkListByModelIds(modelDataTableListVOList.stream().map(ModelDataTableListVO::getId).collect(Collectors.toList()));
        Map<Long, List<ModelDataTableRemarkVO>> modelDataTableRemarkVOMap = modelDataTableRemarkVOS.stream().collect(Collectors.groupingBy(ModelDataTableRemarkVO::getModelId));

        for (ModelDataTableListVO modelDataTableListVO : modelDataTableListVOList) {
            modelDataTableListVO.setServiceName(userMap.getOrDefault(modelDataTableListVO.getServiceId(), new UserVO()).getName());
            modelDataTableListVO.setDeveloperName(userMap.getOrDefault(modelDataTableListVO.getDeveloperId(), new UserVO()).getName());

            List<ModelDataTableRemarkVO> remarkVOS = modelDataTableRemarkVOMap.get(modelDataTableListVO.getId());
            if (CollUtil.isNotEmpty(remarkVOS)) {
                remarkVOS = remarkVOS.stream()
                        .sorted(Comparator.comparing(ModelDataTableRemarkVO::getCreateTime).reversed())
                        .limit(6)
                        .collect(Collectors.toList());
                modelDataTableListVO.setRemarks(remarkVOS);
            }

            modelDataTableListVO.echo();
        }
        return modelDataTableListVOList;
    }

    private void selectModelDataTableListCommon(ModelDataTableListDTO modelDataTableListDTO) {
        if (CollUtil.isNotEmpty(modelDataTableListDTO.getOrderScheduledCounts())) {
            List<SectionDTO> sectionDTOS = new ArrayList<>();
            for (Integer orderScheduledCount : modelDataTableListDTO.getOrderScheduledCounts()) {
                sectionDTOS.add(SectionDTO
                        .builder()
                        .begin(ModelDataTableOrderScheduledCountEnum.getByCode(orderScheduledCount).getBegin())
                        .end(ModelDataTableOrderScheduledCountEnum.getByCode(orderScheduledCount).getEnd())
                        .build());
            }
            modelDataTableListDTO.setOrderScheduledCountSections(sectionDTOS);
        }

        if (ObjectUtil.isNotNull(modelDataTableListDTO.getSortColumn())) {
            modelDataTableListDTO.setSortColumnStr(ModelDataTableListColumnEnum.getColumnSortSqlNameByCode(modelDataTableListDTO.getSortColumn()));
            if (CharSequenceUtil.isNotBlank(modelDataTableListDTO.getSortWay())) {
                modelDataTableListDTO.setSortWay(ModelDataTableListColumnEnum.getColumnSortWayByCode(modelDataTableListDTO.getSortColumn(), modelDataTableListDTO.getSortWay()));
            }
        }
    }

    /**
     * 获取模特数据表缓存键
     * @param dto dto
     * @return 缓存键
     */
    private String getModelDataCacheKey(ModelDataTableListDTO dto) {
        String cacheKey = CacheConstants.MODEL_DATA_TABLE_CACHE_KEY;
        if (ObjectUtil.isNotNull(dto.getDataScopeTimeBegin()) && ObjectUtil.isNotNull(dto.getDataScopeTimeEnd())) {
            cacheKey = cacheKey + "_" + DateUtil.format(dto.getDataScopeTimeBegin(), DatePattern.PURE_DATE_PATTERN) + "_" + DateUtil.format(dto.getDataScopeTimeEnd(), DatePattern.PURE_DATE_PATTERN);
        }
        return cacheKey;
    }
}
