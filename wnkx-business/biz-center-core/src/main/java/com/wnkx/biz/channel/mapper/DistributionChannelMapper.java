package com.wnkx.biz.channel.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.enums.ChannelTypeEnum;
import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionChancelStatisticsDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionChannelListDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.vo.SysUserVO;
import com.ruoyi.system.api.domain.vo.biz.channel.*;
import com.ruoyi.system.api.domain.vo.order.OrderMemberChannelStatisticVO;
import com.wnkx.db.mapper.SuperMapper;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 针对表【distribution_channel(分销渠道信息表)】的数据库操作Mapper
 * @createDate 2024-09-24 17:02:50
 * @Entity com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel
 */
public interface DistributionChannelMapper extends SuperMapper<DistributionChannel> {

    /**
     * 根据分销渠道Id获取分销渠道详情
     *
     * @param distributionChannelId
     * @return
     */
    DistributionChannelDetailVO getByDistributionChannelId(Long distributionChannelId);

    /**
     * 获取分销渠道列表
     *
     * @param dto
     * @return
     */
    List<DistributionChannelVO> queryList(DistributionChannelListDTO dto);

    /**
     * 获取裂变渠道列表
     *
     * @param dto
     * @return
     */
    List<FissionChannelVO> queryFissionChannelList(DistributionChannelListDTO dto);

    /**
     * 缝一下渠道统计
     *
     * @param dto
     * @return
     */
    DistributionChannelStatisticsVO distributionChannelStatistics(DistributionChannelListDTO dto);

    /**
     * 裂变渠道统计
     *
     * @param dto
     * @return
     */
    DistributionChannelStatisticsVO fissionChannelStatistics(DistributionChannelListDTO dto);

    /**
     * 获取裂变渠道统计数据
     *
     * @param dto
     * @return
     */
    OrderMemberChannelStatisticVO getMemberSeedRecordStatisticTotal(DistributionChancelStatisticsDTO dto);

    /**
     * 根据渠道名称获取账号数据
     *
     * @param channelName
     * @return
     */
    default DistributionChannel getByChannelName(String channelName) {
        return this.selectOne(new LambdaQueryWrapper<DistributionChannel>()
                .eq(DistributionChannel::getChannelName, channelName));

    }

    /**
     * 根据专属code获取分销账号信息
     *
     * @param linkCode
     * @return
     */
    default DistributionChannel getByLinkCode(String linkCode) {
        return this.selectOne(new LambdaQueryWrapper<DistributionChannel>()
                .eq(DistributionChannel::getDedicatedLinkCode, linkCode));
    }

    /**
     * 获取渠道注册数据列表
     *
     * @param id
     * @return
     */
    List<ChannelInviteVO> getChannelRegisterList(Long id);

    /**
     * 根据种草码获取分销账号信息
     *
     * @param seedCode
     * @return
     */
    default DistributionChannel getBySeedCode(String seedCode) {
        return this.selectOne(new LambdaQueryWrapper<DistributionChannel>()
                .eq(DistributionChannel::getSeedCode, seedCode.toUpperCase()));
    }

    /**
     * 根据种草官ID获取分销账号信息
     *
     * @param seedId
     * @return
     */
    default DistributionChannel getBySeedId(String seedId) {
        return this.selectOne(new LambdaQueryWrapper<DistributionChannel>()
                .eq(DistributionChannel::getSeedId, seedId));
    }

    /**
     * 修改渠道信息
     *
     * @param distributionChannel
     */
    default void updateFillNullById(DistributionChannel distributionChannel) {
        this.update(null, new LambdaUpdateWrapper<DistributionChannel>()
                .set(DistributionChannel::getChannelName, distributionChannel.getChannelName())
                .set(DistributionChannel::getPosterName, distributionChannel.getPosterName())
                .set(StringUtils.isNotBlank(distributionChannel.getPassword()), DistributionChannel::getPassword, distributionChannel.getPassword())
                .set(DistributionChannel::getRemark, Optional.ofNullable(distributionChannel.getRemark()).orElse(""))
                .set(DistributionChannel::getBrokeRage, distributionChannel.getBrokeRage())
                .set(DistributionChannel::getSettleDiscountType, distributionChannel.getSettleDiscountType())
                .set(StringUtils.isNotBlank(distributionChannel.getPhone()), DistributionChannel::getPhone, distributionChannel.getPhone())
                .set(StringUtils.isBlank(distributionChannel.getPhone()), DistributionChannel::getPhone, "")
                .set(DistributionChannel::getUpdateBy, distributionChannel.getUpdateBy())
                .set(DistributionChannel::getUpdateId, distributionChannel.getUpdateId())
                .eq(DistributionChannel::getId, distributionChannel.getId()));
    }

    /**
     * 根据种草码获取有效 分销数据
     *
     * @param seedCode
     * @return
     */
    DistributionChannelVO getDistributionChannelBySeedCode(String seedCode);

    /**
     * 获取分销渠道创建人列表
     *
     * @param name
     * @return
     */
    List<SysUserVO> createUserList(String name);

    /**
     * 根据种草渠道列表获取分销渠道
     *
     * @param seedCodes
     * @return
     */
    default List<DistributionChannel> queryListBySeedCodes(Collection<String> seedCodes) {
        return this.selectList(new LambdaQueryWrapper<DistributionChannel>()
                .in(DistributionChannel::getSeedCode, seedCodes));

    }

//    /**
//     * 获取裂变分销返佣比例
//     * @return
//     */
//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    default BigDecimal getFissionBrokeRage(){
//        DistributionChannel distributionChannel = this.selectOne(new LambdaQueryWrapper<DistributionChannel>()
//                .eq(DistributionChannel::getChannelType, ChannelTypeEnum.FISSION.getCode())
//                .last("limit 1")
//        );
//        return Optional.ofNullable(distributionChannel).map(DistributionChannel::getBrokeRage).orElse(new BigDecimal(25));
//    }

    /**
     * 获取一个列表渠道数据
     *
     * @return
     */
    default DistributionChannel getFissionOne() {
        return this.selectOne(new LambdaQueryWrapper<DistributionChannel>()
                .eq(DistributionChannel::getChannelType, ChannelTypeEnum.FISSION.getCode())
                .last("limit 1")
        );
    }

//    /**
//     * 修改裂变渠道返佣
//     *
//     * @param brokeRage
//     */
//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    default void updateFissionBrokeRage(BigDecimal brokeRage) {
//        this.update(null, new LambdaUpdateWrapper<DistributionChannel>()
//                .set(DistributionChannel::getBrokeRage, brokeRage)
//                .eq(DistributionChannel::getChannelType, ChannelTypeEnum.FISSION.getCode()));
//
//    }

    /**
     * 修改裂变渠道 结算佣金类型
     *
     * @param brokeRage
     * @param getSettleDiscountType
     */
    default void updateFissionSettle(BigDecimal brokeRage, Integer getSettleDiscountType) {
        this.update(null, new LambdaUpdateWrapper<DistributionChannel>()
                .set(DistributionChannel::getBrokeRage, brokeRage)
                .set(DistributionChannel::getSettleDiscountType, getSettleDiscountType)
                .eq(DistributionChannel::getChannelType, ChannelTypeEnum.FISSION.getCode()));

    }

    /**
     * 获取正常分销渠道数量*
     *
     * @param dto
     * @return
     */
    default Long distributionChannelNum(DistributionChancelStatisticsDTO dto) {
        return this.selectCount(new LambdaQueryWrapper<DistributionChannel>()
                .eq(DistributionChannel::getStatus, StatusEnum.ENABLED.getCode())
                .eq(DistributionChannel::getChannelType, dto.getChannelType())
                .between(ObjectUtil.isNotNull(dto.getStartTime()) && ObjectUtil.isNotNull(dto.getEndTime()), DistributionChannel::getCreateTime, dto.getStartTime(), dto.getEndTime())
        );
    }

    /**
     * 获取正常的分销渠道列表
     */
    default List<DistributionChannel> selectNormalDistributionChannelList() {
        return selectList(new LambdaQueryWrapper<DistributionChannel>()
                .eq(DistributionChannel::getStatus, StatusEnum.ENABLED.getCode())
                .eq(DistributionChannel::getChannelType, ChannelTypeEnum.DISTRIBUTION.getCode())
                .orderByDesc(DistributionChannel::getCreateTime)
        );
    }

    /**
     * 获取正常分销渠道数量*
     *
     * @return
     */
    default Long getChannelCount() {
        return this.selectCount(new LambdaQueryWrapper<DistributionChannel>()
                .eq(DistributionChannel::getChannelType, ChannelTypeEnum.DISTRIBUTION.getCode())
                .eq(DistributionChannel::getStatus, StatusEnum.ENABLED.getCode())
        );
    }

    default String getQrcodeByDedicatedLinkCode(String dedicatedLinkCode) {
        DistributionChannel distributionChannel = selectOne(new LambdaQueryWrapper<DistributionChannel>()
                .eq(DistributionChannel::getDedicatedLinkCode, dedicatedLinkCode));
        return ObjectUtil.isNotNull(distributionChannel) ? distributionChannel.getWeChatUrl() : "";
    }

    /**
     * 根据业务用户id获取分销渠道
     *
     * @param bizUserId
     * @return
     */
    default DistributionChannel getChannelByBizUserId(Long bizUserId) {
        return selectOne(new LambdaQueryWrapper<DistributionChannel>()
                .eq(DistributionChannel::getBizUserId, bizUserId)
        );
    }

    default List<DistributionChannel> getDistributionList(List<Long> ids) {
        return selectList(Wrappers.lambdaQuery(DistributionChannel.class).in(DistributionChannel::getId, ids));
    }

}




