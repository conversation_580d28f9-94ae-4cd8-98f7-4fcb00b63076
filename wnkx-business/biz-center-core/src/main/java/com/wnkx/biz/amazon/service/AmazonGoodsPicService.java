package com.wnkx.biz.amazon.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.biz.amazon.AmazonGoodsPic;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/8 11:47
 */
public interface AmazonGoodsPicService extends IService<AmazonGoodsPic> {

    /**
     * 添加数据
     */
    void addAmazonGoodsPic(List<AmazonGoodsPic> amazonGoodsPics);

    /**
     * 查询数据
     */
    List<AmazonGoodsPic> selectByCondition(List<String> goodsIds);
}
