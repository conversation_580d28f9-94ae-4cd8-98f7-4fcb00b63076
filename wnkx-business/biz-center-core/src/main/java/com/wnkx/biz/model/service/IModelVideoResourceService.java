package com.wnkx.biz.model.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.model.ModelResourceDTO;
import com.ruoyi.system.api.domain.entity.biz.model.ModelVideoResource;

import java.util.Collection;
import java.util.List;

/**
 * 模特案例资源Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface IModelVideoResourceService extends IService<ModelVideoResource>
{

    /**
     * 新增图片视频资源
     * 
     * @param modelResourcesDTO 图片视频资源
     */
    void insertResource(List<ModelResourceDTO> modelResourcesDTO);

    /**
     * 通过模特ID获取关联案例视频
     */
    List<ModelVideoResource> selectListByModelIds(Collection<Long> modelIds);

    /**
     * 通过模特ID删除数据
     */
    void removeByModelId(Long modelId);
}
