package com.wnkx.biz.model.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.system.api.domain.entity.biz.model.ModelTag;
import com.ruoyi.system.api.domain.entity.biz.tag.Tag;
import com.ruoyi.system.api.domain.vo.biz.model.ModelTagVO;
import com.wnkx.biz.model.mapper.ModelTagMapper;
import com.wnkx.biz.model.service.IModelTagService;
import com.wnkx.biz.tag.service.ITagService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 模特分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
@RequiredArgsConstructor
public class ModelTagServiceImpl extends ServiceImpl<ModelTagMapper, ModelTag> implements IModelTagService {


    /**
     * 通过标签ID删除标签表
     */
    @Override
    public void removeByTagId(Long tagId) {
        baseMapper.removeByTagId(tagId);
    }

    @Override
    public Map<Long, List<ModelTagVO>> getModelTagVOMap(Collection<Long> modelId) {
        List<ModelTag> modelTags = selectListByModelId(modelId);
        if (CollUtil.isEmpty(modelTags)) {
            return Collections.emptyMap();
        }

        Set<Long> dictId = modelTags.stream().map(ModelTag::getDictId).collect(Collectors.toSet());
        List<Tag> tags = SpringUtils.getBean(ITagService.class).listByIds(dictId);
        Map<Long, Tag> tagMap = tags.stream().collect(Collectors.toMap(Tag::getId, p -> p));

        return modelTags.stream().map(item -> {
            ModelTagVO modelTagVO = new ModelTagVO();
            modelTagVO.setModelId(item.getModelId());

            if (ObjectUtil.isNotNull(item.getDictId())) {
                Tag tag = tagMap.getOrDefault(item.getDictId(), new Tag());
                modelTagVO.setId(tag.getId());
                modelTagVO.setName(tag.getName());
                modelTagVO.setCategoryId(tag.getCategoryId());
            } else {
                modelTagVO.setName(item.getDictName());
                modelTagVO.setCategoryId(item.getDictCategoryId());
            }
            return modelTagVO;
        }).collect(Collectors.groupingBy(ModelTagVO::getModelId));
    }

    /**
     * 获取标签使用次数
     *
     * @param dictId 标签id
     * @return 标签使用次数
     */
    @Override
    public Long getDictUseCount(Long dictId) {
        return baseMapper.getDictUseCount(dictId);
    }

    /**
     * 通过模特id删除标签表
     */
    @Override
    public void removeByModelId(List<Long> modelId) {
        baseMapper.removeByModelId(modelId);
    }

    /**
     * 根据模特id获取模特关联标签
     *
     * @param modelId 模特id
     * @return 模特关联标签
     */
    @Override
    public List<ModelTag> selectListByModelId(Collection<Long> modelId) {
        if (CollUtil.isEmpty(modelId)) return new ArrayList<>();
        return baseMapper.selectListByModelId(modelId);
    }
}
