package com.wnkx.biz.model.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.biz.model.ModelTravel;

import java.util.Collection;
import java.util.List;

/**
 * 模特行程Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface IModelTravelService extends IService<ModelTravel>
{
    /**
     * 通过模特id获取模特行程
     */
    List<ModelTravel> selectModelTravelByModelIds(Collection<Long> modelId);

    /**
     * 通过模特id删除行程时间表
     */
    void removeByModelId(Long modelId);

    /**
     * 通过模特id删除行程时间表
     */
    void removeByModelIds(List<Long> modelIds);

    /**
     * 添加行程时间记录
     */
    void insertModelTravel(ModelTravel modelTravel);

    /**
     * 批量
     * @param modelTravel
     */
    void insertBatchModelTravel(List<ModelTravel> modelTravel);
}
