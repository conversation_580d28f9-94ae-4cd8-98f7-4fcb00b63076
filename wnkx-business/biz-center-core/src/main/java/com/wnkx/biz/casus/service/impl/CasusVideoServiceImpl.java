package com.wnkx.biz.casus.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.casus.CasusVideoQueryDTO;
import com.ruoyi.system.api.domain.dto.order.casus.CasusVideoSaveDTO;
import com.ruoyi.system.api.domain.dto.order.casus.CasusVideoUpdateDTO;
import com.ruoyi.system.api.domain.entity.order.casus.CasusVideo;
import com.ruoyi.system.api.domain.entity.order.casus.GroupVideo;
import com.wnkx.biz.casus.mapper.CasusVideoMapper;
import com.wnkx.biz.casus.mapper.GroupVideoMapper;
import com.wnkx.biz.casus.service.ICasusVideoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【casus_video(案例视频表)】的数据库操作Service实现
* @createDate 2024-07-10 13:35:30
*/
@Service
@RequiredArgsConstructor
@Slf4j
public class CasusVideoServiceImpl extends ServiceImpl<CasusVideoMapper, CasusVideo>
implements ICasusVideoService {
    private final GroupVideoMapper groupVideoMapper;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(CasusVideoSaveDTO dto) {
        Assert.isTrue(UserTypeConstants.MANAGER.equals(SecurityUtils.getLoginUserType()), "只有运营端才能创建案例视频！");
        CasusVideo casusVideo = BeanUtil.copyProperties(dto, CasusVideo.class);
        casusVideo.setCreateBy(SecurityUtils.getUsername());
        casusVideo.setCreateId(SecurityUtils.getUserId());
        casusVideo.setCreateTime(new Date());
        casusVideo.setUpdateBy(SecurityUtils.getUsername());
        casusVideo.setUpdateId(SecurityUtils.getUserId());
        casusVideo.setUpdateTime(new Date());
        baseMapper.insert(casusVideo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(CasusVideoUpdateDTO dto) {
        Assert.isTrue(UserTypeConstants.MANAGER.equals(SecurityUtils.getLoginUserType()), "只有运营端才能修改案例视频！");
        CasusVideo casusVideo = BeanUtil.copyProperties(dto, CasusVideo.class);
        casusVideo.setUpdateBy(SecurityUtils.getUsername());
        casusVideo.setUpdateId(SecurityUtils.getUserId());
        casusVideo.setUpdateTime(new Date());

        this.updateById(casusVideo);
    }

    @Override
    public List<CasusVideo> queryList(CasusVideoQueryDTO dto) {
        return this.lambdaQuery()
                .like(StringUtils.isNotEmpty(dto.getName()), CasusVideo::getName, dto.getName())
                .eq(StringUtils.isNotNull(dto.getPlatform()), CasusVideo::getPlatform, dto.getPlatform())
                .eq(StringUtils.isNotNull(dto.getId()), CasusVideo::getId, dto.getId())
                .in(StringUtils.isNotEmpty(dto.getIds()), CasusVideo::getId, dto.getIds())
                .list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        Assert.isTrue(UserTypeConstants.MANAGER.equals(SecurityUtils.getLoginUserType()), "只有运营端才能修改案例视频！");
        this.removeById(id);
        groupVideoMapper.delete(new LambdaQueryWrapper<GroupVideo>()
                .eq(GroupVideo::getVideoId, id));
    }
}
