package com.wnkx.biz.casus.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.casus.*;
import com.ruoyi.system.api.domain.entity.order.casus.CasusGroup;
import com.ruoyi.system.api.domain.entity.order.casus.GroupVideo;
import com.ruoyi.system.api.domain.vo.order.casus.CasusGroupVO;
import com.ruoyi.system.api.domain.vo.order.casus.GroupAddVideoVO;
import com.ruoyi.system.api.domain.vo.order.casus.GroupVideoVO;
import com.wnkx.biz.casus.mapper.CasusGroupMapper;
import com.wnkx.biz.casus.service.ICasusGroupService;
import com.wnkx.biz.casus.service.IGroupVideoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【casus_group(案例分组表)】的数据库操作Service实现
* @createDate 2024-07-10 13:35:30
*/
@Service
@RequiredArgsConstructor
@Slf4j
public class CasusGroupServiceImpl extends ServiceImpl<CasusGroupMapper, CasusGroup>
implements ICasusGroupService {
    private final IGroupVideoService groupVideoService;
    @Override
    public List<CasusGroupVO> queryList(CasusGroupDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("cg.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        return baseMapper.queryList(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(CasusGroupSaveDTO dto) {
        Assert.isTrue(UserTypeConstants.MANAGER.equals(SecurityUtils.getLoginUserType()), "只有运营端才能创建案例分组！");
        CasusGroup casusGroup = BeanUtil.copyProperties(dto, CasusGroup.class);
        casusGroup.setCreateBy(SecurityUtils.getUsername());
        casusGroup.setCreateId(SecurityUtils.getUserId());
        casusGroup.setCreateTime(new Date());
        casusGroup.setUpdateBy(SecurityUtils.getUsername());
        casusGroup.setUpdateId(SecurityUtils.getUserId());
        casusGroup.setUpdateTime(new Date());
        baseMapper.insert(casusGroup);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(CasusGroupUpdateDTO dto) {
        Assert.isTrue(UserTypeConstants.MANAGER.equals(SecurityUtils.getLoginUserType()), "只有运营端才能修改案例分组！");
        CasusGroup casusGroup = BeanUtil.copyProperties(dto, CasusGroup.class);
        casusGroup.setUpdateBy(SecurityUtils.getUsername());
        casusGroup.setUpdateId(SecurityUtils.getUserId());
        casusGroup.setUpdateTime(new Date());
        this.updateById(casusGroup);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        Assert.isTrue(UserTypeConstants.MANAGER.equals(SecurityUtils.getLoginUserType()), "只有运营端才能修改案例分组！");
        //todo 需要校验是否在页面中配置
        this.removeById(id);
    }

    @Override
    public List<GroupVideoVO> queryGroupsVideoList(GroupsVideoListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("gv.sort" , OrderByDto.DIRECTION.ASC);
        orderByDto.setField("gv.create_time" , OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        return baseMapper.queryGroupsVideoList(dto);
    }

    @Override
    public List<GroupAddVideoVO> queryAddGroupsVideoList(GroupAddVideoDTO dto) {
        //获取分组数据
        CasusGroup casusGroup = this.getById(dto.getGroupId());
        Assert.notNull(casusGroup, "分组数据不能为空");
        dto.setPlatform(casusGroup.getPlatform());

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("cv.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        return baseMapper.queryAddGroupsVideoList(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addGroupVideo(GroupVideoDTO dto) {
        Assert.isTrue(UserTypeConstants.MANAGER.equals(SecurityUtils.getLoginUserType()), "只有运营端才能添加分组视频！");
        List<GroupVideo> groupVideos= groupVideoService.lambdaQuery()
                .eq(GroupVideo::getGroupId, dto.getGroupId())
                .list();
        List<Long> videoIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(groupVideos)){
            videoIds = groupVideos.stream().map(GroupVideo::getVideoId).collect(Collectors.toList());
        }
        List<GroupVideo> groupVideoList = new ArrayList<>();
        Date date = new Date();
        for (Long videoId : dto.getVideoIds()){
            Assert.isFalse(videoIds.contains(videoId), "已存在视频不能再次添加");
            GroupVideo item = new GroupVideo();
            item.setGroupId(dto.getGroupId());
            item.setVideoId(videoId);
            item.setCreateBy(SecurityUtils.getUsername());
            item.setCreateId(SecurityUtils.getUserId());
            item.setCreateTime(date);
            groupVideoList.add(item);
        }
        groupVideoService.saveBatch(groupVideoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeGroupVideo(GroupVideoDTO dto) {
        Assert.isTrue(UserTypeConstants.MANAGER.equals(SecurityUtils.getLoginUserType()), "只有运营端才能移除分组视频！");
        groupVideoService.remove(new LambdaQueryWrapper<GroupVideo>()
                .eq(GroupVideo::getGroupId, dto.getGroupId())
                .in(GroupVideo::getVideoId, dto.getVideoIds())
        );
    }

    @Override
    public void updateGroupSort(UpdateGroupSortDTO dto) {
        Assert.isTrue(UserTypeConstants.MANAGER.equals(SecurityUtils.getLoginUserType()), "只有运营端才能移除更新分组视频排序！");
        groupVideoService.update(new LambdaUpdateWrapper<GroupVideo>()
                .set(GroupVideo::getSort, dto.getSort())
                .eq(GroupVideo::getGroupId, dto.getGroupId())
                .eq(GroupVideo::getVideoId, dto.getVideoId()));
    }
}
