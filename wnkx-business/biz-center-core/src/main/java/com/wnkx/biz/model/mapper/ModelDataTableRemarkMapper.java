package com.wnkx.biz.model.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.model.ModelDataTableRemark;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-07-08 20:35:34
 */
@Mapper
public interface ModelDataTableRemarkMapper extends SuperMapper<ModelDataTableRemark> {

    /**
     * 查询备注
     */
    default List<ModelDataTableRemark> selectModelDataTableRemarkListByModelId(Long modelId) {
        return selectList(new LambdaQueryWrapper<ModelDataTableRemark>()
                .eq(ModelDataTableRemark::getModelId, modelId)
                .orderByDesc(ModelDataTableRemark::getCreateTime)
        );
    }

    /**
     * 查询备注
     */
    default List<ModelDataTableRemark> selectModelDataTableRemarkListByModelIds(List<Long> modelIds) {
        return selectList(new LambdaQueryWrapper<ModelDataTableRemark>()
                .in(ModelDataTableRemark::getModelId, modelIds)
                .orderByDesc(ModelDataTableRemark::getCreateTime)
        );
    }
}
