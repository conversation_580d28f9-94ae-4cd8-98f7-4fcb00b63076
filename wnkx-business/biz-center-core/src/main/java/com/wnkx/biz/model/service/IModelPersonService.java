package com.wnkx.biz.model.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.entity.ModelPerson;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedOustModelCountInfo;

import java.util.Collection;
import java.util.List;

/**
 * 模特对接人员Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface IModelPersonService extends IService<ModelPerson>
{
    /**
     * 通过模特id获取模特关联运营
     */
    List<ModelPerson> selectListByModelId(Collection<Long> modelId);

    /**
     * 模特列表-获取关联人员下拉框（运营端）
     */
    List<UserVO> modelPersonsSelect(String keyword);

    /**
     * 通过模特id删除关联人员表
     */
    void removeByModelId(List<Long> modelId);

    /**
     * 通过运营id获取模特
     */
    List<ModelPerson> selectListByUserIds(Collection<Long> userIds);

    /**
     * 获取英文部客服 淘汰模特数
     */
    List<CustomerServiceAddedOustModelCountInfo> getEnglishCustomerServiceAddedModelCounts(String date);
}
