package com.wnkx.biz.model.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.model.ModelTravel;
import com.wnkx.biz.model.mapper.ModelTravelMapper;
import com.wnkx.biz.model.service.IModelTravelService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 模特行程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
@RequiredArgsConstructor
public class ModelTravelServiceImpl extends ServiceImpl<ModelTravelMapper, ModelTravel> implements IModelTravelService
{

    /**
     * 添加行程时间记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertModelTravel(ModelTravel modelTravel) {
        //  先删后加
        removeByModelId(modelTravel.getModelId());
        baseMapper.insert(modelTravel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertBatchModelTravel(List<ModelTravel> modelTravel) {
        //  先删后加
        baseMapper.delete(Wrappers.lambdaQuery(ModelTravel.class).in(ModelTravel::getModelId, modelTravel.stream().map(ModelTravel::getModelId).collect(Collectors.toList())));
        baseMapper.saveBatch(modelTravel);
    }

    /**
     * 通过模特id删除行程时间表
     */
    @Override
    public void removeByModelIds(List<Long> modelIds) {
        if (CollUtil.isEmpty(modelIds)) {
            return;
        }
        baseMapper.removeByModelIds(modelIds);
    }

    /**
     * 通过模特id删除行程时间表
     */
    @Override
    public void removeByModelId(Long modelId) {
        removeByModelIds(List.of(modelId));
    }

    /**
     * 通过模特id获取模特行程
     */
    @Override
    public List<ModelTravel> selectModelTravelByModelIds(Collection<Long> modelId) {
        return baseMapper.selectModelTravelByModelIds(modelId);
    }

}
