package com.wnkx.biz.model.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.common.BizResource;
import com.wnkx.biz.model.mapper.BizResourceMapper;
import com.wnkx.biz.model.service.BizResourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/4 14:55
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BizResourceServiceImpl extends ServiceImpl<BizResourceMapper, BizResource> implements BizResourceService {


    /**
     * 通过资源id查询数据(map)
     */
    @Override
    public Map<Long, BizResource> getResourceMapByIds(Collection<Long> resourceIds) {
        if (CollUtil.isEmpty(resourceIds)) {
            return Collections.emptyMap();
        }
        List<BizResource> bizResources = baseMapper.selectBatchIds(resourceIds);
        return bizResources.stream().collect(Collectors.toMap(BizResource::getId, Function.identity()));
    }

    /**
     * 通过资源id查询数据
     */
    @Override
    public List<BizResource> selectListByIds(Collection<Long> resourceIds) {
        if (CollUtil.isEmpty(resourceIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectBatchIds(resourceIds);
    }

    @Override
    public List<String> selectObjectKeyListByIds(Collection<Long> resourceIds) {
        if (CollUtil.isEmpty(resourceIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectBatchIds(resourceIds).stream().map(BizResource::getObjectKey).collect(Collectors.toList());
    }

    /**
     * 批量新增图片资源
     *
     * @param bizResources 图片资源
     */
    @Override
    public List<BizResource> saveBatchBizResource(List<BizResource> bizResources) {
        baseMapper.saveBatch(bizResources);
        return bizResources;
    }

    /**
     * 批量新增数据
     */
    @Override
    public List<Long> saveBatchBizResourceReturnIds(Collection<String> objectKeys) {
        if (CollUtil.isEmpty(objectKeys)) {
            return Collections.emptyList();
        }
        List<BizResource> bizResources = objectKeys.stream().map(objectKey ->
                BizResource.builder().objectKey(objectKey).build()
        ).collect(Collectors.toList());
        baseMapper.saveBatch(bizResources);
        return bizResources.stream().map(BizResource::getId).collect(Collectors.toList());
    }
}
