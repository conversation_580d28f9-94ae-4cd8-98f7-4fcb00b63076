package com.wnkx.biz.amazon.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.amazon.AmazonGoodsPic;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/8 11:47
 */
@Mapper
public interface AmazonGoodsPicMapper extends SuperMapper<AmazonGoodsPic> {

    /**
     * 查询数据
     */
    default List<AmazonGoodsPic> selectByCondition(List<String> goodsIds) {
        return this.selectList(new LambdaQueryWrapper<AmazonGoodsPic>()
                .in(AmazonGoodsPic::getGoodsId, goodsIds)
        );
    }

    /**
     * 添加数据
     */
    void insertAmazonGoodsPic(AmazonGoodsPic amazonGoodsPic);
}
