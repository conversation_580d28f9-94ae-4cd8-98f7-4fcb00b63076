package com.wnkx.biz.wechat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.WechatContactUserTagEnum;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatContactUserConfig;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatContactUserConfigInfo;
import com.wnkx.biz.wechat.service.WeChatContactUserConfigInfoService;
import com.wnkx.biz.wechat.service.WeChatContactUserConfigService;
import com.wnkx.biz.wechat.mapper.WeChatContactUserConfigMapper;
import com.wnkx.biz.wechat.service.WorkWechatApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【we_chat_contact_user_config(对外联系人配置表)】的数据库操作Service实现
 * @createDate 2025-03-05 13:46:50
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WeChatContactUserConfigServiceImpl extends ServiceImpl<WeChatContactUserConfigMapper, WeChatContactUserConfig>
        implements WeChatContactUserConfigService {
    private final WeChatContactUserConfigInfoService weChatContactUserConfigInfoService;
    private final WorkWechatApiService workWechatApiService;

    @Override
    public void init(String contactUserId, String contactUserName) {
        if (!baseMapper.userIdInDb(contactUserId)) {
            WeChatContactUserConfig contactUserConfig = new WeChatContactUserConfig();
            contactUserConfig.setContactUserId(contactUserId);
            contactUserConfig.setContactUserName(contactUserName);
            save(contactUserConfig);
            for (WechatContactUserTagEnum tagEnum : WechatContactUserTagEnum.values()) {
                String url = workWechatApiService.contactMeQrcodeByState(tagEnum.getTag(), contactUserId);
                WeChatContactUserConfigInfo weChatContactUserConfigInfo = new WeChatContactUserConfigInfo();
                weChatContactUserConfigInfo.setUrlType(tagEnum.getCode());
                weChatContactUserConfigInfo.setConfigId(contactUserId);
                weChatContactUserConfigInfo.setConfigUrl(url);
                weChatContactUserConfigInfoService.save(weChatContactUserConfigInfo);
            }
        }

    }

    @Override
    public List<WeChatContactUserConfig> getConfigList() {
        return baseMapper.configList();
    }

    @Override
    public String getAcquisitionByContactUserId(String contactUserId) {
        return baseMapper.getAcquisitionByContactUserId(contactUserId);
    }
}




