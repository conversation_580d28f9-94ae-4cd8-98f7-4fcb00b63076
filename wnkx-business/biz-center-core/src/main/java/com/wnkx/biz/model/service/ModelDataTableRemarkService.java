package com.wnkx.biz.model.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableAddRemarkDTO;
import com.ruoyi.system.api.domain.entity.biz.model.ModelDataTableRemark;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableRemarkVO;

import java.util.List;

/**
 * <AUTHOR> 
 * @Date 2025-07-08 20:35:42 
 */
public interface ModelDataTableRemarkService extends IService<ModelDataTableRemark> {

    /**
     * 添加备注
     */
    void saveModelDataTableRemark(ModelDataTableAddRemarkDTO dto);

    /**
     * 查询备注
     */
    List<ModelDataTableRemarkVO> selectModelDataTableRemarkList(Long modelId);

    /**
     * 查询备注
     */
    List<ModelDataTableRemarkVO> selectModelDataTableRemarkListByModelIds(List<Long> modelIds);
}
