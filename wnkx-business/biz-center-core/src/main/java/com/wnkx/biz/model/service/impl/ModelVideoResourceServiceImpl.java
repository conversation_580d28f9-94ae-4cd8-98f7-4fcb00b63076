package com.wnkx.biz.model.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.dto.biz.model.ModelResourceDTO;
import com.ruoyi.system.api.domain.entity.biz.model.ModelVideoResource;
import com.wnkx.biz.model.mapper.ModelVideoResourceMapper;
import com.wnkx.biz.model.service.IModelVideoResourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 模特案例资源Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ModelVideoResourceServiceImpl extends ServiceImpl<ModelVideoResourceMapper, ModelVideoResource> implements IModelVideoResourceService {

    /**
     * 通过模特ID删除数据
     */
    @Override
    public void removeByModelId(Long modelId) {
        baseMapper.removeByModelId(modelId);
    }

    /**
     * 通过模特ID获取关联案例视频
     */
    @Override
    public List<ModelVideoResource> selectListByModelIds(Collection<Long> modelIds) {
        if (CollUtil.isEmpty(modelIds)) {
            return Collections.emptyList();
        }

        return baseMapper.selectListByModelIds(modelIds);
    }

    /**
     * 新增图片视频资源
     *
     * @param modelResourcesDTO 图片视频资源
     * @return 结果
     */
    @Override
    public void insertResource(List<ModelResourceDTO> modelResourcesDTO) {
        if (CollUtil.isEmpty(modelResourcesDTO)) {
            return;
        }
        List<ModelVideoResource> modelVideoResources = BeanUtil.copyToList(modelResourcesDTO, ModelVideoResource.class);
        baseMapper.saveBatch(modelVideoResources);
    }

}
