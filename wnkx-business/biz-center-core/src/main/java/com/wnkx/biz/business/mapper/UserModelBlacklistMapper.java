package com.wnkx.biz.business.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.entity.biz.model.UserModelBlacklist;
import com.ruoyi.system.api.domain.vo.biz.business.user.UserBlackModelVO;
import com.wnkx.db.mapper.SuperMapper;

import java.util.List;


/**
* <AUTHOR>
* @description 针对表【user_model_blacklist(模特黑名单)】的数据库操作Mapper
* @createDate 2025-01-09 10:46:49
* @Entity com.ruoyi.system.api.domain.entity.biz.model.UserModelBlacklist
*/
public interface UserModelBlacklistMapper extends SuperMapper<UserModelBlacklist> {

    /**
     * * 获取黑名单列表
     * @param bizUserId
     * @return
     */
    List<UserBlackModelVO> userBlackModelList(Long bizUserId);

    /**
     * 根据模特ID列表获取拉黑数据
     * @param modelIds
     * @return
     */
    default List<UserModelBlacklist> userBlackModelListByModelIds(List<Long> modelIds){

        return this.selectList(new LambdaQueryWrapper<UserModelBlacklist>()
                .in(UserModelBlacklist::getModelId, modelIds));
    }

    /**
     * * 检测拉黑数据是否存在
     * @param bizUserId 登录账号id
     * @param modelId   模特ID
     * @return 存在=true  不存在=false
     */
    default boolean hasBlacklist(Long bizUserId, Long modelId) {
        return selectCount(new LambdaUpdateWrapper<UserModelBlacklist>()
                .eq(UserModelBlacklist::getModelId, modelId)
                .eq(UserModelBlacklist::getBizUserId, bizUserId)
        ) >= 1;
    }

    /**
     * 取消拉黑模特
     * @param bizUserId
     * @param modelId
     */
    default void deleteBlacklist(Long bizUserId, Long modelId) {
        delete(new LambdaUpdateWrapper<UserModelBlacklist>()
                .eq(UserModelBlacklist::getModelId, modelId)
                .eq(UserModelBlacklist::getBizUserId, bizUserId)
        );

    }

    /**
     * 获取当前用户拉黑模特列表
     */
    default List<UserModelBlacklist> selectBlackModelListByBizUserId() {
        return selectList(new LambdaQueryWrapper<UserModelBlacklist>()
                .eq(UserModelBlacklist::getBizUserId, SecurityUtils.getBizUserId())
        );
    }
}




