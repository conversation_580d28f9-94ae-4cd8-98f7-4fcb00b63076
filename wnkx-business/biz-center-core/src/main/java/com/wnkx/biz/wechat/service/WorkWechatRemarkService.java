package com.wnkx.biz.wechat.service;

import com.ruoyi.common.core.enums.BizUserAccountTypeEnum;
import com.ruoyi.common.core.enums.UpdateWechatRemarkType;
import com.ruoyi.system.api.domain.dto.biz.wechat.ExternalUserInfoDTO;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelVO;


/**
 *
 * <AUTHOR>
 */
public interface WorkWechatRemarkService {
    void changeCustomerRemark(ExternalUserInfoDTO externalUserInfo, ChannelVO channelByWeChatState, String userId);

    void updateAccountRemark(String accountExternalUserId, String memberCode, UpdateWechatRemarkType type, BizUserAccountTypeEnum accountType);

    void batchUpdateRemark(String accountExternalUserId, String remark);
}
