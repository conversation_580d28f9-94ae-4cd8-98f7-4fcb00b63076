package com.wnkx.biz.model.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.system.api.domain.entity.Model;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelDataStatisticsDay;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelDataStatisticsMonth;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.*;
import com.wnkx.biz.model.service.*;
import com.wnkx.biz.remote.RemoteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/9 16:01
 */
@Service
@RequiredArgsConstructor
public class ModelDataStatisticsServiceImpl implements ModelDataStatisticsService {

    private final IModelService modelService;
    private final ModelChangeRecordService modelChangeRecordService;
    private final RemoteService remoteService;
    private final ModelDataStatisticsDayService modelDataStatisticsDayService;
    private final ModelDataStatisticsMonthService modelDataStatisticsMonthService;

    /**
     * 模特数据-模特排单情况
     */
    @Override
    public DataPieChartVO getModelOrderScheduledData(Date beginTime, Date endTime, BigDecimal beginScore, BigDecimal endScore) {
        List<Model> modelList = modelService.lambdaQuery()
                .between(ObjectUtil.isNotNull(beginTime) && ObjectUtil.isNotNull(endTime), Model::getCreateTime, beginTime, endTime)
                .between(ObjectUtil.isNotNull(beginScore) && ObjectUtil.isNotNull(endScore), Model::getCooperationScore, beginScore, endScore)
                .select(Model::getId)
                .list();

        DataPieChartVO dataPieChartVO = new DataPieChartVO();
        dataPieChartVO.setDateTime(DateUtil.date());
        if (CollUtil.isEmpty(modelList)) {
            List<PieChartVO> pieChartVOS = new ArrayList<>();
            for (String label : ModelOrderScheduledDataEnum.getLabels()) {
                PieChartVO pieChartVO = new PieChartVO();
                pieChartVO.setLabel(label);
                pieChartVO.setCount(0L);
                pieChartVO.setRatio(BigDecimal.ZERO);
                pieChartVOS.add(pieChartVO);
            }
            dataPieChartVO.setPieChartVOS(pieChartVOS);
            return dataPieChartVO;
        }

        List<PieChartVO> modelOrderScheduledData = remoteService.getModelOrderScheduledData(modelList.stream().map(Model::getId).collect(Collectors.toList()));
        dataPieChartVO.setPieChartVOS(modelOrderScheduledData);
        dataPieChartVO.echo();
        return dataPieChartVO;
    }

    /**
     * 模特数据-开发模特合作状态分布
     */
    @Override
    public DataPieChartVO getModelStatusData(Date beginTime, Date endTime, BigDecimal beginScore, BigDecimal endScore) {
        List<Model> modelList = modelService.lambdaQuery()
                .between(ObjectUtil.isNotNull(beginTime) && ObjectUtil.isNotNull(endTime), Model::getCreateTime, beginTime, endTime)
                .between(ObjectUtil.isNotNull(beginScore) && ObjectUtil.isNotNull(endScore), Model::getCooperationScore, beginScore, endScore)
                .select(Model::getStatus)
                .list();

        DataPieChartVO dataPieChartVO = new DataPieChartVO();
        List<PieChartVO> pieChartVOS = generatePieChartData(
                modelList,
                List.of(ModelStatusEnum.NORMAL, ModelStatusEnum.PAUSE, ModelStatusEnum.JOURNEY, ModelStatusEnum.CANCEL),
                Model::getStatus,
                ModelStatusEnum::getCode,
                ModelStatusEnum::getLabel);

        dataPieChartVO.setDateTime(DateUtil.date());
        dataPieChartVO.setPieChartVOS(pieChartVOS);
        dataPieChartVO.echo();
        return dataPieChartVO;
    }

    /**
     * 模特数据-模特接单排行榜
     */
    @Override
    public ModelOrderRankingVO getModelOrderRanking(String date) {
        if (CharSequenceUtil.isBlank(date)) {
            date = DateUtil.format(DateUtil.lastMonth(), DatePattern.NORM_MONTH_PATTERN);
        }
        ModelOrderRankingVO modelOrderRankingVO = new ModelOrderRankingVO();
        modelOrderRankingVO.setWriteTimeEnd(DateUtil.endOfMonth(DateUtil.parse(date, DatePattern.NORM_MONTH_PATTERN)));
        ModelDataStatisticsMonth modelDataStatisticsMonth = modelDataStatisticsMonthService.getByWriteTimeMonth(date);
        if (ObjectUtil.isNull(modelDataStatisticsMonth) || ObjectUtil.isNull(modelDataStatisticsMonth.getRankingListJson())) {
            return modelOrderRankingVO;
        }
        List<ModelOrderRankingListVO> result = JSONUtil.toList(modelDataStatisticsMonth.getRankingListJson(), ModelOrderRankingListVO.class);
        // 最多保留前 10 条
        if (result.size() > 10) {
            result = result.subList(0, 10);
        }

        modelOrderRankingVO.setModelOrderRankingListVOS(result);
        return modelOrderRankingVO;
    }

    /**
     * 模特数据-模特类型分析
     */
    @Override
    public ModelTypeAnalysisVO getModelTypeAnalysis(Integer status) {
        ModelTypeAnalysisVO modelTypeAnalysisVO = new ModelTypeAnalysisVO();

        List<Model> modelList = modelService.selectListByStatus(status);
        if (CollUtil.isEmpty(modelList)) {
            return modelTypeAnalysisVO.echo();
        }

        modelTypeAnalysisVO.setModelCount(Convert.toLong(modelList.size()));
        //  模特类型饼图
        modelTypeAnalysisVO.setModelTypePieChartVOS(generatePieChartData(
                modelList,
                List.of(ModelTypeEnum.INFLUENT, ModelTypeEnum.AVERAGE_PEOPLE),
                Model::getType,
                ModelTypeEnum::getCode,
                ModelTypeEnum::getLabel)
        );
        //  模特性别饼图
        modelTypeAnalysisVO.setModelSexPieChartVOS(generatePieChartData(
                modelList,
                List.of(SexEnum.MAN, SexEnum.WOMAN),
                Model::getSex,
                SexEnum::getCode,
                SexEnum::getLabel));
        //  模特合作深度饼图
        modelTypeAnalysisVO.setModelCooperationPieChartVOS(generatePieChartData(
                modelList,
                List.of(ModelCooperationEnum.ORDINARY, ModelCooperationEnum.QUALITY),
                Model::getCooperation,
                ModelCooperationEnum::getCode,
                ModelCooperationEnum::getLabel));
        //  模特国家饼图
        modelTypeAnalysisVO.setModelNationPieChartVOS(generatePieChartData(
                modelList,
                List.of(NationEnum.USA, NationEnum.CANADA, NationEnum.GERMANY, NationEnum.FRANCE, NationEnum.IT, NationEnum.ES, NationEnum.UK),
                Model::getNation,
                NationEnum::getCode,
                NationEnum::getLabel));
        for (Model model : modelList) {
            //  佣金单位转换美金单位
            model.setCommission(model.getCommission().multiply(CommissionUnitEnum.getByUnit(model.getCommissionUnit()).getToUSDRate()).setScale(2, RoundingMode.DOWN));
        }
        Map<String, List<Model>> groupTop4ByCommission = groupTop4ByCommission(modelList);
        List<PieChartVO> modelCommissionPieChartVOS = new ArrayList<>();
        for (Map.Entry<String, List<Model>> commissionEntry : groupTop4ByCommission.entrySet()) {
            PieChartVO pieChartVO = new PieChartVO();
            pieChartVO.setLabel(commissionEntry.getKey());
            pieChartVO.setCount(Convert.toLong(commissionEntry.getValue().size()));
            pieChartVO.setRatio(BigDecimal.valueOf(commissionEntry.getValue().size())
                    .divide(BigDecimal.valueOf(modelList.size()), 2, RoundingMode.HALF_UP));
            modelCommissionPieChartVOS.add(pieChartVO);

            if (commissionEntry.getKey().equals("其他")) {
                Map<BigDecimal, List<Model>> other = commissionEntry.getValue().stream().collect(Collectors.groupingBy(Model::getCommission));
                List<PieChartVO> otherModelCommissionPieChartVOS = new ArrayList<>();
                for (Map.Entry<BigDecimal, List<Model>> otherEntry : other.entrySet()) {
                    PieChartVO otherPieChartVO = new PieChartVO();
                    otherPieChartVO.setLabel(otherEntry.getKey().stripTrailingZeros().toPlainString() + "美金");
                    otherPieChartVO.setCount(Convert.toLong(otherEntry.getValue().size()));
                    otherPieChartVO.setRatio(BigDecimal.valueOf(otherEntry.getValue().size())
                            .divide(BigDecimal.valueOf(modelList.size()), 2, RoundingMode.HALF_UP));
                    otherModelCommissionPieChartVOS.add(otherPieChartVO);
                }
                otherModelCommissionPieChartVOS.sort((a, b) -> {
                    BigDecimal aValue = new BigDecimal(a.getLabel().replace("美金", ""));
                    BigDecimal bValue = new BigDecimal(b.getLabel().replace("美金", ""));
                    return bValue.compareTo(aValue);
                });
                pieChartVO.setPieChartVOS(otherModelCommissionPieChartVOS);
            }
        }
        //  模特佣金饼图
        modelTypeAnalysisVO.setModelCommissionPieChartVOS(modelCommissionPieChartVOS);

        //  模特年龄层饼图
        modelTypeAnalysisVO.setModelAgeGroupPieChartVOS(generatePieChartData(
                modelList,
                List.of(ModelAgeGroupEnum.INFANT, ModelAgeGroupEnum.CHILD, ModelAgeGroupEnum.ADULT, ModelAgeGroupEnum.AGED),
                Model::getAgeGroup,
                ModelAgeGroupEnum::getCode,
                ModelAgeGroupEnum::getLabel));

        modelTypeAnalysisVO.setModelOrderScheduledPieChartVOS(remoteService.getModelOrderScheduledData(modelList.stream().map(Model::getId).collect(Collectors.toList())));
        modelTypeAnalysisVO.setDateTime(DateUtil.date());
        return modelTypeAnalysisVO.echo();
    }

    /**
     * 模特数据-每月淘汰模特分析
     */
    @Override
    public ModelCountAnalysisVO getOustModelAnalysis(String date) {
        if (CharSequenceUtil.isBlank(date)) {
            date = DateUtil.format(DateUtil.lastMonth(), DatePattern.NORM_MONTH_PATTERN);
        }
        ModelDataStatisticsMonth modelDataStatisticsMonth = modelDataStatisticsMonthService.getByWriteTimeMonth(date);
        if (ObjectUtil.isNull(modelDataStatisticsMonth) || ObjectUtil.isNull(modelDataStatisticsMonth.getOustModelAnalysisJson())) {
            return ModelCountAnalysisVO.builder().writeTimeEnd(DateUtil.endOfMonth(DateUtil.parse(date, DatePattern.NORM_MONTH_PATTERN))).build().echo();
        }
        ModelCountAnalysisVO modelCountAnalysisVO = JSONUtil.toBean(modelDataStatisticsMonth.getOustModelAnalysisJson(), ModelCountAnalysisVO.class);
        modelCountAnalysisVO.setWriteTimeEnd(DateUtil.endOfMonth(DateUtil.parse(date, DatePattern.NORM_MONTH_PATTERN)));
        return modelCountAnalysisVO.echo();
    }

    /**
     * 模特数据-每月新增模特分析
     */
    @Override
    public ModelCountAnalysisVO getNewModelAnalysis(String date) {
        if (CharSequenceUtil.isBlank(date)) {
            date = DateUtil.format(DateUtil.lastMonth(), DatePattern.NORM_MONTH_PATTERN);
        }
        ModelDataStatisticsMonth modelDataStatisticsMonth = modelDataStatisticsMonthService.getByWriteTimeMonth(date);
        if (ObjectUtil.isNull(modelDataStatisticsMonth) || ObjectUtil.isNull(modelDataStatisticsMonth.getNewModelAnalysisJson())) {
            return ModelCountAnalysisVO.builder().writeTimeEnd(DateUtil.endOfMonth(DateUtil.parse(date, DatePattern.NORM_MONTH_PATTERN))).build().echo();
        }
        ModelCountAnalysisVO modelCountAnalysisVO = JSONUtil.toBean(modelDataStatisticsMonth.getNewModelAnalysisJson(), ModelCountAnalysisVO.class);
        modelCountAnalysisVO.setWriteTimeEnd(DateUtil.endOfMonth(DateUtil.parse(date, DatePattern.NORM_MONTH_PATTERN)));
        return modelCountAnalysisVO.echo();
    }

    /**
     * 模特数据-模特数量趋势分析
     */
    @Override
    public ModelNumberTrendAnalysisVO getModelNumberTrendAnalysis(Date beginTime, Date endTime) {
        ModelNumberTrendAnalysisVO modelNumberTrendAnalysisVO = new ModelNumberTrendAnalysisVO();

        List<String> dateArray;
        List<Long> eliminatedNumberArray;
        List<Long> newAdditionsNumberArray;

        if (DateUtil.betweenDay(beginTime, endTime, true) > 30) {
            List<DateTime> dateTimes = DateUtil.rangeToList(beginTime, endTime, DateField.MONTH);
            dateArray = dateTimes.stream().map(item -> DateUtil.format(item, "yyyy.M")).collect(Collectors.toList());
            eliminatedNumberArray = new ArrayList<>(Collections.nCopies(dateArray.size(), 0L));
            newAdditionsNumberArray = new ArrayList<>(Collections.nCopies(dateArray.size(), 0L));

            List<ModelDataStatisticsMonth> modelDataStatisticsMonths = modelDataStatisticsMonthService.getByWriteTimeBetween(beginTime, endTime);
            for (ModelDataStatisticsMonth modelDataStatisticsMonth : modelDataStatisticsMonths) {
                int index = dateArray.indexOf(DateUtil.format(modelDataStatisticsMonth.getWriteTimeBegin(), "yyyy.M"));
                eliminatedNumberArray.set(index, modelDataStatisticsMonth.getModelOustNumber());
                newAdditionsNumberArray.set(index, modelDataStatisticsMonth.getModelNewNumber());
            }
        } else {
            List<DateTime> dateTimes = DateUtil.rangeToList(beginTime, endTime, DateField.DAY_OF_YEAR);
            dateArray = dateTimes.stream().map(item -> DateUtil.format(item, "M.d")).collect(Collectors.toList());
            eliminatedNumberArray = new ArrayList<>(Collections.nCopies(dateArray.size(), 0L));
            newAdditionsNumberArray = new ArrayList<>(Collections.nCopies(dateArray.size(), 0L));

            List<ModelDataStatisticsDay> modelDataStatisticsDays = modelDataStatisticsDayService.getByWriteTimeBetween(beginTime, endTime);
            for (ModelDataStatisticsDay modelDataStatisticsDay : modelDataStatisticsDays) {
                int index = dateArray.indexOf(DateUtil.format(modelDataStatisticsDay.getWriteTimeBegin(), "M.d"));
                eliminatedNumberArray.set(index, modelDataStatisticsDay.getModelOustNumber());
                newAdditionsNumberArray.set(index, modelDataStatisticsDay.getModelNewNumber());
            }
        }

        modelNumberTrendAnalysisVO.setWriteTimeEnd(endTime);
        modelNumberTrendAnalysisVO.setDateArray(dateArray);
        modelNumberTrendAnalysisVO.setEliminatedNumberArray(eliminatedNumberArray);
        modelNumberTrendAnalysisVO.setNewAdditionsNumberArray(newAdditionsNumberArray);
        return modelNumberTrendAnalysisVO;
    }

    /**
     * 模特数据-模特基础数据
     */
    @Override
    public ModelBasicDataVO getModelBasicData() {
        List<Model> modelList = modelService.list();

        ModelBasicDataVO modelBasicDataVO = new ModelBasicDataVO();
        modelBasicDataVO.setNormalModelNumber(modelList.stream().filter(model -> ModelStatusEnum.NORMAL.getCode().equals(model.getStatus())).count());
        modelBasicDataVO.setJourneyModelNumber(modelList.stream().filter(model -> ModelStatusEnum.JOURNEY.getCode().equals(model.getStatus())).count());
        modelBasicDataVO.setActiveModelNumber(modelBasicDataVO.getNormalModelNumber() + modelBasicDataVO.getJourneyModelNumber());
        modelBasicDataVO.setNewAdditionsLastMonthModelNumber(modelList.stream().filter(model -> DateUtil.format(DateUtil.lastMonth(), DatePattern.NORM_MONTH_PATTERN).equals(DateUtil.format(model.getCreateTime(), DatePattern.NORM_MONTH_PATTERN))).count());
        modelBasicDataVO.setPauseModelNumber(modelList.stream().filter(model -> ModelStatusEnum.PAUSE.getCode().equals(model.getStatus())).count());
        modelBasicDataVO.setCancelModelNumber(modelList.stream().filter(model -> ModelStatusEnum.CANCEL.getCode().equals(model.getStatus())).count());
        modelBasicDataVO.setOustModelNumber(modelBasicDataVO.getPauseModelNumber() + modelBasicDataVO.getCancelModelNumber());
        List<Long> finalOustModelIdsByDate = modelChangeRecordService.getFinalOustModelIdsByDate("'%Y-%m'", DateUtil.format(DateUtil.lastMonth(), DatePattern.NORM_MONTH_PATTERN));
        modelBasicDataVO.setEliminatedLastMonthModelNumber(Convert.toLong(finalOustModelIdsByDate.size()));

        ModelBasicDataVO modelBasicData = remoteService.getModelBasicData();
        modelBasicDataVO.setSuccessRateOfTheFirstMatch(modelBasicData.getSuccessRateOfTheFirstMatch());
        modelBasicDataVO.setModelAfterSalesRate(modelBasicData.getModelAfterSalesRate());
        modelBasicDataVO.setModelOvertimeRate(modelBasicData.getModelOvertimeRate());
        modelBasicDataVO.setSuccessRateOfIntentionMatching(modelBasicData.getSuccessRateOfIntentionMatching());
        modelBasicDataVO.setAverageMatchingDuration(modelBasicData.getAverageMatchingDuration());
        modelBasicDataVO.setAverageFeedbackDuration(modelBasicData.getAverageFeedbackDuration());
        modelBasicDataVO.setDateTime(DateUtil.date());

        return modelBasicDataVO.echo();
    }

    private Map<String, List<Model>> groupTop4ByCommission(List<Model> modelList) {
        // 规范化 BigDecimal（去除尾部0）并统计每种 commission 出现次数
        Map<BigDecimal, Long> commissionCount = modelList.stream()
                .map(model -> model.getCommission().stripTrailingZeros())
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        // 找出数量最多的前四个 commission 值
        Set<BigDecimal> top4Commission = commissionCount.entrySet().stream()
                .sorted(Map.Entry.<BigDecimal, Long>comparingByValue().reversed())
                .limit(4)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        // 按照前四个归类，其余归为 "其他"
        return modelList.stream()
                .collect(Collectors.groupingBy(model -> {
                    BigDecimal normalized = model.getCommission().stripTrailingZeros();
                    return top4Commission.contains(normalized) ? normalized.toPlainString() + "美金" : "其他";
                }));
    }

    private <T, E extends Enum<E>> List<PieChartVO> generatePieChartData(
            List<T> dataList,
            List<E> enumList,
            Function<T, ?> categoryExtractor,       // 支持任意类型：Integer, Long, String
            Function<E, Integer> codeGetter,        // 枚举 code 是 Integer
            Function<E, String> labelGetter         // 枚举 label 是 String
    ) {
        // 将 key 统一转为字符串用于比对
        Map<String, Long> countMap = dataList.stream()
                .collect(Collectors.groupingBy(t -> String.valueOf(categoryExtractor.apply(t)), Collectors.counting()));

        int total = dataList.size();
        return enumList.stream()
                .map(e -> {
                    String codeStr = String.valueOf(codeGetter.apply(e));
                    long count = countMap.getOrDefault(codeStr, 0L);
                    BigDecimal ratio = total == 0 ? BigDecimal.ZERO :
                            BigDecimal.valueOf(count).divide(BigDecimal.valueOf(total), 2, RoundingMode.HALF_UP);
                    return PieChartVO.builder()
                            .label(labelGetter.apply(e))
                            .count(count)
                            .ratio(ratio)
                            .build();
                })
                .collect(Collectors.toList());
    }

}
