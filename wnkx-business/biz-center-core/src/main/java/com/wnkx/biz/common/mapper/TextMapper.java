package com.wnkx.biz.common.mapper;

import com.ruoyi.system.api.domain.dto.biz.common.TextHelpListDTO;
import com.ruoyi.system.api.domain.dto.biz.common.TextListDTO;
import com.ruoyi.system.api.domain.entity.biz.common.Text;
import com.ruoyi.system.api.domain.vo.biz.common.TextHelpListVO;
import com.ruoyi.system.api.domain.vo.biz.common.TextHelpVO;
import com.ruoyi.system.api.domain.vo.biz.common.TextListVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:24
 */
@Mapper
public interface TextMapper extends SuperMapper<Text> {

    /**
     * 文本列表
     */
    List<TextListVO> selectListByCondition(@Param("dto") TextListDTO dto);


    /**
     * 帮助中心列表
     * * @param dto
     *
     * @return
     */
    List<TextHelpListVO> selectHelpListByCondition(@Param("dto") TextHelpListDTO dto);


    /**
     * 查询用户帮助中心列表数据
     * * @param dto
     * @return
     */
    List<TextHelpVO> selectUserHelpListByCondition(@Param("dto") TextHelpListDTO dto);
}
