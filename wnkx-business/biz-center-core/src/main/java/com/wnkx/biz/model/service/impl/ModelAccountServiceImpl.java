package com.wnkx.biz.model.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.entity.biz.model.ModelAccount;
import com.wnkx.biz.model.mapper.ModelAccountMapper;
import com.wnkx.biz.model.service.IModelAccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
* <AUTHOR>
* @description 针对表【model_account(模特账号表)】的数据库操作Service实现
* @createDate 2024-07-05 16:17:18
*/
@Service
@RequiredArgsConstructor
@Slf4j
public class ModelAccountServiceImpl extends ServiceImpl<ModelAccountMapper, ModelAccount>
implements IModelAccountService {
    private final RedisService redisService;

    /**
     * 通过模特id获取模特账号
     */
    @Override
    public ModelAccount getOneByModelId(Long modelId) {
        return baseMapper.getOneByModelId(modelId);
    }

    @Override
    public void createModelAccount(Long modelId) {
        ModelAccount modelAccount = new ModelAccount();
        modelAccount.setModelId(modelId);
        modelAccount.setAccount(this.initAccount(modelId));
        modelAccount.setLoginAccount(this.initModelLoginAccount(modelId));
        baseMapper.insert(modelAccount);
    }

    @Override
    public void updateLoginTime(String account) {
        this.lambdaUpdate()
                .set(ModelAccount::getLastLoginTime, new Date())
                .eq(ModelAccount::getAccount, account)
                .update();
    }

    /**
     * 初始化账号
     *
     * @param modelId
     * @return
     */
    public String initAccount(Long modelId) {
        Long reentrancyCount = 5L;
        Long expireTime = 10L;
        if (redisService.setIncr(CacheConstants.MODEL_ACCOUNT_KEY + modelId, expireTime, TimeUnit.SECONDS).compareTo(reentrancyCount) > 0) {
            throw new ServiceException("系统生成随机数失败，请稍后重试！");
        }

        // 2. 使用Snowflake算法生成唯一ID
        long uniqueId = IdUtil.getSnowflakeNextId();
        // 3. 截取唯一ID的最后8位，确保是数字
        String randomAccount = "0" + String.format("%05d", uniqueId % 100_000);

        Long count = this.lambdaQuery()
                .eq(ModelAccount::getAccount, randomAccount)
                .count();
        if (count.compareTo(0L) > 0) {
            return this.initAccount(modelId);
        }
        return randomAccount;
    }

    /**
     * 初始化账号
     *
     * @param modelId
     * @return
     */
    public String initModelLoginAccount(Long modelId) {
        Long reentrancyCount = 5L;
        Long expireTime = 10L;
        if (redisService.setIncr(CacheConstants.MODEL_ACCOUNT_KEY + modelId, expireTime, TimeUnit.SECONDS).compareTo(reentrancyCount) > 0) {
            throw new ServiceException("系统生成随机数失败，请稍后重试！");
        }

        // 2. 使用Snowflake算法生成唯一ID
        long uniqueId = IdUtil.getSnowflakeNextId();
        // 3. 截取唯一ID的最后8位，确保是数字
        String randomAccount = "0" + String.format("%05d", uniqueId % 100_000);

        Long count = this.lambdaQuery()
                .eq(ModelAccount::getLoginAccount, randomAccount)
                .count();
        if (count.compareTo(0L) > 0) {
            return this.initModelLoginAccount(modelId);
        }
        return randomAccount;
    }
}
