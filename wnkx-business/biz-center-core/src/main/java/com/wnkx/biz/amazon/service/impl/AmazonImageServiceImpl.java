package com.wnkx.biz.amazon.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.enums.AmazonOriginEnum;
import com.ruoyi.common.core.enums.OrderTypeEnum;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteOrderService;
import com.ruoyi.system.api.domain.dto.order.AsyncCrawlProductPicDTO;
import com.ruoyi.system.api.domain.dto.order.AsyncCrawlTask;
import com.ruoyi.system.api.domain.dto.order.UpdateBatchOrderVideoProductPicDTO;
import com.ruoyi.system.api.domain.entity.biz.amazon.AmazonGoodsPic;
import com.ruoyi.system.api.domain.vo.AmazonProductInfo;
import com.ruoyi.system.api.domain.vo.FileUploadLinkVo;
import com.wnkx.biz.amazon.service.AmazonGoodsPicService;
import com.wnkx.biz.amazon.service.AmazonImageService;
import com.wnkx.biz.translate.service.TranslateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/8
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AmazonImageServiceImpl implements AmazonImageService {

    private final RemoteOrderService remoteOrderService;
    private final RemoteFileService remoteFileService;

    private static final String AMAZON_PREFIX = "amazon";

    private static final String TIKTOK = "tiktok";

    private final RedisService redisService;
    private final AmazonGoodsPicService amazonGoodsPicService;

    private OkHttpClient proxyOkhttpClient;

    private final TranslateService translateService;

    /**
     * host地址
     */
    @Value(value = "${selenium.proxy.info.host:''}")
    private String proxyHost;

    /**
     * port地址
     */
    @Value(value = "${selenium.proxy.info.port}")
    private Integer proxyPort;


    @PostConstruct
    public void setProxyOkhttpClient() {
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort));
        proxyOkhttpClient = new OkHttpClient.Builder()
                .proxy(proxy)
                .build();
    }

    /**
     * 异步批量抓取amazon产品链接图片并更新视频订单
     */
    @Override
    public void asyncUpdateOrderVideoImage(AsyncCrawlTask asyncCrawlTask) {
        long startTime = System.currentTimeMillis();
        log.info("开始执行，任务数：{}，执行时间：{}", asyncCrawlTask.getAsyncCrawlProductPic().size(), DateUtil.date(startTime));

        List<UpdateBatchOrderVideoProductPicDTO> dtoList = new ArrayList<>();
        // Set<AmazonGoodsPic> saveAmazonGoodsPics = new HashSet<>();
        try {
            Map<String, AmazonGoodsPic> amazonGoodsPicMap = new HashMap<>();
            if (OrderTypeEnum.VIDEO_CART.getCode().equals(asyncCrawlTask.getType())) {
                String amazonGoodsId;
                if (asyncCrawlTask.getAsyncCrawlProductPic().get(0).getProductLink().contains("amazon")) {
                    amazonGoodsId = StringUtils.extractAmazonGoodsId(asyncCrawlTask.getAsyncCrawlProductPic().get(0).getProductLink());
                } else {
                    amazonGoodsId = StringUtils.extractTikTokGoodsId(asyncCrawlTask.getAsyncCrawlProductPic().get(0).getProductLink());
                }
                List<AmazonGoodsPic> amazonGoodsPics = amazonGoodsPicService.selectByCondition(Collections.singletonList(amazonGoodsId));
                if (CollUtil.isNotEmpty(amazonGoodsPics)) {
                    log.info("商品id：{}，已存在于数据表中：{}", amazonGoodsId, amazonGoodsPics.get(0));
                    for (AsyncCrawlProductPicDTO asyncCrawlProductPicDTO : asyncCrawlTask.getAsyncCrawlProductPic()) {
                        UpdateBatchOrderVideoProductPicDTO dto = new UpdateBatchOrderVideoProductPicDTO();
                        dto.setId(asyncCrawlProductPicDTO.getId());
                        dto.setProductLink(asyncCrawlProductPicDTO.getProductLink());
                        dto.setProductPic(amazonGoodsPics.get(0).getObjectKey());
                        dtoList.add(dto);
                    }
                    return;
                }
            } else {
                List<String> amazonGoodsIds = asyncCrawlTask.getAsyncCrawlProductPic().stream().map(item -> {
                    String amazonGoodsId;
                    if (item.getProductLink().contains("amazon")) {
                        amazonGoodsId = StringUtils.extractAmazonGoodsId(item.getProductLink());
                    } else {
                        amazonGoodsId = StringUtils.extractTikTokGoodsId(item.getProductLink());
                    }
                    if (StrUtil.isNotBlank(amazonGoodsId)) {
                        return amazonGoodsId;
                    }
                    return StrUtil.isNotBlank(amazonGoodsId) ? amazonGoodsId : StrUtil.EMPTY;
                }).filter(StrUtil::isNotBlank).collect(Collectors.toList());

                if (CollUtil.isNotEmpty(amazonGoodsIds)) {
                    List<AmazonGoodsPic> amazonGoodsPics = amazonGoodsPicService.selectByCondition(amazonGoodsIds);
                    amazonGoodsPicMap = amazonGoodsPics.stream().collect(Collectors.toMap(AmazonGoodsPic::getGoodsId, p -> p));
                }
            }
            for (AsyncCrawlProductPicDTO asyncCrawlProductPicDTO : asyncCrawlTask.getAsyncCrawlProductPic()) {
                String amazonGoodsId;
                if (asyncCrawlProductPicDTO.getProductLink().contains("amazon")) {
                    amazonGoodsId = StringUtils.extractAmazonGoodsId(asyncCrawlProductPicDTO.getProductLink());
                } else {
                    amazonGoodsId = StringUtils.extractTikTokGoodsId(asyncCrawlProductPicDTO.getProductLink());
                }
                AmazonGoodsPic amazonGoodsPic = amazonGoodsPicMap.get(amazonGoodsId);

                UpdateBatchOrderVideoProductPicDTO dto = new UpdateBatchOrderVideoProductPicDTO();
                dto.setId(asyncCrawlProductPicDTO.getId());
                dto.setProductLink(asyncCrawlProductPicDTO.getProductLink());
                if (amazonGoodsPic != null) {
                    dto.setProductPic(amazonGoodsPic.getObjectKey());
                    dtoList.add(dto);
                    continue;
                }
                long taskStartTime = System.currentTimeMillis();

                // 调用远程服务
                AmazonProductInfo amazonProductInfo;
                if (asyncCrawlProductPicDTO.getProductLink().contains("amazon")) {
                    amazonProductInfo = getAmazonProductInfo(asyncCrawlProductPicDTO.getProductLink());
                } else {
                    amazonProductInfo = getTiktokInfo(asyncCrawlProductPicDTO.getProductLink());
                }
                if (amazonProductInfo == null || CharSequenceUtil.isBlank(amazonProductInfo.getObjectKey())) {
                    log.error("爬取亚马逊图片失败，{}id：{}", OrderTypeEnum.VIDEO_ORDER.getCode().equals(asyncCrawlTask.getType()) ? OrderTypeEnum.VIDEO_ORDER.getLabel() : OrderTypeEnum.VIDEO_CART.getLabel(), asyncCrawlProductPicDTO.getId());
                    continue;
                }
                dto.setProductPic(amazonProductInfo.getObjectKey());
                dtoList.add(dto);

                if (StrUtil.isNotBlank(amazonGoodsId)) {
                    AmazonGoodsPic saveAmazonGoodsPic = new AmazonGoodsPic();
                    saveAmazonGoodsPic.setGoodsId(amazonGoodsId);
                    saveAmazonGoodsPic.setObjectKey(amazonProductInfo.getObjectKey());
                    // saveAmazonGoodsPic.setSpecInfo(amazonProductInfo.getSpecInfo());
                    // saveAmazonGoodsPic.setProductName(amazonProductInfo.getName());
                    // String targetText = remoteTranslateService.translateStr(new TranslateDTO(amazonProductInfo.getName(), 1), SecurityConstants.INNER).getTargetText();
                    // saveAmazonGoodsPic.setProductChineseName(targetText);
                    // saveAmazonGoodsPics.add(saveAmazonGoodsPic);
                    amazonGoodsPicMap.put(amazonGoodsId, saveAmazonGoodsPic);
                }
                log.info("{}id：{}，耗时：{}", OrderTypeEnum.VIDEO_ORDER.getCode().equals(asyncCrawlTask.getType()) ? OrderTypeEnum.VIDEO_ORDER.getLabel() : OrderTypeEnum.VIDEO_CART.getLabel(), asyncCrawlProductPicDTO.getId(), System.currentTimeMillis() - taskStartTime);
            }
        } catch (Exception e) {
            log.error("爬取亚马逊图片失败", e);
        } finally {
            if (CollUtil.isNotEmpty(dtoList)) {
                if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(asyncCrawlTask.getType())) {
                    remoteOrderService.updateBatchOrderVideoProductPic(dtoList, SecurityConstants.INNER);
                } else if (OrderTypeEnum.VIDEO_CART.getCode().equals(asyncCrawlTask.getType())) {
                    remoteOrderService.updateBatchOrderCartProductPic(dtoList, SecurityConstants.INNER);
                }
            }
            // if (CollUtil.isNotEmpty(saveAmazonGoodsPics)) {
            //     amazonGoodsPicService.addAmazonGoodsPic(new ArrayList<>(saveAmazonGoodsPics));
            // }
            log.info("任务完成，总耗时：{}", System.currentTimeMillis() - startTime);
        }
    }

    @Override
    public AmazonProductInfo getAmazonProductInfo(String url) {
        if (!(StrUtil.isNotBlank(url) && url.contains("amazon"))) {
            return null;
        }
        String amazonGoodsId = StringUtils.extractAmazonGoodsId(url);
        List<AmazonGoodsPic> amazonGoodsPics = amazonGoodsPicService.selectByCondition(Collections.singletonList(amazonGoodsId));
        if (CollUtil.isNotEmpty(amazonGoodsPics)) {
            log.info("amazonId：{}，已存在于数据表中：{}", amazonGoodsId, amazonGoodsPics.get(0));
            return new AmazonProductInfo(amazonGoodsPics.get(0).getGoodsId(),
                    amazonGoodsPics.get(0).getProductName(),
                    amazonGoodsPics.get(0).getObjectKey(),
                    amazonGoodsPics.get(0).getSpecInfo(),
                    amazonGoodsPics.get(0).getProductChineseName());
        }
        log.info("开始抓取图片amazonId{}", amazonGoodsId);
        AmazonProductInfo amazonProductInfo = toData(requestForAmazonHtmlData(amazonGoodsId, url), amazonGoodsId);
        if (amazonProductInfo != null && StringUtils.isNotEmpty(amazonProductInfo.getGoodsId())) {
            saveCachePic(amazonProductInfo);
        }
        return amazonProductInfo;
    }

    @Override
    public void verifyAmazonCookie() {
        requestForAmazonHtmlData("B09SYSM86M", "https://www.amazon.com/dp/B09SYSM86M");
    }

    @Override
    public AmazonGoodsPic getAmazonProductInfo(AmazonGoodsPic amazonGoodsPic) {
        AmazonProductInfo amazonProductInfo = toData(requestForAmazonHtmlData(amazonGoodsPic.getGoodsId(),
                "session-id=137-5536406-3090618;session-id-time=2082787201l;i18n-prefs=USD;session-token=3aTiInXt4ozLfYG1TvzmZxXMdwuTPUiKDkPMW+i8MPHIU8zmHWzeYgm00ErLPtiuLZ5CrI/aYhx/HSS6v64k3USsEqc4GCWThH8OW70RL1dmo7vV3VpEY+zq2QA8pyPJOOy99edQcvox7JUf/wE944AtzD8tYMJr9YqFQrXuZG7u4QYDEyr2DiQzjlggCD6LwwVVtlesselxOlpts7laDUQn/BPmfn6IUwkTClrSGrkt/uc86Afo5BBFye+fcjZ6u+stAndix2MO+7St7dpDwwzwM5BbozgFU9xg9xbBEipgHK0Um5Y4IQ3axxaVuq6EeOvuxdKJEa6/USXib/3Pzxnmxu53yFSf;"
        ), amazonGoodsPic.getGoodsId());
        if (amazonProductInfo != null && StringUtils.isNotEmpty(amazonProductInfo.getGoodsId())) {
            AmazonGoodsPic result = new AmazonGoodsPic();
            result.setGoodsId(amazonProductInfo.getGoodsId());
            result.setObjectKey(amazonProductInfo.getObjectKey());
            result.setProductName(amazonProductInfo.getName());
            result.setSpecInfo(amazonProductInfo.getSpecInfo());
            result.setProductChineseName(amazonProductInfo.getProductChineseName());
            return result;
        }
        return null;
    }

    private AmazonProductInfo toData(String htmlData, String amazonGoodsId) {
        if (StringUtils.isBlank(htmlData)) {
            return new AmazonProductInfo();
        }
        Document doc = Jsoup.parse(htmlData);
        Element landingImage = doc.getElementById("landingImage");
        if (ObjectUtil.isNull(landingImage)) {
            return null;
        }
        String src = landingImage.attr("src");
        String name = doc.getElementById("productTitle") != null ? doc.getElementById("productTitle").text() : "";
//        五点特性
        Element outerDiv = htmlData.contains("feature-bullets") ?
                doc.getElementById("feature-bullets") :
                htmlData.contains("bond-feature-bullets-desktop") ?
                        doc.getElementById("bondAboutThisItem_feature_div") :
                        doc.getElementById("productFactsDesktopExpander");
        StringBuilder specInfo = new StringBuilder();
        // 检查最外层的 div 元素是否存在
        if (outerDiv != null) {
            // 获取 ul 元素
            Element ulElement = outerDiv.select("ul.a-unordered-list").first();
            // 检查 ul 元素是否存在
            if (ulElement != null) {
                // 获取所有 li 子元素
                Elements liElements = ulElement.select("li");
                // 创建一个 StringBuilder 来存储结果

                // 遍历 li 元素，并将其内容用换行符连接
                for (Element li : liElements) {
                    // 获取 li 中的文本内容
                    String liText = li.text();
                    specInfo.append(liText).append("\n");
                }
            }
        }

        String image = remoteFileService.uploadFileByLink(FileUploadLinkVo.builder()
                .bucketName(AMAZON_PREFIX)
                .fileUrl(src)
                .useProxy(true)
                .build());
        if (StringUtils.isNotBlank(image)) {
            String targetText = translateService.translate(name, 1).getTargetText();
            return new AmazonProductInfo(amazonGoodsId, name, image, specInfo.toString(), targetText);
        }
        return new AmazonProductInfo();
    }


    private String requestForAmazonHtmlData(String amazonGoodsId, String url) {
        return requestForAmazonHtmlData(amazonGoodsId, url, getCacheCookie());
    }

    private String requestForAmazonHtmlData(String amazonGoodsId, String originUrl, String cookie) {
        String productUrl = formatOriginUrl(originUrl, amazonGoodsId);
        Request request = new Request.Builder()
                .url(productUrl)
                .method("GET", null)
                .addHeader("Cookie",
                        cookie
                )
                .addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36")
                .addHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
                .addHeader("accept-language", "en-US,en;q=0.9")
                .addHeader("Connection", "keep-alive")
                .build();
        try (Response response = proxyOkhttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.info("amazon获取产品信息失败,url={}", productUrl);
                return "";
            }
            String data = response.body() != null ? response.body().string() : "";
            if (data.contains("MEOW")) {
                return data;
            }
            log.warn("抓图失败,amazonId{},未找到MEOW", amazonGoodsId);
        } catch (Exception e) {
            log.warn("抓图异常{}", e.getMessage());

        }
        return "";
    }

    private String formatOriginUrl(String originUrl, String amazonGoodsId) {
        return String.format("%s/dp/%s", AmazonOriginEnum.getLabelByUrl(originUrl).getUrl(), amazonGoodsId);
    }

    private String getCacheCookie() {
        return redisService.getCacheObject(CacheConstants.AMAZON_COOKIE_CACHE_KEY);
    }


    //以下为tiktok


    @Override
    public AmazonProductInfo getTiktokInfo(String url) {
        if (!(StrUtil.isNotBlank(url) && url.contains("shop.tiktok.com"))) {
            return null;
        }
        String tikTokGoodsId = StringUtils.extractTikTokGoodsId(url);
        List<AmazonGoodsPic> tiktokData = amazonGoodsPicService.selectByCondition(Collections.singletonList(tikTokGoodsId));
        if (CollUtil.isNotEmpty(tiktokData)) {
            log.info("tiktokId：{}，已存在于数据表中：{}", tikTokGoodsId, tiktokData.get(0));
            return new AmazonProductInfo(
                    tiktokData.get(0).getGoodsId(),
                    tiktokData.get(0).getProductName(),
                    tiktokData.get(0).getObjectKey(),
                    tiktokData.get(0).getSpecInfo(),
                    tiktokData.get(0).getProductChineseName()
            );
        }
        log.info("开始抓取图片tikTokId{}", tikTokGoodsId);
        return getTikTokData(requestForTiktokHtmlData(tikTokGoodsId), tikTokGoodsId);
    }

    private String requestForTiktokHtmlData(String tiktokId) {
        Request request = new Request.Builder()
                .url(String.format("https://shop.tiktok.com/view/product/%s?__loader=(shop$)/(pdp)/(name$)/(id)/page&__ssrDirect=true", tiktokId))
                .method("GET", null)
                .addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36")
                .addHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
                .addHeader("accept-language", "en-US,en;q=0.9")
                .addHeader("Connection", "keep-alive")
                .build();
        try (Response response = proxyOkhttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                return "";
            }
            String data = response.body() != null ? response.body().string() : "";
            if (data.contains("realRegion")) {
                return data;
            }
            log.warn("抓图失败,tiktokId{}", tiktokId);
            return "";
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private AmazonProductInfo getTikTokData(String jsonData, String tikTokGoodsId) {
        if (StringUtils.isBlank(jsonData) || !jsonData.contains("product_base")) {
            log.info("获取商品详情失败");
            return new AmazonProductInfo();
        }
        try {
            JSONObject productAllInfo = JSON.parseObject(jsonData);
            String name = productAllInfo.getJSONObject("initialData").getJSONObject("productInfo")
                    .getJSONObject("product_base").getString("title");
            String detail = productAllInfo.getJSONObject("initialData").getJSONObject("productInfo")
                    .getJSONObject("product_base").getString("desc_detail");
            String image = productAllInfo.getJSONObject("initialData").getJSONObject("productInfo")
                    .getJSONObject("product_base")
                    .getJSONArray("images").getJSONObject(0)
                    .getJSONArray("url_list").getString(0);
            String detailTextJson = detail.replace("\\n", "\n").replace("\\\"", "\"");
            StringBuilder detailText = new StringBuilder();
            JSONArray detailArray = JSON.parseArray(detailTextJson);
            for (int i = 0; i < detailArray.size(); i++) {
                JSONObject detailData = detailArray.getJSONObject(i);
                if ("text".equals(detailData.getString("type"))) {
                    detailText.append(detailData.getString("text")).append("\n");
                }
            }
            String systemImageUrl = remoteFileService.uploadFileByLink(FileUploadLinkVo.builder()
                    .bucketName(TIKTOK)
                    .fileUrl(image)
                    .useProxy(true)
                    .build());
            if (detailText.length() > 2) {
                detailText.setLength(detailText.length() - 2);
            }
            if (StringUtils.isNotBlank(systemImageUrl)) {
                String targetText = translateService.translate(name,1).getTargetText();
                AmazonProductInfo tikTokGoodsInfo = new AmazonProductInfo(tikTokGoodsId, name, systemImageUrl, detailText.toString(), targetText);
                saveCachePic(tikTokGoodsInfo);
                return tikTokGoodsInfo;
            }
            return new AmazonProductInfo();
        } catch (Exception e) {
            log.warn("处理TikTok数据时异常{},id={}", e.getMessage(), tikTokGoodsId);
            return new AmazonProductInfo();
        }
    }

    private void saveCachePic(AmazonProductInfo tikTokGoodsInfo) {
        AmazonGoodsPic goodsPic = new AmazonGoodsPic();
        goodsPic.setGoodsId(tikTokGoodsInfo.getGoodsId());
        goodsPic.setObjectKey(tikTokGoodsInfo.getObjectKey());
        goodsPic.setProductName(tikTokGoodsInfo.getName());
        goodsPic.setSpecInfo(tikTokGoodsInfo.getSpecInfo());
        goodsPic.setProductChineseName(tikTokGoodsInfo.getProductChineseName());
        amazonGoodsPicService.addAmazonGoodsPic(List.of(goodsPic));
    }

}
