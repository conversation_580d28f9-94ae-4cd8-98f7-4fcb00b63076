package com.wnkx.biz.channel.mapper;

import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserDetailListDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.BusinessChannel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_channel(商家渠道信息表)】的数据库操作Mapper
 * @createDate 2025-01-21 09:11:36
 * @Entity com.ruoyi.system.api.domain.entity.biz.channel.BusinessChannel
 */
public interface BusinessChannelMapper extends BaseMapper<BusinessChannel> {

    /**
     * 查询商家渠道信息数据
     *
     * @param dto
     * @return
     */
    List<BizUserDetailVO> selectBusinessChannelByBusinessId(@Param("dto") BizUserDetailListDTO dto);
}




