package com.wnkx.biz.channel.mapper;

import com.wnkx.db.mapper.SuperMapper;
import com.ruoyi.system.api.domain.entity.biz.channel.OrderMemberMarketingChannel;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_member_marketing_channel(会员市场渠道记录表)】的数据库操作Mapper
* @createDate 2024-12-06 14:56:21
* @Entity com.ruoyi.system.api.domain.entity.biz.channel.OrderMemberMarketingChannel
*/
public interface OrderMemberMarketingChannelMapper extends SuperMapper<OrderMemberMarketingChannel> {

    /**
     * 获取初始化InitOrderMemberMarketingChannel数据
     * @return
     */
    List<OrderMemberMarketingChannel> getInitOrderMemberMarketingChannel();
}




