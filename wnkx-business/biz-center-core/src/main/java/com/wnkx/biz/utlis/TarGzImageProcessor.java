package com.wnkx.biz.utlis;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.enums.ChannelDiscountTypeEnum;
import com.ruoyi.common.core.enums.ChannelTypeEnum;
import com.ruoyi.common.core.enums.FontAlignmentEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.system.api.domain.dto.order.GenerateContentDTO;
import com.ruoyi.system.api.domain.dto.order.PosterDTO;
import com.wnkx.biz.config.ChannelPoserProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveOutputStream;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.rmi.ServerException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;
import java.util.zip.GZIPOutputStream;

@Slf4j
@RequiredArgsConstructor
@Component
public class TarGzImageProcessor {
    private static Integer graphWidth = 350;
    private static Integer graphHeight = 350;
    private static final String FONT_SUFFIX = ".ttf";

    private static final GenerateContentDTO CHANNEL_NAME = new GenerateContentDTO(129, 12, 309, 89, "MiSans-Light", 22, "#FFFFFF", FontAlignmentEnum.LEFT, 132, 29);
    private static final GenerateContentDTO DISCOUNT = new GenerateContentDTO(247, 259, 32, 41, "D-DIN-Bold", 54, "#333333", FontAlignmentEnum.CENTER, 62, 59);
    private static final GenerateContentDTO LB_NO_VIDEO_RATIO_DISCOUNT = new GenerateContentDTO(254, 267, 35, 40, "D-DIN-Bold", 44, "#333333", FontAlignmentEnum.CENTER, 56, 48);
    private static final GenerateContentDTO LB_NO_VIDEO_AMOUNT_DISCOUNT = new GenerateContentDTO(251, 271, 36, 38, "D-DIN-Bold", 40, "#333333", FontAlignmentEnum.CENTER, 61, 43);
    private static final GenerateContentDTO LB_VIDEO_RATIO_DISCOUNT = new GenerateContentDTO(254, 267, 35, 40, "D-DIN-Bold", 44, "#333333", FontAlignmentEnum.CENTER, 56, 48);
    private static final GenerateContentDTO LB_VIDEO_AMOUNT_DISCOUNT = new GenerateContentDTO(251, 271, 36, 38, "D-DIN-Bold", 40, "#333333", FontAlignmentEnum.CENTER, 61, 43);
    private static final GenerateContentDTO ACTIVITY_TIME_LIMIT = new GenerateContentDTO(247, 308, 24, 24, "MiSans-Normal", 14, "#333333", FontAlignmentEnum.CENTER, 79, 19);
    private static final GenerateContentDTO NO_THRESHOLD_COUPON = new GenerateContentDTO(252, 309, 16, 22, "MiSans-Normal", 19, "#333333", FontAlignmentEnum.CENTER, 76, 25);
    private static final GenerateContentDTO ACTIVITY_TIME = new GenerateContentDTO(249, 326, 14, 23, "MiSans-Light", 8, "#333333", FontAlignmentEnum.CENTER, 78, 10);
    private static final GenerateContentDTO TIP_DISCOUNT = new GenerateContentDTO(318, 293, 38, 20, "MiSans-Medium", 14, "#333333", FontAlignmentEnum.CENTER, 12, 9);
    private static final GenerateContentDTO SEED_CODE = new GenerateContentDTO(132, 306, 23, 178, "D-DINExp-Bold", 16, "#333333", FontAlignmentEnum.CENTER, 40, 19);
    private static final GenerateContentDTO LB_NO_VIDEO_SEED_CODE = new GenerateContentDTO(132, 308, 23, 178, "D-DINExp-Bold", 16, "#333333", FontAlignmentEnum.CENTER, 40, 19);
    private static final GenerateContentDTO LB_VIDEO_SEED_CODE = new GenerateContentDTO(137, 313, 18, 173, "D-DINExp-Bold", 16, "#333333", FontAlignmentEnum.CENTER, 40, 19);
    private static final GenerateContentDTO QR_CODE = new GenerateContentDTO(20, 266, 18, 264, 66, 66);

    private static final GenerateContentDTO DISCOUNT_BIG = new GenerateContentDTO(248, 260, 31, 33, "D-DIN-Bold", 54, "#333333", FontAlignmentEnum.CENTER, 69, 59);
    private static final GenerateContentDTO TIP_DISCOUNT_BIG = new GenerateContentDTO(312, 287, 45, 26, "MiSans-Medium", 14, "#333333", FontAlignmentEnum.CENTER, 12, 19);
    private static final GenerateContentDTO ACTIVITY_TIME_LIMIT_BIG = new GenerateContentDTO(250, 312, 17, 25, "MiSans-Normal", 18, "#333333", FontAlignmentEnum.CENTER, 79, 19);

    /**
     * 我已经在蜗牛海拍拍摄
     */
    private static final GenerateContentDTO LB_VIDEO_CONTENT_FIRST = new GenerateContentDTO(45, 242, 91, 191, "PingFang-Regular", 12, "#0D2363", FontAlignmentEnum.CENTER, 115, 17);
    /**
     * 视频数量长度 = （3 - n） * 8
     * rigth = 163 + （3 - n） * 8
     * textboxWidth =  25 + （3 - n） * 8
     */
    private static final GenerateContentDTO LB_VIDEO_CONTENT_VIDEO_NUM = new GenerateContentDTO(162, 242, 91, 163, "D-DIN-Bold", 16, "#3366FF", FontAlignmentEnum.CENTER, 25, 17);

    /**
     * left =  189 - （3 - n） * 8
     * rigth = 163 + （3 - n） * 8
     */
    private static final GenerateContentDTO LB_VIDEO_CONTENT_FINALLY = new GenerateContentDTO(189, 242, 91, 46, "PingFang-Regular", 12, "#0D2363", FontAlignmentEnum.CENTER, 115, 17);

    private final ThreadPoolTaskExecutor asyncPoolTaskExecutor;
    private static BufferedImage templateModelImage;
    private static BufferedImage templateModelImage20250407;
    private static BufferedImage templateImage;
    private static BufferedImage templateImage20250407;
    private static BufferedImage templateImage2025052301;
    private static BufferedImage templateImage2025052302;
    private static BufferedImage templateImage2025052311;
    private static BufferedImage templateImage2025052312;
    private static BufferedImage templateImage2025060401;
    private static BufferedImage templateImage2025060402;
    private static BufferedImage templateImage2025060411;
    private static BufferedImage templateImage2025060412;

    private final ChannelPoserProperties channelPoserProperties;
    private final OkHttpClient okHttpClient;


    @PostConstruct
    private void init() {
        try {
            // 从配置类获取路径
            String templateModelPath = channelPoserProperties.getTemplateModelPath();
            String templateModelPath20250407 = channelPoserProperties.getTemplateModelPath20250407();
            String templatePath = channelPoserProperties.getTemplatePath();
            String templatePath20250407 = channelPoserProperties.getTemplatePath20250407();
            String templatePath2025052301 = channelPoserProperties.getTemplatePath2025052301();
            String templatePath2025052302 = channelPoserProperties.getTemplatePath2025052302();
            String templatePath2025052311 = channelPoserProperties.getTemplatePath2025052311();
            String templatePath2025052312 = channelPoserProperties.getTemplatePath2025052312();
            String templatePath2025060401 = channelPoserProperties.getTemplatePath2025060401();
            String templatePath2025060402 = channelPoserProperties.getTemplatePath2025060402();
            String templatePath2025060411 = channelPoserProperties.getTemplatePath2025060411();
            String templatePath2025060412 = channelPoserProperties.getTemplatePath2025060412();

            // 加载模板图片
            InputStream templateModelInputStream = TarGzImageProcessor.class.getResourceAsStream(templateModelPath);
            if (templateModelInputStream == null) {
                log.error("读取海报模特模板失败");
                throw new RuntimeException("读取海报模特模板失败");
            }
            templateModelImage = ImageIO.read(templateModelInputStream);

            InputStream templateModelInputStream20250407 = TarGzImageProcessor.class.getResourceAsStream(templateModelPath20250407);
            if (templateModelInputStream20250407 == null) {
                log.error("读取海报模特模板失败");
                throw new RuntimeException("读取海报模特模板失败");
            }
            templateModelImage20250407 = ImageIO.read(templateModelInputStream20250407);

            InputStream templateInputStream = TarGzImageProcessor.class.getResourceAsStream(templatePath);
            if (templateInputStream == null) {
                log.error("读取海报模板失败");
                throw new RuntimeException("读取海报模板失败");
            }
            templateImage = ImageIO.read(templateInputStream);

            InputStream templateInputStream20250407 = TarGzImageProcessor.class.getResourceAsStream(templatePath20250407);
            if (templateInputStream20250407 == null) {
                log.error("读取海报模板失败");
                throw new RuntimeException("读取海报模板失败");
            }
            templateImage20250407 = ImageIO.read(templateInputStream20250407);

            InputStream templateInputStream2025052301 = TarGzImageProcessor.class.getResourceAsStream(templatePath2025052301);
            if (templateInputStream2025052301 == null) {
                log.error("读取海报模板失败");
                throw new RuntimeException("读取海报模板失败");
            }
            templateImage2025052301 = ImageIO.read(templateInputStream2025052301);

            InputStream templateInputStream2025052302 = TarGzImageProcessor.class.getResourceAsStream(templatePath2025052302);
            if (templateInputStream2025052302 == null) {
                log.error("读取海报模板失败");
                throw new RuntimeException("读取海报模板失败");
            }
            templateImage2025052302= ImageIO.read(templateInputStream2025052302);


            InputStream templateInputStream2025052311 = TarGzImageProcessor.class.getResourceAsStream(templatePath2025052311);
            if (templateInputStream2025052311 == null) {
                log.error("读取海报模板失败");
                throw new RuntimeException("读取海报模板失败");
            }
            templateImage2025052311 = ImageIO.read(templateInputStream2025052311);


            InputStream templateInputStream2025052312 = TarGzImageProcessor.class.getResourceAsStream(templatePath2025052312);
            if (templateInputStream2025052312 == null) {
                log.error("读取海报模板失败");
                throw new RuntimeException("读取海报模板失败");
            }
            templateImage2025052312 = ImageIO.read(templateInputStream2025052312);

            InputStream templateInputStream2025060401 = TarGzImageProcessor.class.getResourceAsStream(templatePath2025060401);
            if (templateInputStream2025060401 == null) {
                log.error("读取海报模板失败");
                throw new RuntimeException("读取海报模板失败");
            }
            templateImage2025060401 = ImageIO.read(templateInputStream2025060401);

            InputStream templateInputStream2025060402 = TarGzImageProcessor.class.getResourceAsStream(templatePath2025060402);
            if (templateInputStream2025060402 == null) {
                log.error("读取海报模板失败");
                throw new RuntimeException("读取海报模板失败");
            }
            templateImage2025060402 = ImageIO.read(templateInputStream2025060402);

            InputStream templateInputStream2025060411 = TarGzImageProcessor.class.getResourceAsStream(templatePath2025060411);
            if (templateInputStream2025060411 == null) {
                log.error("读取海报模板失败");
                throw new RuntimeException("读取海报模板失败");
            }
            templateImage2025060411 = ImageIO.read(templateInputStream2025060411);

            InputStream templateInputStream2025060412 = TarGzImageProcessor.class.getResourceAsStream(templatePath2025060412);
            if (templateInputStream2025060412 == null) {
                log.error("读取海报模板失败");
                throw new RuntimeException("读取海报模板失败");
            }
            templateImage2025060412 = ImageIO.read(templateInputStream2025060412);


        } catch (IOException e) {
            log.error("初始化模板图片失败", e);
            throw new RuntimeException("初始化模板图片失败");
        }
    }

    public void generatePosterToTarGz(String activityName, List<PosterDTO> posterDTOS, HttpServletResponse response) {
        try {
            // 创建线程池
            List<Future<File>> futures = new ArrayList<>();

            // 多线程生成图片
            for (PosterDTO posterDTO : posterDTOS) {
                futures.add(asyncPoolTaskExecutor.submit(() -> generateImage(selectTemplate(posterDTO), posterDTO)));
            }

            // 等待任务完成并收集生成的文件
            List<File> generatedFiles = new ArrayList<>();
            for (Future<File> future : futures) {
                generatedFiles.add(future.get());
            }

            // 打包并压缩为 .tar.gz
            String tarGzFileName = URLEncoder.encode(activityName, StandardCharsets.UTF_8) + ".tar.gz";
            String dirname = activityName + ".tar.gz";
            File tarGzFile = new File(System.getProperty("java.io.tmpdir"), dirname);
            compressToTarGz(generatedFiles, tarGzFile.getAbsolutePath());

            // 设置响应头
            response.setContentType("application/octet-stream; charset=UTF-8");

            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + tarGzFileName.replace("+", "%20"));

            // 将文件写入响应输出流
            try (InputStream fis = new FileInputStream(tarGzFile);
                 OutputStream os = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }

            // 删除临时文件
            for (File file : generatedFiles) {
                file.delete();
            }
            tarGzFile.delete();
        } catch (Exception e) {
            log.error("生成海报失败", e);
            throw new ServiceException("生成海报失败,请稍后重试~");
        }
    }
    public void generateFissionPosterToTarGz(String activityName, List<PosterDTO> posterDTOS, HttpServletResponse response) {
        try {
            // 创建线程池
            List<Future<File>> futures = new ArrayList<>();

            // 多线程生成图片
            for (PosterDTO posterDTO : posterDTOS) {
                futures.add(asyncPoolTaskExecutor.submit(() -> generateFissionImage(selectTemplate(posterDTO), posterDTO)));
            }

            // 等待任务完成并收集生成的文件
            List<File> generatedFiles = new ArrayList<>();
            for (Future<File> future : futures) {
                generatedFiles.add(future.get());
            }

            // 打包并压缩为 .tar.gz
            String tarGzFileName = URLEncoder.encode(activityName, StandardCharsets.UTF_8) + ".tar.gz";
            String dirname = activityName + ".tar.gz";
            File tarGzFile = new File(System.getProperty("java.io.tmpdir"), dirname);
            compressToTarGz(generatedFiles, tarGzFile.getAbsolutePath());

            // 设置响应头
            response.setContentType("application/octet-stream; charset=UTF-8");

            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + tarGzFileName.replace("+", "%20"));

            // 将文件写入响应输出流
            try (InputStream fis = new FileInputStream(tarGzFile);
                 OutputStream os = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }

            // 删除临时文件
            for (File file : generatedFiles) {
                file.delete();
            }
            tarGzFile.delete();
        } catch (Exception e) {
            log.error("生成海报失败", e);
            throw new ServiceException("生成海报失败,请稍后重试~");
        }
    }

    /**
     * 选择海报模板
     */
    private BufferedImage selectTemplate(PosterDTO posterDTO) {
        BufferedImage template;
        if (ChannelTypeEnum.FISSION.getCode().equals(posterDTO.getChannelType())){
            //裂变渠道海报模板
            if (ObjectUtil.isNull(posterDTO.getVideoNum()) || posterDTO.getVideoNum() == 0) {
                //无视频
                if (ChannelDiscountTypeEnum.FIXED_RATIO.getCode().equals(posterDTO.getDiscountType())) {
                    //固定金额
                    template = templateImage2025052311;
                }else {
                    //固定折扣
                    template = templateImage2025052312;
                }
            }else {
                //有视频
                if (ChannelDiscountTypeEnum.FIXED_RATIO.getCode().equals(posterDTO.getDiscountType())) {
                    //固定金额
                    template = templateImage2025052301;
                }else {
                    //固定折扣
                    template = templateImage2025052302;
                }
            }
        }else if (channelPoserProperties.getTemplateType() == 1) {
            if (CharSequenceUtil.isNotBlank(posterDTO.getActivityTime())){
                //使用旧数据
                if (CharSequenceUtil.isNotBlank(posterDTO.getPosterName())) {
                    template = templateModelImage;
                } else {
                    template = templateModelImage20250407;
                }
            }else {
                //有模特
                if (CharSequenceUtil.isNotBlank(posterDTO.getPosterName())) {
                    //有海报名称
                    if (ChannelDiscountTypeEnum.FIXED_RATIO.getCode().equals(posterDTO.getDiscountType())) {
                        //固定比例
                        template = templateImage2025060411;
                    }else {
                        //固定金额
                        template = templateImage2025060412;
                    }
                } else {
                    //无海报名称
                    if (ChannelDiscountTypeEnum.FIXED_RATIO.getCode().equals(posterDTO.getDiscountType())) {
                        //固定比例
                        template = templateImage2025060401;
                    }else {
                        //固定金额
                        template = templateImage2025060402;
                    }
                }
            }
        } else {
            if (CharSequenceUtil.isNotBlank(posterDTO.getPosterName())) {
                template = templateImage;
            } else {
                template = templateImage20250407;
            }
        }
        return template;
    }

    public void generatePosterPreview(PosterDTO posterDTO, HttpServletResponse response) {
        try {
            // 生成图片
            BufferedImage image = generateImageBuffered(selectTemplate(posterDTO), posterDTO);

            // 设置响应头，返回图片
            response.setContentType("image/png");
            response.setHeader(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
            response.setHeader(HttpHeaders.PRAGMA, "no-cache");
            response.setDateHeader(HttpHeaders.EXPIRES, 0);

            // 写入图片流到响应
            try (OutputStream os = response.getOutputStream()) {
                ImageIO.write(image, "png", os);
                os.flush();
            }
        } catch (Exception e) {
            log.error("生成海报失败", e);
            throw new ServiceException("生成海报失败,请稍后重试~");
        }
    }


    public void generateFissionPosterPreview(PosterDTO posterDTO, HttpServletResponse response) {
        try {
            // 生成图片
            BufferedImage image = generateFissionImageBuffered(selectTemplate(posterDTO), posterDTO);

            // 设置响应头，返回图片
            response.setContentType("image/png");
            response.setHeader(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
            response.setHeader(HttpHeaders.PRAGMA, "no-cache");
            response.setDateHeader(HttpHeaders.EXPIRES, 0);

            // 写入图片流到响应
            try (OutputStream os = response.getOutputStream()) {
                ImageIO.write(image, "png", os);
                os.flush();
            }
        } catch (Exception e) {
            log.error("生成海报失败", e);
            throw new ServiceException("生成海报失败,请稍后重试~");
        }
    }

    public void generatePosterToPic(PosterDTO posterDTO, HttpServletResponse response) {
        try {
            File file = generateImage(selectTemplate(posterDTO), posterDTO);

            // 设置响应头
            response.setContentType("application/octet-stream; charset=UTF-8");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + URLEncoder.encode(file.getName(), StandardCharsets.UTF_8).replace("+", "%20"));

            // 将文件写入响应输出流
            try (InputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }

            file.delete();
        } catch (Exception e) {
            log.error("生成海报失败", e);
            throw new ServiceException("生成海报失败,请稍后重试~");
        }
    }
    public void generateFissionPosterToPic(PosterDTO posterDTO, HttpServletResponse response) {
        try {
            File file = generateFissionImage(selectTemplate(posterDTO), posterDTO);

            // 设置响应头
            response.setContentType("application/octet-stream; charset=UTF-8");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + URLEncoder.encode(file.getName(), StandardCharsets.UTF_8).replace("+", "%20"));

            // 将文件写入响应输出流
            try (InputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }

            file.delete();
        } catch (Exception e) {
            log.error("生成海报失败", e);
            throw new ServiceException("生成海报失败,请稍后重试~");
        }
    }

    /**
     * 基于模板生成新图片
     */
    private File generateImage(BufferedImage template, PosterDTO posterDTO) throws IOException {
        BufferedImage newImage = generateImageBuffered(template, posterDTO);
        // 保存图片到临时文件
        String name = getFileName(posterDTO);
        File outputFile = new File(System.getProperty("java.io.tmpdir"), name + ".png");
        ImageIO.write(newImage, "png", outputFile);

        return outputFile;
    }
    /**
     * 基于模板生成新图片
     */
    private File generateFissionImage(BufferedImage template, PosterDTO posterDTO) throws IOException {
        BufferedImage newImage = generateFissionImageBuffered(template, posterDTO);
        // 保存图片到临时文件
        String name = getFileName(posterDTO);
        File outputFile = new File(System.getProperty("java.io.tmpdir"),name  + ".png");
        ImageIO.write(newImage, "png", outputFile);

        return outputFile;
    }

    private String getFileName(PosterDTO posterDTO) {
        String name = (CharSequenceUtil.isNotBlank(posterDTO.getPosterName()) ? posterDTO.getPosterName() : posterDTO.getChannelName()) + StrPool.DASHED;
        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(posterDTO.getDiscountType())){
            name = name + "会员立减"+ posterDTO.getDiscount() +"元（"+ posterDTO.getSeedCode() + "）";
        }else {
           name = name +  "会员" + posterDTO.getDiscount() + "折（"+ posterDTO.getSeedCode() + "）";

        }
        return name;
    }

    private BufferedImage generateImageBuffered(BufferedImage template, PosterDTO posterDTO) throws IOException {
        // 克隆模板图片
        BufferedImage newImage = new BufferedImage(
                template.getWidth(),
                template.getHeight(),
                template.getType()
        );
        Graphics2D g2d = newImage.createGraphics();
        g2d.drawImage(template, 0, 0, null);

        // 设置抗锯齿和字体渲染优化
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_LCD_HRGB);

        List<GenerateContentDTO> generateContentDTOS = new ArrayList<>();
        if (StrUtil.isNotBlank(posterDTO.getPosterName())){
            generateContentDTOS.add(GenerateContentDTO.builder().content(posterDTO.getPosterName()).left(CHANNEL_NAME.getLeft() * channelPoserProperties.getPixelMultiple()).top(CHANNEL_NAME.getTop() * channelPoserProperties.getPixelMultiple()).bottom(CHANNEL_NAME.getBottom() * channelPoserProperties.getPixelMultiple()).right(CHANNEL_NAME.getRight() * channelPoserProperties.getPixelMultiple()).font(CHANNEL_NAME.getFont()).fontSize(CHANNEL_NAME.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(CHANNEL_NAME.getAlignment()).color(CHANNEL_NAME.getColor()).textboxWidth(CHANNEL_NAME.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(CHANNEL_NAME.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());
        }
        if (CharSequenceUtil.isNotBlank(posterDTO.getActivityTime())){
            generateContentDTOS.add(GenerateContentDTO.builder().content(posterDTO.getDiscount()).left(DISCOUNT.getLeft() * channelPoserProperties.getPixelMultiple()).top(DISCOUNT.getTop() * channelPoserProperties.getPixelMultiple()).bottom(DISCOUNT.getBottom() * channelPoserProperties.getPixelMultiple()).right(DISCOUNT.getRight() * channelPoserProperties.getPixelMultiple()).font(DISCOUNT.getFont()).fontSize(DISCOUNT.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(DISCOUNT.getAlignment()).color(DISCOUNT.getColor()).textboxWidth(DISCOUNT.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(DISCOUNT.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());
            generateContentDTOS.add(GenerateContentDTO.builder().content(posterDTO.getActivityTimeLimit()).left(ACTIVITY_TIME_LIMIT.getLeft() * channelPoserProperties.getPixelMultiple()).top(ACTIVITY_TIME_LIMIT.getTop() * channelPoserProperties.getPixelMultiple()).bottom(ACTIVITY_TIME_LIMIT.getBottom() * channelPoserProperties.getPixelMultiple()).right(ACTIVITY_TIME_LIMIT.getRight() * channelPoserProperties.getPixelMultiple()).font(ACTIVITY_TIME_LIMIT.getFont()).fontSize(ACTIVITY_TIME_LIMIT.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(ACTIVITY_TIME_LIMIT.getAlignment()).color(ACTIVITY_TIME_LIMIT.getColor()).textboxWidth(ACTIVITY_TIME_LIMIT.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(ACTIVITY_TIME_LIMIT.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());
            generateContentDTOS.add(GenerateContentDTO.builder().content(posterDTO.getActivityTime()).left(ACTIVITY_TIME.getLeft() * channelPoserProperties.getPixelMultiple()).top(ACTIVITY_TIME.getTop() * channelPoserProperties.getPixelMultiple()).bottom(ACTIVITY_TIME.getBottom() * channelPoserProperties.getPixelMultiple()).right(ACTIVITY_TIME.getRight() * channelPoserProperties.getPixelMultiple()).font(ACTIVITY_TIME.getFont()).fontSize(ACTIVITY_TIME.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(ACTIVITY_TIME.getAlignment()).color(ACTIVITY_TIME.getColor()).textboxWidth(ACTIVITY_TIME.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(ACTIVITY_TIME.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());
            generateContentDTOS.add(GenerateContentDTO.builder().content("折").left(TIP_DISCOUNT_BIG.getLeft() * channelPoserProperties.getPixelMultiple()).top(TIP_DISCOUNT_BIG.getTop() * channelPoserProperties.getPixelMultiple()).bottom(TIP_DISCOUNT_BIG.getBottom() * channelPoserProperties.getPixelMultiple()).right(TIP_DISCOUNT_BIG.getRight() * channelPoserProperties.getPixelMultiple()).font(TIP_DISCOUNT_BIG.getFont()).fontSize(TIP_DISCOUNT_BIG.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(TIP_DISCOUNT_BIG.getAlignment()).color(TIP_DISCOUNT_BIG.getColor()).textboxWidth(TIP_DISCOUNT_BIG.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(TIP_DISCOUNT_BIG.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());
            generateContentDTOS.add(GenerateContentDTO.builder().content(posterDTO.getSeedCode()).left(SEED_CODE.getLeft() * channelPoserProperties.getPixelMultiple()).top(SEED_CODE.getTop() * channelPoserProperties.getPixelMultiple()).bottom(SEED_CODE.getBottom() * channelPoserProperties.getPixelMultiple()).right(SEED_CODE.getRight() * channelPoserProperties.getPixelMultiple()).font(SEED_CODE.getFont()).fontSize(SEED_CODE.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(SEED_CODE.getAlignment()).color(SEED_CODE.getColor()).textboxWidth(SEED_CODE.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(SEED_CODE.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());

        } else {
            //无视频
            if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(posterDTO.getDiscountType())) {
                //固定金额
                generateContentDTOS.add(GenerateContentDTO.builder().content(posterDTO.getDiscount()).left(LB_NO_VIDEO_AMOUNT_DISCOUNT.getLeft() * channelPoserProperties.getPixelMultiple()).top(LB_NO_VIDEO_AMOUNT_DISCOUNT.getTop() * channelPoserProperties.getPixelMultiple()).bottom(LB_NO_VIDEO_AMOUNT_DISCOUNT.getBottom() * channelPoserProperties.getPixelMultiple()).right(LB_NO_VIDEO_AMOUNT_DISCOUNT.getRight() * channelPoserProperties.getPixelMultiple()).font(LB_NO_VIDEO_AMOUNT_DISCOUNT.getFont()).fontSize(LB_NO_VIDEO_AMOUNT_DISCOUNT.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(LB_NO_VIDEO_AMOUNT_DISCOUNT.getAlignment()).color(LB_NO_VIDEO_AMOUNT_DISCOUNT.getColor()).textboxWidth(LB_NO_VIDEO_AMOUNT_DISCOUNT.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(LB_NO_VIDEO_AMOUNT_DISCOUNT.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());
            } else {
                //固定折扣
                generateContentDTOS.add(GenerateContentDTO.builder().content(posterDTO.getDiscount()).left(LB_NO_VIDEO_RATIO_DISCOUNT.getLeft() * channelPoserProperties.getPixelMultiple()).top(LB_NO_VIDEO_RATIO_DISCOUNT.getTop() * channelPoserProperties.getPixelMultiple()).bottom(LB_NO_VIDEO_RATIO_DISCOUNT.getBottom() * channelPoserProperties.getPixelMultiple()).right(LB_NO_VIDEO_RATIO_DISCOUNT.getRight() * channelPoserProperties.getPixelMultiple()).font(LB_NO_VIDEO_RATIO_DISCOUNT.getFont()).fontSize(LB_NO_VIDEO_RATIO_DISCOUNT.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(LB_NO_VIDEO_RATIO_DISCOUNT.getAlignment()).color(LB_NO_VIDEO_RATIO_DISCOUNT.getColor()).textboxWidth(LB_NO_VIDEO_RATIO_DISCOUNT.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(LB_NO_VIDEO_RATIO_DISCOUNT.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());
            }
            generateContentDTOS.add(GenerateContentDTO.builder().content(posterDTO.getSeedCode()).left(LB_NO_VIDEO_SEED_CODE.getLeft() * channelPoserProperties.getPixelMultiple()).top(LB_NO_VIDEO_SEED_CODE.getTop() * channelPoserProperties.getPixelMultiple()).bottom(LB_NO_VIDEO_SEED_CODE.getBottom() * channelPoserProperties.getPixelMultiple()).right(LB_NO_VIDEO_SEED_CODE.getRight() * channelPoserProperties.getPixelMultiple()).font(LB_NO_VIDEO_SEED_CODE.getFont()).fontSize(LB_NO_VIDEO_SEED_CODE.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(LB_NO_VIDEO_SEED_CODE.getAlignment()).color(LB_NO_VIDEO_SEED_CODE.getColor()).textboxWidth(LB_NO_VIDEO_SEED_CODE.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(LB_NO_VIDEO_SEED_CODE.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());

        }
        g2d.drawImage(downloadImageFromUrl(posterDTO.getWeChatUrl()), QR_CODE.getLeft() * channelPoserProperties.getPixelMultiple(), QR_CODE.getTop() * channelPoserProperties.getPixelMultiple(), QR_CODE.getTextboxWidth() * channelPoserProperties.getPixelMultiple(), QR_CODE.getTextboxHeight() * channelPoserProperties.getPixelMultiple(), null);

        generateContent(generateContentDTOS, g2d);

        // 释放资源
        g2d.dispose();
        return newImage;
    }


    /**
     * 裂变渠道海报数据
     * @param template
     * @param posterDTO
     * @return
     * @throws IOException
     */
    private BufferedImage generateFissionImageBuffered(BufferedImage template, PosterDTO posterDTO) throws IOException {
        // 克隆模板图片
        BufferedImage newImage = new BufferedImage(
                template.getWidth(),
                template.getHeight(),
                template.getType()
        );
        Graphics2D g2d = newImage.createGraphics();
        g2d.drawImage(template, 0, 0, null);

        // 设置抗锯齿和字体渲染优化
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_LCD_HRGB);

        List<GenerateContentDTO> generateContentDTOS = new ArrayList<>();
        generateContentDTOS.add(GenerateContentDTO.builder().content(posterDTO.getPosterName()).left(CHANNEL_NAME.getLeft() * channelPoserProperties.getPixelMultiple()).top(CHANNEL_NAME.getTop() * channelPoserProperties.getPixelMultiple()).bottom(CHANNEL_NAME.getBottom() * channelPoserProperties.getPixelMultiple()).right(CHANNEL_NAME.getRight() * channelPoserProperties.getPixelMultiple()).font(CHANNEL_NAME.getFont()).fontSize(CHANNEL_NAME.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(CHANNEL_NAME.getAlignment()).color(CHANNEL_NAME.getColor()).textboxWidth(CHANNEL_NAME.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(CHANNEL_NAME.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());
        if (ObjectUtil.isNull(posterDTO.getVideoNum()) || posterDTO.getVideoNum() == 0) {
            //无视频
            if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(posterDTO.getDiscountType())) {
                //固定金额
                generateContentDTOS.add(GenerateContentDTO.builder().content(posterDTO.getDiscount()).left(LB_NO_VIDEO_AMOUNT_DISCOUNT.getLeft() * channelPoserProperties.getPixelMultiple()).top(LB_NO_VIDEO_AMOUNT_DISCOUNT.getTop() * channelPoserProperties.getPixelMultiple()).bottom(LB_NO_VIDEO_AMOUNT_DISCOUNT.getBottom() * channelPoserProperties.getPixelMultiple()).right(LB_NO_VIDEO_AMOUNT_DISCOUNT.getRight() * channelPoserProperties.getPixelMultiple()).font(LB_NO_VIDEO_AMOUNT_DISCOUNT.getFont()).fontSize(LB_NO_VIDEO_AMOUNT_DISCOUNT.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(LB_NO_VIDEO_AMOUNT_DISCOUNT.getAlignment()).color(LB_NO_VIDEO_AMOUNT_DISCOUNT.getColor()).textboxWidth(LB_NO_VIDEO_AMOUNT_DISCOUNT.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(LB_NO_VIDEO_AMOUNT_DISCOUNT.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());
            } else{
                //固定折扣
                generateContentDTOS.add(GenerateContentDTO.builder().content(posterDTO.getDiscount()).left(LB_NO_VIDEO_RATIO_DISCOUNT.getLeft() * channelPoserProperties.getPixelMultiple()).top(LB_NO_VIDEO_RATIO_DISCOUNT.getTop() * channelPoserProperties.getPixelMultiple()).bottom(LB_NO_VIDEO_RATIO_DISCOUNT.getBottom() * channelPoserProperties.getPixelMultiple()).right(LB_NO_VIDEO_RATIO_DISCOUNT.getRight() * channelPoserProperties.getPixelMultiple()).font(LB_NO_VIDEO_RATIO_DISCOUNT.getFont()).fontSize(LB_NO_VIDEO_RATIO_DISCOUNT.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(LB_NO_VIDEO_RATIO_DISCOUNT.getAlignment()).color(LB_NO_VIDEO_RATIO_DISCOUNT.getColor()).textboxWidth(LB_NO_VIDEO_RATIO_DISCOUNT.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(LB_NO_VIDEO_RATIO_DISCOUNT.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());
            }
            generateContentDTOS.add(GenerateContentDTO.builder().content(posterDTO.getSeedCode()).left(LB_NO_VIDEO_SEED_CODE.getLeft() * channelPoserProperties.getPixelMultiple()).top(LB_NO_VIDEO_SEED_CODE.getTop() * channelPoserProperties.getPixelMultiple()).bottom(LB_NO_VIDEO_SEED_CODE.getBottom() * channelPoserProperties.getPixelMultiple()).right(LB_NO_VIDEO_SEED_CODE.getRight() * channelPoserProperties.getPixelMultiple()).font(LB_NO_VIDEO_SEED_CODE.getFont()).fontSize(LB_NO_VIDEO_SEED_CODE.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(LB_NO_VIDEO_SEED_CODE.getAlignment()).color(LB_NO_VIDEO_SEED_CODE.getColor()).textboxWidth(LB_NO_VIDEO_SEED_CODE.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(LB_NO_VIDEO_SEED_CODE.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());

        } else {
            String videoNumStr = "";
            if (posterDTO.getVideoNum().compareTo(999) > 0) {
                videoNumStr = "999+";
            }else {
                videoNumStr = posterDTO.getVideoNum().toString();
            }
            int videoNumLength = videoNumStr.length();
            //有视频
            // “我已在蜗牛海拍拍摄
            generateContentDTOS.add(GenerateContentDTO.builder().content("“我已在蜗牛海拍拍摄").left(LB_VIDEO_CONTENT_FIRST.getLeft() * channelPoserProperties.getPixelMultiple()).top(LB_VIDEO_CONTENT_FIRST.getTop() * channelPoserProperties.getPixelMultiple()).bottom(LB_VIDEO_CONTENT_FIRST.getBottom() * channelPoserProperties.getPixelMultiple()).right(LB_VIDEO_CONTENT_FIRST.getRight() * channelPoserProperties.getPixelMultiple()).font(LB_VIDEO_CONTENT_FIRST.getFont()).fontSize(LB_VIDEO_CONTENT_FIRST.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(LB_VIDEO_CONTENT_FIRST.getAlignment()).color(LB_VIDEO_CONTENT_FIRST.getColor()).textboxWidth(LB_VIDEO_CONTENT_FIRST.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(LB_VIDEO_CONTENT_FIRST.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());

            //视频数据
            generateContentDTOS.add(GenerateContentDTO.builder().content(videoNumStr).left(LB_VIDEO_CONTENT_VIDEO_NUM.getLeft() * channelPoserProperties.getPixelMultiple()).top(LB_VIDEO_CONTENT_VIDEO_NUM.getTop() * channelPoserProperties.getPixelMultiple()).bottom(LB_VIDEO_CONTENT_VIDEO_NUM.getBottom() * channelPoserProperties.getPixelMultiple()).right((LB_VIDEO_CONTENT_VIDEO_NUM.getRight() + (3 - videoNumLength) * 8) * channelPoserProperties.getPixelMultiple()).font(LB_VIDEO_CONTENT_VIDEO_NUM.getFont()).fontSize(LB_VIDEO_CONTENT_VIDEO_NUM.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(LB_VIDEO_CONTENT_VIDEO_NUM.getAlignment()).color(LB_VIDEO_CONTENT_VIDEO_NUM.getColor()).textboxWidth((LB_VIDEO_CONTENT_VIDEO_NUM.getTextboxWidth() - (3 - videoNumLength) * 8) * channelPoserProperties.getPixelMultiple()).textboxHeight(LB_VIDEO_CONTENT_VIDEO_NUM.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());

            //个视频，推荐你来拍”
            generateContentDTOS.add(GenerateContentDTO.builder().content("个视频，推荐你来拍”").left((LB_VIDEO_CONTENT_FINALLY.getLeft() - (3 - videoNumLength) * 8) * channelPoserProperties.getPixelMultiple()).top(LB_VIDEO_CONTENT_FINALLY.getTop() * channelPoserProperties.getPixelMultiple()).bottom(LB_VIDEO_CONTENT_FINALLY.getBottom() * channelPoserProperties.getPixelMultiple()).right((LB_VIDEO_CONTENT_FINALLY.getRight() + (3 - videoNumLength) * 8) * channelPoserProperties.getPixelMultiple()).font(LB_VIDEO_CONTENT_FINALLY.getFont()).fontSize(LB_VIDEO_CONTENT_FINALLY.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(LB_VIDEO_CONTENT_FINALLY.getAlignment()).color(LB_VIDEO_CONTENT_FINALLY.getColor()).textboxWidth(LB_VIDEO_CONTENT_FINALLY.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(LB_VIDEO_CONTENT_FINALLY.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());

            if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(posterDTO.getDiscountType())) {
                //固定金额
                generateContentDTOS.add(GenerateContentDTO.builder().content(posterDTO.getDiscount()).left(LB_VIDEO_AMOUNT_DISCOUNT.getLeft() * channelPoserProperties.getPixelMultiple()).top(LB_VIDEO_AMOUNT_DISCOUNT.getTop() * channelPoserProperties.getPixelMultiple()).bottom(LB_VIDEO_AMOUNT_DISCOUNT.getBottom() * channelPoserProperties.getPixelMultiple()).right(LB_VIDEO_AMOUNT_DISCOUNT.getRight() * channelPoserProperties.getPixelMultiple()).font(LB_VIDEO_AMOUNT_DISCOUNT.getFont()).fontSize(LB_VIDEO_AMOUNT_DISCOUNT.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(LB_VIDEO_AMOUNT_DISCOUNT.getAlignment()).color(LB_VIDEO_AMOUNT_DISCOUNT.getColor()).textboxWidth(LB_VIDEO_AMOUNT_DISCOUNT.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(LB_VIDEO_AMOUNT_DISCOUNT.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());
            } else{
                //固定折扣
                generateContentDTOS.add(GenerateContentDTO.builder().content(posterDTO.getDiscount()).left(LB_VIDEO_RATIO_DISCOUNT.getLeft() * channelPoserProperties.getPixelMultiple()).top(LB_VIDEO_RATIO_DISCOUNT.getTop() * channelPoserProperties.getPixelMultiple()).bottom(LB_VIDEO_RATIO_DISCOUNT.getBottom() * channelPoserProperties.getPixelMultiple()).right(LB_VIDEO_RATIO_DISCOUNT.getRight() * channelPoserProperties.getPixelMultiple()).font(LB_VIDEO_RATIO_DISCOUNT.getFont()).fontSize(LB_VIDEO_RATIO_DISCOUNT.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(LB_VIDEO_RATIO_DISCOUNT.getAlignment()).color(LB_VIDEO_RATIO_DISCOUNT.getColor()).textboxWidth(LB_VIDEO_RATIO_DISCOUNT.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(LB_VIDEO_RATIO_DISCOUNT.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());
            }
            generateContentDTOS.add(GenerateContentDTO.builder().content(posterDTO.getSeedCode()).left(LB_VIDEO_SEED_CODE.getLeft() * channelPoserProperties.getPixelMultiple()).top(LB_VIDEO_SEED_CODE.getTop() * channelPoserProperties.getPixelMultiple()).bottom(LB_VIDEO_SEED_CODE.getBottom() * channelPoserProperties.getPixelMultiple()).right(LB_VIDEO_SEED_CODE.getRight() * channelPoserProperties.getPixelMultiple()).font(LB_VIDEO_SEED_CODE.getFont()).fontSize(LB_VIDEO_SEED_CODE.getFontSize() * channelPoserProperties.getPixelMultiple()).alignment(LB_VIDEO_SEED_CODE.getAlignment()).color(LB_VIDEO_SEED_CODE.getColor()).textboxWidth(LB_VIDEO_SEED_CODE.getTextboxWidth() * channelPoserProperties.getPixelMultiple()).textboxHeight(LB_VIDEO_SEED_CODE.getTextboxHeight() * channelPoserProperties.getPixelMultiple()).build());
        }

        g2d.drawImage(downloadImageFromUrl(posterDTO.getWeChatUrl()), QR_CODE.getLeft() * channelPoserProperties.getPixelMultiple(), QR_CODE.getTop() * channelPoserProperties.getPixelMultiple(), QR_CODE.getTextboxWidth() * channelPoserProperties.getPixelMultiple(), QR_CODE.getTextboxHeight() * channelPoserProperties.getPixelMultiple(), null);

        generateContent(generateContentDTOS, g2d);

        // 释放资源
        g2d.dispose();
        return newImage;
    }

    private static void generateContent(List<GenerateContentDTO> generateContentDTOS, Graphics2D g2d) {
        for (GenerateContentDTO generateContentDTO : generateContentDTOS) {
            if (CharSequenceUtil.isBlank(generateContentDTO.getContent())) {
                continue;
            }
            // 设置字体样式和颜色
            Font font = FontLoader.getFont(generateContentDTO.getFont() + FONT_SUFFIX, generateContentDTO.getFontSize());
            FontMetrics metrics = g2d.getFontMetrics(font);

            Integer fontSize = generateContentDTO.getFontSize();
            // 动态调整字体大小
            while (metrics.stringWidth(generateContentDTO.getContent()) - generateContentDTO.getTextboxWidth() > 3) {
                fontSize--;
                font = font.deriveFont((float) fontSize);
                metrics = g2d.getFontMetrics(font);
            }
            // 设置矩形框（仅作视觉参考，可省略）
            // g2d.setColor(Color.GRAY); // 边框颜色
            // g2d.drawRect(generateContentDTO.getLeft(), generateContentDTO.getTop(), generateContentDTO.getTextboxWidth(), generateContentDTO.getTextboxHeight());

            // 设置最终字体和颜色
            g2d.setFont(font);
            g2d.setColor(Color.decode(generateContentDTO.getColor()));

            int textX;
            // 计算文本的居中位置
            if (FontAlignmentEnum.LEFT.equals(generateContentDTO.getAlignment())) {
                textX = generateContentDTO.getLeft();
            } else {
                textX = generateContentDTO.getLeft() + (generateContentDTO.getTextboxWidth() - metrics.stringWidth(generateContentDTO.getContent())) / 2;
            }
            int textY = generateContentDTO.getTop() + ((generateContentDTO.getTextboxHeight() - metrics.getHeight()) / 2) + metrics.getAscent(); // 文本垂直居中

            // 绘制文本
            g2d.drawString(generateContentDTO.getContent(), textX, textY);
        }
    }

    private BufferedImage downloadImageFromUrl(String imageUrl) throws IOException {
        if (!isURL(imageUrl)) {
            return null;
        }
        Request request = new Request.Builder()
                .url(imageUrl)
                .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("下载图片请求失败");
                throw new ServerException("下载图片请求失败 " + response);
            }

            ResponseBody body = response.body();
            if (body == null) {
                log.error("下载图片请求体为空");
                throw new ServerException("下载图片请求体为空");
            }

            try (InputStream inputStream = body.byteStream()) {
                return ImageIO.read(inputStream);
            }
        } catch (IOException e) {
            log.error("下载图片失败", e);
            throw new ServerException("下载图片失败 URL: " + imageUrl, e);
        }
    }

    public static boolean isURL(String str) {
        str = str.toLowerCase();
        String regex = "^((https|http|ftp|rtsp|mms)?://)"
                + "?(([0-9a-z_!~*'().&=+$%-]+: )?[0-9a-z_!~*'().&=+$%-]+@)?"
                + "(([0-9]{1,3}\\.){3}[0-9]{1,3}"
                + "|"
                + "([0-9a-z_!~*'()-]+\\.)*"
                + "([0-9a-z][0-9a-z-]{0,61})?[0-9a-z]\\."
                + "[a-z]{2,6})"
                + "(:[0-9]{1,5})?"
                + "((/?)|"
                + "(/[0-9a-z_!~*'().;?:@&=+$,%#-]+)+/?)$";
        return str.matches(regex);
    }

    public static Integer getWidth(Integer left, Integer right) {
        return graphWidth - left - right;
    }

    public static Integer getHeight(Integer top, Integer bottom) {
        return graphHeight - top - bottom;
    }

    /**
     * 将文件打包并压缩为 .tar.gz
     */
    private void compressToTarGz(List<File> files, String outputTarGzPath) throws IOException {
        try (
                FileOutputStream fos = new FileOutputStream(outputTarGzPath);
                GZIPOutputStream gos = new GZIPOutputStream(fos);
                TarArchiveOutputStream taos = new TarArchiveOutputStream(gos)
        ) {
            taos.setLongFileMode(TarArchiveOutputStream.LONGFILE_GNU);
            for (File file : files) {
                // 创建 Tar 条目
                TarArchiveEntry entry = new TarArchiveEntry(file, file.getName());
                entry.setSize(file.length());
                taos.putArchiveEntry(entry);

                // 写入文件内容
                try (FileInputStream fis = new FileInputStream(file)) {
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = fis.read(buffer)) != -1) {
                        taos.write(buffer, 0, len);
                    }
                }

                // 关闭当前条目
                taos.closeArchiveEntry();

                // 删除临时文件
                file.delete();
            }
        }
    }
}
