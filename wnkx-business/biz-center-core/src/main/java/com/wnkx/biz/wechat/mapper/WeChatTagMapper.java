package com.wnkx.biz.wechat.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatTag;
import com.wnkx.db.mapper.SuperMapper;

/**
 * <AUTHOR>
 * @description 针对表【we_chat_tag(企业微信标签信息表)】的数据库操作Mapper
 * @createDate 2024-10-09 15:17:55
 * @Entity com.ruoyi.system.api.domain.entity.biz.wechat.WeChatTag
 */
public interface WeChatTagMapper extends SuperMapper<WeChatTag> {

    /**
     * 根据标签名称获取企业微信标签信息
     *
     * @param tagName
     * @return
     */
    default WeChatTag getByTagName(String tagName) {
        return this.selectOne(new LambdaQueryWrapper<WeChatTag>()
                .eq(WeChatTag::getTagName, tagName));
    }

    /**
     * 根据标签Id修改标签名称
     *
     * @param tagId
     * @param tagName
     */
    default void updateTagNameByTagId(String tagId, String tagName) {
        this.update(null, new LambdaUpdateWrapper<WeChatTag>()
                .set(WeChatTag::getTagName, tagName)
                .eq(WeChatTag::getTagId, tagId));
    }
}




