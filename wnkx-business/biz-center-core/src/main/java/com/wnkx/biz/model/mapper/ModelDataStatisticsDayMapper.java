package com.wnkx.biz.model.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelDataStatisticsDay;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-05-08 09:55:02
 */
@Mapper
public interface ModelDataStatisticsDayMapper extends SuperMapper<ModelDataStatisticsDay> {

    /**
     * 模特数据-模特基础数据
     */
    default ModelDataStatisticsDay getByWriteTime(String date) {
        return selectOne(new LambdaQueryWrapper<ModelDataStatisticsDay>()
                .last("WHERE DATE_FORMAT(write_time_begin,'%Y-%m-%d') = '" + date + "'")
        );
    }

    /**
     * 通过开始时间 结束时间 获取模特数据统计_每日记录表
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 模特数据统计_每日记录表
     */
    default List<ModelDataStatisticsDay> getByWriteTimeBetween(Date beginTime, Date endTime) {
        return selectList(new LambdaQueryWrapper<ModelDataStatisticsDay>()
                .ge(ModelDataStatisticsDay::getWriteTimeBegin, beginTime)
                .le(ModelDataStatisticsDay::getWriteTimeEnd, endTime)
        );
    }
}
