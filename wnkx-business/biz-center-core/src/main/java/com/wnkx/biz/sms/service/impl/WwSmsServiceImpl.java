package com.wnkx.biz.sms.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.ErrorConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.redis.service.RedisService;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.sms.v20190711.SmsClient;
import com.tencentcloudapi.sms.v20190711.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20190711.models.SendSmsResponse;
import com.wnkx.biz.config.SmsConfig;
import com.wnkx.biz.config.TencentSmsConfig;
import com.wnkx.biz.sms.service.SmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 望为验证码服务
 *
 * <AUTHOR>
 * @date 2024/8/28
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WwSmsServiceImpl implements SmsService {


    private final RedisService redisService;
    private final OkHttpClient okHttpClient;
    private final SmsConfig smsConfig;
    private final TencentSmsConfig tencentSmsConfig;
    @Value("${sms.debugger.enable}")
    private String debuggerEnable;
    @Value("${sms.debugger.platform:''}")
    private String sendPlatform;
    private final SmsClient smsClient;


    @Override
    public String sendCode(String phoneNum) {
        checkPhoneStatus(phoneNum);
        final String code = String.valueOf(RandomUtil.randomInt(1001, 9999));
        if (Boolean.TRUE.toString().equals(debuggerEnable)) {
            String debuggerCode = "1234";
            redisService.setCacheObject(CacheConstants.SMS_PHONE + phoneNum, debuggerCode, CacheConstants.SMS_PHONE_EXPIRATION, TimeUnit.MINUTES);
            return debuggerCode;
        }
        if ("tencent".equals(sendPlatform)) {
            //企业微信发送验证码
            if (sendTencentSms(phoneNum, code)) {
                redisService.setCacheObject(CacheConstants.SMS_PHONE + phoneNum, code, CacheConstants.SMS_PHONE_EXPIRATION, TimeUnit.MINUTES);
                return code;
            }
        }
        if (sendSms(phoneNum, code)) {
            redisService.setCacheObject(CacheConstants.SMS_PHONE + phoneNum, code, CacheConstants.SMS_PHONE_EXPIRATION, TimeUnit.MINUTES);
            return code;
        }
        throw new ServiceException(ErrorConstants.SMS_ERROR_TIP);
    }

    /**
     * 10分钟内同一手机号只能发送3次验证码,
     * 验证码有效期10分钟，
     * 同一手机号验证码发送间隔1分钟
     *
     * @param phoneNum 手机号
     */
    private void checkPhoneStatus(String phoneNum) {
        if (StringUtils.isBlank(phoneNum)) {
            throw new ServiceException("手机号不可为空");
        }
        if (Boolean.TRUE.equals(redisService.hasKey(CacheConstants.SMS_PHONE + phoneNum)) &&
                redisService.getExpire(CacheConstants.SMS_PHONE + phoneNum) > CacheConstants.SMS_PHONE_AGAIN_EXPIRATION
        ) {
            throw new ServiceException("验证码尚未过期，请稍后再试~" + phoneNum);
        }
        String key = CacheConstants.SMS_SECURITY_PHONE + phoneNum;
        if (Boolean.TRUE.equals(redisService.hasKey(key))) {
            if ((Integer) redisService.getCacheObject(key) >= 3) {
                throw new ServiceException(ErrorConstants.SEND_CODE_TO_MORE);
            }
            redisService.setIncr(key);
        } else {
            redisService.setCacheObject(key, 1, CacheConstants.SMS_SECURITY_PHONE_EXPIRATION, TimeUnit.SECONDS);
        }
    }

    @Override
    public boolean verifyCode(String phoneNum, String code) {
        return redisService.releaseLock(CacheConstants.SMS_PHONE + phoneNum, code) > 0;
    }

    @Override
    public Integer getSendCodeCount(String phone) {
        return redisService.getCacheObject(CacheConstants.SMS_SECURITY_PHONE + phone);
    }

    private boolean sendTencentSms(String phoneNum, String code) {
        SendSmsRequest req = new SendSmsRequest();
        req.setSmsSdkAppid(tencentSmsConfig.getSdkAppId());
        req.setSign("泉州润一进出口");
        req.setTemplateID(tencentSmsConfig.getTemplateId());
        req.setPhoneNumberSet(new String[]{"+86" + phoneNum});
        req.setTemplateParamSet(new String[]{code, "10"});
        try {
            SendSmsResponse sendSmsResponse = smsClient.SendSms(req);
            log.info("发送短信验证码成功,手机号:{},验证码:{}", phoneNum, code);
            log.info("短信发送Id:{}", sendSmsResponse.getRequestId());
            return true;
        } catch (TencentCloudSDKException e) {
            log.error("腾讯sdk错误,发送短信验证码失败,手机号:{},验证码:{},错误信息:{}", phoneNum, code,e.getMessage());
        }
        throw new ServiceException(String.format("%s~%s", ErrorConstants.SMS_ERROR_TIP, phoneNum));
    }

    private boolean sendSms(String phoneNum, String code) {
        RequestBody reqBody = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("content", String.format(smsConfig.getContent(), code))
                .addFormDataPart("template_id", smsConfig.getTemplateId())
                .addFormDataPart("phone_number", phoneNum)
                .build();
        Request request = new Request.Builder()
                .url(smsConfig.getUrl())
                .method("POST", reqBody)
                .addHeader("Authorization", smsConfig.getAppCode())
                .build();
        try (Response response = okHttpClient.newCall(request).execute()) {
            final String respData = response.body().string();
            if (response.isSuccessful()) {
                log.info("发送短信验证码成功,手机号:{},验证码:{},返回数据:{}", phoneNum, code, respData);
                return true;
            }
            log.warn(ErrorConstants.SMS_ERROR_TIP + ":{}{}", respData, phoneNum);
            if (response.code() == HttpStatus.FORBIDDEN.value() && respData.contains("RATE_LIMIT")) {
                throw new ServiceException(String.format("发送短信验证码过于频繁,请稍后再试~%s", phoneNum));
            }
        } catch (IOException e) {
            log.warn(ErrorConstants.SMS_ERROR_TIP + ",读取返回数据流失败{}", e.getMessage());
        }
        throw new ServiceException(String.format("%s~%s", ErrorConstants.SMS_ERROR_TIP, phoneNum));
    }

}
