package com.wnkx.biz.casus.mapper;

import com.ruoyi.system.api.domain.dto.order.casus.CasusGroupDTO;
import com.ruoyi.system.api.domain.dto.order.casus.GroupAddVideoDTO;
import com.ruoyi.system.api.domain.dto.order.casus.GroupsVideoListDTO;
import com.ruoyi.system.api.domain.entity.order.casus.CasusGroup;
import com.ruoyi.system.api.domain.vo.order.casus.CasusGroupVO;
import com.ruoyi.system.api.domain.vo.order.casus.GroupAddVideoVO;
import com.ruoyi.system.api.domain.vo.order.casus.GroupVideoVO;
import com.wnkx.db.mapper.SuperMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【casus_group(案例分组表)】的数据库操作Mapper
* @createDate 2024-07-10 13:35:30
* @Entity
*/
public interface CasusGroupMapper extends SuperMapper<CasusGroup> {

    /**
     * 获取案例分组列表
     * @param dto
     * @return
     */
    List<CasusGroupVO> queryList(CasusGroupDTO dto);

    /**
     * 获取分组视频列表
     * @param dto
     * @return
     */
    List<GroupVideoVO> queryGroupsVideoList(GroupsVideoListDTO dto);


    /**
     * 获取可添加视频列表
     * @param dto
     * @return
     */
    List<GroupAddVideoVO> queryAddGroupsVideoList(GroupAddVideoDTO dto);


}
