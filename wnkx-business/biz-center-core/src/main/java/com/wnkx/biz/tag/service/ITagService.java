package com.wnkx.biz.tag.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.tag.TagDTO;
import com.ruoyi.system.api.domain.dto.biz.tag.TagListDTO;
import com.ruoyi.system.api.domain.dto.biz.tag.TagSortDTO;
import com.ruoyi.system.api.domain.entity.biz.tag.Tag;
import com.ruoyi.system.api.domain.vo.TagListVO;
import com.ruoyi.system.api.domain.vo.biz.tag.TagSimpleVO;

import java.util.Collection;
import java.util.List;

/**
 * 标签Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface ITagService extends IService<Tag>
{
    /**
     * 查询标签
     * 
     * @param id 标签主键
     * @return 标签
     */
    public Tag selectTagById(Long id);

    /**
     * 查询标签列表
     *
     * @param tagListDTO 标签
     * @return 标签集合
     */
    public List<TagListVO> selectTagList(TagListDTO tagListDTO);

    /**
     * 新增标签
     *
     * @param tagDTO 标签
     * @return 结果
     */
    public void insertTag(TagDTO tagDTO);

    /**
     * 修改标签
     *
     * @param tagDTO 标签
     * @return 结果
     */
    public void updateTag(TagDTO tagDTO);

    /**
     * 标签排序
     * @param tagSort
     */
    void sort(TagSortDTO tagSort);


    /**
     * 删除标签信息
     *
     * @param id 标签主键
     * @return 结果
     */
    public void deleteTagById(Long id);

    /**
     * 获取当前分类的一级二级标签
     */
    List<TagListVO> stair(Long categoryId);

    /**
     * 根据标签id获取标签信息
     */
    List<TagListVO> queryList(Collection<Long> tagIds);

    /**
     * 获取当前分类的指定级别标签
     */
    List<TagListVO> rank(Long categoryId,Integer rank);

    /**
     * 获取指定分类的指定级别及以上标签
     */
    List<TagListVO> rankUpslope(Long categoryId, Integer rank);

    /**
     * 获取标签下拉框
     */
    List<TagSimpleVO> tagSelect(Long categoryId);
}
