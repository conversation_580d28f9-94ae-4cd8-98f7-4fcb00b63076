package com.wnkx.biz.wechat.service;

import com.ruoyi.common.core.enums.BizUserAccountTypeEnum;
import com.ruoyi.common.core.enums.UpdateWechatRemarkType;
import com.ruoyi.system.api.domain.dto.biz.business.account.CheckPhoneDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.PhoneLoginDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.RefreshTicketDTO;
import com.ruoyi.system.api.domain.dto.biz.wechat.*;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatExternalUser;
import com.ruoyi.system.api.domain.vo.CheckWechatVO;
import com.ruoyi.system.api.domain.vo.biz.business.PhoneLoginVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserVO;
import com.ruoyi.system.api.domain.vo.wechat.JoinBusinessVO;

import java.util.Map;

/**
 * 微信服务
 *
 * <AUTHOR>
 * @date 2024/6/19
 */
public interface WechatService {

    /**
     * 检查登录状态
     */
    WechatLoginCheckDTO check(String ticket);

    /**
     * 检查登录状态
     * @param ticket
     * @return
     */
    WechatLoginCheckDTO checkInsertBizUser(String ticket);

    /**
     * 渠道登录
     * @param dto
     * @return
     */
    WechatLoginCheckDTO channelLogin(WeChatOauth2LoginRequestDTO dto);

    /**
     * oauth2登录
     */
    WechatOauth2LoginDTO oauth2(WeChatOauth2LoginRequestDTO requestDTO);


    /**
     * 检查微信是否能够申请数据*
     * @param requestDTO
     * @return
     */
    CheckWechatVO checkWechat(WeChatOauth2LoginRequestDTO requestDTO);

    /**
     * 查看手机号是否可用
     * @param dto
     * @return
     */
    void checkPhone(CheckPhoneDTO dto);

    /**
     * 刷新ticket
     * @param dto
     */
    void refreshTicket(RefreshTicketDTO dto);

    /**
     * 加入商家*
     * @param joinBusinessDTO
     * @return
     */
    JoinBusinessVO joinBusiness(JoinBusinessDTO joinBusinessDTO);

    /**
     * 手机号登录
     * @param dto
     * @return
     */
    PhoneLoginVO phoneLogin(PhoneLoginDTO dto);


    /**
     * 添加申请表数据*
     * @param externalUser
     * @param joinBusinessDTO
     */
    void addBusinessAccountApply(WeChatExternalUser externalUser, JoinBusinessDTO joinBusinessDTO);

    /**
     * 企业微信登录成功调用
     *
     * @param unionId 用户unionId
     */
    void workWechatLogin(String unionId);

    /**
     * 根据ticket 获取用户信息
     *
     * @param ticket ticket
     * @return 用户unionId与 extraUserId
     */
    ExternalContactInfoDTO getInfoByTicket(String ticket);

    /**
     * 根据ticket 获取unionId
     * @param ticket
     * @return
     */
    String getRedisUnionIdByTicket(String ticket);


    /**
     * 验证ticket是不是已经被验证过了
     *
     * @param ticket ticket
     * @return 是否
     */
    boolean isTicketVerify(String ticket);


    /**
     * 企业微信换绑定成功调用
     */
    void workWechatRebind(ExternalContactInfoDTO externalContact);


    /**
     * 检查扫码状态
     *
     * @param ticket
     */
    void checkTicketStatus(String ticket);

    /**
     * 添加账户
     */
    void addAccount(String externalUserId, ExternalContactInfoDTO dto);

    /**
     * 修改账户
     * @param externalUserId
     * @param dto
     */
    void updateAccount(String externalUserId, ExternalContactInfoDTO dto);

    public void cleanUserExternalUserid(String externalUserId);

    /**
     * 根据ticket
     * @param ticket
     * @return
     */
    String getUnionIdByTicket(String ticket);

    /**
     * 根据unionId获取ticket
     * @param unionId
     * @return
     */
    String getTicketByUnionId(String unionId);

    /**
     * 批量修改账户备注
     * @param accountExternalUserIds 外部联系人id列表 -> 商家编码
     * @param type 修改类型
     * @param accountTypeEnumMap 账号类型
     */
    void updateAccountRemarkBatch(Map<String,String> accountExternalUserIds, UpdateWechatRemarkType type, Map<String, BizUserAccountTypeEnum> accountTypeEnumMap);

    /**
     * 异步批量打标签
     * @param business
     * @param updateWechatRemarkType
     */
    void asyncUpdateWorkWechatRemark(Business business, UpdateWechatRemarkType updateWechatRemarkType);


    /**
     * 清空账户备注
     * @param accountExternalUserId 外部联系人id列表
     * @param type 修改类型
     * @param accountType 账号类型
     */
    void cleanAccountRemark(String accountExternalUserId, UpdateWechatRemarkType type, BizUserAccountTypeEnum accountType);


    void subAccountVerifySuccess(ExternalContactInfoDTO externalContact);

    WechatLoginCheckDTO joinBusinessCheck(String code);

    WechatLoginCheckDTO mobieCheck(String ticket);

    /**
     * 通过TICKET获取登录用户信息
     */
    BizUserVO getLoginUserByTicket(String ticket);
}
