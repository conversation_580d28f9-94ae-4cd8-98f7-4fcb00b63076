package com.wnkx.biz.wechat.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dypnsapi20170525.models.VerifyPhoneWithTokenRequest;
import com.ruoyi.common.core.constant.*;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.Biz200Exception;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.config.WechatConfig;
import com.ruoyi.system.api.config.WorkWeChatConfig;
import com.ruoyi.system.api.domain.config.Region;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessOwnerAccountContactUserDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.*;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserDTO;
import com.ruoyi.system.api.domain.dto.biz.wechat.*;
import com.ruoyi.system.api.domain.entity.BusinessAccountRebindLog;
import com.ruoyi.system.api.domain.entity.biz.business.BizUser;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountApply;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatExternalUser;
import com.ruoyi.system.api.domain.entity.order.OrderMember;
import com.ruoyi.system.api.domain.vo.CheckWechatVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountApplyVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.business.PhoneLoginVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserVO;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelVO;
import com.ruoyi.system.api.domain.vo.biz.channel.DistributionChannelVO;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelBrokeRageVO;
import com.ruoyi.system.api.domain.vo.wechat.JoinBusinessVO;
import com.ruoyi.system.api.model.LoginBusiness;
import com.ruoyi.system.api.model.LoginChannel;
import com.wnkx.biz.business.mapper.BusinessAccountRebindLogMapper;
import com.wnkx.biz.business.service.IBizUserService;
import com.wnkx.biz.business.service.IBusinessAccountApplyService;
import com.wnkx.biz.business.service.IBusinessAccountService;
import com.wnkx.biz.business.service.IBusinessService;
import com.wnkx.biz.config.ChannelMarketingProperties;
import com.wnkx.biz.config.IpRegionConfig;
import com.wnkx.biz.core.AliyunCore;
import com.wnkx.biz.core.ChannelCore;
import com.wnkx.biz.remote.RemoteService;
import com.wnkx.biz.sms.service.SmsService;
import com.wnkx.biz.wechat.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 微信登录
 *
 * <AUTHOR>
 * @date 2024/6/19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WechatServiceImpl implements WechatService {

    private final RedisService redisService;

    private final WechatConfig wechatConfig;

    private final IBusinessAccountService businessAccountService;

    private final IBusinessService businessService;

    private final IBusinessAccountApplyService businessAccountApplyService;

    private final TokenService tokenService;

    private final IWeChatExternalUserService weChatExternalUserService;

    private final IBizUserService bizUserService;

    private final SmsService smsService;

    private final ChannelCore channelCore;

    private final WorkWechatApiService workWechatApiService;

    private final RemoteService remoteService;

    private final WorkWeChatConfig workWeChatConfig;

    private final AliyunCore aliyunCore;

    private final BusinessAccountRebindLogMapper rebindLogMapper;

    private final WeChatContactUserConfigService weChatContactUserConfigService;

    private final WorkWechatRemarkService workWechatRemarkService;


    @Value("${tencent.wechat.subscribe.type:2}")
    private Integer workWxtype;
    private String UNAUTHORIZED_USER = "用户未授权";
    private String FAILED_TO_OBTAIN_MERCHANT_DATA = "获取商家数据失败！";
    private String THE_BINDING_USER_INFORMATION_HAS_EXPIRED = "绑定用户信息已经过期";
    private final ChannelMarketingProperties channelMarketingProperties;


    /**
     * 通过TICKET获取登录用户信息
     */
    @Override
    public BizUserVO getLoginUserByTicket(String ticket) {
        String unionId = getRedisTicketUnionId(ticket);
        Assert.isTrue(CharSequenceUtil.isNotBlank(unionId), "停留时间过长，请刷新后重试~");

        BizUserVO bizUserVO = new BizUserVO();
        BizUser byUnionId = bizUserService.getByUnionId(unionId);
        if (ObjectUtil.isNull(byUnionId)) {
            return bizUserVO;
        }

        bizUserVO.setName(byUnionId.getName());
        bizUserVO.setPhone(byUnionId.getPhone());

        return bizUserVO;
    }

    @Override
    public WechatLoginCheckDTO check(String ticket) {
        final Integer code = redisService.getCacheObject(CacheConstants.WECHAT_LOGIN_STATUS_PREFIX + ticket);
        if (code == null) {
            return WechatLoginCheckDTO.builder()
                    .loginStatus(WxChatLoginStatusEnum.EXPIRE)
                    .build();
        }
        final WxChatLoginStatusEnum loginStatus = WxChatLoginStatusEnum.getStatusByCode(code);
        if (loginStatus.getCode().equals(WxChatLoginStatusEnum.LOGIN_SUCCESS.getCode())) {
            if (ticket.startsWith(TokenConstants.REB) || ticket.startsWith(TokenConstants.VER)) {
                return WechatLoginCheckDTO.builder()
                        .loginStatus(loginStatus)
                        .token(ticket)
                        .build();
            }
            if (ticket.startsWith(TokenConstants.REG) || ticket.startsWith(TokenConstants.REG_PLUS)) {
                WechatLoginCheckDTO wechatLoginCheckDTO = WechatLoginCheckDTO.builder()
                        .loginStatus(loginStatus)
                        .build();
                if (!ticket.contains(TokenConstants.SUB_ACCOUNT_VER)) {
                    wechatLoginCheckDTO.setToken(login(ticket));
                } else {
                    wechatLoginCheckDTO.setToken(ticket);
                }
                return wechatLoginCheckDTO;
            }
        }
        return WechatLoginCheckDTO.builder()
                .loginStatus(loginStatus)
                .build();

    }

    @Override
    public WechatLoginCheckDTO checkInsertBizUser(String ticket) {
        final Integer code = redisService.getCacheObject(CacheConstants.WECHAT_LOGIN_STATUS_PREFIX + ticket);
        if (code == null) {
            return WechatLoginCheckDTO.builder()
                    .loginStatus(WxChatLoginStatusEnum.EXPIRE)
                    .build();
        }
        final WxChatLoginStatusEnum loginStatus = WxChatLoginStatusEnum.getStatusByCode(code);
        if (loginStatus.getCode().equals(WxChatLoginStatusEnum.LOGIN_SUCCESS.getCode()) && ticket.startsWith(TokenConstants.REG)) {
            return WechatLoginCheckDTO.builder()
                    .loginStatus(loginStatus)
                    .token(ticket)
                    .build();
        }

        return WechatLoginCheckDTO.builder()
                .loginStatus(loginStatus)
                .build();
    }


    @Override
    public WechatLoginCheckDTO channelLogin(WeChatOauth2LoginRequestDTO dto) {
        final WechatTokenDTO wechatTokenDTO = appLogin(dto.getCode());
        log.info("渠道登录信息:{},{}", wechatTokenDTO, dto);
        if (wechatTokenDTO == null) {
            return WechatLoginCheckDTO.builder()
                    .loginStatus(WxChatLoginStatusEnum.UNKNOWN)
                    .build();
        }
        if (wechatTokenDTO.getIsSnapshotuser() != null && wechatTokenDTO.getIsSnapshotuser().equals(WechatTokenDTO.SNAP_USER_FLAG)) {
            return WechatLoginCheckDTO.builder()
                    .loginStatus(WxChatLoginStatusEnum.SNAP_USER)
                    .build();
        }
        if (wechatTokenDTO.getUnionid() == null) {
            throw new Biz200Exception(UNAUTHORIZED_USER);
        }
        final BusinessAccountVO businessAccount = businessAccountService.getBusinessAccount(wechatTokenDTO.getUnionid());
        final String ticket = dto.getState();
        log.info("当前ticket{}", ticket);
        /**
         * 渠道端登录注册
         */
        if (ticket.startsWith(TokenConstants.CHANNEL_REG)) {
            return oauth2ChannelLogin(ticket, businessAccount, wechatTokenDTO);
        }
        return WechatLoginCheckDTO.builder()
                .loginStatus(WxChatLoginStatusEnum.UNKNOWN)
                .build();
    }

    /**
     * 微信普通登录授权到这里，需要返回是不是要求用户加企业微信 或者是成功登录
     *
     * @param requestDTO 请求参数
     */

    @Override
    public WechatOauth2LoginDTO oauth2(WeChatOauth2LoginRequestDTO requestDTO) {
        final WechatTokenDTO wechatTokenDTO = appLogin(requestDTO.getCode());
        log.info("oauth2登录信息:{}", wechatTokenDTO);
        if (wechatTokenDTO == null) {
            return WechatOauth2LoginDTO.builder()
                    .loginStatus(WxChatLoginStatusEnum.UNKNOWN)
                    .build();
        }
        if (wechatTokenDTO.getIsSnapshotuser() != null && wechatTokenDTO.getIsSnapshotuser().equals(WechatTokenDTO.SNAP_USER_FLAG)) {
            return WechatOauth2LoginDTO.builder()
                    .loginStatus(WxChatLoginStatusEnum.SNAP_USER)
                    .build();
        }
        if (wechatTokenDTO.getUnionid() == null) {
            throw new Biz200Exception(UNAUTHORIZED_USER);
        }
        final BusinessAccountVO businessAccount = businessAccountService.getBusinessAccount(wechatTokenDTO.getUnionid());
        final String ticket = requestDTO.getState();
        log.info("当前ticket{}", ticket);

        /**
         * 新登录流程
         */
        if (ticket.startsWith(TokenConstants.REG_PLUS)) {
            return oauth2LoginPlus(ticket, businessAccount, wechatTokenDTO);

        }

        /**
         * 登录
         */
        if (ticket.startsWith(TokenConstants.REG)) {
            if (businessAccount != null && ObjectUtil.isNotNull(redisService.getCacheObject(CacheConstants.BANG_ACCOUNT + ticket))) {
                redisService.deleteObject(CacheConstants.BANG_ACCOUNT + ticket);
                updateRedisTicketStatus(ticket, WxChatLoginStatusEnum.BINDING);
                return WechatOauth2LoginDTO.builder()
                        .loginStatus(WxChatLoginStatusEnum.LOGIN_SUCCESS)
                        .build();

            }
            return oauth2LoginDTO(ticket, businessAccount, wechatTokenDTO);
        }

        /**
         * 绑定
         */
        if (ticket.startsWith(TokenConstants.REB)) {
            if (businessAccount != null) {
                updateRedisTicketStatus(ticket, WxChatLoginStatusEnum.BINDING);
                return WechatOauth2LoginDTO.builder()
                        .loginStatus(WxChatLoginStatusEnum.LOGIN_SUCCESS)
                        .build();
            }
            return oauth2Rebind(ticket, wechatTokenDTO);
        }

        /**
         * 校验
         */
        if (ticket.startsWith(TokenConstants.VER)) {
            if (businessAccount == null) {
                updateRedisTicketStatus(ticket, WxChatLoginStatusEnum.UN_REGISTER);
                return WechatOauth2LoginDTO.builder()
                        .loginStatus(WxChatLoginStatusEnum.UN_REGISTER)
                        .build();
            }
            if (ticket.contains(TokenConstants.SUB_ACCOUNT_VER)) {
//                子账户验证，特殊处理，需要校验微信好友
                return subAccountOauth2Verify(ticket, wechatTokenDTO);
            }
            return oauth2Verify(ticket, wechatTokenDTO);
        }

        /**
         * 商家端-子账号绑定
         */
        if (ticket.startsWith(TokenConstants.SUB_ACCOUNT_VER_PLUS)) {
            if (businessAccount != null && ObjectUtil.isNotNull(businessAccount.getBusinessId()) && businessAccount.getBusinessId() != 0) {
                updateRedisTicketStatus(ticket, WxChatLoginStatusEnum.BINDING);
                return WechatOauth2LoginDTO.builder()
                        .loginStatus(WxChatLoginStatusEnum.LOGIN_SUCCESS)
                        .build();

            }
            return oauth2SubAccountBinding(ticket, businessAccount, wechatTokenDTO);
        }

        return WechatOauth2LoginDTO.builder()
                .loginStatus(WxChatLoginStatusEnum.UNKNOWN)
                .build();
    }

    /**
     * 商家端-子账号绑定
     */
    private WechatOauth2LoginDTO oauth2SubAccountBinding(String ticket, BusinessAccountVO businessAccount, WechatTokenDTO wechatTokenDTO) {
        log.info("oauth2SubAccountBinding登录{},{},{}", ticket, businessAccount, wechatTokenDTO);

        final String unionId = wechatTokenDTO.getUnionid();
        ChannelVO channelByTicket = channelCore.getChannelByTicket(ticket);

        boolean hasValidChannel = ObjectUtil.isNotNull(channelByTicket) && StrUtil.isNotBlank(channelByTicket.getWeChatUrl());
        Integer type = getValidChannelChannelType();
        String weChatUrl = hasValidChannel ? getValidChannelWeChatUrl(channelByTicket) :
                type.equals(WechatOauth2LoginDTO.TYPE_LINK) ?
                        String.format("%s?customer_channel=%s", getCurrentActiveAcquisitionLink(), ChannelTypeEnum.PREFIX + ChannelTypeEnum.WEBSITE.getLabel() + "-1")
                        : getSysDefaultQrcode();


        log.info("获取企微类型: type={}, url={}", type, weChatUrl);


        if (businessAccount == null) {
            updateRedisWorkWechatStatus(unionId, ticket);
            updateRedisTicketUnionId(ticket, unionId);
            WeChatExternalUser weChatExternalUser = weChatExternalUserService.getOneByUnionid(unionId);

            if (ticket.contains(TokenConstants.SUB_ACCOUNT_VER)) {
//                    子账号多一层用户校验,缓存用户是否相等
//                      注意！ 此方法会改动原weChatExternalUser信息
                if (checkUserAddUser(weChatExternalUser, getMemberCodeFromStatus(ticket))) {
                    //  有缓存的企业微信回调数据 那么直接帮他创建账号 然后 返回登陆成功
                    bizUserService.updateContactUserName(unionId, weChatExternalUser.getConnectUserName());
                    weChatExternalUserService.updateContactUserInfo(
                            weChatExternalUser.getExternalUserid(),
                            weChatExternalUser.getConnectUserId(),
                            weChatExternalUser.getConnectUserName()
                    );
                    return createAccountAndLogin(weChatExternalUser, unionId);
                }
//                需要添加好友，这里已经是获取到对应类型的二维码
                WechatOauth2LoginDTO wechatUrlByMemberCode = getBusinessWechatUrlByMemberCode(getMemberCodeFromStatus(ticket));
                type = wechatUrlByMemberCode.getType();
                weChatUrl = wechatUrlByMemberCode.getUrl();
            }

            if (weChatExternalUser != null) {
                //  2、如果有缓存的企业微信回调数据 那么直接帮他创建账号 然后 返回登陆成功
                return createAccountAndLogin(weChatExternalUser, unionId);
            }

            // 新用户注册 这里需要绑定关系
            // 更新redis 建立unionId与ticket的关系
            updateRedisTicketStatus(ticket, WxChatLoginStatusEnum.LOGINING);
            WechatOauth2LoginDTO oauth2LoginDTO = WechatOauth2LoginDTO.builder()
                    .type(type)
                    .url(weChatUrl)
                    .phone(StatusTypeEnum.YES.getCode())
                    .loginStatus(WxChatLoginStatusEnum.LOGINING)
                    .build();
            String redisPhone = getRedisTicketPhone(ticket);
            boolean phoneMatchesTicket = StrUtil.isNotBlank(redisPhone) && ticket.equals(getPhoneTicket(redisPhone));
            oauth2LoginDTO.setPhone(phoneMatchesTicket ? StatusTypeEnum.NO.getCode() : StatusTypeEnum.YES.getCode());

            String ip = SecurityUtils.getCurrentOperateIp();
            Region region = IpRegionConfig.getRegion(ip);
            log.debug(region.toString());
            updateRedisUnionIdIp(unionId, region.getCode());
            return oauth2LoginDTO;

        }

        // 用户禁用
        if (checkBan(ticket, businessAccount)) {
            return WechatOauth2LoginDTO.builder()
                    .loginStatus(WxChatLoginStatusEnum.ACCOUNT_DISABLE)
                    .build();
        }
        return loginSuccess(ticket, unionId);
    }

    /**
     * 验证扫码进入子账号身份
     *
     * @param ticket
     * @param wechatTokenDTO
     * @return
     */
    private WechatOauth2LoginDTO subAccountOauth2Verify(String ticket, WechatTokenDTO wechatTokenDTO) {
        WeChatExternalUser weChatExternalUser = weChatExternalUserService.getOneByUnionid(wechatTokenDTO.getUnionid());
        if (weChatExternalUser != null) {
            if (checkUserAddUser(weChatExternalUser, getMemberCodeFromStatus(ticket))) {
                bizUserService.updateContactUserName(weChatExternalUser.getUnionid(), weChatExternalUser.getConnectUserName());
                weChatExternalUserService.updateContactUserInfo(
                        weChatExternalUser.getExternalUserid(),
                        weChatExternalUser.getConnectUserId(),
                        weChatExternalUser.getConnectUserName()
                );
                return oauth2Verify(ticket, wechatTokenDTO);
            }
        }
        updateRedisTicketStatus(ticket, WxChatLoginStatusEnum.LOGINING);
        updateRedisWorkWechatStatus(wechatTokenDTO.getUnionid(), ticket);
        WechatOauth2LoginDTO wechatUrlByMemberCode = getBusinessWechatUrlByMemberCode(getMemberCodeFromStatus(ticket));
        return WechatOauth2LoginDTO.builder()
                .type(wechatUrlByMemberCode.getType())
                .url(wechatUrlByMemberCode.getUrl())
                .phone(StatusTypeEnum.YES.getCode())
                .loginStatus(WxChatLoginStatusEnum.LOGINING)
                .build();
    }

    /**
     * 子账号添加客服后确定身份后回调
     *
     * @param externalContact 外部联系人信息
     */
    @Override
    public void subAccountVerifySuccess(ExternalContactInfoDTO externalContact) {
        bizUserService.updateContactUserName(externalContact.getUnionid(), externalContact.getConnectUserName());
        weChatExternalUserService.updateContactUserInfo(
                externalContact.getExternalUserid(),
                externalContact.getConnectUserId(),
                externalContact.getConnectUserName()
        );
        String ticket = getRedisWorkWechatStatus(externalContact.getUnionid());
        loginSuccess(ticket, externalContact.getUnionid());
    }

    /**
     * 用于检查用户加入子账号状态
     * 根据加入子账号号，redis中businessDto会被情空进行判断
     *
     * @param code 微信回调code
     * @return
     */
    @Override
    public WechatLoginCheckDTO joinBusinessCheck(String code) {
        String redisCodeUnionId = getRedisCodeUnionId(code);
        if (StringUtils.isNotBlank(redisCodeUnionId)) {
            JoinBusinessDTO redisUnionIdJoinBusiness = getRedisUnionIdJoinBusiness(redisCodeUnionId);
            if (redisUnionIdJoinBusiness != null) {
//                还在登录，未加客服
                return WechatLoginCheckDTO.builder()
                        .loginStatus(WxChatLoginStatusEnum.LOGINING)
                        .build();
            }
//            登录成功
            return WechatLoginCheckDTO.builder()
                    .loginStatus(WxChatLoginStatusEnum.LOGIN_SUCCESS)
                    .build();
        }
//        过期
        return WechatLoginCheckDTO.builder()
                .loginStatus(WxChatLoginStatusEnum.EXPIRE)
                .build();
    }


    @Override
    public WechatLoginCheckDTO mobieCheck(String ticket) {
        final Integer code = redisService.getCacheObject(CacheConstants.WECHAT_LOGIN_STATUS_PREFIX + ticket);
        if (code == null) {
            return WechatLoginCheckDTO.builder()
                    .loginStatus(WxChatLoginStatusEnum.LOGIN_SUCCESS)
                    .build();
        }
        final WxChatLoginStatusEnum loginStatus = WxChatLoginStatusEnum.getStatusByCode(code);
        return WechatLoginCheckDTO.builder()
                .loginStatus(loginStatus)
                .build();
    }


    @Override
    public CheckWechatVO checkWechat(WeChatOauth2LoginRequestDTO requestDTO) {
        final WechatTokenDTO wechatTokenDTO = appLogin(requestDTO.getCode());

        log.info("微信信息:{}", wechatTokenDTO);
        Assert.notNull(wechatTokenDTO, "当前页面停留时间过长,请重新扫码后再试~");
        if (ObjectUtil.isNull(wechatTokenDTO.getUnionid())) {
            return CheckWechatVO.builder()
                    .message("请关闭页面重新扫码")
                    .status(CheckWechatEnum.THE_USER_HAS_NOT_AUTHORIZED.getCode())
                    .build();
        }
        //判断是否已是公司子账号、是否其他公司子绑定、是否主账号、主账号会员已到期、已加入申请表（redis:  code - {access_token,openid,uni}）
        AccountApplyQueryDTO accountApplyQueryDTO = new AccountApplyQueryDTO();
        accountApplyQueryDTO.setUnionid(wechatTokenDTO.getUnionid());
        accountApplyQueryDTO.setAuditStatusList(List.of(AccountAuditStatusEnum.UN_AUDIT.getCode()));
        List<BusinessAccountApplyVO> businessAccountApplyVOS = businessAccountApplyService.list(accountApplyQueryDTO);

        //获取需要加入公司的主账号
        String memberCode = requestDTO.getState().substring(TokenConstants.SUB_ACCOUNT_VER.length());
        BusinessDTO businessDTO = new BusinessDTO();
        businessDTO.setMemberCode(memberCode);
        final Business business = businessService.queryOne(businessDTO);
        Assert.notNull(business, FAILED_TO_OBTAIN_MERCHANT_DATA);

        if (CollUtil.isNotEmpty(businessAccountApplyVOS)) {
            String ownerAccount = businessAccountApplyVOS.get(0).getOwnerAccount();
            BusinessAccountDTO businessAccountDTO = new BusinessAccountDTO();
            businessAccountDTO.setAccount(ownerAccount);
            BusinessAccountVO businessAccountOne = businessAccountService.getBusinessAccountOne(businessAccountDTO);
            Assert.notNull(businessAccountOne, FAILED_TO_OBTAIN_MERCHANT_DATA);
            //已申请子账号，无法加入
            return CheckWechatVO.builder()
                    .message(String.format(CheckWechatEnum.ACCOUNT_APPLY.getLabel(), businessAccountOne.getBusinessVO().getName()))
                    .status(CheckWechatEnum.ACCOUNT_APPLY.getCode())
                    .applyBusinessName(business.getName())
                    .businessName(businessAccountOne.getBusinessVO().getName())
                    .build();

        }

        if (StatusTypeEnum.NO.getCode().equals(business.getMemberType())) {
            //企业主账号会员已到期，无法加入
            return CheckWechatVO.builder()
                    .message(CheckWechatEnum.OWNER_BUSINESS_EXPIRE.getLabel())
                    .status(CheckWechatEnum.OWNER_BUSINESS_EXPIRE.getCode())
                    .applyBusinessName(business.getName())
                    .businessName("")
                    .build();
        }


        final BizUser bizUser = bizUserService.getByUnionId(wechatTokenDTO.getUnionid());
        if (ObjectUtil.isNull(bizUser)) {
            updateRedisCodeUnionId(requestDTO.getCode(), wechatTokenDTO.getUnionid());
            return CheckWechatVO.builder()
                    .message(CheckWechatEnum.NO_BIZ_USER.getLabel())
                    .status(CheckWechatEnum.NO_BIZ_USER.getCode())
                    .applyBusinessName(business.getName())
                    .businessName("")
                    .build();
        }
        final BusinessAccountVO businessAccount = businessAccountService.queryOne(BusinessAccountDTO.builder()
                .unionid(wechatTokenDTO.getUnionid())
                .build());


        //获取申请记录表  未加入business_account的数据
        if (businessAccount != null) {
            Business wechatBusiness = businessService.getById(businessAccount.getBusinessId());
            if (ObjectUtil.isNull(wechatBusiness)) {
                wechatBusiness = new Business();
            }
            if (StatusTypeEnum.YES.getCode().equals(businessAccount.getIsOwnerAccount())) {
                //您已是平台主账号
                return CheckWechatVO.builder()
                        .message(CheckWechatEnum.ALREADY_OWNER.getLabel())
                        .status(CheckWechatEnum.ALREADY_OWNER.getCode())
                        .applyBusinessName(business.getName())
                        .businessName(wechatBusiness.getName())
                        .build();
            }
            //您已是平台主账号下成员
            if (business.getOwnerAccount().equals(wechatBusiness.getOwnerAccount())) {
                return CheckWechatVO.builder()
                        .message(String.format(CheckWechatEnum.ALREADY_SON.getLabel(), wechatBusiness.getName()))
                        .status(CheckWechatEnum.ALREADY_SON.getCode())
                        .applyBusinessName(business.getName())
                        .businessName(wechatBusiness.getName())
                        .build();
            }
            //您已是其他平台主账号主账号
            return CheckWechatVO.builder()
                    .message(String.format(CheckWechatEnum.ALREADY_OTHER_SON.getLabel(), wechatBusiness.getName()))
                    .status(CheckWechatEnum.ALREADY_OTHER_SON.getCode())
                    .applyBusinessName(business.getName())
                    .businessName(wechatBusiness.getName())
                    .build();
        }

        updateRedisCodeUnionId(requestDTO.getCode(), wechatTokenDTO.getUnionid());

        List<OrderMember> validOrderMemberList = remoteService.getValidOrderMemberList(bizUser.getId());
        if (CollUtil.isNotEmpty(validOrderMemberList)) {
            //  缓存code跟bizUser
            redisService.setCacheObject(CacheConstants.SUB_ACCOUNT_WECHAT_AUTH_CODE_CACHE_KEY + requestDTO.getCode(), bizUser, 1L, TimeUnit.HOURS);
            boolean anyUnPay = validOrderMemberList.stream().anyMatch(item -> OrderMemberStatusEnum.UN_PAY.getCode().equals(item.getStatus()));
            boolean anyUnCheck = validOrderMemberList.stream().anyMatch(item -> OrderMemberStatusEnum.UN_CHECK.getCode().equals(item.getStatus()));

            String message = CharSequenceUtil.EMPTY;
            if (anyUnPay && anyUnCheck) {
                message = "您的账号存在未支付和待审核的会员开通订单，\n是否取消相关订单？";
            } else if (anyUnPay) {
                message = "您的账号存在未支付的会员开通订单，\n是否取消相关订单？";
            } else if (anyUnCheck) {
                message = "您的账号存在待审核的会员开通订单，\n是否取消相关订单？";
            }
            //存在有效订单数据 无法添加
            return CheckWechatVO.builder()
                    .message(message)
                    .status(CheckWechatEnum.CUSTOM.getCode())
                    .applyBusinessName(business.getName())
                    .unPayedMemberOrderNums(validOrderMemberList.stream().map(OrderMember::getOrderNum).collect(Collectors.toSet()))
                    .build();
        }

        if (CharSequenceUtil.isBlank(bizUser.getPhone())) {
            return CheckWechatVO.builder()
                    .message(CheckWechatEnum.NO_BIZ_USER_PHONE.getLabel())
                    .status(CheckWechatEnum.NO_BIZ_USER_PHONE.getCode())
                    .applyBusinessName(business.getName())
                    .build();
        }
        return CheckWechatVO.builder()
                .message(CheckWechatEnum.SUCCESS.getLabel())
                .status(CheckWechatEnum.SUCCESS.getCode())
                .applyBusinessName(business.getName())
                .businessName("")
                .build();
    }

    @Override
    public void checkPhone(CheckPhoneDTO dto) {
        BizUser bizUser = bizUserService.getByPhone(dto.getPhone());
        if (ObjectUtil.isNotNull(bizUser) && ObjectUtil.isNotNull(bizUser.getUnionid())) {
            throw new Biz200Exception(Biz200Exception.ERROR_IN_DATA, "该手机号已注册，无法重复绑定");
        }
    }

    @Override
    public void refreshTicket(RefreshTicketDTO dto) {
        String unionId = getUnionIdByTicket(dto.getTicket());
        updateRedisWorkWechatStatus(unionId, dto.getTicket());
        updateRedisTicketUnionId(dto.getTicket(), unionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JoinBusinessVO joinBusiness(JoinBusinessDTO joinBusinessDTO) {
        log.info("joinBusiness请求参数:{}", joinBusinessDTO.toString());
        //查看是否添加联系人
        String unionId = getRedisCodeUnionId(joinBusinessDTO.getCode());
        Assert.notBlank(unionId, "当前页面停留时间过长,请重新扫码后再试~");
        log.info("joinBusiness请求参数:{},{}", joinBusinessDTO, unionId);

        //  校验是否已是主账号
        BusinessAccountVO businessAccountVO = businessAccountService.getBusinessAccount(unionId);
        if (ObjectUtil.isNotNull(businessAccountVO) && MemberTypeEnum.RECHARGE.getCode().equals(businessAccountVO.getBusinessVO().getMemberType())) {
            return JoinBusinessVO.builder()
                    .message(CheckWechatEnum.ALREADY_OWNER.getLabel())
                    .status(CheckWechatEnum.ALREADY_OWNER.getCode())
                    .build();
        }

        //查看是否存在登录账号
        BizUser bizUser = bizUserService.getByUnionId(unionId);
        if (ObjectUtil.isNull(bizUser) || CharSequenceUtil.isBlank(bizUser.getPhone())) {
            //如果不存在登录账号
            Assert.isTrue(StringUtils.isNotBlank(joinBusinessDTO.getPhone()), "手机号不能为空");
            Assert.isNull(bizUserService.getByPhone(joinBusinessDTO.getPhone()), "您的手机号尚未在系统中完成注册，请在官网注册完成后继续加入子账号~");
            Assert.isTrue(StringUtils.isNotBlank(joinBusinessDTO.getPhoneCaptcha()), "验证码不能为空");
            Assert.isTrue(smsService.verifyCode(joinBusinessDTO.getPhone(), joinBusinessDTO.getPhoneCaptcha()), "验证码错误");
        } else if (remoteService.getValidOrderCount(bizUser.getId()).compareTo(0L) > 0) {
            return JoinBusinessVO.builder()
                    .message("您的账号存在未取消的会员订单，暂时无法成为子账号\n" +
                            "请先取消关闭会员订单，再重新扫码加入。\n" +
                            "（如有疑问也可联系客服处理）")
                    .status(CheckWechatEnum.THE_USER_HAS_NOT_AUTHORIZED.getCode())
                    .build();
        }

        if (ObjectUtil.isNotNull(bizUser) && CharSequenceUtil.isNotBlank(bizUser.getPhone())) {
            Assert.isNull(joinBusinessDTO.getPhone(), "此账号已绑定手机号，请重进该界面进行子账号绑定");
        }

        WeChatExternalUser weChatExternalUser = weChatExternalUserService.getOneByUnionid(unionId);
        String memberCodeFromStatus = getMemberCodeFromStatus(joinBusinessDTO.getState());
        if (weChatExternalUser != null) {
            log.info("获取三方表返回数据:{}", weChatExternalUser.toString());
//            注意！ 此方法会改动原weChatExternalUser表信息
            if (checkUserAddUser(weChatExternalUser, memberCodeFromStatus)) {
//                一致，直接进入下一步
                addBusinessAccountApply(weChatExternalUser, joinBusinessDTO);
                return JoinBusinessVO.builder()
                        .needAddWeChat(StatusTypeEnum.NO.getCode())
                        .build();

            }
        }
        updateRedisUnionIdJoinBusiness(unionId, joinBusinessDTO);
        WechatOauth2LoginDTO wechatUrlByMemberCode = getBusinessWechatUrlByMemberCode(memberCodeFromStatus);
        JoinBusinessVO build = JoinBusinessVO.builder()
                .type(wechatUrlByMemberCode.getType())
                .url(wechatUrlByMemberCode.getUrl())
                .needAddWeChat(StatusTypeEnum.YES.getCode())
                .build();
        log.info("返回数据：{}", build.toString());
        return build;
    }

    private boolean checkUserAddUser(WeChatExternalUser weChatExternalUser, String memberCodeFromStatus) {
        if (weChatExternalUser == null) {
            return false;
        }
//        获取主账号加的客服id,比对它自己本身加的id是否一致
        BusinessOwnerAccountContactUserDTO contactUserDTO = businessService.getBusinessOwnerUserContactUserNameByMemberCode(memberCodeFromStatus);
        if (StringUtils.isNotBlank(weChatExternalUser.getConnectUserId())) {
            if (weChatExternalUser.getConnectUserId().equals(contactUserDTO.getContactUserId())) {
                return true;
            }
        }
        ExternalUserInfoDTO externalUserInfo = workWechatApiService.getExternalUserInfo(weChatExternalUser.getExternalUserid());
        if (!"ok".equals(externalUserInfo.getErrmsg())) {
            return false;
        }
        List<ExternalUserInfoDTO.FollowUser> dataList = externalUserInfo.getFollow_user().stream()
                .filter(followUser -> followUser.getUserid().equals(contactUserDTO.getContactUserId()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(dataList)
        ) {
            weChatExternalUser.setConnectUserId(dataList.get(0).getUserid());
            externalUserInfo.getExternalContact().setConnectUserName(
                    workWeChatConfig.getContactUserName().get(
                            workWeChatConfig.getContactUser().indexOf(dataList.get(0).getUserid())
                    ));
            weChatExternalUser.setConnectUserName(
                    workWeChatConfig.getContactUserName().get(
                            workWeChatConfig.getContactUser().indexOf(dataList.get(0).getUserid())
                    ));
            return true;
        } else {
            return false;
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBusinessAccountApply(WeChatExternalUser weChatExternalUser, JoinBusinessDTO joinBusinessDTO) {
        Assert.notNull(weChatExternalUser, "外部联系人信息不能为空！");
        Assert.notBlank(weChatExternalUser.getUnionid(), "外部联系人unionid不能为空！");
        if (null == joinBusinessDTO) {
            joinBusinessDTO = getRedisUnionIdJoinBusiness(weChatExternalUser.getUnionid());
        }
        Assert.notNull(joinBusinessDTO, "添加信息不能为空！");

        BusinessDTO businessDTO = new BusinessDTO();
        businessDTO.setMemberCode(getMemberCodeFromStatus(joinBusinessDTO.getState()));
        Business business = businessService.queryOne(businessDTO);
        Assert.notNull(business, FAILED_TO_OBTAIN_MERCHANT_DATA);

        BizUser bizUser = bizUserService.getByUnionId(weChatExternalUser.getUnionid());
        if (ObjectUtil.isNull(bizUser)) {
            Assert.isTrue(StrUtil.isNotBlank(joinBusinessDTO.getPhone()), "请求参数手机号不能为空！");
            Assert.isNull(bizUserService.getByPhone(joinBusinessDTO.getPhone()), "手机号已注册！");
            bizUser = bizUserService.initBizUserBaseOnPhone(BizUserDTO.builder()
                    .connectUserName(weChatExternalUser.getConnectUserName())
                    .nickName(weChatExternalUser.getName())
                    .externalUserId(weChatExternalUser.getExternalUserid())
                    .pic(weChatExternalUser.getAvatar())
                    .phone(joinBusinessDTO.getPhone())
                    .unionid(weChatExternalUser.getUnionid())
                    .name(joinBusinessDTO.getName())
                    .build());
            channelCore.reSetBizUserChannelInfo(weChatExternalUser, bizUser.getId(), null);
        } else if (CharSequenceUtil.isBlank(bizUser.getPhone())) {
            bizUserService.setPhone(bizUser.getUnionid(), joinBusinessDTO.getPhone());
            bizUser.setPhone(joinBusinessDTO.getPhone());
        } else {
//            加入子账号后，需要更新子账号的对接客服为新添加的这个客服
            bizUserService.updateContactUserName(bizUser.getUnionid(), weChatExternalUser.getConnectUserName());
            weChatExternalUserService.updateContactUserInfo(
                    bizUser.getExternalUserId(),
                    weChatExternalUser.getConnectUserId(),
                    weChatExternalUser.getConnectUserName()
            );
//            子账号列表展示使用
            bizUser.setConnectUserName(weChatExternalUser.getConnectUserName());
        }
        Assert.isTrue(StrUtil.isNotBlank(bizUser.getPhone()), "登录账号手机号不能为空！");

        Assert.isNull(businessAccountService.getByBizUserId(bizUser.getId()), "登录账号已存在");

        BusinessAccountApplyDTO businessAccountApplyDto = new BusinessAccountApplyDTO();
        businessAccountApplyDto.setOwnerAccount(business.getOwnerAccount());
        businessAccountApplyDto.setBusinessId(business.getId());
        businessAccountApplyDto.setBizUserId(bizUser.getId());
        businessAccountApplyDto.setName(joinBusinessDTO.getName());
        businessAccountApplyDto.setNickName(weChatExternalUser.getName());
        businessAccountApplyDto.setPic(weChatExternalUser.getAvatar());
        businessAccountApplyDto.setUnionid(weChatExternalUser.getUnionid());
        businessAccountApplyDto.setExternalUserId(weChatExternalUser.getExternalUserid());
        BusinessAccountApply applyLog = businessAccountApplyService.save(businessAccountApplyDto);

        saveLog(applyLog, bizUser);
    }

    private void saveLog(BusinessAccountApply model, BizUser user) {
        BusinessAccountRebindLog log = new BusinessAccountRebindLog();
        BeanUtil.copyProperties(model, log);
        log.setId(null);
        log.setApplyId(model.getId());
        log.setPhone(user.getPhone());
        log.setConnectUserName(user.getConnectUserName());
//        log.setAccount(model.getOwnerAccount());
        rebindLogMapper.insert(log);
    }


    /**
     * 验证账户所有权
     *
     * @param ticket         验证码
     * @param wechatTokenDTO wechat信息
     */
    private WechatOauth2LoginDTO oauth2Verify(String ticket, WechatTokenDTO wechatTokenDTO) {
        return loginSuccess(ticket, wechatTokenDTO.getUnionid());
    }

    /**
     * 重新绑定企业微信
     */
    private WechatOauth2LoginDTO oauth2Rebind(String ticket, WechatTokenDTO wechatTokenDTO) {
        //  1、检查缓存表 获取数据
        WeChatExternalUser weChatExternalUser = weChatExternalUserService.getOneByUnionid(wechatTokenDTO.getUnionid());
        if (weChatExternalUser != null) {
            //  2、如果有缓存的企业微信回调数据 然后 返回登陆成功 同时调用workWechatRebind(externalUserInfo.getExternalContact())
            updateRedisWorkWechatStatus(wechatTokenDTO.getUnionid(), ticket);
            updateRedisTicketUnionId(ticket, wechatTokenDTO.getUnionid());
            workWechatRebind(BeanUtil.copyProperties(weChatExternalUser, ExternalContactInfoDTO.class));

            return WechatOauth2LoginDTO.builder().loginStatus(WxChatLoginStatusEnum.LOGIN_SUCCESS).build();
        }
        updateRedisTicketStatus(ticket, WxChatLoginStatusEnum.LOGINING);
        updateRedisWorkWechatStatus(wechatTokenDTO.getUnionid(), ticket);
        updateRedisTicketUnionId(ticket, wechatTokenDTO.getUnionid());
        return getSysWebsiteOneWechatUrl();
    }

    /**
     * 更新全局变量 返回token
     */
    private String loginUser(BusinessAccountVO accountVO) {
        final LoginBusiness login = new LoginBusiness(accountVO);
        final Map<String, Object> tokenMap = tokenService.createToken(login);
        return (String) tokenMap.get(CommonConstant.ACCESS_TOKEN);
    }

    private String loginChannel(DistributionChannelVO accountVO) {
        final LoginChannel login = new LoginChannel(accountVO);
        final Map<String, Object> tokenMap = tokenService.createToken(login);
        return (String) tokenMap.get(CommonConstant.ACCESS_TOKEN);
    }

    private String login(String ticket) {
        String unionId = getRedisTicketUnionId(ticket);
        if (WxChatLoginStatusEnum.LOGIN_SUCCESS.getCode().equals(getRedisTicketStatus(ticket).getCode())
                && getRedisWorkWechatStatus(unionId).equals(ticket)) {
            final BusinessAccountVO userInfoVo = businessAccountService.getBusinessAccount(unionId);
            if (userInfoVo == null) {
                throw new ServiceException("业务异常");
            }
            redisService.deleteObject(CacheConstants.WECHAT_LOGIN_STATUS_PREFIX + ticket);
            redisService.deleteObject(CacheConstants.WORK_WECHAT_LOGIN_STATUS_PREFIX + unionId);

            String redisTicketPhone = getRedisTicketPhone(ticket);
            businessAccountService.updateLoginTime(userInfoVo);
            if (StringUtils.isNotBlank(redisTicketPhone)) {
                redisService.deleteObject(CacheConstants.PHONE_LOGIN_TICKET_PHONE_KEY + ticket);
                redisService.deleteObject(CacheConstants.PHONE_LOGIN_PHONE_TICKET_KEY + redisTicketPhone);
            }
            return loginUser(userInfoVo);
        }
        throw new Biz200Exception("登录已过期");
    }

    @Override
    public void workWechatLogin(String unionId) {
        //企业微信返回登录成功 并带上 unionid
        final String ticket = getRedisWorkWechatStatus(unionId);
        if (StringUtils.isBlank(ticket)) {
            throw new Biz200Exception("登录已过期");
        }

        if (ticket.startsWith(TokenConstants.REG) || ticket.startsWith(TokenConstants.REG_PLUS)) {
            BizUser bizUser = bizUserService.getByUnionId(unionId);
            Assert.notNull(bizUser, "登录失败，请联系蜗牛客服处理~");
            Assert.isTrue(bizUser.getStatus().compareTo(StatusEnum.UN_ENABLED.getCode()) != 0, "该账户已被禁用,请联系管理员!");
            updateRedisTicketStatus(ticket, WxChatLoginStatusEnum.LOGIN_SUCCESS);
            return;
        }
        updateRedisTicketStatus(ticket, WxChatLoginStatusEnum.LOGIN_SUCCESS);
    }

    @Override
    public ExternalContactInfoDTO getInfoByTicket(String ticket) {
        final String unionId = getRedisTicketUnionId(ticket);
        if (StringUtils.isBlank(unionId)) {
            throw new ServiceException(THE_BINDING_USER_INFORMATION_HAS_EXPIRED);
        }
        final ExternalContactInfoDTO redisUnionIdExternalUserId = getRedisUnionIdExternalUserId(unionId);
        if (redisUnionIdExternalUserId == null) {
            throw new ServiceException(THE_BINDING_USER_INFORMATION_HAS_EXPIRED);
        }
        return redisUnionIdExternalUserId;
    }

    @Override
    public String getRedisUnionIdByTicket(String ticket) {
        final String unionId = getRedisTicketUnionId(ticket);
        if (StringUtils.isBlank(unionId)) {
            throw new ServiceException(THE_BINDING_USER_INFORMATION_HAS_EXPIRED);
        }

        return unionId;
    }

    @Override
    public boolean isTicketVerify(String ticket) {
        final WxChatLoginStatusEnum redisTicketStatus = getRedisTicketStatus(ticket);
        return redisTicketStatus.getCode().equals(WxChatLoginStatusEnum.LOGIN_SUCCESS.getCode());
    }

    @Override
    public void workWechatRebind(ExternalContactInfoDTO externalContact) {
        //        设置到缓存 unionId - externalUserId
        updateRedisUnionIdExternalUserId(externalContact.getUnionid(), externalContact);
        //        更新redisStatus
        workWechatLogin(externalContact.getUnionid());
    }

    @Override
    public void checkTicketStatus(String ticket) {
        WechatLoginCheckDTO wechatLoginCheckDTO = this.check(ticket);
        if (com.ruoyi.common.core.utils.StringUtils.isNull(wechatLoginCheckDTO)
                || com.ruoyi.common.core.utils.StringUtils.isNull(wechatLoginCheckDTO.getLoginStatus())
                || WxChatLoginStatusEnum.LOGIN_SUCCESS.getCode().equals(wechatLoginCheckDTO.getLoginStatus().getCode())) {
            throw new ServiceException("扫码失败: " + wechatLoginCheckDTO.getLoginStatus().getDesc());
        }
    }

    @Override
    public String getUnionIdByTicket(String ticket) {
        String unionId = getRedisTicketUnionId(ticket);
        if (StringUtils.isBlank(unionId)) {
            throw new ServiceException("微信二维码已过期，请重新扫描");
        }
        return unionId;
    }

    @Override
    public String getTicketByUnionId(String unionId) {
        String ticket = getRedisWorkWechatStatus(unionId);
        if (StringUtils.isBlank(ticket)) {
            throw new ServiceException(THE_BINDING_USER_INFORMATION_HAS_EXPIRED);
        }
        return ticket;
    }

    @Override
    public void updateAccountRemarkBatch(Map<String, String> accountExternalUserIds, UpdateWechatRemarkType type, Map<String, BizUserAccountTypeEnum> accountTypeEnumMap) {
        accountExternalUserIds.forEach((accountExternalUserId, memberCode) -> workWechatRemarkService.updateAccountRemark(accountExternalUserId, memberCode, type, accountTypeEnumMap.get(accountExternalUserId)));
    }


    /**
     * 只能在数据不变情况下进行
     *
     * @param business
     * @param updateWechatRemarkType
     */
    @Override
    public void asyncUpdateWorkWechatRemark(Business business, UpdateWechatRemarkType updateWechatRemarkType) {
        //数据需要再异步之前处理 防止脏读
        List<BizUser> bizUserList = bizUserService.getBizUserByBusinessId(business.getId());
        ThreadUtil.execAsync(() -> {
            if (CollUtil.isEmpty(bizUserList)) {
                return;
            }
            Map<String, String> accountmap = new HashMap<>();
            Map<String, BizUserAccountTypeEnum> accountTypeEnumMap = new HashMap<>();
            for (BizUser item : bizUserList) {
                accountmap.put(item.getExternalUserId(), business.getMemberCode());
                accountTypeEnumMap.put(item.getExternalUserId(), BizUserAccountTypeEnum.getByCode(item.getAccountType()));
            }

            try {
                accountmap.forEach((accountExternalUserId, memberCode) -> workWechatRemarkService.updateAccountRemark(accountExternalUserId, memberCode, updateWechatRemarkType, accountTypeEnumMap.get(accountExternalUserId)));
            } catch (Exception e) {
                log.error("更新企业微信备注数据,商家编码:{},异常:{}", business.getMemberCode(), e.getMessage());
            }
        });
    }

    @Override
    public void cleanAccountRemark(String accountExternalUserId, UpdateWechatRemarkType type, BizUserAccountTypeEnum accountType) {
        workWechatRemarkService.updateAccountRemark(accountExternalUserId, "", type, accountType);
    }


    /**
     * 微信网页登录
     *
     * @param code code
     * @return WechatTokenDTO
     */
    private WechatTokenDTO appLogin(String code) {
        final String data = HttpUtil.get(String.format(WxConstant.WEIXIN_OAUTH2_TOKEN_URL, wechatConfig.getAppId(), wechatConfig.getSecret(), code));
        return JSONObject.parseObject(data, WechatTokenDTO.class);
    }

    /**
     * oauth2ChannelLogin  渠道扫码登录
     *
     * @param ticket          ticket
     * @param businessAccount 用户账户
     * @param wechatTokenDTO  微信回调信息
     * @return WechatOauth2LoginDTO
     */
    private WechatLoginCheckDTO oauth2ChannelLogin(String ticket,
                                                   BusinessAccountVO businessAccount,
                                                   WechatTokenDTO wechatTokenDTO) {

        log.info("渠道登录{},{},{}", ticket, businessAccount, wechatTokenDTO);
        if (businessAccount == null) {
            //扫码账号与登录账号不一致
            return WechatLoginCheckDTO.builder()
                    .loginStatus(WxChatLoginStatusEnum.LOGIN_NO_CHANNEL)
                    .build();
        }
        BizUser bizUser = bizUserService.getById(getBizUserIdFromTicket(ticket));
        if (!bizUser.getUnionid().equals(wechatTokenDTO.getUnionid())) {
            return WechatLoginCheckDTO.builder()
                    .loginStatus(WxChatLoginStatusEnum.LOGIN_NO_CHANNEL)
                    .build();
        }

        //查看渠道是否为空
        DistributionChannel distributionChannel = channelCore.getChannelByBizUserId(bizUser.getId());
        if (ObjectUtil.isNull(distributionChannel)) {
//            if (StatusTypeEnum.YES.getCode().equals(businessAccount.getIsMock())) {
//                return WechatLoginCheckDTO.builder()
//                        .loginStatus(WxChatLoginStatusEnum.LOGIN_NO_CHANNEL)
//                        .build();
//            }
//            if (ObjectUtil.isNull(businessAccount.getBusinessVO().getMemberValidity())) {
//                return WechatLoginCheckDTO.builder()
//                        .loginStatus(WxChatLoginStatusEnum.LOGIN_CHANNEL_NO_MEMBER)
//                        .build();
//            }
            //初始化渠道数据
            String channelName = bizUser.getNickName();
            ChannelBrokeRageVO fissionBrokeRage = channelCore.getFissionDiscountV1();
            distributionChannel = channelCore.saveDistributionChannel(channelName, DistributionChannel.builder()
                    .phone(bizUser.getPhone())
                    .channelName(channelName)
                    .posterName(channelName)
                    .bizUserId(bizUser.getId())
                    .settleDiscountType(fissionBrokeRage.getSettleDiscountType())
                    .channelType(ChannelTypeEnum.FISSION.getCode())
                    .brokeRage(fissionBrokeRage.getSettleDiscount())
                    .build(), ChannelTypeEnum.FISSION);
            bizUserService.setSeedId(bizUser.getId(), distributionChannel.getSeedId());
        }
        DistributionChannelVO distributionChannelVO = BeanUtil.copyProperties(distributionChannel, DistributionChannelVO.class);
//        if (StatusTypeEnum.YES.getCode().equals(businessAccount.getIsMock())) {
//            distributionChannelVO.setChannelAccountType(ChanelAccountTypeEnum.UNBIND.getCode());
//        } else if (DateUtils.getEndOfToday().compareTo(DateUtils.getEndOfDay(businessAccount.getBusinessVO().getMemberValidity())) > 0) {
//            distributionChannelVO.setChannelAccountType(ChanelAccountTypeEnum.EXPIRED.getCode());
//        }

        businessAccountService.updateLoginTime(businessAccount);
        return WechatLoginCheckDTO.builder()
                .loginStatus(WxChatLoginStatusEnum.LOGIN_SUCCESS)
                .token(loginChannel(distributionChannelVO))
                .build();
    }

    /**
     * oauth2Login
     *
     * @param ticket          ticket
     * @param businessAccount 用户账户
     * @param wechatTokenDTO  微信回调信息
     * @return WechatOauth2LoginDTO
     */
    private WechatOauth2LoginDTO oauth2LoginDTO(String ticket,
                                                BusinessAccountVO businessAccount,
                                                WechatTokenDTO wechatTokenDTO
    ) {
        log.info("oAuth2登录{},{},{}", ticket, businessAccount, wechatTokenDTO);

        final String unionId = wechatTokenDTO.getUnionid();
        ChannelVO channelByTicket = channelCore.getChannelByTicket(ticket);

        boolean hasValidChannel = ObjectUtil.isNotNull(channelByTicket) && StrUtil.isNotBlank(channelByTicket.getWeChatUrl());
        Integer type = getValidChannelChannelType();
        String weChatUrl = hasValidChannel ? getValidChannelWeChatUrl(channelByTicket) :
                type.equals(WechatOauth2LoginDTO.TYPE_LINK) ?
                        String.format("%s?customer_channel=%s", getCurrentActiveAcquisitionLink(), ChannelTypeEnum.PREFIX + ChannelTypeEnum.WEBSITE.getLabel() + "-1")
                        : getSysDefaultQrcode();


        log.info("获取企微类型: type={}, url={}", type, weChatUrl);


        if (businessAccount == null) {
            updateRedisWorkWechatStatus(unionId, ticket);
            updateRedisTicketUnionId(ticket, unionId);
            WeChatExternalUser weChatExternalUser = weChatExternalUserService.getOneByUnionid(unionId);

            if (ticket.contains(TokenConstants.SUB_ACCOUNT_VER)) {
//                    子账号多一层用户校验,缓存用户是否相等
//                      注意！ 此方法会改动原weChatExternalUser信息
                if (checkUserAddUser(weChatExternalUser, getMemberCodeFromStatus(ticket))) {
                    //  有缓存的企业微信回调数据 那么直接帮他创建账号 然后 返回登陆成功
                    bizUserService.updateContactUserName(unionId, weChatExternalUser.getConnectUserName());
                    weChatExternalUserService.updateContactUserInfo(
                            weChatExternalUser.getExternalUserid(),
                            weChatExternalUser.getConnectUserId(),
                            weChatExternalUser.getConnectUserName()
                    );
                    return createAccountAndLogin(weChatExternalUser, unionId);
                }
//                需要添加好友，这里已经是获取到对应类型的二维码
                WechatOauth2LoginDTO wechatUrlByMemberCode = getBusinessWechatUrlByMemberCode(getMemberCodeFromStatus(ticket));
                type = wechatUrlByMemberCode.getType();
                weChatUrl = wechatUrlByMemberCode.getUrl();
            }

            if (weChatExternalUser != null) {
                //  2、如果有缓存的企业微信回调数据 那么直接帮他创建账号 然后 返回登陆成功
                return createAccountAndLogin(weChatExternalUser, unionId);
            }

            // 新用户注册 这里需要绑定关系
            // 更新redis 建立unionId与ticket的关系
            updateRedisTicketStatus(ticket, WxChatLoginStatusEnum.LOGINING);
            WechatOauth2LoginDTO oauth2LoginDTO = WechatOauth2LoginDTO.builder()
                    .type(type)
                    .url(weChatUrl)
                    .phone(StatusTypeEnum.YES.getCode())
                    .loginStatus(WxChatLoginStatusEnum.LOGINING)
                    .build();
            String redisPhone = getRedisTicketPhone(ticket);
            boolean phoneMatchesTicket = StrUtil.isNotBlank(redisPhone) && ticket.equals(getPhoneTicket(redisPhone));
            oauth2LoginDTO.setPhone(phoneMatchesTicket ? StatusTypeEnum.NO.getCode() : StatusTypeEnum.YES.getCode());

            String ip = SecurityUtils.getCurrentOperateIp();
            Region region = IpRegionConfig.getRegion(ip);
            log.debug(region.toString());
            updateRedisUnionIdIp(unionId, region.getCode());
            return oauth2LoginDTO;

        }

        String redisTicketPhone = getRedisTicketPhone(ticket);
        if (StrUtil.isNotBlank(redisTicketPhone)) {
            BizUser byUnionId = bizUserService.getByUnionId(unionId);

            if (CharSequenceUtil.isBlank(byUnionId.getPhone())) {
                byUnionId.setPhone(redisTicketPhone);
                bizUserService.updateById(byUnionId);
            } else if (!byUnionId.getPhone().equals(redisTicketPhone)) {
                // 注册流程 商家登录端输入手机号、验证码后 根据返回数据扫码进入（已绑定登录账号的微信）
                updateRedisTicketStatus(ticket, WxChatLoginStatusEnum.BINDING);
                return WechatOauth2LoginDTO.builder()
                        .loginStatus(WxChatLoginStatusEnum.BINDING)
                        .build();
            }
        }

        // 用户禁用
        if (checkBan(ticket, businessAccount)) {
            return WechatOauth2LoginDTO.builder()
                    .loginStatus(WxChatLoginStatusEnum.ACCOUNT_DISABLE)
                    .build();
        }
        return loginSuccess(ticket, unionId);
    }

    private void updateRedisUnionIdIp(String unionId, String ip) {
        redisService.setCacheObject(CacheConstants.WECHAT_LOGIN_UNION_ID_IP_PREFIX + unionId, ip, CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
    }


    /**
     *
     */
    private WechatOauth2LoginDTO createAccountAndLogin(WeChatExternalUser externalUser, String unionId) {
        ExternalContactInfoDTO contactInfo = BeanUtil.copyProperties(externalUser, ExternalContactInfoDTO.class);
        addAccount(externalUser.getExternalUserid(), contactInfo);
        workWechatLogin(unionId);
        return WechatOauth2LoginDTO.builder().loginStatus(WxChatLoginStatusEnum.LOGIN_SUCCESS).build();
    }

    /**
     * oauth2Login
     *
     * @param ticket          ticket
     * @param businessAccount 用户账户
     * @param wechatTokenDTO  微信回调信息
     * @return WechatOauth2LoginDTO
     */
    private WechatOauth2LoginDTO oauth2LoginPlus(String ticket,
                                                 BusinessAccountVO businessAccount,
                                                 WechatTokenDTO wechatTokenDTO) {
        log.info("oAuth2登录{},{},{}", ticket, businessAccount, wechatTokenDTO);
        if (ObjectUtil.isNotNull(businessAccount)) {
            //有登录账号
            // 用户禁用 抛出已绑定
            if (checkBan(ticket, businessAccount)) {
                return WechatOauth2LoginDTO.builder()
                        .loginStatus(WxChatLoginStatusEnum.ACCOUNT_DISABLE)
                        .build();
            }
            //登录成功
            return loginSuccess(ticket, wechatTokenDTO.getUnionid());
        }
        //无登录账号
        updateRedisWorkWechatStatus(wechatTokenDTO.getUnionid(), ticket);
        updateRedisTicketUnionId(ticket, wechatTokenDTO.getUnionid());
        // 更新redis 建立unionId与ticket的关系
        updateRedisTicketStatus(ticket, WxChatLoginStatusEnum.LOGINING);
        return WechatOauth2LoginDTO.builder()
                .loginStatus(WxChatLoginStatusEnum.LOGIN_NO_PHONE_PLUS)
                .build();

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PhoneLoginVO phoneLogin(PhoneLoginDTO dto) {
        Assert.isTrue(StringUtils.isNotBlank(dto.getTicket()), "ticket不能为空");
        if (StatusTypeEnum.YES.getCode().equals(dto.getIsAliyunLogin())) {
            VerifyPhoneWithTokenRequest verifyPhoneWithTokenRequest = new VerifyPhoneWithTokenRequest();
            verifyPhoneWithTokenRequest.setSpToken(dto.getPhoneCaptcha());
            verifyPhoneWithTokenRequest.setPhoneNumber(dto.getPhone());
            if (!aliyunCore.verifyPhoneWithToken(verifyPhoneWithTokenRequest)) {
                return PhoneLoginVO.builder()
                        .loginStatus(WxChatLoginStatusEnum.ALIYUN_VERIFY_PHONE_ERROR)
                        .msg(WxChatLoginStatusEnum.ALIYUN_VERIFY_PHONE_ERROR.getDesc())
                        .build();
            }
        } else if (!smsService.verifyCode(dto.getPhone(), dto.getPhoneCaptcha())) {
            //验证码错误异常
            return PhoneLoginVO.builder()
                    .loginStatus(WxChatLoginStatusEnum.CAPTCHA_FAULT_ERROR)
                    .msg(WxChatLoginStatusEnum.CAPTCHA_FAULT_ERROR.getDesc())
                    .build();
        }
        //根据手机号获取登录信息
        BizUser bizUserByPhone = bizUserService.getByPhone(dto.getPhone());
        Assert.notNull(bizUserByPhone, "登录失败，请联系蜗牛客服处理~");

        Assert.isTrue(!bizUserByPhone.getStatus().equals(StatusEnum.UN_ENABLED.getCode()), "该账号已禁用,请联系管理员!");
        if (StrUtil.isNotBlank(bizUserByPhone.getUnionid())) {
            //手机号对应账号已存在微信信息
            return PhoneLoginVO.builder()
                    .loginStatus(WxChatLoginStatusEnum.PHONE_BINDING)
                    .build();
        }
        String unionId = getRedisTicketUnionId(dto.getTicket());
        //获取登录信息
        BizUser bizUserByUnionId = bizUserService.getByUnionId(unionId);
        if (ObjectUtil.isNotNull(bizUserByUnionId)) {
            //错误状态 微信已存在账号数据
            return PhoneLoginVO.builder()
                    .loginStatus(WxChatLoginStatusEnum.WECHAT_BINDING)
                    .build();
        }
        if (getTicketByUnionId(unionId).equals(dto.getTicket())) {
            WeChatExternalUser weChatExternalUser = weChatExternalUserService.getOneByUnionid(unionId);
            if (ObjectUtil.isNull(weChatExternalUser)) {
                ChannelVO channelByTicket = channelCore.getChannelByTicket(dto.getTicket());
                Integer type = ObjectUtil.isNotNull(channelByTicket) && StrUtil.isNotBlank(channelByTicket.getWeChatUrl()) ? 2 : workWxtype;
                String weChatUrl = ObjectUtil.isNotNull(channelByTicket) && StrUtil.isNotBlank(channelByTicket.getWeChatUrl()) ? channelByTicket.getWeChatUrl() : getSysDefaultQrcode();
                updateRedisTicketStatus(dto.getTicket(), WxChatLoginStatusEnum.LOGINING);
                updateRedisTicketPhone(dto.getTicket(), dto.getPhone());
                return PhoneLoginVO.builder()
                        .type(type)
                        .url(weChatUrl)
                        .loginStatus(WxChatLoginStatusEnum.LOGINING)
                        .build();
            }
            bizUserService.updateById(bizUserByPhone.setNickName(weChatExternalUser.getName())
                    .setExternalUserId(weChatExternalUser.getExternalUserid())
                    .setPic(weChatExternalUser.getAvatar())
                    .setConnectUserName(weChatExternalUser.getConnectUserName())
                    .setUnionid(weChatExternalUser.getUnionid()));
            channelCore.reSetBizUserChannelInfo(weChatExternalUser, bizUserByPhone.getId(), dto.getTicket());


            BusinessAccountVO businessAccount = businessAccountService.getBusinessAccount(unionId);
            businessAccountService.updateLoginTime(businessAccount);
            updateRedisTicketStatus(dto.getTicket(), WxChatLoginStatusEnum.LOGIN_SUCCESS);
            updateRedisWorkWechatStatus(unionId, dto.getTicket());
            updateRedisTicketUnionId(dto.getTicket(), unionId);
            return PhoneLoginVO.builder()
                    .loginStatus(WxChatLoginStatusEnum.LOGIN_SUCCESS)
                    .build();

        } else {
            throw new ServiceException("二维码已过期，请重新扫码执行流程~");
        }
    }


    private boolean checkBan(String ticket, BusinessAccountVO businessAccount) {
        if (businessAccount.getUserStatus().equals(StatusEnum.UN_ENABLED.getCode())) {
            updateRedisTicketStatus(ticket, WxChatLoginStatusEnum.ACCOUNT_DISABLE);
            return true;
        }
        return false;
    }

    private void updateRedisTicketPhone(String ticket, String phone) {
        redisService.setCacheObject(CacheConstants.PHONE_LOGIN_TICKET_PHONE_KEY + ticket, phone, CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
        redisService.setCacheObject(CacheConstants.PHONE_LOGIN_PHONE_TICKET_KEY + phone, ticket, CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
    }


    private void updateRedisTicketStatus(String ticket, WxChatLoginStatusEnum loginStatusEnum) {
        redisService.setCacheObject(CacheConstants.WECHAT_LOGIN_STATUS_PREFIX + ticket, loginStatusEnum.getCode(), CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
    }

    //加入商家-设置code-unionId
    private void updateRedisCodeUnionId(String code, String unionId) {
        redisService.setCacheObject(CacheConstants.WECHAT_JOIN_BUSINESS_CODE_UNIONID_PREFIX + code, unionId, CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
    }

    //加入商家-获取unionId
    private String getRedisCodeUnionId(String code) {
        return redisService.getCacheObject(CacheConstants.WECHAT_JOIN_BUSINESS_CODE_UNIONID_PREFIX + code);
    }

    //加入商家-设置unionId-员工信息
    private void updateRedisUnionIdJoinBusiness(String unionId, JoinBusinessDTO joinBusinessDTO) {
        redisService.setCacheObject(CacheConstants.WECHAT_JOIN_BUSINESS_UNIONI_DTO_PREFIX + unionId, joinBusinessDTO, CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
    }

    //加入商家-设置unionId-员工信息
    private JoinBusinessDTO getRedisUnionIdJoinBusiness(String unionId) {
        return redisService.getCacheObject(CacheConstants.WECHAT_JOIN_BUSINESS_UNIONI_DTO_PREFIX + unionId);
    }

    private WxChatLoginStatusEnum getRedisTicketStatus(String ticket) {
        final Integer code = redisService.getCacheObject(CacheConstants.WECHAT_LOGIN_STATUS_PREFIX + ticket);
        if (code == null) {
            throw new ServiceException("页面超时,缓存过期");
        }
        return WxChatLoginStatusEnum.getStatusByCode(code);
    }

    /**
     * redis unionId - ticket关系
     */
    private void updateRedisWorkWechatStatus(String unionId, String ticket) {
        redisService.setCacheObject(CacheConstants.WORK_WECHAT_LOGIN_STATUS_PREFIX + unionId, ticket, CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
    }

    /**
     * redis unionId - ticket关系
     */
    private String getRedisWorkWechatStatus(String unionId) {
        return redisService.getCacheObject(CacheConstants.WORK_WECHAT_LOGIN_STATUS_PREFIX + unionId);
    }

    /**
     * redis ticket - unionId关系
     */
    private void updateRedisTicketUnionId(String ticket, String unionId) {
        redisService.setCacheObject(CacheConstants.WORK_WECHAT_LOGIN_TICKET_UNIONID_PREFIX + ticket, unionId, CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
    }

    /**
     * redis ticket - unionId关系
     */
    private String getRedisTicketUnionId(String ticket) {
        return redisService.getCacheObject(CacheConstants.WORK_WECHAT_LOGIN_TICKET_UNIONID_PREFIX + ticket);
    }

    /**
     * redis ticket - phone关系
     */
    private String getRedisTicketPhone(String ticket) {
        return redisService.getCacheObject(CacheConstants.PHONE_LOGIN_TICKET_PHONE_KEY + ticket);
    }

    /**
     * redis phone - ticket关系
     */
    private String getPhoneTicket(String phone) {
        return redisService.getCacheObject(CacheConstants.PHONE_LOGIN_PHONE_TICKET_KEY + phone);
    }

    /**
     * redis unionId - externalUserId关系
     */
    private void updateRedisUnionIdExternalUserId(String unionId, ExternalContactInfoDTO externalContact) {
        redisService.setCacheObject(CacheConstants.WORK_WX_UNIONID_EXTRA_PREFIX + unionId, externalContact, CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
    }

    /**
     * redis unionId - externalUserId关系
     */
    private ExternalContactInfoDTO getRedisUnionIdExternalUserId(String unionId) {
        return redisService.getCacheObject(CacheConstants.WORK_WX_UNIONID_EXTRA_PREFIX + unionId);
    }

    /**
     * redis 获取当前系统默认的二维码
     */
    private String getSysDefaultQrcode() {
        return redisService.getCacheObject(CacheConstants.CURRENT_ACTIVE_URL_PREFIX + WechatContactUserTagEnum.SYS_WEBSITE_1.getShortTag());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addAccount(String externalUserId, ExternalContactInfoDTO dto) {
        final BizUser bizUser = bizUserService.getByUnionId(dto.getUnionid());
        if (bizUser == null) {
            // 获取ticket
            String ticket = getRedisWorkWechatStatus(dto.getUnionid());
            String redisPhone = getRedisTicketPhone(ticket);

            if (StringUtils.isNotBlank(ticket)
                    && dto.getUnionid().equals(getRedisTicketUnionId(ticket))) {
                //获取手机号
                BizUser bizUserByUnionId = bizUserService.getByUnionId(dto.getUnionid());
                if (ObjectUtil.isNull(bizUserByUnionId)) {
                    // 初始化登录账号数据
                    bizUserByUnionId = bizUserService.initBizUserBaseOnUnionId(BizUserDTO.builder()
                            .unionid(dto.getUnionid())
                            .nickName(dto.getName())
                            .pic(dto.getAvatar())
                            .phone(redisPhone)
                            .connectUserName(dto.getConnectUserName())
                            .build());
                }

                //根据ticket 设置注册渠道  根据dto.channel 设置 添加企微渠道
                channelCore.reSetBizUserChannelInfo(BeanUtil.copyProperties(dto, WeChatExternalUser.class), bizUserByUnionId.getId(), ticket);
            }
        }
        // 解除了企微好友再次加入
        businessAccountService.setUserExternalUserid(dto.getUnionid(), externalUserId);
    }


    @Override
    public void updateAccount(String externalUserId, ExternalContactInfoDTO dto) {
        final BusinessAccountVO businessAccount = businessAccountService.getBusinessAccount(dto.getUnionid());
        if (businessAccount == null) {
            // 首次注册 直接跳过
            return;
        }
        // 解除了企微好友再次加入
        businessAccountService.setUserExternalUserid(dto.getUnionid(), externalUserId);
    }

    @Override
    public void cleanUserExternalUserid(String externalUserId) {
        businessAccountService.cleanUserExternalUserid(externalUserId);
    }

    /**
     * 登录成功
     */
    private WechatOauth2LoginDTO loginSuccess(String ticket, String unionId) {
        // 登录成功 更新redis 返回基础信息
        BizUser user = bizUserService.getByUnionId(unionId);
        if (user != null && user.getStatus().equals(StatusEnum.UN_ENABLED.getCode())) {
            return WechatOauth2LoginDTO.builder().loginStatus(WxChatLoginStatusEnum.ACCOUNT_DISABLE).build();
        }
        updateRedisTicketStatus(ticket, WxChatLoginStatusEnum.LOGIN_SUCCESS);
        updateRedisWorkWechatStatus(unionId, ticket);
        updateRedisTicketUnionId(ticket, unionId);
        return WechatOauth2LoginDTO.builder().loginStatus(WxChatLoginStatusEnum.LOGIN_SUCCESS).build();
    }

    private String getMemberCodeFromStatus(String status) {
        return status.substring(status.indexOf(TokenConstants.SUB_ACCOUNT_VER) + TokenConstants.SUB_ACCOUNT_VER.length());
    }

    private Long getBizUserIdFromTicket(String ticket) {
        return Convert.toLong(ticket.substring(TokenConstants.CHANNEL_REG.length()));
    }

    /**
     * 获取当前系统默认二维码链接
     *
     * @return 默认二维码链接
     */
    private WechatOauth2LoginDTO getSysWebsiteOneWechatUrl() {
        if (isCurrentTypeEqualLink()) {
            return WechatOauth2LoginDTO.builder()
                    .type(WechatOauth2LoginDTO.TYPE_LINK)
                    .url(String.format("%s?customer_channel=%s", getCurrentActiveAcquisitionLink(), ChannelTypeEnum.PREFIX + ChannelTypeEnum.WEBSITE.getLabel() + "-1"))
                    .loginStatus(WxChatLoginStatusEnum.LOGINING)
                    .build();
        }
        return WechatOauth2LoginDTO.builder()
                .type(workWxtype)
                .url(getSysDefaultQrcode())
                .loginStatus(WxChatLoginStatusEnum.LOGINING)
                .build();
    }

    /**
     * 获取商家子账号类企业微信二维码链接
     *
     * @param memberCode 会员编码
     * @return 商家子账号类企业微信二维码链接
     */
    private WechatOauth2LoginDTO getBusinessWechatUrlByMemberCode(String memberCode) {
        if (isCurrentTypeEqualLink()) {
            BusinessOwnerAccountContactUserDTO contactUserDTO = businessService.getBusinessOwnerUserContactUserNameByMemberCode(memberCode);
            String contactUserId = contactUserDTO.getContactUserId();
            String url = weChatContactUserConfigService.getAcquisitionByContactUserId(contactUserId);
            return WechatOauth2LoginDTO.builder()
                    .url(String.format("%s?customer_channel=%s", url, ChannelTypeEnum.PREFIX + ChannelTypeEnum.BUSINESS.getLabel() + "-" + memberCode))
                    .type(WechatOauth2LoginDTO.TYPE_LINK)
                    .build();
        }
        return WechatOauth2LoginDTO.builder()
                .url(businessService.getBusinessWechatUrlByMemberCode(memberCode))
                .type(WechatOauth2LoginDTO.TYPE_QRCODE)
                .build();
    }

    /**
     * 当前是否生效为获客链接
     *
     * @return
     */
    private boolean isCurrentTypeEqualLink() {
        String currentType = redisService.getCacheObject(Constants.SYS_CONFIG_KEY + ConfigConstants.SYS_ACCOUNT_ADD_USER_WAY);
        try {
            return Integer.parseInt(currentType) == (WechatOauth2LoginDTO.TYPE_LINK);
        } catch (Exception e) {
            log.warn("系统获客链接参数配置错误！");
            return false;
        }
    }

    /**
     * 获取当前有效的获客链接
     *
     * @return 当前有效的获客链接
     */
    private String getCurrentActiveAcquisitionLink() {
        return redisService.getCacheObject(CacheConstants.CURRENT_ACTIVE_URL_PREFIX + WechatContactUserTagEnum.ACQUISITION.getShortTag());
    }

    /**
     * 获取当前二维码类型
     *
     * @return 当前二维码类型
     */
    private Integer getValidChannelChannelType() {
        return isCurrentTypeEqualLink() ? WechatOauth2LoginDTO.TYPE_LINK : WechatOauth2LoginDTO.TYPE_QRCODE;
    }


    /**
     * 通过渠道信息获取二维码链接
     *
     * @param channelByTicket 渠道信息
     * @return 二维码链接
     */
    private String getValidChannelWeChatUrl(ChannelVO channelByTicket) {
        boolean isChannelMarking = channelByTicket.getChannelType().equals(ChannelTypeEnum.MARKETING);
        String link = String.format("%s%s-%s", ChannelTypeEnum.PREFIX,
                isChannelMarking ? ChannelTypeEnum.MARKETING.getLabel() : ChannelTypeEnum.DISTRIBUTION.getLabel(),
                channelByTicket.getDedicatedLinkCode()
        );
        return isCurrentTypeEqualLink() ?
                String.format("%s?customer_channel=%s", getCurrentActiveAcquisitionLink(), link) :
                channelByTicket.getWeChatUrl();
    }
}