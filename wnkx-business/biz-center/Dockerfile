FROM nexus.dev.woniu.world:28086/wnkx/baseline/eclipse-temurin:11-jdk-alpine-wnkx

LABEL MAINTAINER=CI
WORKDIR /apps
ADD target/biz-center.jar /apps/
ENV TZ=Asia/Shanghai

CMD java -server $JVM_ARGS -javaagent:/apps/opentelemetry-javaagent.jar \
         -Dotel.resource.attributes=service.name=biz-center,token=$APM_TOKEN\
         -Dotel.exporter.otlp.endpoint=http://pl.ap-guangzhou.apm.tencentcs.com:4317 \
         -jar biz-center.jar $DEPLOY_ARGS
