server:
  port: 7010
  shutdown: graceful
spring:
  main:
    allow-circular-references: true
  application:
    name: biz-center
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${wnkx.ds.ip}:${wnkx.ds.port}/${wnkx.ds.biz-db:biz-center}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: ${wnkx.ds.username}
    password: ${wnkx.ds.password}
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.wnkx.biz.**.domain
  global-config:
    db-config:
      id-type: auto

# swagger配置
swagger:
  title: 业务模块接口文档   #标题
  description: 业务模块接口文档   #描述
  version: 1.0.0  #版本
  contact:
    name: wnkx   #作者
