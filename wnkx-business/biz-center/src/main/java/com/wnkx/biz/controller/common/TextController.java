package com.wnkx.biz.controller.common;

import cn.hutool.core.convert.Convert;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.AgreementEnum;
import com.ruoyi.common.core.enums.TextTypeEnum;
import com.ruoyi.common.core.validated.CommonValidatedGroup;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.biz.common.*;
import com.ruoyi.system.api.domain.vo.biz.common.*;
import com.wnkx.biz.common.service.TextService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:23
 */
@RestController
@RequestMapping("/text")
@Api(value = "文本管理服务", tags = "文本管理服务")
@RequiredArgsConstructor
public class TextController extends BaseController {

    private final TextService textService;

    /**
     * 文本列表
     */
    @GetMapping("/list")
    @ApiOperation("文本列表")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("system:text:list")
    public R<List<TextListVO>> selectListByCondition(TextListDTO dto) {
        dto.setType(TextTypeEnum.PROTOCOL.getCode());
        List<TextListVO> list = textService.selectListByCondition(dto);
        return R.ok(list);
    }

    /**
     * 添加数据
     */
    @PostMapping("/add")
    @ApiOperation("添加数据")
    @RequiresPermissions("system:text:add")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<String> add(@RequestBody @Validated TextDTO dto) {
        textService.add(dto);
        return R.ok();
    }

    /**
     * 编辑数据
     */
    @PutMapping("/edit")
    @ApiOperation("编辑数据")
    @RequiresPermissions("system:text:edit")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<String> edit(@RequestBody @Validated(CommonValidatedGroup.EditValidatedGroup.class) TextDTO dto) {
        textService.edit(dto);
        return R.ok();
    }

    /**
     * 删除数据
     */
    @DeleteMapping("/delete")
    @ApiOperation("删除数据")
    @RequiresPermissions("system:text:delete")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<String> delete(@RequestParam Long id) {
        textService.delete(id);
        return R.ok();
    }

    /**
     * 查看数据
     */
    @GetMapping("/{id}")
    @ApiOperation("查看数据")
    @RequiresPermissions("system:text:list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<TextVO> detail(@PathVariable Long id) {
        TextVO textVO = textService.detail(id);
        return R.ok(textVO);
    }

    /**
     * 查询用户协议
     */
    @GetMapping("/user-agreement")
    @ApiOperation("查询用户协议")
    public R<TextVO> userAgreement() {
        TextVO textVO = textService.detail(Convert.toLong(AgreementEnum.USER_AGREEMENT.getCode()));
        return R.ok(textVO);
    }

    /**
     * 查询支付协议
     */
    @GetMapping("/payment-agreement")
    @ApiOperation("查询支付协议")
    public R<TextVO> paymentAgreement() {
        TextVO textVO = textService.detail(Convert.toLong(AgreementEnum.PAYMENT_AGREEMENT.getCode()));
        return R.ok(textVO);
    }

    /**
     * 查询隐私协议
     */
    @GetMapping("/privacy-agreement")
    @ApiOperation("查询隐私协议")
    public R<TextVO> privacyAgreement() {
        TextVO textVO = textService.detail(Convert.toLong(AgreementEnum.PRIVACY_AGREEMENT.getCode()));
        return R.ok(textVO);
    }

    /**
     * 查询裂变协议
     */
    @GetMapping("/fission-agreement")
    @ApiOperation("查询裂变协议")
    public R<TextVO> fissionAgreement() {
        TextVO textVO = textService.detail(Convert.toLong(AgreementEnum.FISSION_AGREEMENT.getCode()));
        return R.ok(textVO);
    }

    /**
     * 查询平台协议
     */
    @GetMapping("/platform-agreement")
    @ApiOperation("查询平台协议")
    public R<TextVO> platformAgreement() {
        TextVO textVO = textService.detail(Convert.toLong(AgreementEnum.PLATFORM_AGREEMENT.getCode()));
        return R.ok(textVO);
    }


    @GetMapping("/help/list")
    @ApiOperation(value = "获取帮助中心列表", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("system:help:list")
    public R<PageInfo<TextHelpListVO>> selectHelpListByCondition(TextHelpListDTO dto) {
        return R.ok(toPage(textService.selectHelpListByCondition(dto)));
    }

    @PostMapping("/help/add")
    @ApiOperation("添加帮助数据")
    @RequiresPermissions("system:help:add")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<String> helpAdd(@RequestBody @Validated TextHelpDTO dto) {
        textService.addHelp(dto);
        return R.ok();
    }

    @PutMapping("/help/edit")
    @ApiOperation("编辑帮助数据")
    @RequiresPermissions("system:help:edit")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<String> helpEdit(@RequestBody @Validated(CommonValidatedGroup.EditValidatedGroup.class) TextHelpDTO dto) {
        textService.editHelp(dto);
        return R.ok();
    }

    @PutMapping("/help/updateStatus")
    @ApiOperation("修改帮助数据状态")
    @RequiresPermissions("system:help:status")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<String> updateStatus(@RequestBody @Validated TextHelpStatusDTO dto) {
        textService.updateStatus(dto);
        return R.ok();
    }

    @DeleteMapping("/help/delete")
    @ApiOperation("删除帮助数据")
    @RequiresPermissions("system:help:delete")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<String> helpDelete(@RequestParam Long id) {
        textService.delete(id);
        return R.ok();
    }

    @GetMapping("/help/{id}")
    @ApiOperation("查看数据")
    @RequiresPermissions(value = {"system:help:detail", "system:help:edit"}, logical = Logical.OR)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<TextHelpVO> helpDetail(@PathVariable Long id) {
        return R.ok(textService.helpDetail(id));
    }



    @GetMapping("/user/help/list")
    @ApiOperation(value = "获取帮助中心列表(商家端)", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    public R<List<UserTextHelpListVO>> selectUserHelpListByCondition() {
        return R.ok(textService.selectUserHelpListByCondition());
    }
}
