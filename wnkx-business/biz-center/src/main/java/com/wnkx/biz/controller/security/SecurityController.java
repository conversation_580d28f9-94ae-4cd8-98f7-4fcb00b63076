package com.wnkx.biz.controller.security;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.system.api.domain.vo.SmsVo;
import com.wnkx.biz.security.SecurityService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 验证码服务(内部调用)
 * SecurityController
 *
 * <AUTHOR>
 * @date 2024/8/28
 */
@RestController
@RequestMapping("/security")
@RequiredArgsConstructor
public class SecurityController {
    private final SecurityService securityService;

    @InnerAuth
    @PostMapping("/sms")
    public R<String> sms(@RequestBody SmsVo smsVo) {
        securityService.sendCode(smsVo);
        return R.ok();
    }
    @InnerAuth
    @PostMapping("/registerSendCode")
    public R<String> registerSendCode(@RequestBody SmsVo smsVo) {
        securityService.registerSendCode(smsVo);
        return R.ok();
    }
}
