package com.wnkx.biz.controller.channel;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityEditDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityInfoDTO;
import com.wnkx.biz.channel.service.DistributionChannelActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/marketing/center/distribution/activity")
@Api(value = "营销中心-分销渠道活动", tags = "营销中心-分销渠道活动")
@RequiredArgsConstructor
public class DistributionChannelActivityController extends BaseController {
    private final DistributionChannelActivityService distributionChannelActivityService;

    @GetMapping("")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "活动列表", response = PageInfo.class)
    @RequiresPermissions("channel:activity:list")
    public R<PageInfo<ChannelActivityDTO>> inviteList(ChannelActivityDTO dto) {
        return R.ok(toPage(distributionChannelActivityService.list(dto)));
    }


    @ApiOperation("新增活动")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("channel:activity:add")
    @PostMapping("")
    public R<String> saveActivity(@RequestBody @Validated ChannelActivityEditDTO dto) {
        distributionChannelActivityService.saveChannelActivity(dto);
        return R.ok();
    }

    @ApiOperation("修改活动")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("channel:activity:edit")
    @PutMapping("")
    public R<String> editActivity(@RequestBody @Validated ChannelActivityEditDTO dto) {
        distributionChannelActivityService.editChannelActivity(dto);
        return R.ok();
    }

    @GetMapping("/{id}")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "活动详情")
    @RequiresPermissions(value = {"channel:activity:view", "channel:activity:edit"}, logical = Logical.OR)
    public R<ChannelActivityEditDTO> getByDistributionChannelId(@PathVariable Long id) {
        return R.ok(distributionChannelActivityService.getActivityInfo(id));
    }

    @PostMapping("/{id}/download")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "下载活动海报")
    @RequiresPermissions("channel:activity:download")
    public void download(@PathVariable Long id, HttpServletResponse response) {
        distributionChannelActivityService.download(id, response);
    }


    @GetMapping("/channel")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "渠道信息检索")
    @RequiresPermissions(value = {"channel:activity:view", "channel:activity:edit"}, logical = Logical.OR)
    public R<PageInfo<ChannelActivityInfoDTO>> getByDistributionChannelList(
            @ApiParam(value = "渠道名称") @RequestParam(required = false)
            String channelName,
            @ApiParam(value = "开始时间") @RequestParam(required = false)
            Date startTime,
            @ApiParam(value = "结束时间") @RequestParam(required = false)
            Date endTime,
            @ApiParam(value = "活动id") @RequestParam(required = false)
            Long activityId)
    {
        PageUtils.startPage();
        return R.ok(toPage(distributionChannelActivityService.getActivityChannelList(channelName, startTime, endTime, activityId)));
    }

}
