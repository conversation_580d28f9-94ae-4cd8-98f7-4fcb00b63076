package com.wnkx.biz.controller.wx;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.dto.biz.business.account.CheckPhoneDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.PhoneLoginDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.RefreshTicketDTO;
import com.ruoyi.system.api.domain.dto.biz.wechat.JoinBusinessDTO;
import com.ruoyi.system.api.domain.dto.biz.wechat.WeChatOauth2LoginRequestDTO;
import com.ruoyi.system.api.domain.dto.biz.wechat.WechatLoginCheckDTO;
import com.ruoyi.system.api.domain.dto.biz.wechat.WechatOauth2LoginDTO;
import com.ruoyi.system.api.domain.vo.CheckWechatVO;
import com.ruoyi.system.api.domain.vo.biz.business.PhoneLoginVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserVO;
import com.ruoyi.system.api.domain.vo.wechat.JoinBusinessVO;
import com.wnkx.biz.wechat.service.WechatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 微信登录
 *
 * <AUTHOR>
 * @date 2024/6/19
 */
@RestController
@RequestMapping("/auth")
@Api(value = "微信业务", tags = "微信业务")
@RequiredArgsConstructor
public class WxController {

    private final WechatService wechatService;

    @GetMapping("/wechat/check")
    @ApiOperation(value = "检查二维码状态")
    public R<WechatLoginCheckDTO> check(@RequestParam String ticket) {
        final WechatLoginCheckDTO check = wechatService.check(ticket);
        return R.ok(check);
    }

    @GetMapping("/mobile/wechat/check")
    @ApiOperation(value = "检查二维码状态")
    public R<WechatLoginCheckDTO> mobieCheck(@RequestParam String ticket) {
        final WechatLoginCheckDTO check = wechatService.mobieCheck(ticket);
        return R.ok(check);
    }

    @PostMapping("/wechat/oauth2")
    @Log(title = "微信授权", businessType = BusinessType.GRANT)
    @ApiOperation(value = "微信授权")
    public R<WechatOauth2LoginDTO> oauth2(@RequestBody WeChatOauth2LoginRequestDTO requestDTO) {
        return R.ok(wechatService.oauth2(requestDTO));
    }
    @PostMapping("/wechat/channelLogin")
    @Log(title = "渠道登录", businessType = BusinessType.GRANT)
    @ApiOperation(value = "微信授权")
    public R<WechatLoginCheckDTO> channelLogin(@RequestBody WeChatOauth2LoginRequestDTO requestDTO) {
        return R.ok(wechatService.channelLogin(requestDTO));
    }

    @PostMapping("/wechat/checkWechat")
    @ApiOperation(value = "检查申请人是否可用")
    public R<CheckWechatVO> checkWechat(@RequestBody WeChatOauth2LoginRequestDTO requestDTO) {
        return R.ok(wechatService.checkWechat(requestDTO));
    }
    @PostMapping("/wechat/checkPhone")
    @ApiOperation(value = "检查手机号是否可用")
    public R<String> checkPhone(@RequestBody CheckPhoneDTO dto) {
        wechatService.checkPhone(dto);
        return R.ok();
    }
    @PostMapping("/wechat/refreshTicket")
    @ApiOperation(value = "刷新ticket")
    public R<String> refreshTicket(@RequestBody RefreshTicketDTO dto) {
        wechatService.refreshTicket(dto);
        return R.ok();
    }

    @PostMapping("/wechat/joinBusiness")
    @Log(title = "加入商家", businessType = BusinessType.INSERT)
    @ApiOperation(value = "加入商家")
    public R<JoinBusinessVO> joinBusiness(@RequestBody @Validated JoinBusinessDTO requestDTO) {
        return R.ok(wechatService.joinBusiness(requestDTO));
    }

    @GetMapping("/wechat/business/check")
    @Log(title = "商家子账号检查", businessType = BusinessType.INSERT)
    @ApiOperation(value = "子账号检查")
    public R<WechatLoginCheckDTO> joinBusinessCheck(@RequestParam String code) {
        return R.ok(wechatService.joinBusinessCheck(code));
    }

    @GetMapping("/wechat/checkInsertBizUser")
    @ApiOperation(value = "检查新增账号二维码状态")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<WechatLoginCheckDTO> checkInsertBizUser(@RequestParam String ticket) {
        final WechatLoginCheckDTO check = wechatService.checkInsertBizUser(ticket);
        return R.ok(check);
    }

    @PostMapping("/phoneLogin")
    @ApiOperation("手机号登录")
    public R<PhoneLoginVO> phoneLogin(@RequestBody @Validated PhoneLoginDTO dto) {
        return R.ok(wechatService.phoneLogin(dto));
    }

    /**
     * 通过TICKET获取登录用户信息
     */
    @GetMapping("/wechat/get-login-user-by-ticket")
    @ApiOperation("通过TICKET获取登录用户信息")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    public R<BizUserVO> getLoginUserByTicket(@RequestParam String ticket) {
        BizUserVO bizUserVO = wechatService.getLoginUserByTicket(ticket);
        return R.ok(bizUserVO);
    }
}
