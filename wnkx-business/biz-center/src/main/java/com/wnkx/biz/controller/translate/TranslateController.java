package com.wnkx.biz.controller.translate;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.domain.dto.biz.translate.TranslateBatchDTO;
import com.ruoyi.system.api.domain.dto.biz.translate.TranslateDTO;
import com.ruoyi.system.api.domain.vo.biz.translate.TranslateVO;
import com.wnkx.biz.translate.service.TranslateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 翻译服务
 *
 * <AUTHOR>
 * @date 2024/6/11
 */
@RestController
@RequestMapping("/translate")
@Api(value = "翻译服务", tags = "翻译服务")
@RequiredArgsConstructor
public class TranslateController {

    private final TranslateService translateService;

    @PostMapping("/translateStr")
    @ApiOperation(value = "单句翻译")
    public R<TranslateVO> translateStr(@RequestBody @Validated TranslateDTO dto) {
        return R.ok(translateService.translate(dto.getOriginText(), dto.getLanguage()));
    }

    @PostMapping()
    @ApiOperation(value = "批量翻译")
    public R<List<String>> translateBatch(@RequestBody TranslateBatchDTO translateBatchDTO) {
        return R.ok(translateService.translate(translateBatchDTO.getWordList(), translateBatchDTO.getLanguage()));
    }
}
