package com.wnkx.biz.controller.page;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.biz.page.PageConfigQueryListDTO;
import com.ruoyi.system.api.domain.entity.biz.page.HomePage;
import com.ruoyi.system.api.domain.entity.biz.page.PageConfigInfo;
import com.ruoyi.system.api.domain.vo.TagListVO;
import com.ruoyi.system.api.domain.vo.biz.page.PageConfigVO;
import com.wnkx.biz.page.service.IPageConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 页面配置Controller
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@RestController
@RequestMapping("/page/config")
@Api(value = "页面配置服务", tags = "页面配置服务")
@RequiredArgsConstructor
public class PageConfigController extends BaseController
{
    private final IPageConfigService pageConfigService;

    /**
     * 查询页面配置列表
     */
    @GetMapping("/list")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.MODEL_TYPE})
    @ApiOperation(value = "查询页面配置列表", response = TagListVO.class)
    // @RequiresPermissions("custom:page:list")
    public PageInfo<PageConfigVO> list(@Validated PageConfigQueryListDTO dto) {
        List<PageConfigVO> list = pageConfigService.queryList(dto);
        return toPage(list);
    }

    /**
     * 获取精选案例配置详细信息
     */
    @GetMapping(value = "/getChooseCaseInfo/{id}")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.MODEL_TYPE})
    @ApiOperation(value = "获取精选案例配置详细信息",response = PageConfigInfo.class)
    @RequiresPermissions("custom:page:list")
    public R<PageConfigInfo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(pageConfigService.getInfo(id));
    }
    /**
     * 获取页面配置详细信息
     */
    @GetMapping(value = "/getHomePage/{id}")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.MODEL_TYPE})
    @ApiOperation(value = "获取页面配置详细信息",response = HomePage.class)
    @RequiresPermissions("custom:page:list")
    public R<HomePage> getHomePage(@PathVariable("id") Long id)
    {
        return R.ok(pageConfigService.getHomePage(id));
    }

    /**
     * 保存精选案例配置
     */
    @Log(title = "保存精选案例配置", businessType = BusinessType.INSERT)
    @PostMapping("/saveChooseCase")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "保存精选案例配置")
    // @RequiresPermissions("custom:home-page:edit")
    public R<String> saveChooseCase(@RequestBody @Validated PageConfigInfo dto) {
        pageConfigService.saveChooseCase(dto);
        return R.ok();
    }
    /**
     * 保存页面配置详情
     */
    @Log(title = "保存首页配置", businessType = BusinessType.INSERT)
    @PostMapping("/saveHomePage")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "保存首页配置")
    // @RequiresPermissions("custom:home-page:edit")
    public R<String> saveHomePage(@RequestBody @Validated HomePage dto) {
        pageConfigService.saveHomePage(dto);
        return R.ok();
    }
}
