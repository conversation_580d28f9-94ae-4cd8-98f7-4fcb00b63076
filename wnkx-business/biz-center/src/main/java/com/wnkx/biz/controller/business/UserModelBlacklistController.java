package com.wnkx.biz.controller.business;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.entity.biz.model.UserModelBlacklist;
import com.ruoyi.system.api.domain.vo.biz.business.user.UserBlackModelVO;
import com.wnkx.biz.business.service.IUserModelBlacklistService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :商家余额详情
 * @create :2024-12-25 17:55
 **/
@RestController
@RequestMapping("/business/blacklist")
@Api(value = "黑名单业务" , tags = "黑名单业务")
@RequiredArgsConstructor
public class UserModelBlacklistController extends BaseController {

    private final IUserModelBlacklistService userModelBlacklistService;

    @GetMapping("/list")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "获取登录账号黑名单列表" , response = PageInfo.class)
    public R<PageInfo<UserBlackModelVO>> getBlacklistList() {
        return R.ok(toPage(userModelBlacklistService.userBlackModelList(SecurityUtils.getBizUserId())));
    }

    @ApiOperation("拉黑模特")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @PostMapping("/blackModel/{modelId}")
    public R<String> blackModel(@PathVariable Long modelId) {
        userModelBlacklistService.blackModel(modelId);
        return R.ok();
    }
    @ApiOperation("取消拉黑模特")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @PostMapping("/cancelBlackModel/{modelId}")
    public R<String> cancelBlackModel(@PathVariable Long modelId) {
        userModelBlacklistService.cancelBlackModel(modelId);
        return R.ok();
    }

    /**
     * 获取当前用户拉黑模特列表
     */
    @GetMapping("/balck-model-list")
    @InnerAuth
    public R<List<UserModelBlacklist>> selectBlackModelListByBizUserId() {
        return R.ok(userModelBlacklistService.selectBlackModelListByBizUserId());
    }

    /**
     * 获取用户拉黑模特列表
     */
    @GetMapping("/balck-model-list/by-biz-user-id")
    @InnerAuth
    public R<List<UserBlackModelVO>> userBlackModelListByBizUserId(@RequestParam Long bizUserId) {
        return R.ok(userModelBlacklistService.userAllBlackModelList(bizUserId));
    }
}
