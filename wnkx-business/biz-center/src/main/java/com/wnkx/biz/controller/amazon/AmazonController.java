package com.wnkx.biz.controller.amazon;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.system.api.domain.dto.order.AsyncCrawlTask;
import com.ruoyi.system.api.domain.entity.biz.amazon.AmazonGoodsPic;
import com.ruoyi.system.api.domain.vo.AmazonProductInfo;
import com.wnkx.biz.amazon.service.AmazonGoodsPicService;
import com.wnkx.biz.amazon.service.AmazonImageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/8
 */
@RestController
@RequestMapping("/amazon")
@Api(value = "Amazon业务", tags = "Amazon业务")
@RequiredArgsConstructor
public class AmazonController {

    private final AmazonGoodsPicService amazonGoodsPicService;
    private final AmazonImageService amazonImageService;

    /**
     * 添加数据
     */
    @PostMapping("/add-amazon-goods-pic")
    @InnerAuth
    public void addAmazonGoodsPic(@RequestBody List<AmazonGoodsPic> amazonGoodsPics) {
        amazonGoodsPicService.addAmazonGoodsPic(amazonGoodsPics);
    }

    /**
     * 异步批量抓取amazon产品链接图片并更新视频订单
     */
    @PostMapping("/async-update-order-video-image")
    @InnerAuth
    public R<String> asyncUpdateOrderVideoImage(@RequestBody AsyncCrawlTask asyncCrawlTask) {
        amazonImageService.asyncUpdateOrderVideoImage(asyncCrawlTask);
        return R.ok();
    }


    @PostMapping("/product/info")
    @ApiOperation("tiktok/amazon图片获取")
    public R<AmazonProductInfo> getTiktokInfo(@RequestBody Map<String,String> urlMap) {
        String url = urlMap.getOrDefault("url", "");
        if (url.contains("amazon")){
            return R.ok(amazonImageService.getAmazonProductInfo(url));
        }else {
            return R.ok(amazonImageService.getTiktokInfo(url));
        }
    }

}
