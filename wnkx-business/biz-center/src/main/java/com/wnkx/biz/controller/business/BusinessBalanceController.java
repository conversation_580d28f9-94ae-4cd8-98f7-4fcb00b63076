package com.wnkx.biz.controller.business;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.BusinessBalanceFlowDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessBalanceFlowListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceFlowDetailListDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceFlow;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceFlowExportVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceFlowVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailFlowExportVO;
import com.wnkx.biz.business.service.IBusinessBalanceFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :商家余额流水
 * @create :2024-06-27 18:55
 **/
@RestController
@RequestMapping("/business")
@Api(value = "商家流水业务" , tags = "商家流水业务")
@RequiredArgsConstructor
public class BusinessBalanceController extends BaseController {
    private final IBusinessBalanceFlowService businessBalanceFlowService;

    /**
     * 获取商家余额流水列表（商家端）
     */
    @GetMapping("/businessBalanceFlowList")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "获取商家余额流水列表（商家端）" , response = PageInfo.class)
    public R<PageInfo<BusinessBalanceFlowVO>> businessBalanceFlowList(BusinessBalanceFlowListDTO dto) {
        List<BusinessBalanceFlow> businessBalanceFlows = businessBalanceFlowService.businessBalanceFlowList(dto);
        List<BusinessBalanceFlowVO> businessBalanceFlowVOS = businessBalanceFlowService.transitionVo(businessBalanceFlows);
        return R.ok(PageUtils.getDataTable(businessBalanceFlowVOS, businessBalanceFlows));
    }

    /**
     * 导出商家余额流水列表（商家端）
     */
    @PostMapping("/export/businessBalanceFlowList")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "导出商家余额流水列表（商家端）" )
    public void exportOrderMemberList(BusinessBalanceFlowListDTO dto, HttpServletResponse response) {
        List<BusinessBalanceFlow> businessBalanceFlows = businessBalanceFlowService.businessBalanceFlowList(dto);
        List<BusinessBalanceFlowExportVO> businessBalanceFlowVOS = BeanUtil.copyToList(businessBalanceFlows, BusinessBalanceFlowExportVO.class);
        ExcelUtil<BusinessBalanceFlowExportVO> util = new ExcelUtil<>(BusinessBalanceFlowExportVO.class);
        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "商家余额流水列表导出");
        util.exportExcel(response, businessBalanceFlowVOS, "商家余额流水列表导出");
    }

    /**
     * 获取商家余额流水列表（运营端）
     */
    @GetMapping("/backend/businessBalanceFlowList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:balance:details")
    @ApiOperation(value = "获取商家余额流水列表（运营端）" , response = PageInfo.class)
    public R<PageInfo<BusinessBalanceFlowVO>> businessBalanceFlowListBackend(BusinessBalanceFlowListDTO dto) {
        List<BusinessBalanceFlow> businessBalanceFlows = businessBalanceFlowService.businessBalanceFlowList(dto);
        List<BusinessBalanceFlowVO> businessBalanceFlowVOS = businessBalanceFlowService.transitionVo(businessBalanceFlows);
        return R.ok(PageUtils.getDataTable(businessBalanceFlowVOS, businessBalanceFlows));
    }

    /**
     * 导出商家余额流水列表（运营端）
     */
    @PostMapping("/backend/export/businessBalanceFlowList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "导出商家余额流水列表（运营端）")
    @RequiresPermissions("merchant:balance:info-export")
    public void exportBusinessBalanceFlowList(BusinessBalanceFlowListDTO dto, HttpServletResponse response) {
        List<BusinessBalanceFlow> businessBalanceFlows = businessBalanceFlowService.businessBalanceFlowList(dto);
        List<BusinessBalanceFlowExportVO> businessBalanceFlowVOS = BeanUtil.copyToList(businessBalanceFlows, BusinessBalanceFlowExportVO.class);
        ExcelUtil<BusinessBalanceFlowExportVO> util = new ExcelUtil<>(BusinessBalanceFlowExportVO.class);
        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "商家余额流水列表导出");
        util.exportExcel(response, businessBalanceFlowVOS, "商家余额流水列表导出");
    }

    @PostMapping("/backend/businessBalanceFlowDetailList/export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "导出全部余额明细（运营端）")
    @RequiresPermissions("merchant:balance:export-detail")
    public void businessBalanceFlowDetailList(BusinessBalanceFlowDetailListDTO dto, HttpServletResponse response) {

        List<BusinessBalanceDetailFlowExportVO> businessBalanceDetailFlowExports = businessBalanceFlowService.getBusinessBalanceDetailFlowExports(dto);
        ExcelUtil<BusinessBalanceDetailFlowExportVO> util = new ExcelUtil<>(BusinessBalanceDetailFlowExportVO.class);
        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "全部余额明细导出");
        util.exportExcel(response, businessBalanceDetailFlowExports, "全部余额明细导出");
    }

    /**
     * 添加商家余额流水（商家端使用）
     */
    @PostMapping("/addBusinessBalanceFlow")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "添加商家余额流水")
    public R<String> addBusinessBalanceFlow(@RequestBody @Validated BusinessBalanceFlowDTO dto) {
        businessBalanceFlowService.addBusinessBalanceFlow(dto);
        return R.ok();
    }
}
