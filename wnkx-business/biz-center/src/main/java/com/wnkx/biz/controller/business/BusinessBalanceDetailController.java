package com.wnkx.biz.controller.business;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetail;
import com.ruoyi.system.api.domain.vo.biz.business.balance.PayoutBusinessBalanceDetailVO;
import com.wnkx.biz.business.service.IBusinessBalanceDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :商家余额详情
 * @create :2024-12-25 17:55
 **/
@RestController
@RequestMapping("/business/balance/detail")
@Api(value = "商家余额详情" , tags = "商家余额业务")
@RequiredArgsConstructor
public class BusinessBalanceDetailController extends BaseController {

    private final IBusinessBalanceDetailService businessBalanceDetailService;

    @GetMapping("/list/{businessId}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "根据商家ID获取商家可用余额详情数据" , response = PageInfo.class)
    public R<List<PayoutBusinessBalanceDetailVO>> getBusinessBalanceDetailListByBusinessId(@PathVariable("businessId") Long id) {
        List<BusinessBalanceDetail> businessBalanceDetails = businessBalanceDetailService.getValidBusinessBalanceDetailListByBusinessId(id);
        return R.ok(businessBalanceDetailService.getBusinessBalanceDetailVOList(businessBalanceDetails, StatusTypeEnum.YES.getCode()));
    }
}
