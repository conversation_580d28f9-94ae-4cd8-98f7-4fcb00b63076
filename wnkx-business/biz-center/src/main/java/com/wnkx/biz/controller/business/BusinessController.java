package com.wnkx.biz.controller.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.ExcelExp;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.MemberTypeEnum;
import com.ruoyi.common.core.enums.MemberValidTypeEnum;
import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.dto.BusinessBalanceDTO;
import com.ruoyi.system.api.domain.dto.FlowMemberDto;
import com.ruoyi.system.api.domain.dto.biz.business.*;
import com.ruoyi.system.api.domain.dto.biz.business.account.BindingAccountDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.CheckPhoneDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.UnBindOwnerDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.UnbindAccountDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.*;
import com.ruoyi.system.api.domain.dto.biz.business.balance.*;
import com.ruoyi.system.api.domain.dto.order.WithdrawDepositRecordDTO;
import com.ruoyi.system.api.domain.dto.order.casus.BalancePayOutDTO;
import com.ruoyi.system.api.domain.entity.biz.business.*;
import com.ruoyi.system.api.domain.vo.MemberConfigVo;
import com.ruoyi.system.api.domain.vo.biz.business.*;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailLockInfoVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.WithdrawDepositRecordVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserListVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.SaveBizUserVO;
import com.ruoyi.system.api.domain.vo.order.BusinessRemarkFlowVO;
import com.ruoyi.system.api.domain.vo.order.WorkbenchVO;
import com.wnkx.biz.business.service.IBusinessAccountService;
import com.wnkx.biz.business.service.IBusinessBalanceService;
import com.wnkx.biz.business.service.IBusinessMemberValidityFlowService;
import com.wnkx.biz.business.service.IBusinessService;
import com.wnkx.biz.channel.service.IBizUserChannelService;
import com.wnkx.biz.core.SystemCore;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :商家控制层
 * @create :2024-06-24 09:10
 **/
@RestController
@RequestMapping("/business")
@Api(value = "商家业务", tags = "商家业务")
@RequiredArgsConstructor
public class BusinessController extends BaseController {
    private final IBusinessAccountService businessAccountService;
    private final IBusinessService businessService;
    private final SystemCore systemCore;
    private final IBusinessMemberValidityFlowService businessMemberValidityFlowService;
    private final IBizUserChannelService bizUserChannelService;
    private final IBusinessBalanceService businessBalanceService;

    @PostMapping("/getBusinessVo")
    @ApiOperation("获取商家信息")
    @InnerAuth
    public R<BusinessVO> getBusinessVo(@RequestBody BusinessDTO dto) {
        return R.ok(businessService.queryBusinessVo(dto));
    }
    @PostMapping("/getFinanceWorkbenchVo")
    @ApiOperation("获取工作台-财务部 提现待审批、分销待结算")
    @InnerAuth
    public R<WorkbenchVO> getFinanceWorkbenchVo() {
        return R.ok(businessService.getFinanceWorkbenchVo());
    }

    @ApiOperation("修改商家余额")
    @InnerAuth
    @PostMapping("/backend/updateBusinessBalance")
    public R<String> updateBusinessBalance(@RequestBody @Validated BusinessBalanceDTO dto) {
        businessBalanceService.updateBusinessBalance(dto);
        return R.ok("成功");
    }

    @ApiOperation("保存商家有效流水")
    @InnerAuth
    @PostMapping("/saveBusinessMemberValidityFlow")
    public R<String> saveBusinessMemberValidityFlow(@RequestBody @Validated BatchBusinessMemberValidityFlowDTO dto) {
        businessMemberValidityFlowService.remove(new LambdaQueryWrapper<BusinessMemberValidityFlow>().eq(BusinessMemberValidityFlow::getType, MemberValidTypeEnum.USER.getCode()));
        businessMemberValidityFlowService.saveBatch(dto.getList());
        return R.ok("成功");
    }

    @PostMapping("/getBusinessAccountOne")
    @InnerAuth
    @ApiOperation("根据获取用户信息")
    public R<BusinessAccountVO> getBusinessAccountOne(@RequestBody BusinessAccountDTO dto) {
        BusinessAccountVO businessAccount = businessAccountService.getBusinessAccountOne(dto);
        if (ObjectUtil.isNotNull(businessAccount)) {
            businessAccount.setUnionid("");
            businessAccount.setExternalUserId("");
            businessAccount.setBusinessAccountVOS(new ArrayList<>());
        }
        return R.ok(businessAccount);
    }

    @GetMapping("/getBusinessMemberActivity")
    @InnerAuth
    @ApiOperation("获取会员活动")
    public R<BusinessMemberActivity> getBusinessMemberActivity(Integer packageType) {
        return R.ok(businessAccountService.getBusinessMemberActivity(packageType));
    }

    @DeleteMapping("/deleteBusiness/{id}")
    @InnerAuth
    @ApiOperation("删除商家数据")
    public R<String> deleteBusiness(@PathVariable("id") Long id) {
        businessAccountService.deleteBusiness(id);
        return R.ok();
    }

    @PostMapping("/getBizUserDetailList")
    @ApiOperation("获取无分页登录账号信息")
    @InnerAuth
    public R<List<BizUserDetailVO>> getBizUserDetailList(@RequestBody BizUserDetailListDTO dto) {
        return R.ok(businessAccountService.bizUserDetailList(dto));
    }

    @PostMapping("/getBizUserInfo")
    @ApiOperation("获取用户信息")
    public R<List<BusinessAccountDetailVO>> getBizUserInfo(@RequestBody BusinessAccountDetailDTO dto) {
        return R.ok(businessAccountService.getBizUserInfo(dto));
    }

    /**
     * 财务管理-财务对账-入驻会员 开始-----------------------
     */

    @ApiOperation(value = "入驻会员（运营端使用）", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @GetMapping("/backend/residentBusinessList")
    @RequiresPermissions("finance:enter:list")
    public R<PageInfo<ResidentBusinessVO>> residentBusinessList(ResidentBusinessDTO dto) {
        return R.ok(toPage(businessAccountService.queryResidentBusiness(dto)));
    }

    @ApiOperation(value = "入驻会员导出（运营端使用）")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @PostMapping("/backend/residentBusinessList/export")
    @RequiresPermissions("finance:residentBusiness:list:export")
    public void exportResidentBusinessList(ResidentBusinessDTO dto, HttpServletResponse response) {
        List<ResidentBusinessExportVO> residentBusinessExportVOList = businessAccountService.exportResidentBusinessList(dto);
        List<BusinessMemberValidityFlowExportVO> businessMemberValidityFlowExportVOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(residentBusinessExportVOList)) {
            List<BusinessMemberValidityFlowExportVO> businessMemberValidFlowExportList = businessAccountService.getBusinessMemberValidFlowExportList(BusinessMemberValidityFlowDTO.builder()
                    .businessIds(residentBusinessExportVOList.stream().map(ResidentBusinessExportVO::getId).filter(ObjectUtil::isNotNull).collect(Collectors.toList())).build());
            if (CollUtil.isNotEmpty(businessMemberValidFlowExportList)) {
                businessMemberValidityFlowExportVOS.addAll(businessMemberValidFlowExportList);
            }
        }

        ExcelExp exp = new ExcelExp("入驻会员导出", residentBusinessExportVOList, ResidentBusinessExportVO.class);
        ExcelExp exp1 = new ExcelExp("商家会员有效期修改流水导出", businessMemberValidityFlowExportVOS, BusinessMemberValidityFlowExportVO.class);
        List<ExcelExp> mysheet = new ArrayList<ExcelExp>();
        mysheet.add(exp);
        mysheet.add(exp1);
        ExcelUtil.setAttachmentResponseHeader(response, "入驻会员导出");
        ExcelUtil<List<ExcelExp>> util = new ExcelUtil<List<ExcelExp>>(mysheet);
        // 设置响应头信息
        util.exportExcelManySheet(response, mysheet);
    }

    @ApiOperation(value = "会员有效期记录（运营端使用）", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("finance:residentBusiness:businessMemberValidityFlowList")
    @GetMapping("/backend/businessMemberFlowList")
    public R<PageInfo<BusinessMemberValidityFlowVO>> getBusinessMemberFlowList(BusinessMemberValidityFlowDTO dto) {
        return R.ok(toPage(businessAccountService.getBusinessMemberValidFlowList(dto)));
    }

    @ApiOperation(value = "会员有效期关联订单（运营端使用）")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("finance:residentBusiness:businessMemberValidityFlowList")
    @GetMapping("/backend/getRelationOrder")
    public R<BusinessMemberValidityFlowVO> getRelationOrder(@RequestParam("businessId") Long businessId) {
        return R.ok(businessAccountService.getRelationOrder(businessId));
    }

    @ApiOperation(value = "会员有效期记录导出（运营端使用）", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("finance:residentBusiness:businessMemberValidityFlowListExport")
    @PostMapping("/backend/businessMemberFlowList/export")
    public void exportBusinessMemberFlowList(BusinessMemberValidityFlowDTO dto, HttpServletResponse response) {
        List<BusinessMemberValidityFlowVO> businessMemberValidFlowList = businessAccountService.getBusinessMemberValidFlowList(dto);
        List<BusinessMemberValidityFlowExportVO> businessMemberValidityFlowExportVOS = BeanUtil.copyToList(businessMemberValidFlowList, BusinessMemberValidityFlowExportVO.class);
        ExcelUtil<BusinessMemberValidityFlowExportVO> util = new ExcelUtil<>(BusinessMemberValidityFlowExportVO.class);
        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "会员有效期记录导出");
        util.exportExcel(response, businessMemberValidityFlowExportVOS, "会员有效期记录导出");
    }
    /**
     * 财务管理-财务对账-入驻会员 结束-----------------------
     */


    /**
     * 用户管理 - 商家列表 开始-----------------------------------
     */
    @ApiOperation(value = "获取商家统计（运营端使用）", response = BusinessStatisticsVO.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @GetMapping("/backend/businessStatistics")
    @RequiresPermissions("merchant:manage:list")
    public R<BusinessStatisticsVO> businessStatistics() {
        return R.ok(businessAccountService.businessStatistics());
    }

    /**
     * 获取商家列表（运营端使用）
     */
    @ApiOperation(value = "获取商家列表（运营端使用）", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @GetMapping("/backend/businessList")
    @RequiresPermissions("merchant:manage:list")
    public R<PageInfo<BusinessDetailVO>> businessList(BusinessDTO dto) {
        List<BusinessDetailVO> businessDetailVOS = businessService.queryBusinessList(dto);
        businessAccountService.loadBusinessVODetail(businessDetailVOS);
        return R.ok(toPage(businessDetailVOS));
    }

    @ApiOperation(value = "获取商家列表导出（运营端使用）")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @PostMapping("/backend/businessList/export")
    @RequiresPermissions("merchant:manage:export")
    public void businessListExport(BusinessDTO dto, HttpServletResponse response) {
        List<BusinessDetailVO> businessDetailVOS = businessService.queryBusinessList(dto);
        businessAccountService.loadBusinessVODetail(businessDetailVOS);
        List<BusinessDetailExportVO> businessDetailExportVOS = businessAccountService.businessListExport(businessDetailVOS);
        if (CollUtil.isNotEmpty(businessDetailExportVOS)) {
            businessDetailExportVOS.sort(Comparator.comparing(BusinessDetailExportVO::getMemberLastTime));
        }
        ExcelUtil<BusinessDetailExportVO> util = new ExcelUtil<>(BusinessDetailExportVO.class);
        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "商家列表导出");
        util.exportExcel(response, businessDetailExportVOS, "商家列表导出");
    }


    /**
     * 编辑商家数据（运营端使用）
     *
     * @param dto
     */
    @ApiOperation("编辑商家数据（运营端使用）")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:manage:edit")
    @PutMapping("/backend/updateBusiness")
    public R<String> updateBusiness(@RequestBody @Validated EditBusinessDTO dto) {
        businessAccountService.updateBusiness(dto);
        return R.ok();
    }

    /**
     * 修改会员时间（运营端使用）
     *
     * @param dto
     */
    @ApiOperation("修改会员时间（运营端使用）")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:manage:updateMemberValidity")
    @PutMapping("/backend/updateMemberValidity")
    public R<String> updateMemberValidity(@RequestBody @Validated EditMemberValidityDTO dto) {
        businessAccountService.updateMemberValidity(dto);
        return R.ok();
    }

    /**
     * @param businessId
     * @return
     */
    @ApiOperation("检查是否可7天无理由退款")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @GetMapping("/backend/checkUnableSettlement/{businessId}")
    public R<CheckUnableSettlementVO> checkUnableSettlement(@PathVariable("businessId") Long businessId) {
        return R.ok(businessAccountService.checkUnableSettlement(businessId, false));
    }

    /**
     * 获取商家有效期流水记录（运营端使用）
     *
     * @param businessId
     */
    @ApiOperation(value = "获取商家有效期流水记录（运营端使用）", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:manage:updateMemberValidity")
    @GetMapping("/backend/businessMemberValidityFlowList")
    public R<PageInfo<BusinessMemberValidityFlowVO>> getBusinessMemberValidityFlowListByBusinessId(Long businessId) {
        return R.ok(toPage(businessAccountService.getBusinessMemberValidityFlowListByBusinessId(businessId)));
    }

    @ApiOperation(value = "获取商家子账号列表", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @GetMapping("/backend/queryBusinessAccountListByBusinessId/{businessId}")
    public R<PageInfo<BusinessAccountVO>> queryBusinessAccountListByBusinessId(@PathVariable("businessId") Long businessId) {
        BusinessAccountDTO dto = new BusinessAccountDTO();
        dto.setBusinessId(businessId);
        dto.setIsOwnerAccount(StatusTypeEnum.NO.getCode());
        dto.setUserStatus(StatusEnum.ENABLED.getCode());
        return R.ok(toPage(businessAccountService.queryList(dto)));
    }

    @ApiOperation(value = "获取商家主账号换绑记录列表（运营端使用）", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @GetMapping("/backend/getOwnerFlowListByBusinessId")
    public R<PageInfo<BusinessOwnerFlowVO>> getOwnerFlowListByBusinessId(Long businessId) {
        return R.ok(toPage(businessAccountService.getOwnerFlowListByBusinessId(businessId)));
    }

    @ApiOperation(value = "换绑主账号（运营端使用）", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @PutMapping("/backend/exchangeBindOwner")
    public R<String> exchangeBindOwner(@RequestBody @Validated UnBindOwnerDTO dto) {
        businessAccountService.exchangeBindOwner(dto);
        return R.ok();
    }

    /**
     * 修改商家客服（运营端使用）
     *
     * @param dto
     */
    @ApiOperation("修改商家客服")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:manage:changeConectUser")
    @PutMapping("/backend/updateWaiter")
    public R<String> updateWaiter(@RequestBody @Validated UpdateWaiterDTO dto) {
        businessAccountService.updateWaiter(dto);
        return R.ok();
    }

    /**
     * 商家状态修改（运营端使用）
     *
     * @param dto
     */
    @ApiOperation("商家状态修改")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:manage:status")
    @PutMapping("/backend/updateBusinessStatus")
    public R<String> updateBusinessStatus(@RequestBody BusinessStatusDTO dto) {
        businessAccountService.updateBusinessStatus(dto);
        return R.ok();
    }

    @ApiOperation("商家备注修改")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @PutMapping("/backend/updateBusinessRemark")
    @RequiresPermissions("merchant:manage:remark")
    public R<String> updateBusinessRemark(@RequestBody @Validated BusinessRemarkDTO dto) {
        businessAccountService.updateBusinessRemark(dto);
        return R.ok();
    }

    @ApiOperation(value = "获取商家备注流水（运营端使用）")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @GetMapping("/backend/businessRemarkFlowList")
    @RequiresPermissions("merchant:manage:remark")
    public R<List<BusinessRemarkFlowVO>> businessRemarkFlowList(Long businessId) {
        return R.ok(businessAccountService.getListByBusinessId(businessId));
    }

    /**
     * 用户管理 - 商家列表 结束-----------------------------------
     */


    /**
     * 用户管理 - 商家余额 开始-----------------------------------
     */

    @ApiOperation(value = "获取商家余额列表（运营端使用）", response = BusinessStatisticsVO.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:balance:list")
    @GetMapping("/backend/businessBalanceList")
    public R<PageInfo<BusinessBalanceVO>> businessBalanceList(BusinessDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("balance", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        dto.setMemberStatusList(List.of(
                MemberTypeEnum.RECHARGE.getCode()
                , MemberTypeEnum.EXPIRE.getCode()
                , MemberTypeEnum.NO_EXPIRE.getCode())
        );
        List<Business> businesses = businessService.queryList(dto);
        List<BusinessBalanceVO> businessBalanceVOS = businessAccountService.loadBusinessAccount(businesses);
        return R.ok(PageUtils.getDataTable(businessBalanceVOS, businesses));
    }

    @GetMapping("/backend/getBusinessBalanceDetailVo")
    @ApiOperation("获取商家余额信息(运营端)")
    @RequiresPermissions("merchant:balance:details")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    public R<BusinessBalanceInfoVO> getBackendBusinessBalanceDetailVo(@ApiParam("商家ID") @RequestParam Long businessId) {
        BusinessDTO dto = new BusinessDTO();
        dto.setId(businessId);
        return R.ok(businessService.getBusinessBalanceDetailVo(dto));
    }

    @ApiOperation(value = "获取商家余额统计（运营端使用）", response = BusinessStatisticsVO.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:balance:list")
    @GetMapping("/backend/businessBalanceTotal")
    public R<BigDecimal> businessBalanceTotal() {
        return R.ok(businessAccountService.businessBalanceTotal());
    }

    @ApiOperation(value = "导出商家余额列表（运营端使用）")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:balance:export")
    @PostMapping("/backend/export/businessBalanceList")
    public void exportBusinessBalanceList(BusinessDTO dto, HttpServletResponse response) {
        dto.setMemberStatusList(List.of(
                MemberTypeEnum.RECHARGE.getCode()
                , MemberTypeEnum.EXPIRE.getCode()
                , MemberTypeEnum.NO_EXPIRE.getCode())
        );
        List<BusinessBalanceVO> businessBalanceVOS = businessAccountService.businessBalanceList(dto);

        List<BusinessBalanceExportVO> businessBalanceExportVOS = businessAccountService.exportBusinessBalanceExportList(businessBalanceVOS);
        ExcelUtil<BusinessBalanceExportVO> util = new ExcelUtil<>(BusinessBalanceExportVO.class);
        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "商家余额导出");
        util.exportExcel(response, businessBalanceExportVOS, "商家余额导出");
    }

    @ApiOperation("发起提现")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:balance:withdraw")
    @PostMapping("/backend/balancePayOut")
    public R<String> balancePayOut(@RequestBody @Validated BalancePayOutDTO dto) {
        businessAccountService.balancePayOut(dto);
        return R.ok("成功");
    }

    /**
     * 获取视频订单提现记录
     */
    @ApiOperation(value = "获取视频订单提现记录")
    @PostMapping("/withdraw-deposit-record")
    @InnerAuth
    public R<List<WithdrawDepositRecordVO>> withdrawDepositRecord(@RequestBody WithdrawDepositRecordDTO dto) {
        List<WithdrawDepositRecordVO> list = businessAccountService.withdrawDepositRecord(dto);
        return R.ok(list);
    }


    /**
     * 用户管理 - 商家余额 结束-----------------------------------
     */


    /**
     * 用户管理 - 账号列表 开始-----------------------------------
     */
    @ApiOperation(value = "获取登录账号列表", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:account:list")
    @GetMapping("/backend/bizUserList")
    public R<PageInfo<BizUserListVO>> bizUserList(BizUserListDTO dto) {
        return R.ok(toPage(businessAccountService.bizUserList(dto)));
    }

    @ApiOperation("修改登录账号")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:account:edit")
    @PutMapping("/backend/editBizUser")
    public R<String> editBizUser(@RequestBody @Validated BizUserEditDTO dto) {
        businessAccountService.editBizUser(dto);
        return R.ok();
    }

    @ApiOperation("修改登录账号状态")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:account:status")
    @PutMapping("/backend/editBizUserStatus")
    public R<Void> editBizUserStatus(@RequestBody BizUserEditStatusDTO dto) {
        businessAccountService.editBizUserStatus(dto);
        return R.ok();
    }

    @ApiOperation("修改手机号")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:account:edit:phone")
    @PutMapping("/backend/editBizUserPhone")
    public R<String> editBizUserPhone(@RequestBody @Validated BizUserEditPhoneDTO dto) {
        businessAccountService.editBizUserPhone(dto);
        return R.ok();
    }

    @ApiOperation("新增登录账号")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:account:add")
    @PostMapping("/backend/saveBizUser")
    public R<SaveBizUserVO> saveBizUser(@RequestBody @Validated BizUserSaveDTO dto) {
        return R.ok(businessAccountService.saveBizUser(dto));
    }

    /**
     * 用户管理 - 账号列表 结束-----------------------------------
     */


    @GetMapping("/getBusinessBalanceDetailVo")
    @ApiOperation("获取商家余额信息(商家端)")
    @LoginUserType(userTypes = {UserTypeConstants.USER_TYPE})
    public R<BusinessBalanceInfoVO> getBusinessBalanceDetailVo() {
        BusinessDTO dto = new BusinessDTO();
        dto.setId(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId());
        return R.ok(businessService.getBusinessBalanceDetailVo(dto));
    }

    @PutMapping("/updateInvoice")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isOwnerAccount = true)
    @ApiOperation("发票信息管理(商家端)")
    public R<String> updateInvoice(@Validated @RequestBody BusinessInvoiceDTO dto) {
        businessAccountService.updateInvoice(dto);
        return R.ok();
    }

    @ApiOperation("账号状态变更（商家端使用）-弃用")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE, isOwnerAccount = true)
    @PutMapping("/updateBusinessAccountStatus")
    public R<String> updateBusinessAccountStatus(@RequestBody @Validated BusinessAccountStatusDTO dto) {
        businessAccountService.updateBusinessAccountStatus(dto);
        return R.ok();
    }

    @ApiOperation("检查扫码用户是否正确")
    @GetMapping("/checkAccount")
    public R<String> checkAccount(@RequestParam(value = "ticket") String ticket,
                                  @RequestParam(value = "account") String account
    ) {
        try {
            businessAccountService.checkAccount(ticket, account);
        } catch (IllegalArgumentException e) {
            return R.ok(e.getMessage());
        }
        return R.ok();
    }


    @ApiOperation("余额提现审核")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @PostMapping("/backend/businessBalanceAuditFlow/auditPayOut")
    @RequiresPermissions(value = {"withdraw:deposit:cancel", "withdraw:deposit:confirm"}, logical = Logical.OR)
    public R<String> auditPayOut(@RequestBody @Validated BusinessBalanceAuditFlowAuditDTO dto) {
        businessAccountService.auditPayOut(dto);
        return R.ok("成功");
    }

    @ApiOperation("预付款审核")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @PostMapping("/backend/businessBalancePrepay/audit")
    @RequiresPermissions(value = {
            "finance:prepay:audit",
            "finance:prepay:reaudit"
    }, logical = Logical.OR)
    public R<String> auditBusinessBalancePrepay(@RequestBody @Validated AuditBusinessBalancePrepayDTO dto) {
        businessBalanceService.auditBusinessBalancePrepay(dto);
        return R.ok("成功");
    }

    @ApiOperation("会员状态流转")
    @PostMapping("/flowMember")
    public R<BusinessAccountVO> flowMember(@RequestBody @Validated FlowMemberDto dto) {
        return R.ok(businessAccountService.flowMember(dto));
    }

    @ApiOperation("有效余额提现审核数据")
    @PostMapping("/queryValidBalanceAuditFlowList")
    @InnerAuth
    public R<List<BusinessBalanceAuditFlow>> queryValidBalanceAuditFlowList(@RequestBody BusinessBalanceAuditFlowValidListDTO dto) {
        return R.ok(businessAccountService.queryValidBalanceAuditFlowList(dto));
    }

    @ApiOperation("会员配置")
    @GetMapping("/getMemberConfig")
    public R<MemberConfigVo> getMemberConfig() {
        return R.ok(systemCore.getMemberConfig());
    }

    @ApiOperation("解绑账号")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isOwnerAccount = true)
    @PostMapping("/unbindLoginUser")
    public R<String> unbindLoginUser(@RequestBody @Validated UnbindAccountDTO dto) {
        businessAccountService.unbindLoginUser(dto);
        return R.ok();
    }

    @ApiOperation("检查手机号（绑定使用）")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isOwnerAccount = true)
    @PostMapping("/checkPhoneStatus")
    public R<CheckPhoneVO> checkPhoneStatus(@RequestBody @Validated CheckPhoneDTO dto) {
        return R.ok(businessAccountService.checkPhoneStatus(dto));
    }

    @ApiOperation("绑定账号")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isOwnerAccount = true)
    @PostMapping("/bindingLoginUser")
    public R<String> bindingLoginUser(@RequestBody @Validated BindingAccountDTO dto) {
        businessAccountService.bindingLoginUser(dto);
        return R.ok();
    }

    /**
     * 换绑微信
     *
     * @param dto
     */
    @Deprecated(since = "2025-01-16", forRemoval = true)
    @ApiOperation("换绑微信")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @PutMapping("/rebind")
    public R rebind(@RequestBody @Validated RebindDTO dto) {
//        businessAccountService.rebind(dto);
        return R.ok();
    }

    @PutMapping("/resetBusinessName")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isOwnerAccount = true)
    @ApiOperation("修改商家名称（商家端使用）")
    public R<String> resetBusinessName(@Validated @RequestBody ResetBusinessNameDTO dto) {
        businessAccountService.resetBusinessName(dto);
        return R.ok();
    }

    @PutMapping("/resetBusinessScale")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isOwnerAccount = true)
    @ApiOperation("修改商家规模（商家端使用）")
    public R<String> resetBusinessScale(@Validated @RequestBody ResetBusinessScaleDTO dto) {
        businessAccountService.resetBusinessScale(dto);
        return R.ok();
    }

    @PutMapping("/initBusinessInfo")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isOwnerAccount = true)
    @ApiOperation("编辑商家数据（商家端使用）")
    public R<String> initBusinessInfo(@Validated @RequestBody InitBusinessInfoDTO dto) {
        businessAccountService.initBusinessInfo(dto);
        return R.ok();
    }

    @PostMapping("/getBusinessAccountDetailVOs")
    @ApiOperation("获取无分页用户信息")
    public R<List<BusinessAccountDetailVO>> getBusinessAccountDetailVOs(@RequestBody BusinessAccountDetailDTO dto) {
        return R.ok(businessAccountService.getBusinessAccountDetailVOs(dto));
    }

    @ApiOperation(value = "获取下单用户列表", response = BusinessAccount.class)
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @GetMapping("/businessAccountList")
    public R<PageInfo<OrderUserVO>> businessAccountList(BusinessAccountDTO dto) {
        List<BusinessAccountVO> businessAccounts = businessAccountService.orderUserList(dto);
        return R.ok(PageUtils.getDataTable(BeanUtil.copyToList(businessAccounts, OrderUserVO.class), businessAccounts));
    }

    @ApiOperation(value = "商务经理列表")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @GetMapping("/backend/businessManagerList")
    public R<List<String>> businessManagerList(Integer type) {
        return R.ok(businessAccountService.businessManagerList(type));
    }

    @ApiOperation("获取有效锁定数据")
    @PostMapping("/queryValidLockList")
    @InnerAuth
    public R<List<BusinessBalanceDetailLockInfoVO>> queryValidLockList(@RequestBody BusinessBalanceDetailLockInfoDTO dto) {
        return R.ok(businessAccountService.queryValidLockList(dto));
    }

    @GetMapping("/getSubAccountList/{businessId}")
    @ApiOperation("获取子账号列表")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    public R<PageInfo<BusinessSubAccountListVo>> getSubAccountList(@PathVariable Long businessId) {
        List<BusinessSubAccountListVo> list = businessAccountService.getSubAccountList(businessId);
        return R.ok(PageUtils.getDataTable(BeanUtil.copyToList(list, BusinessSubAccountListVo.class), list));
    }

    @ApiOperation("获取用户渠道")
    @PostMapping("/getUserChannel")
    @InnerAuth
    public R<List<BizUserDetailVO>> getUserChannel(@RequestBody @Validated BizUserDetailListDTO dto) {
        return R.ok(bizUserChannelService.getUserChannel(dto));
    }

    /**
     * 商家排单时 更新最近排单时间 以及删除未排单事件
     */
    @PostMapping("/update-recent-order-time")
    @InnerAuth
    public R<Boolean> updateRecentOrderTime(@RequestParam Long businessId) {
        businessService.updateRecentOrderTime(businessId);
        return R.ok(Boolean.TRUE);
    }
    /**
     * 商家提交凭证、微信、支付宝支付成功
     */
    @PostMapping("/update-pay-succeed")
    @InnerAuth
    public R<Boolean> updatePaySucceed(@RequestParam Long businessId) {
        businessService.updatePaySucceed(businessId);
        return R.ok(Boolean.TRUE);
    }


    @PostMapping("/inner/businessBalancePrepay/updateOrderPayStatus")
    @ApiOperation(value = "支付成功更新状态（内部使用）")
    @InnerAuth
    public R<BusinessBalancePrepay> innerUpdateOrderPayStatus(@RequestBody @Valid PrepayUpdatePayStatusDTO dto) {
        return R.ok(businessBalanceService.updateOrderPayStatus(dto));
    }

    /**
     * 查询商家信息
     */
    @GetMapping("/inner/select-business-list")
    @InnerAuth
    public R<List<Business>> selectBusinessList() {
        List<Business> businesses = businessService.queryList(new BusinessDTO());
        return R.ok(businesses);
    }

    /**
     * 根据裂变种草id获取用户信息
     */
    @PostMapping("/inner/userMemberStatusBySeedId")
    @InnerAuth
    public R<List<BusinessAccountDetailVO>> userMemberStatusBySeedId(@RequestBody Collection<String> seedId) {
        List<BusinessAccountDetailVO> result = businessAccountService.getUserMemberStatusBySeedId(seedId);
        return R.ok(result);
    }
}
