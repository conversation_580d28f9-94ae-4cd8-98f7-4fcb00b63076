package com.wnkx.biz.controller.common;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.entity.biz.common.BizResource;
import com.wnkx.biz.model.service.BizResourceService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/14 9:38
 */
@RestController
@RequestMapping("/biz-resource")
@Api(value = "业务资源表服务", tags = "业务资源表服务")
@RequiredArgsConstructor
public class BizResourceController {
    private final BizResourceService bizResourceService;


    /**
     * 新增图片资源
     *
     * @param bizResources 图片资源
     */
    @PostMapping("/insert-resource")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @InnerAuth
    public R<List<BizResource>> saveBatchBizResource(@RequestBody List<BizResource> bizResources) {
        return R.ok(bizResourceService.saveBatchBizResource(bizResources));
    }
}
