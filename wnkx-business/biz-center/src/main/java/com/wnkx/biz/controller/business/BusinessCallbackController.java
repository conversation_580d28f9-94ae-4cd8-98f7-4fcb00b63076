package com.wnkx.biz.controller.business;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.BusinessCallbackStatusEnum;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessCallbackListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.ReturnVisitRecordDTO;
import com.ruoyi.system.api.domain.dto.biz.business.WriteReturnVisitDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessCallbackRecord;
import com.ruoyi.system.api.domain.vo.biz.business.*;
import com.wnkx.biz.business.service.BusinessCallbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12 17:58
 */
@RestController
@RequestMapping("/business-callback")
@Api(value = "商家回访业务", tags = "商家回访业务")
@RequiredArgsConstructor
public class BusinessCallbackController extends BaseController {


    private final BusinessCallbackService businessCallbackService;

    /**
     * 商家回访-待回访列表
     */
    @GetMapping("/wait-for-return-visit")
    @ApiOperation("商家回访-待回访列表")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<PageInfo<BusinessCallbackListVO>> selectWaitForReturnVisitListByCondition(BusinessCallbackListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("bce.callback_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("bc.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        dto.setStatus(BusinessCallbackStatusEnum.WAIT_FOR_RETURN_VISIT.getCode());
        List<BusinessCallbackListVO> businessCallbackListVOS = businessCallbackService.selectCallbackListByCondition(dto);
        return R.ok(toPage(businessCallbackListVOS));
    }

    /**
     * 商家回访-回访中列表
     */
    @GetMapping("/in-the-return-visit")
    @ApiOperation("商家回访-回访中列表")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<PageInfo<BusinessCallbackListVO>> selectInTheReturnVisitListByCondition(BusinessCallbackListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("bc.mark_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("bc.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        dto.setStatus(BusinessCallbackStatusEnum.IN_THE_RETURN_VISIT.getCode());
        List<BusinessCallbackListVO> businessCallbackListVOS = businessCallbackService.selectCallbackListByCondition(dto);
        return R.ok(toPage(businessCallbackListVOS));
    }

    /**
     * 商家回访-已回访列表
     */
    @GetMapping("/already-visited")
    @ApiOperation("商家回访-已回访列表")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<PageInfo<BusinessCallbackListVO>> selectAlreadyVisitedListByCondition(BusinessCallbackListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("bc.write_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("bc.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        dto.setStatus(BusinessCallbackStatusEnum.ALREADY_VISITED.getCode());
        List<BusinessCallbackListVO> businessCallbackListVOS = businessCallbackService.selectCallbackListByCondition(dto);
        return R.ok(toPage(businessCallbackListVOS));
    }

    /**
     * 商家回访-回访详情
     */
    @GetMapping("/callback-detail")
    @ApiOperation("商家回访-回访详情")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<BusinessCallbackVO> getCallbackDetailById(@ApiParam("回访ID") @RequestParam Long id) {
        BusinessCallbackVO businessCallbackVO = businessCallbackService.getCallbackDetailById(id);
        return R.ok(businessCallbackVO);
    }

    /**
     * 商家回访-标记回访
     */
    @PostMapping("/mark-return-visit")
    @ApiOperation("商家回访-标记回访")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:returnVisit:markers")
    public R<Void> markReturnVisit(@ApiParam("待回访列表主键ID") @RequestParam Long id) {
        businessCallbackService.markReturnVisit(id);
        return R.ok();
    }

    /**
     * 商家回访-填写回访记录
     */
    @PostMapping("/write-return-visit")
    @ApiOperation("商家回访-填写回访记录")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:returnVisit:edit")
    public R<Void> writeReturnVisit(@RequestBody WriteReturnVisitDTO dto) {
        businessCallbackService.writeReturnVisit(dto);
        return R.ok();
    }

    /**
     * 商家回访-填写回访记录-回访账号下拉框
     */
    @GetMapping("/write-return-visit-account")
    @ApiOperation("商家回访-填写回访记录-回访账号下拉框")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:returnVisit:edit")
    public R<List<BusinessAccountVO>> writeReturnVisitAccount(Long businessId) {
        List<BusinessAccountVO> businessAccountVOS = businessCallbackService.writeReturnVisitAccount(businessId);
        return R.ok(businessAccountVOS);
    }

    /**
     * 商家回访-回访记录-回访账号下拉框
     */
    @GetMapping("/return-visit-account")
    @ApiOperation("商家回访-回访记录-回访账号下拉框")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:returnVisit:record")
    public R<List<BusinessCallbackRecord>> returnVisitAccount(@ApiParam("商家ID") @RequestParam Long businessId) {
        List<BusinessCallbackRecord> businessCallbackRecords = businessCallbackService.returnVisitAccount(businessId);
        return R.ok(businessCallbackRecords);
    }

    /**
     * 商家回访-回访记录
     */
    @GetMapping("/return-visit-record")
    @ApiOperation("商家回访-回访记录")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("merchant:returnVisit:record")
    public R<PageInfo<BusinessCallbackRecordListVO>> returnVisitRecord(@Validated ReturnVisitRecordDTO dto) {
        List<BusinessCallbackRecordListVO> businessCallbackRecordListVOS = businessCallbackService.returnVisitRecord(dto);
        return R.ok(toPage(businessCallbackRecordListVOS));
    }

    /**
     * 商家回访-回访状态数统计
     */
    @GetMapping("/return-visit-status-count")
    @ApiOperation("商家回访-回访状态数统计")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<BusinessCallbackStatusCountVO> returnVisitStatusCount() {
        BusinessCallbackStatusCountVO businessCallbackStatusCountVO = businessCallbackService.returnVisitStatusCount();
        return R.ok(businessCallbackStatusCountVO);
    }
}
