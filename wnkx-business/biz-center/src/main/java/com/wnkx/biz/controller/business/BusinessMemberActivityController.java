package com.wnkx.biz.controller.business;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.AccountAuditStatusEnum;
import com.ruoyi.common.core.validated.CommonValidatedGroup;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.business.account.AccountApplyQueryDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.AuditAccountApplyDTO;
import com.ruoyi.system.api.domain.dto.biz.business.activity.BusinessMemberActivityDTO;
import com.ruoyi.system.api.domain.dto.biz.business.activity.UpdateMemberActivityDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberActivity;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountApplyVO;
import com.wnkx.biz.business.service.IBusinessAccountApplyService;
import com.wnkx.biz.business.service.IBusinessMemberActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :商家业务-会员活动
 * @create :2025-01-17 10:55
 **/
@RestController
@RequestMapping("/business/member/activity")
@Api(value = "商家业务-会员活动" , tags = "商家业务-会员活动")
@RequiredArgsConstructor
public class BusinessMemberActivityController extends BaseController {
    private final IBusinessMemberActivityService businessMemberActivityService;

    @GetMapping("/backend/list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "会员活动列表" , response = PageInfo.class)
    @RequiresPermissions("channel:memberActivity:list")
    public R<PageInfo<BusinessMemberActivity>> backendList() {
        return R.ok(toPage(businessMemberActivityService.queryList()));
    }
    @GetMapping("/info/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "会员活动详情")
    @RequiresPermissions("channel:memberActivity:view")
    public R<BusinessMemberActivity> info(@PathVariable Long id) {
        return R.ok(businessMemberActivityService.getById(id));
    }

    @PostMapping("/saveMemberActivity")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "保存会员活动")
    @RequiresPermissions("channel:memberActivity:add")
    public R<String> saveMemberActivity(@RequestBody @Validated(CommonValidatedGroup.SaveValidatedGroup.class) BusinessMemberActivityDTO dto) {
        businessMemberActivityService.saveMemberActivity(dto);
        return R.ok();
    }
    @PutMapping("/updateMemberActivity")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "修改会员活动")
    @RequiresPermissions("channel:memberActivity:edit")
    public R<String> updateMemberActivity(@RequestBody @Validated(CommonValidatedGroup.EditValidatedGroup.class) BusinessMemberActivityDTO dto) {
        businessMemberActivityService.updateMemberActivity(dto);
        return R.ok();
    }

    @PutMapping("/updateStatus")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "更新会员活动状态")
    @RequiresPermissions("channel:memberActivity:status")
    public R<String> updateStatus(@RequestBody @Validated UpdateMemberActivityDTO dto) {
        businessMemberActivityService.updateStatus(dto);
        return R.ok();
    }


    @GetMapping("/list")
    @ApiOperation(value = "会员活动列表")
    public R<List<BusinessMemberActivity>> list() {
        return R.ok(businessMemberActivityService.getTodayActivity());
    }

}
