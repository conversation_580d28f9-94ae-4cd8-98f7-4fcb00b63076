package com.wnkx.biz.controller.tag;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.ModelTagEnum;
import com.ruoyi.common.core.validated.CommonValidatedGroup;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.biz.tag.TagDTO;
import com.ruoyi.system.api.domain.dto.biz.tag.TagListDTO;
import com.ruoyi.system.api.domain.dto.biz.tag.TagSortDTO;
import com.ruoyi.system.api.domain.entity.biz.tag.Tag;
import com.ruoyi.system.api.domain.vo.TagListVO;
import com.ruoyi.system.api.domain.vo.biz.tag.TagSimpleVO;
import com.wnkx.biz.tag.service.ITagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * 标签Controller
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/tag")
@Api(value = "标签服务", tags = "标签服务")
@RequiredArgsConstructor
public class TagController extends BaseController {
    private final ITagService tagService;

    /**
     * 查询模特标签和类目标签列表 根据categoryId
     */
    @GetMapping("/list")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "查询模特标签和类目标签列表 根据categoryId", response = TagListVO.class)
    // @RequiresPermissions("tag:tag:list")
    public R<List<TagListVO>> list(@Validated TagListDTO tagListDTO) {
        List<TagListVO> list = tagService.selectTagList(tagListDTO);
        return R.ok(list);
    }

    /**
     * 查询模特标签列表
     */
    @GetMapping("/model-tag-list")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "查询模特标签列表", response = TagListVO.class)
    // @RequiresPermissions("tag:model:list")
    public R<List<TagListVO>> selectModelTagList(TagListDTO tagListDTO) {
        tagListDTO.setCategoryId(ModelTagEnum.TAG.getCode());
        List<TagListVO> list = tagService.selectTagList(tagListDTO);
        return R.ok(list);
    }

    /**
     * 查询类目标签列表
     */
    @GetMapping("/category-tag-list")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "查询类目标签列表", response = TagListVO.class)
    // @RequiresPermissions("tag:category:list")
    public R<List<TagListVO>> selectCategoryTagList(TagListDTO tagListDTO) {
        tagListDTO.setCategoryId(ModelTagEnum.CATEGORY.getCode());
        List<TagListVO> list = tagService.selectTagList(tagListDTO);
        return R.ok(list);
    }

    /**
     * 获取标签详细信息
     */
    @GetMapping(value = "/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取标签详细信息", response = Tag.class)
    @RequiresPermissions(value = {"tag:model:list", "tag:category:list"},logical = Logical.OR)
    public R<Tag> getInfo(@PathVariable("id") Long id) {
        return R.ok(tagService.selectTagById(id));
    }

    /**
     * 新增模特标签
     */
    @Log(title = "标签", businessType = BusinessType.INSERT)
    @PostMapping("add-model-tag")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "新增模特标签")
    @RequiresPermissions(value = {"tag:model:add", "tag:model:sub"},logical = Logical.OR)
    public R<String> addModelTag(@RequestBody @Validated TagDTO tagDTO) {
        tagDTO.setCategoryId(ModelTagEnum.TAG.getCode());
        tagService.insertTag(tagDTO);
        return R.ok();
    }

    /**
     * 新增类目标签
     */
    @Log(title = "标签", businessType = BusinessType.INSERT)
    @PostMapping("add-category-tag")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "新增类目标签")
    @RequiresPermissions(value = {"tag:category:add", "tag:category:sub"},logical = Logical.OR)
    public R<String> addCategoryTag(@RequestBody @Validated TagDTO tagDTO) {
        tagDTO.setCategoryId(ModelTagEnum.CATEGORY.getCode());
        tagService.insertTag(tagDTO);
        return R.ok();
    }

    /**
     * 修改模特标签、启用禁用模特标签状态
     */
    @Log(title = "标签", businessType = BusinessType.UPDATE)
    @PutMapping("edit-model-tag")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "修改模特标签、启用禁用模特标签状态")
    @RequiresPermissions(value = {"tag:model:status", "tag:model:edit"},logical = Logical.OR)
    public R<String> editModelTag(@RequestBody @Validated(CommonValidatedGroup.EditValidatedGroup.class) TagDTO tagDTO) {
        tagDTO.setCategoryId(ModelTagEnum.TAG.getCode());
        tagService.updateTag(tagDTO);
        return R.ok();
    }

    /**
     * 修改类目标签、启用禁用类目标签状态
     */
    @Log(title = "标签", businessType = BusinessType.UPDATE)
    @PutMapping("edit-category-tag")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "修改类目标签、启用禁用类目标签状态")
    @RequiresPermissions(value = {"tag:category:status", "tag:category:edit"},logical = Logical.OR)
    public R<String> editCategoryTag(@RequestBody @Validated(CommonValidatedGroup.EditValidatedGroup.class) TagDTO tagDTO) {
        tagDTO.setCategoryId(ModelTagEnum.CATEGORY.getCode());
        tagService.updateTag(tagDTO);
        return R.ok();
    }

    /**
     * 获取当前分类的一级二级标签
     */
    @GetMapping(value = "/stair/{categoryId}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取当前分类的一级二级标签", response = TagListVO.class)
    public R<List<TagListVO>> stair(@ApiParam("1009:模特标签,1008:类目标签") @PathVariable("categoryId") Long categoryId) {
        return R.ok(tagService.stair(categoryId));
    }

    /**
     * 删除模特标签
     */
    @Log(title = "标签", businessType = BusinessType.DELETE)
    @DeleteMapping("delete-model-tag")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "删除模特标签")
    @RequiresPermissions("tag:model:delete")
    public R<String> removeModelTag(@ApiParam("主键id") @RequestParam Long id) {
        tagService.deleteTagById(id);
        return R.ok();
    }

    /**
     * 删除类目标签
     */
    @Log(title = "标签", businessType = BusinessType.DELETE)
    @DeleteMapping("delete-category-tag")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "删除类目标签")
    @RequiresPermissions("tag:category:delete")
    public R<String> remove(@ApiParam("主键id") @RequestParam Long id) {
        tagService.deleteTagById(id);
        return R.ok();
    }

    /**
     * 根据标签id获取标签信息
     */
    @PostMapping(value = "/query-list")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @InnerAuth
    public R<List<TagListVO>> queryList(@RequestBody Collection<Long> tagIds) {
        List<TagListVO> list = tagService.queryList(tagIds);
        return R.ok(list);
    }

    /**
     * 获取当前分类的指定级别标签
     */
    @GetMapping(value = "/rank")
    @ApiOperation(value = "获取当前分类的指定级别标签", response = TagListVO.class)
    public R<List<TagListVO>> rank(@ApiParam("1009:模特标签,1008:类目标签") @RequestParam("categoryId") Long categoryId,
                                   @ApiParam("标签级别 1:1级标签,2:2级标签 以此类推") @RequestParam("rank") Integer rank) {
        return R.ok(tagService.rank(categoryId, rank));
    }

    /**
     * 获取指定分类的指定级别及以上标签
     */
    @GetMapping(value = "/rank-upslope")
    @ApiOperation(value = "获取指定分类的指定级别及以上标签", response = TagListVO.class)
    public R<List<TagListVO>> rankUpslope(@ApiParam("1009:模特标签,1008:类目标签") @RequestParam("categoryId") Long categoryId,
                                   @ApiParam("标签级别 1:1级标签,2:2级标签 以此类推") @RequestParam("rank") Integer rank) {
        return R.ok(tagService.rankUpslope(categoryId, rank));
    }

    /**
     * 获取标签下拉框
     */
    @GetMapping("/tag-select")
    @ApiOperation(value = "获取标签下拉框", response = TagSimpleVO.class)
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    public R<List<TagSimpleVO>> tagSelect(@ApiParam("1009:模特标签,1008:类目标签") @RequestParam("categoryId") Long categoryId) {
        List<TagSimpleVO> list = tagService.tagSelect(categoryId);
        return R.ok(list);
    }

    /**
     * 模特标签排序
     */
    @Log(title = "标签", businessType = BusinessType.UPDATE)
    @PutMapping("/model-tag-sort")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "模特标签排序")
    @RequiresPermissions("tag:model:sort")
    public R<String> modelTagSort(@RequestBody @Validated TagSortDTO dto) {
        tagService.sort(dto);
        return R.ok();
    }

    /**
     * 类目标签排序
     */
    @Log(title = "标签", businessType = BusinessType.UPDATE)
    @PutMapping("/category-tag-sort")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "类目标签排序")
    @RequiresPermissions("tag:category:sort")
    public R<String> categoryTagSort(@RequestBody @Validated TagSortDTO dto) {
        tagService.sort(dto);
        return R.ok();
    }
}
