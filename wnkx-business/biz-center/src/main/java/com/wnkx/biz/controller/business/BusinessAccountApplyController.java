package com.wnkx.biz.controller.business;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.AccountAuditStatusEnum;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.business.account.AccountApplyQueryDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.AuditAccountApplyDTO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountApplyVO;
import com.wnkx.biz.business.service.IBusinessAccountApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :商家余额流水
 * @create :2024-06-27 18:55
 **/
@RestController
@RequestMapping("/business/account/apply")
@Api(value = "商家业务-账号申请" , tags = "商家业务-账号申请")
@RequiredArgsConstructor
public class BusinessAccountApplyController extends BaseController {
    private final IBusinessAccountApplyService businessAccountApplyService;


    @GetMapping("/list")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isOwnerAccount = true)
    @ApiOperation(value = "账号申请列表（商家端）" , response = PageInfo.class)
    public R<PageInfo<BusinessAccountApplyVO>> list(AccountApplyQueryDTO dto) {
        dto.setOwnerAccount(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getAccount());
        return R.ok(toPage(businessAccountApplyService.list(dto)));
    }

    @GetMapping("/businessAccountApplyNum")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isOwnerAccount = true)
    @ApiOperation(value = "账号申请表待审批数量（商家端）" , response = PageInfo.class)
    public R<Integer> businessAccountApplyNum(AccountApplyQueryDTO dto) {
        dto.setOwnerAccount(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getAccount());
        dto.setAuditStatus(AccountAuditStatusEnum.UN_AUDIT.getCode());
        return R.ok(businessAccountApplyService.businessAccountApplyNum(dto));
    }

    @PostMapping("/audit")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isOwnerAccount = true)
    @ApiOperation(value = "账号申请审批（商家端）" , response = PageInfo.class)
    public R<String> audit(@RequestBody @Validated AuditAccountApplyDTO dto) {
        businessAccountApplyService.audit(dto);
        return R.ok();
    }

}
