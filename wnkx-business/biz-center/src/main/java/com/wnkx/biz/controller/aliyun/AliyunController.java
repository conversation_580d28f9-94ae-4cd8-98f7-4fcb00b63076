package com.wnkx.biz.controller.aliyun;

import com.aliyun.dypnsapi20170525.models.*;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.domain.dto.biz.aliyun.PhoneWithTokenDTO;
import com.wnkx.biz.core.AliyunCore;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :阿里云接口
 * @create :2025-01-06 17:10
 **/
@RestController
@RequestMapping("/aliyun")
@Api(value = "阿里云业务", tags = "阿里云业务")
@RequiredArgsConstructor
public class AliyunController {
    private final AliyunCore aliyunCore;

    @ApiOperation("获取授权code")
    @GetMapping("/getAuthToken")
    public R<GetAuthTokenResponseBody.GetAuthTokenResponseBodyTokenInfo> getAuthToken(){
        return R.ok(aliyunCore.getAuthToken());
    }
    @ApiOperation("一键登录取号")
    @GetMapping("/getPhoneWithToken")
    public R<GetPhoneWithTokenResponseBody.GetPhoneWithTokenResponseBodyData> getPhoneWithToken(PhoneWithTokenDTO dto){
        return R.ok(aliyunCore.getPhoneWithToken(dto));
    }
}
