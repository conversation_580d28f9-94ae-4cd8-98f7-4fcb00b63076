package com.wnkx.biz.controller.business;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.BusinessBalanceAuditStatusEnum;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceAuditFlowListDTO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceAuditFlowDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceAuditFlowStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceAuditFlowVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceAuditFlowDetailExportVO;
import com.wnkx.biz.business.service.IBusinessBalanceAuditFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :商家控制层
 * @create :2024-06-24 09:10
 **/
@RestController
@RequestMapping("/business")
@Api(value = "商家余额提现业务", tags = "商家余额提现业务")
@RequiredArgsConstructor
public class BusinessBalanceAuditFlowController extends BaseController {
    private final IBusinessBalanceAuditFlowService businessBalanceAuditFlowService;

    /**
     * 获取余额提现审核表列表（运营端）
     */
    @GetMapping("/backend/businessBalanceAuditFlow/list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取余额提现审核表列表（运营端）")
    @RequiresPermissions(value = {"withdraw:deposit:list", "task:inform:list"}, logical = Logical.OR)
    public R<PageInfo<BusinessBalanceAuditFlowVO>> businessBalanceAuditFlowList(BusinessBalanceAuditFlowListDTO dto) {
        List<BusinessBalanceAuditFlowVO> businessBalanceAuditFlowVOS = businessBalanceAuditFlowService.queryList(dto);
        return R.ok(toPage(businessBalanceAuditFlowVOS));
    }

    /**
     * 导出余额提现审核表列表（运营端）
     */
    @PostMapping("/backend/businessBalanceAuditFlow/export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "导出余额提现审核表列表（运营端）")
    @RequiresPermissions("withdraw:deposit:list")
    public void businessBalanceAuditFlowListExport(BusinessBalanceAuditFlowListDTO dto, HttpServletResponse response) {
        List<BusinessBalanceAuditFlowVO> businessBalanceAuditFlowVOS = businessBalanceAuditFlowService.queryList(dto);
        ExcelUtil<BusinessBalanceAuditFlowVO> util = new ExcelUtil<>(BusinessBalanceAuditFlowVO.class);
        ExcelUtil.setAttachmentResponseHeader(response, "余额提现审核导出");
        util.exportExcel(response, businessBalanceAuditFlowVOS, "余额提现审核导出");
    }

    /**
     * 标记提现已通知
     * @param id 提现id
     */
    @PostMapping("/backend/businessBalanceAuditFlow/markNotice/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "标记提现已通知")
    public R<String> markNoticeBusinessBalanceAuditFlow(@PathVariable Long id) {
        businessBalanceAuditFlowService.markNotice(id);
        return R.ok("");
    }


    /**
     * 获取余额提现审核详情（运营端）
     */
    @GetMapping("/backend/businessBalanceAuditFlow/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取余额提现审核详情（运营端）" , response = PageInfo.class)
    @RequiresPermissions("withdraw:deposit:records")
    public R<BusinessBalanceAuditFlowDetailVO> businessBalanceAuditFlowList(@PathVariable("id") Long id) {
        BusinessBalanceAuditFlowDetailVO businessBalanceAuditFlowDetailVO = businessBalanceAuditFlowService.getDetailById(id);
        return R.ok(businessBalanceAuditFlowDetailVO);
    }

    /**
     * 获取余额提现审核统计（运营端）
     */
    @GetMapping("/backend/businessBalanceAuditFlow/statistics")
    @RequiresPermissions("withdraw:deposit:list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取余额提现审核统计（运营端）")
    public R<BusinessBalanceAuditFlowStatisticsVO> getStatistics() {
        BusinessBalanceAuditFlowStatisticsVO statistics = businessBalanceAuditFlowService.getStatistics();
        return R.ok(statistics);
    }



    /**
     * 提现明细（运营端）
     */
    @GetMapping("/backend/businessBalanceAuditFlow/auditList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "提现明细（运营端）")
    @RequiresPermissions("finance:withdraw:list")
    public R<PageInfo<BusinessBalanceAuditFlowVO>> auditBusinessBalanceAuditFlowList(BusinessBalanceAuditFlowListDTO dto) {
        dto.setAuditStatus(Arrays.asList(BusinessBalanceAuditStatusEnum.APPROVE.getCode()));
        dto.setSearchNameMemberCodeAccount(dto.getKeyword());
        List<BusinessBalanceAuditFlowVO> businessBalanceAuditFlowVOS = businessBalanceAuditFlowService.queryList(dto);
        return R.ok(toPage(businessBalanceAuditFlowVOS));
    }

    @PostMapping("/backend/businessBalanceAuditFlow/auditList/export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "提现明细导出（运营端）")
    @RequiresPermissions("finance:withdraw:export")
    public void auditBusinessBalanceAuditFlowListExport(BusinessBalanceAuditFlowListDTO dto, HttpServletResponse response) {
        dto.setAuditStatus(Arrays.asList(BusinessBalanceAuditStatusEnum.APPROVE.getCode()));
        dto.setSearchNameMemberCodeAccount(dto.getKeyword());
        List<BusinessBalanceAuditFlowDetailExportVO> businessBalanceAuditFlowDetailExportVOS = businessBalanceAuditFlowService.queryExportList(dto);
        ExcelUtil<BusinessBalanceAuditFlowDetailExportVO> util = new ExcelUtil<>(BusinessBalanceAuditFlowDetailExportVO.class);
        ExcelUtil.setAttachmentResponseHeader(response, "提现明细导出");
        util.exportExcel(response, businessBalanceAuditFlowDetailExportVOS, "提现明细导出");
    }

}
