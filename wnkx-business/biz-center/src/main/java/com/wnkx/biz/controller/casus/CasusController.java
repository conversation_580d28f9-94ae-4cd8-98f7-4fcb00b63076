package com.wnkx.biz.controller.casus;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.order.casus.*;
import com.ruoyi.system.api.domain.entity.order.casus.CasusVideo;
import com.ruoyi.system.api.domain.vo.order.casus.CasusGroupVO;
import com.ruoyi.system.api.domain.vo.order.casus.CasusVideoVO;
import com.ruoyi.system.api.domain.vo.order.casus.GroupAddVideoVO;
import com.ruoyi.system.api.domain.vo.order.casus.GroupVideoVO;
import com.wnkx.biz.casus.service.ICasusGroupService;
import com.wnkx.biz.casus.service.ICasusVideoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-07-10 13:52
 **/
@RestController
@RequestMapping("/operate/casus")
@Api(value = "运营管理-案例管理", tags = "运营管理-案例管理")
@RequiredArgsConstructor
public class CasusController extends BaseController {
    private final ICasusVideoService casusVideoService;
    private final ICasusGroupService casusGroupService;

    @GetMapping("/video/list")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE,UserTypeConstants.MODEL_TYPE})
    @ApiOperation("获取案例视频列表")
    @RequiresPermissions("case:video:list")
    public R<PageInfo<CasusVideoVO>> videoList(CasusVideoQueryDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<CasusVideo> casusVideos = casusVideoService.queryList(dto);
        List<CasusVideoVO> casusVideoVOS = BeanUtil.copyToList(casusVideos, CasusVideoVO.class);
        return R.ok(PageUtils.getDataTable(casusVideoVOS, casusVideos));
    }

    @PostMapping("/video/save")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("保存案例视频")
    @RequiresPermissions("case:video:add")
    public R<String> saveVideo(@RequestBody @Validated CasusVideoSaveDTO dto){
        casusVideoService.save(dto);
        return R.ok();
    }

    @PutMapping("/video/update")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("修改案例视频")
    @RequiresPermissions("case:video:edit")
    public R<String> updateVideo(@RequestBody @Validated CasusVideoUpdateDTO dto){
        casusVideoService.update(dto);
        return R.ok();
    }
    @DeleteMapping("/video/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("删除视频")
    @RequiresPermissions("case:video:delete")
    public R<String> deleteVideo(@PathVariable("id") Long id){
        casusVideoService.delete(id);
        return R.ok();
    }

    @GetMapping("/group/list")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE,UserTypeConstants.MODEL_TYPE})
    @ApiOperation("获取案例分组类表")
    @RequiresPermissions(value = {"case:group:list", "custom:page:list"}, logical = Logical.OR)
    public R<PageInfo<CasusGroupVO>> groupList(CasusGroupDTO dto) {
        List<CasusGroupVO> casusGroupVOS = casusGroupService.queryList(dto);
        return R.ok(toPage(casusGroupVOS));
    }

    @PostMapping("/group/save")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("保存案例分组")
    @RequiresPermissions("case:group:add")
    public R<String> groupSave(@RequestBody @Validated CasusGroupSaveDTO dto) {
        casusGroupService.save(dto);
        return R.ok();
    }

    @PutMapping("/group/update")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("修改案例分组")
    @RequiresPermissions("case:group:edit")
    public R<String> groupUpdate(@RequestBody @Validated CasusGroupUpdateDTO dto){
        casusGroupService.update(dto);
        return R.ok();
    }

    @DeleteMapping("/group/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("删除分组")
    @RequiresPermissions("case:group:delete")
    public R<String> delete(@PathVariable("id") Long id){
        casusGroupService.delete(id);
        return R.ok();
    }

    @GetMapping("/group/groupsVideoList")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE,UserTypeConstants.MODEL_TYPE})
    @ApiOperation("获取案例分组视频列表")
    @RequiresPermissions("case:group:manage")
    public R<PageInfo<GroupVideoVO>> queryGroupsVideoList(@Validated GroupsVideoListDTO dto){
        List<GroupVideoVO> groupVideoVOS = casusGroupService.queryGroupsVideoList(dto);
        return R.ok(toPage(groupVideoVOS));
    }

    @GetMapping("/group/addGroupsVideoList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("获取分组可添加视频列表")
    @RequiresPermissions("case:group:manage")
    public R<PageInfo<GroupAddVideoVO>>  queryAddGroupsVideoList(@Validated GroupAddVideoDTO dto){
        return R.ok(toPage(casusGroupService.queryAddGroupsVideoList(dto)));
    }

    @PostMapping("/group/addGroupVideo")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("添加分组案例视频")
    @RequiresPermissions("case:group:manage")
    public R<String> addGroupVideo(@RequestBody @Validated GroupVideoDTO dto){
        casusGroupService.addGroupVideo(dto);
        return R.ok();
    }

    @DeleteMapping("/group/removeGroupVideo")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("清除分组案例视频")
    @RequiresPermissions("case:group:manage")
    public R<String> removeGroupVideo(@RequestBody @Validated GroupVideoDTO dto){
        casusGroupService.removeGroupVideo(dto);
        return R.ok();
    }

    @PutMapping("/group/updateGroupSort")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("更新分组视频顺序")
    @RequiresPermissions("case:group:manage")
    public R<String> updateGroupSort(@RequestBody @Validated UpdateGroupSortDTO dto){
        casusGroupService.updateGroupSort(dto);
        return R.ok();
    }

}
