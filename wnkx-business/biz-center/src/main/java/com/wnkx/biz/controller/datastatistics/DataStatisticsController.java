package com.wnkx.biz.controller.datastatistics;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.*;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.member.*;
import com.wnkx.biz.model.service.ModelDataStatisticsService;
import com.wnkx.biz.statistics.BusinessDataStatisticsCore;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/8 10:09
 */
@RestController
@RequestMapping("/data-statistics")
@Api(value = "数据统计服务", tags = "数据统计服务")
@RequiredArgsConstructor
public class DataStatisticsController extends BaseController {

    private final ModelDataStatisticsService modelDataStatisticsService;

    private final BusinessDataStatisticsCore businessDataStatisticsCore;

    /**
     * 模特数据-模特基础数据
     */
    @GetMapping("/model-basics-data")
    @ApiOperation("模特数据-模特基础数据")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<ModelBasicDataVO> getModelBasicData() {
        ModelBasicDataVO modelBasicDataVO = modelDataStatisticsService.getModelBasicData();
        return R.ok(modelBasicDataVO);
    }
    /**
     * 模特数据-模特数量趋势分析
     */
    @GetMapping("/model-number-trend-analysis")
    @ApiOperation("模特数据-模特数量趋势分析")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<ModelNumberTrendAnalysisVO> getModelNumberTrendAnalysis(@ApiParam("开始时间") @RequestParam Date beginTime,
                                                                     @ApiParam("结束时间") @RequestParam Date endTime) {
        ModelNumberTrendAnalysisVO modelNumberTrendAnalysisVO = modelDataStatisticsService.getModelNumberTrendAnalysis(beginTime, endTime);
        return R.ok(modelNumberTrendAnalysisVO);
    }

    /**
     * 模特数据-每月新增模特分析
     */
    @GetMapping("/new-model-analysis")
    @ApiOperation("模特数据-每月新增模特分析")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<ModelCountAnalysisVO> getNewModelAnalysis(@ApiParam("月份") @RequestParam(required = false) String date) {
        ModelCountAnalysisVO modelCountAnalysisVO = modelDataStatisticsService.getNewModelAnalysis(date);
        return R.ok(modelCountAnalysisVO);
    }

    /**
     * 模特数据-每月淘汰模特分析
     */
    @GetMapping("/oust-model-analysis")
    @ApiOperation("模特数据-每月淘汰模特分析")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<ModelCountAnalysisVO> getOustModelAnalysis(@ApiParam("月份") @RequestParam(required = false) String date) {
        ModelCountAnalysisVO modelCountAnalysisVO = modelDataStatisticsService.getOustModelAnalysis(date);
        return R.ok(modelCountAnalysisVO);
    }

    /**
     * 模特数据-模特类型分析
     */
    @GetMapping("/model-type-analysis")
    @ApiOperation("模特数据-模特类型分析")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<ModelTypeAnalysisVO> getModelTypeAnalysis(@ApiParam("模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)") @RequestParam Integer status) {
        ModelTypeAnalysisVO modelTypeAnalysis = modelDataStatisticsService.getModelTypeAnalysis(status);
        return R.ok(modelTypeAnalysis);
    }

    /**
     * 模特数据-模特接单排行榜
     */
    @GetMapping("/model-order-ranking")
    @ApiOperation("模特数据-模特接单排行榜")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<ModelOrderRankingVO> getModelOrderRanking(@ApiParam("月份") @RequestParam(required = false) String date) {
        ModelOrderRankingVO modelOrderRankingVO = modelDataStatisticsService.getModelOrderRanking(date);
        return R.ok(modelOrderRankingVO);
    }


    @GetMapping("/business-order-data")
    @ApiOperation("会员数据-会员排单数占比")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<BusinessAnalysisVO> getBusinessOrderAnalysisVO(@ApiParam("开始时间") @RequestParam(required = false) Date beginTime,
                                                                 @ApiParam("结束时间") @RequestParam(required = false) Date endTime) {
        BusinessAnalysisVO businessOrderAnalysisVO = businessDataStatisticsCore.getBusinessOrderAnalysisVO(beginTime, endTime);
        return R.ok(businessOrderAnalysisVO);
    }

    @GetMapping("/business-member-type-data")
    @ApiOperation("会员数据-会员类型分析")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<BusinessAnalysisVO> getBusinessOrderAnalysisVO() {
        BusinessAnalysisVO businessAnalysisVO = businessDataStatisticsCore.getBusinessMemberTypeAnalysisVO();
        return R.ok(businessAnalysisVO);
    }

    @GetMapping("/business-exit-data")
    @ApiOperation("会员数据-会员退款单数占比")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<BusinessAnalysisVO> getBusinessExitAnalysis(@ApiParam("开始时间") @RequestParam(required = false) Date beginTime,
                                                            @ApiParam("结束时间") @RequestParam(required = false) Date endTime) {
        BusinessAnalysisVO businessOrderAnalysisVO = businessDataStatisticsCore.getBusinessExitAnalysis(beginTime, endTime);
        return R.ok(businessOrderAnalysisVO);
    }

    @GetMapping("/business-source-data")
    @ApiOperation("会员数据-会员来源分析")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<BusinessAnalysisVO> getBusinessSourceAnalysis(@ApiParam("开始时间") @RequestParam(required = false) Date beginTime,
                                                            @ApiParam("结束时间") @RequestParam(required = false) Date endTime) {
        BusinessAnalysisVO businessOrderAnalysisVO = businessDataStatisticsCore.getBusinessSourceAnalysis(beginTime, endTime);
        return R.ok(businessOrderAnalysisVO);
    }
    @GetMapping("/business-member-type-order-data")
    @ApiOperation("会员数据-会员排单数统计")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<MemberTypeOrderCountResultVO> getMemberTypeOrderCountVO(@ApiParam("类型:1-周 2-月 3-年") @RequestParam(required = false) Integer type,
                                                                     @ApiParam("开始时间") @RequestParam(required = false) Date beginTime,
                                                                     @ApiParam("结束时间") @RequestParam(required = false) Date endTime) {
        return R.ok(businessDataStatisticsCore.getMemberTypeOrderCountVO(beginTime, endTime, type));
    }

    @GetMapping("/business-member-trend-data")
    @ApiOperation("会员数据-会员趋势")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<MemberTrendListVO> getMemberTrendVO(@ApiParam("类型:1-周 2-月 3-年") @RequestParam(required = false) Integer type,
                                                   @ApiParam("开始时间") @RequestParam(required = false) Date beginTime,
                                                   @ApiParam("结束时间") @RequestParam(required = false) Date endTime) {
        return R.ok(businessDataStatisticsCore.getMemberTrendVO(beginTime, endTime, type));
    }

    @GetMapping("/business-member-base-recharge-data")
    @ApiOperation("会员数据-会员充值数据")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<MemberRechargeCountVO> getMemberRechargeCountVO() {
        return R.ok(businessDataStatisticsCore.getMemberRechargeCountVO());
    }

    @GetMapping("/business-member-base-expire-data")
    @ApiOperation("会员数据-会员过期数据")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<MemberExpireCountVO> getMemberExpireCountVO() {
        return R.ok(businessDataStatisticsCore.getMemberExpireCountVO());
    }

    /**
     * 模特数据-开发模特合作状态分布
     */
    @GetMapping("/model-status-data")
    @ApiOperation(value = "模特数据-开发模特合作状态分布", response = DataPieChartVO.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<DataPieChartVO> getModelStatusData(
            @ApiParam("开始时间") @RequestParam(required = false) Date beginTime,
            @ApiParam("结束时间") @RequestParam(required = false) Date endTime,
            @ApiParam("开始分数") @RequestParam(required = false) BigDecimal beginScore,
            @ApiParam("结束分数") @RequestParam(required = false) BigDecimal endScore
    ) {
        DataPieChartVO dataPieChartVO = modelDataStatisticsService.getModelStatusData(beginTime, endTime, beginScore, endScore);
        return R.ok(dataPieChartVO);
    }

    /**
     * 模特数据-模特排单情况
     */
    @GetMapping("/model-order-scheduled-data")
    @ApiOperation(value = "模特数据-模特排单情况", response = DataPieChartVO.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<DataPieChartVO> getModelOrderScheduledData(
            @ApiParam("开始时间") @RequestParam(required = false) Date beginTime,
            @ApiParam("结束时间") @RequestParam(required = false) Date endTime,
            @ApiParam("开始分数") @RequestParam(required = false) BigDecimal beginScore,
            @ApiParam("结束分数") @RequestParam(required = false) BigDecimal endScore
    ) {
        DataPieChartVO dataPieChartVO = modelDataStatisticsService.getModelOrderScheduledData(beginTime, endTime, beginScore, endScore);
        return R.ok(dataPieChartVO);
    }
}
