package com.wnkx.biz.controller.channel;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.ChannelTypeEnum;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionChancelStatisticsDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionChannelListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.InviteListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.EditFissionChannelDiscountDTO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserListVO;
import com.ruoyi.system.api.domain.vo.biz.channel.*;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelBrokeRageVO;
import com.wnkx.biz.business.service.IBusinessAccountService;
import com.wnkx.biz.channel.service.IDistributionChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-07-10 13:52
 **/
@RestController
@RequestMapping("/fission-channel")
@Api(value = "营销中心-裂变渠道", tags = "营销中心-裂变渠道")
@RequiredArgsConstructor
public class FissionChannelController extends BaseController {
    private final IDistributionChannelService distributionChannelService;
    private final IBusinessAccountService businessAccountService;


    @GetMapping("/backend/list")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "裂变渠道列表", response = PageInfo.class)
    @RequiresPermissions("channel:fission:list")
    public R<PageInfo<FissionChannelVO>> videoList(DistributionChannelListDTO dto) {
        return R.ok(toPage(distributionChannelService.queryFissionChannelList(dto)));
    }

    @GetMapping("/backend/list/statistics")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "裂变渠道列表统计")
    @RequiresPermissions("channel:fission:list")
    public R<DistributionChannelStatisticsVO> videoListStatistics(DistributionChannelListDTO dto) {
        return R.ok(distributionChannelService.fissionStatistics(dto));
    }

    @GetMapping("/backend/statistics")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "裂变渠道统计", response = DistributionChannelStatisticsVO.class)
    @RequiresPermissions("channel:fission:list")
    public R<DistributionChannelStatisticsVO> statistics(DistributionChancelStatisticsDTO dto) {
        dto.setChannelType(ChannelTypeEnum.FISSION.getCode());
        return R.ok(distributionChannelService.statistics(dto));
    }

    @GetMapping("/backend/{id}")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "裂变渠道详情", response = DistributionChannelDetailVO.class)
    @RequiresPermissions("channel:fission:detail")
    public R<FissionChannelVO> getByChannelId(@PathVariable Long id) {
        return R.ok(distributionChannelService.getFissionChannelVOById(id));
    }



    @GetMapping("/backend/inviteList")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "邀请记录列表", response = PageInfo.class)
    @RequiresPermissions("channel:fission:detail")
    public R<PageInfo<ChannelInviteVO>> inviteList(@Validated InviteListDTO dto) {
        dto.setChannelType(ChannelTypeEnum.FISSION.getCode());
        return R.ok(toPage(distributionChannelService.inviteList(dto)));
    }

    @GetMapping("/preview/poster/{id}")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "预览裂变渠道海报")
    public void previewPoster(@PathVariable Long id, HttpServletResponse response) {
        distributionChannelService.previewFissionPoster(id, response);
    }


    @PostMapping("/download/poster/{id}")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "下载裂变渠道海报")
    @RequiresPermissions("channel:fission:download")
    public void downloadPoster(@PathVariable Long id, HttpServletResponse response) {
        distributionChannelService.downloadFissionManagerPoster(id, response);
    }

    /**
     * 下载全部裂变渠道海报
     */
    @PostMapping("/download/poster/allByIds")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "根据id列表下载全部裂变渠道海报")
    @RequiresPermissions("channel:fission:download-all")
    public void downloadAllPoster(@RequestBody List<Long> channelIds, HttpServletResponse response) {
        distributionChannelService.downloadAllFissionPosterByIds(channelIds, response);
    }
    @ApiOperation(value = "查看账号信息", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @GetMapping("/backend/bizUserList")
    @RequiresPermissions("channel:fission:account")
    public R<PageInfo<BizUserListVO>> bizUserList(BizUserListDTO dto) {
        return R.ok(toPage(businessAccountService.bizUserList(dto)));
    }

//    @GetMapping("/getFissionMemberDiscount")
//    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
//    @ApiOperation(value = "获取裂变会员折扣", response = PageInfo.class)
//    @RequiresPermissions("channel:fission:editMemberDiscount")
//    public R<FissionMemberDiscountVO> getFissionMemberDiscount() {
//        return R.ok(distributionChannelService.getFissionMemberDiscount());
//    }
//
//    @PutMapping("/editFissionMemberDiscount")
//    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
//    @ApiOperation(value = "修改裂变会员折扣", response = PageInfo.class)
//    @RequiresPermissions("channel:fission:editMemberDiscount")
//    public R<String> editFissionMemberDiscount(@Validated @RequestBody EditFissionMemberDiscountDTO dto) {
//        distributionChannelService.editFissionMemberDiscount(dto);
//        return R.ok();
//    }
    /**
     * -----------------------------------------------------------------------------------------------------------
     */


    @PutMapping("/editFissionChannelDiscount")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "修改裂变渠道折扣")
    @RequiresPermissions("channel:fission:editMemberDiscount")
    public R<String> editFissionChannelDiscount(@Validated @RequestBody EditFissionChannelDiscountDTO dto) {
        dto.setChannelType(ChannelTypeEnum.FISSION.getCode());
        distributionChannelService.editFissionMemberDiscountV1(dto);
        return R.ok();
    }

    @GetMapping("/getFissionDiscountV1")
//    @LoginUserType(userTypes = {UserTypeConstants.USER_TYPE, UserTypeConstants.MANAGER_TYPE, UserTypeConstants.CHANNEL_TYPE})
    @ApiOperation(value = "获取裂变折扣")
    public R<ChannelBrokeRageVO> getFissionDiscountV1() {
        return R.ok(distributionChannelService.getFissionDiscountV1());
    }




}
