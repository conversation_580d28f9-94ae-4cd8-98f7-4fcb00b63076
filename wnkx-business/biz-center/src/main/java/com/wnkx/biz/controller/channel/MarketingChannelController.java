package com.wnkx.biz.controller.channel;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.validated.CommonValidatedGroup;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.InviteListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.MarketingChannelDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.MarketingChannelListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.VisitMarketingChannelDTO;
import com.ruoyi.system.api.domain.vo.SysUserVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserListVO;
import com.ruoyi.system.api.domain.vo.biz.channel.*;
import com.wnkx.biz.business.service.IBusinessAccountService;
import com.wnkx.biz.channel.service.IMarketingChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/25 9:19
 */
@RestController
@RequestMapping("/marketing-channel")
@Api(value = "市场渠道信息服务", tags = "市场渠道信息服务")
@RequiredArgsConstructor
public class MarketingChannelController extends BaseController {

    private final IMarketingChannelService marketingChannelService;
    private final IBusinessAccountService businessAccountService;

    /**
     * 查询市场渠道列表
     */
    @GetMapping("/list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询市场渠道列表", response = PageInfo.class)
    @RequiresPermissions("channel:market:list")
    public R<PageInfo<MarketingChannelListVO>> marketingChannelListByCondition(MarketingChannelListDTO listDTO) {
        return R.ok(toPage(marketingChannelService.marketingChannelListByCondition(listDTO)));
    }

    /**
     * 新增市场渠道
     */
    @PostMapping("/save")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "新增市场渠道", response = String.class)
    @RequiresPermissions("channel:market:add")
    public R<String> saveMarketingChannel(@RequestBody @Validated(CommonValidatedGroup.SaveValidatedGroup.class) MarketingChannelDTO dto) {
        marketingChannelService.saveMarketingChannel(dto);
        return R.ok();
    }

    /**
     * 查询市场渠道详情
     */
    @GetMapping("/detail/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询市场渠道详情", response = MarketingChannelDetailVO.class)
    @RequiresPermissions("channel:market:detail")
    public R<MarketingChannelDetailVO> getMarketingChannelDetail(@PathVariable("id") Long id) {
        return R.ok(marketingChannelService.getMarketingChannelDetail(id));
    }

    /**
     * 编辑市场渠道
     */
    @PutMapping("/edit")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "编辑市场渠道", response = String.class)
    @RequiresPermissions("channel:market:edit")
    public R<String> editMarketingChannel(@RequestBody @Validated(CommonValidatedGroup.EditValidatedGroup.class) MarketingChannelDTO dto) {
        marketingChannelService.editMarketingChannel(dto);
        return R.ok();
    }

    /**
     * 下载物料
     */
    @GetMapping("/download-material/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "下载物料", response = DownloadMaterialVO.class)
    @RequiresPermissions(value = {"channel:market:copy", "channel:market:download"}, logical = Logical.OR)
    public R<DownloadMaterialVO> downloadMaterial(@PathVariable("id") Long id) {
        DownloadMaterialVO downloadMaterialVO = marketingChannelService.downloadMaterial(id);
        return R.ok(downloadMaterialVO);
    }


    /**
     * 通过专属链接code获取专属企微二维码
     */
    @GetMapping("/qrcode")
    @ApiOperation(value = "通过专属链接code获取专属企微二维码", response = String.class)
    public R<QrcodeByDedicatedLinkCodeVO> getQrcodeByDedicatedLinkCode() {
        return R.ok(marketingChannelService.getQrcodeByDedicatedLinkCode(""));
    }

    /**
     * 通过专属链接code获取专属企微二维码
     */
    @GetMapping("/qrcode/{dedicatedLinkCode}")
    @ApiOperation(value = "通过专属链接code获取专属企微二维码", response = String.class)
    public R<QrcodeByDedicatedLinkCodeVO> getQrcodeByDedicatedLinkCode(@PathVariable("dedicatedLinkCode") String dedicatedLinkCode) {
        return R.ok(marketingChannelService.getQrcodeByDedicatedLinkCode(dedicatedLinkCode));
    }

    /**
     * 导出市场渠道列表
     */
    @PostMapping("/export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "导出市场渠道列表")
    @RequiresPermissions("channel:market:export")
    public void exportMarketingChannelList(MarketingChannelListDTO listDTO, HttpServletResponse response) {
        marketingChannelService.exportMarketingChannelList(listDTO, response);
    }

    @GetMapping("/statistics")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询市场渠道统计")
    @RequiresPermissions("channel:market:list")
    public R<MarketingChannelStatisticsVO> marketingChannelStatistics(MarketingChannelListDTO listDTO) {
        return R.ok(marketingChannelService.marketingChannelStatistics(listDTO));
    }

    @GetMapping("/createUserList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取市场渠道创建人列表", response = PageInfo.class)
    public R<PageInfo<SysUserVO>> createUserList(String username) {
        return R.ok(toPage(marketingChannelService.createUserList(username)));
    }

    @GetMapping("/getChannelInviteList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取邀请记录列表", response = PageInfo.class)
    @RequiresPermissions("channel:market:detail")
    public R<PageInfo<ChannelInviteVO>> getChannelInviteList(@Validated InviteListDTO dto) {
        return R.ok(toPage(marketingChannelService.getChannelInviteList(dto)));
    }

    @GetMapping("/getChannelInviteList/statistics")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取邀请记录统计")
    @RequiresPermissions("channel:market:detail")
    public R<ChannelInviteStatisticsVO> getChannelInviteStatistics(@Validated InviteListDTO dto) {
        return R.ok(marketingChannelService.getChannelInviteStatistics(dto));
    }


    @PostMapping("/getChannelInviteList/export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "导出邀请记录列表")
    @RequiresPermissions("channel:market:channelInviteList:export")
    public void exportChannelInviteList(@Validated InviteListDTO dto, HttpServletResponse response) {
        List<ChannelInviteExportVO> channelInviteExportVOS = marketingChannelService.exportChannelInviteList(dto);
        ExcelUtil<ChannelInviteExportVO> util = new ExcelUtil<>(ChannelInviteExportVO.class);
        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "市场渠道列表导出");
        util.exportExcel(response, channelInviteExportVOS, "市场渠道列表导出");
    }

    @GetMapping("/visit")
    @ApiOperation(value = "访问市场渠道")
    public void visit(VisitMarketingChannelDTO dto) {
        marketingChannelService.visit(dto);
    }

    @ApiOperation(value = "查看账号信息", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @GetMapping("/backend/bizUserList")
    @RequiresPermissions("channel:market:account")
    public R<PageInfo<BizUserListVO>> bizUserList(BizUserListDTO dto) {
        return R.ok(toPage(businessAccountService.bizUserList(dto)));
    }
}