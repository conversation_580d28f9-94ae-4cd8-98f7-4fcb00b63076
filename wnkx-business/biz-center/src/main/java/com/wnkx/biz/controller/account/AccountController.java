package com.wnkx.biz.controller.account;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.UserStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.QrCodeDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.*;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.UpdateNameDTO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountInfoVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.business.ChannelPhoneLoginVO;
import com.ruoyi.system.api.domain.vo.biz.business.PhoneLoginVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelVO;
import com.ruoyi.system.api.model.LoginModel;
import com.wnkx.biz.business.service.IBizUserService;
import com.wnkx.biz.business.service.IBusinessAccountService;
import com.wnkx.biz.model.service.IModelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2024/6/21
 */
@RestController
@RequestMapping("/account")
@Api(value = "商家登录业务", tags = "商家登录业务")
@RequiredArgsConstructor
public class AccountController {
    private final IBusinessAccountService businessAccountService;
    private final IModelService modelService;
    private final IBizUserService bizUserService;

    @GetMapping("/info/{unionId}")
    @ApiOperation("根据unionId获取用户信息")
    @InnerAuth
    public R<BusinessAccountVO> getAccountInfo(@PathVariable("unionId") String unionId) {
        BusinessAccountVO businessAccount = businessAccountService.getBusinessAccount(unionId);
        if (ObjectUtil.isNotNull(businessAccount)) {
            businessAccount.setUnionid("");
            businessAccount.setExternalUserId("");
            businessAccount.setBusinessAccountVOS(new ArrayList<>());
        }
        return R.ok(businessAccount);
    }

    @PostMapping("/phoneLogin")
    @ApiOperation("手机号登录")
    @InnerAuth
    public R<PhoneLoginVO> phoneLogin(@RequestBody @Validated PhoneLoginDTO dto) {
        return R.ok(businessAccountService.phoneLogin(dto));
    }

    @PostMapping("/channelPhoneLogin")
    @ApiOperation("手机号登录(渠道端)")
    @InnerAuth
    public R<ChannelPhoneLoginVO> channelPhoneLogin(@RequestBody @Validated ChannelPhoneLoginDTO dto) {
        return R.ok();
    }


    @PostMapping("/generateQrcode")
    @ApiOperation("生成二维码")
    @InnerAuth
    public R<QrCodeDTO> generateQrcode(@RequestParam Integer type, @RequestParam(name = "code", required = false) String code) {
        return R.ok(businessAccountService.generateQrcode(type, code));
    }
    @PostMapping("/backend/generateQrcode")
    @ApiOperation("生成二维码")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<QrCodeDTO> backendGenerateQrcode(@RequestParam Integer type) {
        return R.ok(businessAccountService.generateQrcode(type, null));
    }

    @PostMapping("/bizUser/checkPhone")
    @ApiOperation("检查登录手机号")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    public R<String> bizUserCheckPhone(@RequestBody @Validated CheckPhoneDTO dto) {
        businessAccountService.bizUserCheckPhone(dto);
        return R.ok();
    }

    @PostMapping("/bizUser/checkBindingPhone")
    @ApiOperation("检查换绑手机号")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    public R<String> checkBindingPhone(@RequestBody @Validated CheckPhoneDTO dto) {
        businessAccountService.checkPhone(dto.getPhone());
        return R.ok();
    }

    @PostMapping("/bizUser/bizUserUpdatePhone")
    @ApiOperation("变更手机号")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    public R<String> bizUserUpdatePhone(@RequestBody @Validated UpdatePhoneDTO dto) {
        businessAccountService.updateBizUserPhone(dto);
        return R.ok();
    }


    @PostMapping("/bizUser/bizUserUpdateWeChat")
    @ApiOperation("换绑微信")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    public R<String> bizUserUpdateWeChat(@RequestBody @Validated UpdateWeChatDTO dto) {
        return R.ok(businessAccountService.updateBizUserWeChat(dto));
    }
    @PostMapping("/bizUser/bizUserUpdateName")
    @ApiOperation("变更员工名称")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    public R<String> bizUserUpdateName(@RequestBody @Validated UpdateNameDTO dto) {
        businessAccountService.bizUserUpdateName(dto);
        return R.ok();
    }


    @GetMapping("/info")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation("获取用户信息")
    public R<BusinessAccountInfoVO> getAccountInfo() {
        if (SecurityUtils.getLoginUserType().equals(UserTypeConstants.USER)) {
            businessAccountService.refreshToken();
            final BusinessAccountVO businessAccountVO = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO();
            if (UserStatus.DELETED.getCode().equals(String.valueOf(businessAccountVO.getUserStatus()))) {
                throw new ServiceException("对不起，您的账号：" + businessAccountVO.getAccount() + " 已被删除，如需恢复，请联系商家~");
            }
            if (UserStatus.DISABLE.getCode().equals(String.valueOf(businessAccountVO.getUserStatus()))) {
                throw new ServiceException("您的账户已被禁用!");
            }
            BusinessAccountInfoVO businessAccountInfoVO = BeanUtil.copyProperties(businessAccountVO, BusinessAccountInfoVO.class);
            businessAccountInfoVO.setPhone(businessAccountService.getPhone(businessAccountInfoVO.getPhone()));
            return R.ok(businessAccountInfoVO);
        }
        return R.ok();
    }


    @PostMapping("/refreshToken")
    @ApiOperation("刷新token")
    @InnerAuth
    public R<String> refreshToken() {
        if (SecurityUtils.getLoginUserType().equals(UserTypeConstants.USER)) {
            businessAccountService.refreshToken();
        }
        return R.ok();
    }

    @GetMapping("/modelLoginForAccount")
    @ApiOperation("账户登录")
    public R<String> modelLoginForAccount(String account) {
        modelService.loginForAccount(account);
        return R.ok();
    }


    /**
     * 获取模特信息详细信息
     */
    @GetMapping(value = "/getModelByModelAccount/{account}")
    @ApiOperation(value = "获取模特信息详细信息", response = ModelVO.class)
    public R<LoginModel> getModelByModelAccount(@PathVariable("account") String account) {
        ModelVO modelVO = modelService.selectModelByModelAccount(account);
        return R.ok(new LoginModel(modelVO));
    }

    /**
     * 模特登录-新
     */
    @GetMapping(value = "/getModelByModelLoginAccount/{account}")
    @ApiOperation(value = "获取模特信息详细信息", response = ModelVO.class)
    public R<LoginModel> getModelByModelLoginAccount(@PathVariable("account") String account) {
        ModelVO modelVO = modelService.selectModelByModelLoginAccount(account);
        return R.ok(new LoginModel(modelVO));
    }

    /**
     * 校验手机号是否绑定用户
     */
    @GetMapping("/phone/check")
    @ApiOperation("校验手机号是否绑定用户")
    public R<Boolean> phoneCheck(@RequestParam String phone) {
        Boolean phoneCheck = bizUserService.phoneCheck(phone);
        return R.ok(phoneCheck);
    }
}
