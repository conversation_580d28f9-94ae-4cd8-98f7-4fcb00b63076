package com.wnkx.biz.controller.business;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.*;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.business.balance.*;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.ruoyi.system.api.domain.vo.biz.business.*;
import com.ruoyi.system.api.domain.vo.biz.business.prepay.PrepayUnPayVO;
import com.ruoyi.system.api.domain.vo.order.MemberUnPayVO;
import com.ruoyi.system.api.domain.vo.order.RealTimeExchangeRateVO;
import com.wnkx.biz.business.service.IBusinessBalancePrepayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;


/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :商家控制层
 * @create :2024-06-24 09:10
 **/
@RestController
@RequestMapping("/business")
@Api(value = "商家预付款业务", tags = "商家预付款业务")
@RequiredArgsConstructor
@Validated
public class BusinessBalancePrepayController extends BaseController {
    private final IBusinessBalancePrepayService businessBalancePrepayService;

    @PostMapping("/backend/businessBalancePrepay/save")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "添加商家余额预付数据（运营端）")
    @RequiresPermissions("merchant:balance:add-imprest")
    public R<String> insertBusinessBalancePrepay(@RequestBody @Valid BusinessBalancePrepayDTO dto) {
        businessBalancePrepayService.insertBusinessBalancePrepay(dto);
        return R.ok();
    }
    /**
     * 获取预付款审核表列表（运营端）
     */
    @GetMapping("/backend/businessBalancePrepay/list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取预付款审核表列表（运营端）")
    @RequiresPermissions("finance:prepay:list")
    public R<PageInfo<BusinessBalancePrepayVO>> businessBalancePrepayList(BusinessBalancePrepayListDTO dto) {
        List<BusinessBalancePrepayVO> businessBalancePrepayVOS = businessBalancePrepayService.queryList(dto);
        businessBalancePrepayService.load(businessBalancePrepayVOS);
        return R.ok(toPage(businessBalancePrepayVOS));
    }

    /**
     * 导出预付款（运营端）
     */
    @PostMapping("/backend/businessBalancePrepay/list/export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "导出预付款（运营端）")
    @RequiresPermissions("finance:prepay:export")
    public void exportBusinessBalancePrepayList(BusinessBalancePrepayListDTO dto, HttpServletResponse response) {
        List<BusinessBalancePrepayExportVO> businessBalancePrepayExportVOS = businessBalancePrepayService.exportBusinessBalancePrepayList(dto);

        ExcelUtil<BusinessBalancePrepayExportVO> util = new ExcelUtil<>(BusinessBalancePrepayExportVO.class);
        ExcelUtil.setAttachmentResponseHeader(response, "预付款列表导出");
        util.exportExcel(response, businessBalancePrepayExportVOS, "预付款列表导出");
    }

    /**
     * 获取预付款审核详情（运营端）
     */
    @GetMapping("/backend/businessBalancePrepay/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取预付款审核详情（运营端）", response = PageInfo.class)
    @RequiresPermissions(value = {
            "finance:prepay:detail",
            "finance:prepay:audit",
            "finance:prepay:reaudit"
    }, logical = Logical.OR)
    public R<BusinessBalancePrepayDetailVO> businessBalancePrepayDetailVO(@PathVariable("id") Long id) {
        return R.ok(businessBalancePrepayService.getDetailById(id));
    }

    /**
     * 获取预付款审核统计（运营端）
     */
    @GetMapping("/backend/businessBalancePrepay/statistics")
    @RequiresPermissions("finance:prepay:list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取预付款审核统计（运营端）")
    public R<BusinessBalancePrepayStatisticsVO> getStatistics() {
        return R.ok(businessBalancePrepayService.getStatistics());
    }


    @PostMapping("/inner/businessBalancePrepay/list")
    @ApiOperation(value = "获取预付款审核表列表（内部使用）")
    @InnerAuth
    public R<List<BusinessBalancePrepayVO>> innerBusinessBalancePrepayList(@RequestBody BusinessBalancePrepayListDTO dto) {
        return R.ok(businessBalancePrepayService.innerQueryList(dto));
    }

    @GetMapping("/inner/businessBalancePrepay/statistics")
    @ApiOperation(value = "获取预付款审核统计（内部端）")
    @InnerAuth
    public R<BusinessBalancePrepayStatisticsVO> getInnerStatistics() {
        return R.ok(businessBalancePrepayService.getStatistics());
    }

    @PostMapping("/businessBalancePrepay/online/save")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "创建充值订单（商家端）")
    @MemberAuth
    public R<BusinessBalancePrepay> initBusinessBalancePrepay(@RequestBody @Valid OnlineBusinessBalancePrepayDTO dto) {
        return R.ok(businessBalancePrepayService.initBusinessBalancePrepay(dto));
    }

    @GetMapping("/businessBalancePrepay/online/list")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "钱包充值订单记录（商家端）", response = PageInfo.class)
    public R<PageInfo<BusinessBalancePrepay>> onlineBusinessBalancePrepay() {
        return R.ok(toPage(businessBalancePrepayService.onlineBusinessBalancePrepay()));
    }
    @GetMapping("/businessBalancePrepay/online/{id}")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "获取充值详情（商家端）")
    public R<BusinessBalancePrepay> onlineDetail(@PathVariable("id") Long id) {
        return R.ok(businessBalancePrepayService.getById(id));
    }

    @PostMapping(value = "/businessBalancePrepay/online/submit-credential")
    @ApiOperation(value = "提交凭证信息")
    public R<String> submitCredential(@RequestBody @Validated OnlineRechargeSubmitCredentialDTO dto) {
        dto.setIsAnother(StatusTypeEnum.NO.getCode());
        businessBalancePrepayService.submitCredential(dto);
        return R.ok();
    }

    @GetMapping("/online/getOnlineUnPay")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "查询线上钱包充值未支付数据", response = MemberUnPayVO.class)
    public R<PrepayUnPayVO> getOnlineUnPay() {
        return R.ok(businessBalancePrepayService.getOnlineUnPay(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId()));
    }

    @PostMapping(value = "/online/cancel")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "取消线上钱包充值订单")
    public R<String> cancelOnlineOrder(@ApiParam(value = "订单Id") @RequestParam Long id) {
        businessBalancePrepayService.cancelOnlineOrder(id);
        return R.ok();
    }

    @PostMapping(value = "/getCurrentExchange")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取当前汇率接口")
    public R<RealTimeExchangeRateVO> getCurrentExchange() {
        return R.ok(businessBalancePrepayService.getCurrentExchange());
    }

    @PostMapping(value = "/inner/online/cancel")
    @ApiOperation(value = "取消线上钱包充值订单")
    @InnerAuth
    public R<String> innerCancelOnlineOrder(@ApiParam(value = "订单Id") @RequestParam Long id) {
        businessBalancePrepayService.cancelOnlineOrder(id);
        return R.ok("取消成功");
    }


    @PostMapping("/inner/businessBalancePrepay/updateAppId")
    @ApiOperation(value = "修改钱包充值appid（内部使用）")
    @InnerAuth
    public R<String> innerBusinessBalancePrepayUpdateAppId(@RequestBody @Valid PrepayUpdateAppIdDTO dto) {
        businessBalancePrepayService.updateAppId(dto);
        return R.ok("修改成功");
    }

    @GetMapping("/inner/businessBalancePrepay/online/{prepayNum}")
    @ApiOperation(value = "获取充值详情（内部端）")
    @InnerAuth
    public R<BusinessBalancePrepay> innerGetOnlineDetailByPrepayNum(@PathVariable("prepayNum") String prepayNum) {
        return R.ok(businessBalancePrepayService.getByPreNum(prepayNum));
    }

    @PostMapping(value = "/inner/businessBalancePrepay/online/submit-credential")
    @ApiOperation(value = "提交凭证信息（内部端）")
    @InnerAuth
    public R<String> innerSubmitCredential(@RequestBody @Validated OnlineRechargeSubmitCredentialDTO dto) {
        businessBalancePrepayService.submitCredential(dto);
        return R.ok("提交凭证成功");
    }

    @PostMapping("/inner/businessBalancePrepay/online/updateBatchFieldNullToNull")
    @InnerAuth
    public R<String> updateBatchFieldNullToNull(@RequestBody Collection<String> orderNums) {
        businessBalancePrepayService.updateBatchFieldNullToNull(orderNums);
        return R.ok("修改成功");
    }
    
}
