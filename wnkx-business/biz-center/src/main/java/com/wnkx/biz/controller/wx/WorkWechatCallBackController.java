package com.wnkx.biz.controller.wx;

import com.wnkx.biz.wechat.service.WechatCallBackService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 微信业务回调接口
 *
 * <AUTHOR>
 * @date 2024/6/18
 */
@RestController
@RequestMapping("/callback/workWechat/api")
@Api(value = "微信业务", tags = "企业微信业务")
@RequiredArgsConstructor
public class WorkWechatCallBackController {

    private final WechatCallBackService wechatCallBackService;

    /**
     * 此接口为企业微信验证回调url是否正确接口 正常业务无需调用
     */
    @GetMapping("")
    public String verify(
            @RequestParam("msg_signature") String sVerifyMsgSig,
            @RequestParam("timestamp") String sVerifyTimeStamp,
            @RequestParam("nonce") String sVerifyNonce,
            @RequestParam("echostr") String sVerifyEchoStr
    ) {
        return wechatCallBackService.verify(sVerifyMsgSig, sVerifyTimeStamp, sVerifyNonce, sVerifyEchoStr);
    }

    /**
     * 此接口为企业微信回调接口
     */
    @PostMapping()
    public void notifyWorkWechatChange(
            @RequestParam("msg_signature") String sVerifyMsgSig,
            @RequestParam("timestamp") String sVerifyTimeStamp,
            @RequestParam("nonce") String sVerifyNonce,
            @RequestBody() String postData
    ) {
        wechatCallBackService.callBack(sVerifyMsgSig, sVerifyTimeStamp, sVerifyNonce, postData);
    }


}
