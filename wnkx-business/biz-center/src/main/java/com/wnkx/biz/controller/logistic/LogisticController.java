package com.wnkx.biz.controller.logistic;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.dto.biz.logistic.LogisticListDTO;
import com.ruoyi.system.api.domain.vo.LogisticInfoVO;
import com.ruoyi.system.api.domain.vo.LogisticVO;
import com.ruoyi.system.api.domain.vo.ModelEndLogisticVO;
import com.wnkx.biz.logistic.service.ILogisticService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.List;

/**
 * 物流信息Controller
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@RestController
@RequestMapping("/logistic")
@Api(value = "物流信息服务", tags = "物流信息服务")
@RequiredArgsConstructor
public class LogisticController extends BaseController {

    private final ILogisticService logisticService;

    /**
     * 注册物流单号
     */
    @ApiOperation("注册物流单号")
    @PostMapping("register")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @InnerAuth
    public R<Boolean> register(@RequestBody Collection<String> numbers) {
        try {
            logisticService.register(numbers);
            return R.ok(true);
        } catch (Exception e) {
            return R.ok(false);
        }
    }

    /**
     * 接收API推送的物流信息
     *
     * @param request 请求
     */
    @ApiOperation("接收API推送的物流信息")
    @PostMapping("/notify")
    public void notify(HttpServletRequest request) {
        logisticService.notify(request);
    }

    /**
     * 通过物流单号查询物流信息
     *
     * @return 物流信息
     */
    @PostMapping("/list")
    @InnerAuth
    public R<List<LogisticVO>> selectListByNumbers(@RequestBody Collection<String> numbers) {
        List<LogisticVO> logisticVO = logisticService.selectListByNumbers(numbers);
        return R.ok(logisticVO);
    }

    /**
     * 通过条件查询物流单号
     */
    @PostMapping("/get-numbers")
    @InnerAuth
    public R<Collection<String>> getNumbersByCondition(@RequestBody LogisticListDTO dto) {
        Collection<String> logisticVO = logisticService.getNumbersByCondition(dto);
        return R.ok(logisticVO);
    }

    /**
     * 通过物流单号获取最新的物流信息
     */
    @PostMapping("/get-last-logistic-info")
    @InnerAuth
    public R<List<LogisticInfoVO>> getLastLogisticInfo(@RequestBody Collection<String> numbers) {
        List<LogisticInfoVO> list = logisticService.getLastLogisticInfo(numbers);
        return R.ok(list);
    }

    /**
     * 通过物流单号获取物流信息（模特端 时区为UTC+0）
     */
    @GetMapping("/get-logistic-info")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    public R<ModelEndLogisticVO> getLogisticInfo(@RequestParam String number) {
        LogisticVO logisticVO = logisticService.getLogisticInfo(number);
        return R.ok(BeanUtil.copyProperties(logisticVO, ModelEndLogisticVO.class));
    }
}
