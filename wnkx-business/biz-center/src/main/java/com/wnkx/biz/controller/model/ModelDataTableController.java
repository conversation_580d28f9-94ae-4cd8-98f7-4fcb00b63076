package com.wnkx.biz.controller.model;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableAddRemarkDTO;
import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableListDTO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableDetailVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableListVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableRemarkVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableServiceUserVO;
import com.wnkx.biz.model.service.ModelDataTableRemarkService;
import com.wnkx.biz.model.service.ModelDataTableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/4 18:01
 */
@RestController
@RequestMapping("/model-data-table")
@Api(value = "模特数据表信息服务", tags = "模特数据表信息服务")
@Validated
@RequiredArgsConstructor
public class ModelDataTableController extends BaseController {

    private final ModelDataTableService modelDataTableService;
    private final ModelDataTableRemarkService modelDataTableRemarkService;

    /**
     * 查询模特数据表列表
     */
    @GetMapping("/list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询模特数据表列表", response = ModelDataTableListVO.class)
    public R<PageInfo<ModelDataTableListVO>> selectModelDataTableListByCondition(@Validated ModelDataTableListDTO modelDataTableListDTO) {
        List<ModelDataTableListVO> modelDataTableListVOList = modelDataTableService.selectModelDataTableListByConditionFromRedis(modelDataTableListDTO);
        PageInfo<ModelDataTableListVO> pageInfo = new PageInfo<>();
        pageInfo.setTotal(modelDataTableListVOList.size());
        pageInfo.setRows(PageUtils.getPage(modelDataTableListVOList));
        return R.ok(pageInfo);
    }

    /**
     * 模特数据表列表-客服下拉框
     */
    @GetMapping("/list/service-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "模特数据表列表-客服下拉框", response = ModelDataTableServiceUserVO.class)
    public R<List<ModelDataTableServiceUserVO>> getServiceSelect() {
        List<ModelDataTableServiceUserVO> list = modelDataTableService.getServiceSelect();
        return R.ok(list);
    }

    /**
     * 模特数据表列表-开发人下拉框
     */
    @GetMapping("/list/developer-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "模特数据表列表-开发人下拉框", response = ModelDataTableServiceUserVO.class)
    public R<List<ModelDataTableServiceUserVO>> getDeveloperSelect() {
        List<ModelDataTableServiceUserVO> list = modelDataTableService.getDeveloperSelect();
        return R.ok(list);
    }

    /**
     * 模特详情
     */
    @GetMapping("/detail")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "模特详情")
    public R<ModelDataTableDetailVO> getModelDataTableDetailVO(@RequestParam Long modelId) {
        ModelDataTableDetailVO modelDataTableDetailVO = modelDataTableService.getModelDataTableDetailVO(modelId);
        return R.ok(modelDataTableDetailVO);
    }

    /**
     * 添加备注
     */
    @PostMapping("/add-remark")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "添加备注")
    public R<Void> addRemark(@RequestBody @Validated ModelDataTableAddRemarkDTO dto) {
        modelDataTableRemarkService.saveModelDataTableRemark(dto);
        return R.ok();
    }

    /**
     * 查询备注
     */
    @GetMapping("/remark-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询备注")
    public R<PageInfo<ModelDataTableRemarkVO>> selectModelDataTableRemarkList(@RequestParam Long modelId) {
        List<ModelDataTableRemarkVO> list = modelDataTableRemarkService.selectModelDataTableRemarkList(modelId);
        return R.ok(toPage(list));
    }

    /**
     * 获取模特数据表最后更新时间
     */
    @GetMapping("/get-last-update-time")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取模特数据表最后更新时间")
    public R<String> getModelDataTableLastUpdateTime(ModelDataTableListDTO modelDataTableListDTO) {
        String lastUpdateTime = modelDataTableService.getModelDataTableLastUpdateTime(modelDataTableListDTO);
        return R.ok(lastUpdateTime);
    }
}
