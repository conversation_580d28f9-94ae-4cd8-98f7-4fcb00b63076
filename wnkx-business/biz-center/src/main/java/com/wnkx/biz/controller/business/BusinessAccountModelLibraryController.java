package com.wnkx.biz.controller.business;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.log.enums.OperatorType;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountCollectModelDTO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountCollectModelVO;
import com.wnkx.biz.business.service.IBusinessAccountModelLibraryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/27 11:08
 */
@RestController
@RequestMapping("/business/account")
@Api(value = "商家端-模特库", tags = "商家端-模特库")
@RequiredArgsConstructor
public class BusinessAccountModelLibraryController extends BaseController {

    private final IBusinessAccountModelLibraryService modelMerchantService;

    /**
     * 商家端-模特库-模特列表
     */
    @GetMapping("/model-list")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "商家端-模特库-模特列表", response = PageInfo.class)
    @Log(title = "模特列表", businessType = BusinessType.OTHER, operatorType = OperatorType.OTHER, isSaveResponseData = false)
    public R<PageInfo<BusinessAccountCollectModelVO>> selectBusinessAccountModelListByCondition(BusinessAccountCollectModelDTO businessAccountCollectModelDTO) {
        List<BusinessAccountCollectModelVO> list = modelMerchantService.selectBusinessAccountModelListByCondition(businessAccountCollectModelDTO);
        return R.ok(toPage(list));
    }

    /**
     * 商家端-模特库-模特列表-获取模特信息详细信息
     */
    @GetMapping(value = "/model/{id}")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "商家端-模特库-模特列表-获取模特信息详细信息", response = BusinessAccountCollectModelVO.class)
    public R<BusinessAccountCollectModelVO> getModelInfo(@ApiParam("模特id") @PathVariable Long id,
                                                         @ApiParam("关键字") @RequestParam(required = false) String keyword,
                                                         @ApiParam("擅长品类") @RequestParam(required = false) List<Long> specialtyCategory) {
        BusinessAccountCollectModelVO info = modelMerchantService.getModelInfo(id, keyword, specialtyCategory);
        return R.ok(info);
    }

    /**
     * 商家端-模特库-我的收藏-收藏 or 取消收藏
     */
    @PostMapping(value = "/collect-model")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "商家端-模特库-我的收藏-收藏 or 取消收藏")
    public R<String> collectModel(@ApiParam("模特id") @RequestParam Long id) {
        modelMerchantService.collectModel(id);
        return R.ok();
    }

    /**
     * 商家端-模特库-我的收藏-收藏模特列表
     */
    @GetMapping("/collect-model-list")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "商家端-模特库-我的收藏-收藏模特列表", response = PageInfo.class)
    public R<PageInfo<BusinessAccountCollectModelVO>> selectBusinessAccountCollectModelListByCondition(BusinessAccountCollectModelDTO businessAccountCollectModelDTO) {
        List<BusinessAccountCollectModelVO> list = modelMerchantService.selectBusinessAccountCollectModelListByCondition(businessAccountCollectModelDTO);
        return R.ok(toPage(list));
    }

    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation("判断选中模特是否处于暂无档期状态")
    @PostMapping("/modelIsSlotOrNo")
    public R<List<Long>> modelIsSlotOrNo(@RequestBody BusinessAccountCollectModelDTO dto) {
        return R.ok(modelMerchantService.modelIsSlotOrNo(dto));
    }
}
