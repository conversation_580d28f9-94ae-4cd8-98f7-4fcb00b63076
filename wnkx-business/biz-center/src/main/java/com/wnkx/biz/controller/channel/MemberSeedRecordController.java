package com.wnkx.biz.controller.channel;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionStatusDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.FlowMemberSeedRecordDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.WithdrawalDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.WithdrawalListDTO;
import com.ruoyi.system.api.domain.vo.biz.channel.fission.*;
import com.wnkx.biz.channel.service.IDistributionChannelService;
import com.wnkx.biz.channel.service.MemberSeedRecordService;
import com.wnkx.biz.channel.service.MemberSeedRecordWithdrawalService;
import com.wnkx.biz.config.MemberSeedRecordConfig;
import com.wnkx.biz.core.MemberSeedRecordWithdrawalCore;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-05-19 10:06
 **/
@RestController
@RequestMapping("/member-seed-record")
@Api(value = "会员种草记录", tags = "会员种草记录信息服务")
@RequiredArgsConstructor
public class MemberSeedRecordController extends BaseController {
    private final IDistributionChannelService distributionChannelService;
    private final MemberSeedRecordService memberSeedRecordService;
    private final MemberSeedRecordWithdrawalService memberSeedRecordWithdrawalService;
    private final MemberSeedRecordConfig memberSeedRecordConfig;
    private final MemberSeedRecordWithdrawalCore memberSeedRecordWithdrawalCore;


    @GetMapping("/getFissionStatisticsVO")
    @ApiOperation(value = "会员种草记录金额统计(渠道端)")
    @LoginUserType(userTypes = UserTypeConstants.CHANNEL_TYPE)
    public R<FissionAmountStatisticsVO> getFissionStatisticsVO() {
        return R.ok(memberSeedRecordService.getFissionStatisticsVO(SecurityUtils.getUserId()));
    }

    @GetMapping("/memberSeedRecordList")
    @ApiOperation(value = "获取种草转化记录列表（渠道端）", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.CHANNEL_TYPE)
    public R<PageInfo<MemberSeedRecordVO>> memberSeedRecordList() {
        return R.ok(toPage(memberSeedRecordService.memberSeedRecordList(SecurityUtils.getUserId())));
    }
    @GetMapping("/queryMemberSeedRecordWithdrawalList")
    @ApiOperation(value = "获取提现记录列表（渠道端）", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.CHANNEL_TYPE)
    public R<PageInfo<MemberSeedRecordWithdrawalVO>> queryMemberSeedRecordWithdrawalListByChannelId() {
        return R.ok(toPage(memberSeedRecordWithdrawalService.queryMemberSeedRecordWithdrawalListByChannelId(SecurityUtils.getUserId())));
    }

    @GetMapping("/getMemberSeedRecordWithdrawalDetail/{id}")
    @ApiOperation(value = "获取提现记录详情（渠道端）")
    @LoginUserType(userTypes = UserTypeConstants.CHANNEL_TYPE)
    public R<MemberSeedRecordWithdrawalVO> getMemberSeedRecordWithdrawalDetail(@PathVariable Long id) {
        return R.ok(memberSeedRecordWithdrawalService.getMemberSeedRecordWithdrawalDetail(id));
    }

    @GetMapping("/getLastWithdrawalDetailByAccountType")
    @ApiOperation(value = "根据提现类型获取上次提现信息（渠道端）")
    @LoginUserType(userTypes = UserTypeConstants.CHANNEL_TYPE)
    public R<MemberSeedRecordWithdrawalVO> getLastWithdrawalDetailByAccountType(@RequestParam(required = false) Integer accountType) {
        return R.ok(memberSeedRecordWithdrawalService.getLastWithdrawalDetailByAccountType(accountType, SecurityUtils.getUserId()));
    }

    @PostMapping("/withdrawal")
    @ApiOperation(value = "发起提现")
    @LoginUserType(userTypes = UserTypeConstants.CHANNEL_TYPE)
    public R<WithdrawalVO> withdrawal(@RequestBody @Validated WithdrawalDTO dto) {
        dto.setChannelId(SecurityUtils.getUserId());
        return R.ok(memberSeedRecordService.withdrawal(dto));
    }

    @GetMapping("/checkWithdrawal")
    @ApiOperation(value = "检查是否可提现")
    @LoginUserType(userTypes = UserTypeConstants.CHANNEL_TYPE)
    public R<Boolean> checkWithdrawal() {
        return R.ok(memberSeedRecordService.checkWithdrawal(SecurityUtils.getUserId()));
    }

    /**
     * -------------------------------------内部端--------------------------------------------
     */
    @GetMapping("/backend/getFissionStatisticsVO")
    @ApiOperation(value = "会员种草记录金额统计（运营端）")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<FissionAmountStatisticsVO> getBackendFissionStatisticsVO() {
        return R.ok(memberSeedRecordService.getFissionStatisticsVO(null));
    }

    @GetMapping("/backend/getMemberSeedRecordWithdrawalDetail/{id}")
    @ApiOperation(value = "获取提现记录详情（运营端）")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("finance:zhongcao:detail")
    public R<MemberSeedRecordWithdrawalVO> getBackendMemberSeedRecordWithdrawalDetail(@PathVariable Long id) {
        return R.ok(memberSeedRecordWithdrawalService.getMemberSeedRecordWithdrawalDetail(id));
    }


    @GetMapping("/backend/queryMemberSeedRecordWithdrawalList")
    @ApiOperation(value = "获取裂变拉新结算列表（运营端）", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<PageInfo<MemberSeedRecordWithdrawalVO>> queryMemberSeedRecordWithdrawalList(WithdrawalListDTO dto) {
        return R.ok(toPage(memberSeedRecordWithdrawalService.queryMemberSeedRecordWithdrawalList(dto)));
    }

    @PostMapping("/backend/queryMemberSeedRecordWithdrawalList/export")
    @ApiOperation(value = "种草官结算导出（运营端）")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("finance:zhongcao:export")
    public void queryMemberSeedRecordWithdrawalListExport(WithdrawalListDTO dto, HttpServletResponse response) {
        List<MemberSeedRecordWithdrawalExportVO> memberSeedRecordWithdrawalExportVOS = memberSeedRecordWithdrawalService.queryMemberSeedRecordWithdrawalListExport(dto);
        ExcelUtil<MemberSeedRecordWithdrawalExportVO> util = new ExcelUtil<>(MemberSeedRecordWithdrawalExportVO.class);
        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "种草官结算导出");
        util.exportExcel(response, memberSeedRecordWithdrawalExportVOS, "种草官结算导出");
    }

    @GetMapping("/backend/getFissionCountStatisticsVO")
    @ApiOperation(value = "获取裂变拉新结算数量统计(运营端)")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<FissionCountStatisticsVO> getFissionCountStatisticsVO() {
        return R.ok(memberSeedRecordWithdrawalService.getFissionCountStatisticsVO());
    }

    @GetMapping("/backend/queryFissionSettleRecordList")
    @ApiOperation(value = "获取裂变结算记录（运营端）", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("finance:fissionRecord:list")
    public R<PageInfo<FissionSettleRecordVO>> queryFissionSettleRecordList(WithdrawalListDTO dto) {
        return R.ok(toPage(memberSeedRecordWithdrawalService.queryFissionSettleRecordList(dto)));
    }


    @PostMapping("/backend/queryFissionSettleRecordList/export")
    @ApiOperation(value = "获取种草结算记录导出（运营端）")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("finance:fissionRecord:export")
    public void queryFissionSettleRecordListExport(WithdrawalListDTO dto, HttpServletResponse response) {
        List<FissionSettleRecordExportVO> fissionSettleRecordExportVOS = memberSeedRecordWithdrawalService.queryFissionSettleRecordListExport(dto);
        ExcelUtil<FissionSettleRecordExportVO> util = new ExcelUtil<>(FissionSettleRecordExportVO.class);
        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "财务管理种草明细导出");
        util.exportExcel(response, fissionSettleRecordExportVOS, "财务管理种草明细导出");
    }


    @ApiOperation("修改裂变渠道状态")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @PutMapping("/backend/updateStatus")
    @RequiresPermissions("channel:fission:switch")
    public R<String> updateStatus(@RequestBody @Validated DistributionStatusDTO dto) {
        distributionChannelService.updateStatus(dto);
        return R.ok();
    }

    @ApiOperation("打款账号列表")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @GetMapping("/backend/payAccountList")
    public R<List<String>> payAccountList() {
        return R.ok(memberSeedRecordConfig.getPayAccount());
    }

    @ApiOperation("审核种草结算记录")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @PutMapping("/backend/auditMemberSeedRecord")
    @RequiresPermissions(value = {"finance:zhongcao:through", "finance:zhongcao:refuse", "finance:zhongcao:batchThrough", "finance:zhongcao:batchRefuse"}, logical = Logical.OR)
    public R<String> auditMemberSeedRecord(@RequestBody @Validated FlowMemberSeedRecordDTO dto) {
        memberSeedRecordWithdrawalCore.flowMemberSeedRecord(dto);
        return R.ok();
    }

    @ApiOperation("打款种草结算记录")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @PutMapping("/backend/transferMemberSeedRecord")
    @RequiresPermissions(value = {"finance:zhongcao:markPayment", "finance:zhongcao:markAnomaly", "finance:zhongcao:batchPayment", "finance:zhongcao:batchAnomaly"}, logical = Logical.OR)
    public R<String> transferMemberSeedRecord(@RequestBody @Validated FlowMemberSeedRecordDTO dto) {
        memberSeedRecordWithdrawalCore.flowMemberSeedRecord(dto);
        return R.ok();
    }

}
