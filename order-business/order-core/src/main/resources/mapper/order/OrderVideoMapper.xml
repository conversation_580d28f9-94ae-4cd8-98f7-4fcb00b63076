<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.order.OrderVideo" id="OrderVideoResult">
        <result property="id"    column="id"    />
        <result property="orderNum"    column="order_num"    />
        <result property="videoCode"    column="video_code"    />
        <result property="status"    column="status"    />
        <result property="platform"    column="platform"    />
        <result property="isCare"    column="is_care"    />
        <result property="productChinese"    column="product_chinese"    />
        <result property="productEnglish"    column="product_english"    />
        <result property="productLink"    column="product_link"    />
        <result property="productPic"    column="product_pic"    />
        <result property="referenceVideoLink"    column="reference_video_link"    />
        <result property="videoFormat"    column="video_format"    />
        <result property="shootingCountry"    column="shooting_country"    />
        <result property="modelType"    column="model_type"    />
        <result property="isObject"    column="is_object"    />
        <result property="picCount"    column="pic_count"    />
        <result property="intentionModelId"    column="intention_model_id"    />
        <result property="shootModelId"    column="shoot_model_id"    />
        <result property="logisticFlag"    column="logistic_flag"    />
        <result property="contactId"    column="contact_id"    />
        <result property="issueId"    column="issue_id"    />
        <result property="amount"    column="amount"    />
        <result property="videoPrice"    column="video_price"    />
        <result property="picPrice"    column="pic_price"    />
        <result property="exchangePrice"    column="exchange_price"    />
        <result property="servicePrice"    column="service_price"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOrderVideoVo">
        select ov.id,  ov.order_num, ov.video_code, ov.amount_dollar, ov.`status`, ov.status_time, ov.platform,ov.is_care, ov.product_chinese, ov.product_english,
               ov.product_link, ov.product_pic, ov.reference_video_link, ov.video_format, ov.shooting_country,ov.rollback_id,
               ov.model_type, ov.is_object, ov.pic_count,ov.refund_pic_count,ov.commission_pays_taxes, ov.reference_pic as reference_pic_id,ov.intention_model_id, ov.shoot_model_id,
               ov.logistic_flag, ov.contact_id, ov.issue_id, ov.amount,ov.video_promotion_amount,ov.pay_amount,ov.pay_amount_dollar, ov.video_price,ov.pic_price,ov.exchange_price,ov.exchange_price,ov.service_price,ov.video_duration,
               ov.un_confirm_time,ov.un_finished_time,ov.create_order_business_id,ov.create_order_biz_user_id,
               ov.create_order_user_id,ov.create_order_user_account,ov.create_order_user_name,ov.create_order_user_nick_name,ov.shipping_remark,ov.create_time, ov.update_time,ov.is_gund
        from order_video ov
    </sql>
    <sql id="getOrderVideoCurrentInfo">
        select id,platform,video_format,shooting_country,model_type,reference_pic as reference_pic_id,product_chinese,product_english,
               product_link,product_pic,reference_video_link,intention_model_id,video_duration,video_style
        from order_video
    </sql>

    <select id="selectAccountOrderList" resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoVO">
        select ov.id,  ov.order_num, ov.video_code, ov.amount_dollar, ov.`status`, ov.status_time, ov.platform,ov.is_care, ov.product_chinese, ov.product_english,
        ov.product_link, ov.product_pic, ov.reference_video_link, ov.video_format, ov.shooting_country,ov.rollback_id,
        ov.model_type, ov.is_object, ov.pic_count,ov.refund_pic_count,ov.commission_pays_taxes, ov.reference_pic as reference_pic_id,ov.intention_model_id, ov.shoot_model_id,
        ovmsa.logistic_flag, ov.contact_id, ov.issue_id, ov.amount,ov.video_promotion_amount,ov.pay_amount,ov.pay_amount_dollar, ov.video_price,ov.pic_price,ov.exchange_price,ov.exchange_price,ov.service_price,ov.video_duration,
        ov.create_order_user_id,ov.create_order_user_account,ov.create_order_user_name,ov.create_order_user_nick_name,ov.shipping_remark,ov.create_time, ov.update_time
        from order_video ov
        left join order_video_model_shipping_address ovmsa on ovmsa.video_id = ov.id AND (
        ov.rollback_id = ovmsa.rollback_id
        OR
        (
        ov.rollback_id IS NULL
        AND ovmsa.rollback_id IS NULL
        )
        )
        <where>
            ov.create_order_business_id = #{dto.merchantId}
            and ov.status in (${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getcode},${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_CONFIRM.getcode},${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode},${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode})
            <if test="dto.createOrderUserAccount != null and dto.createOrderUserAccount != ''">and ov.create_order_user_account = #{dto.createOrderUserAccount}</if>
        </where>
        order by status_time desc
        limit 5
    </select>

    <select id="selectChineseUnConfirmList" resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoVO">
        select
        ov.video_code,
        ov.product_chinese,
        ov.product_english,
        ov.pic_count,
        ov.create_order_user_id,ov.create_order_user_account,ov.create_order_user_name,ov.create_order_user_nick_name,
        ov.is_care,
        ov.intention_model_id
        from order_video ov
        <where>
            ov.status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_CONFIRM.getcode}
            <if test="userId != null">
                and (ov.contact_id = #{userId} OR ov.issue_id = #{userId})
            </if>
        </where>
        order by ov.status_time desc
    </select>
    <select id="selectEnglishUnMatchList" resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoVO">
        select
        ov.video_code,
        ov.product_chinese,
        ov.product_english,
        ov.pic_count,
        ov.create_order_user_id,ov.create_order_user_account,ov.create_order_user_name,ov.create_order_user_nick_name,
        ov.is_care,
        ov.intention_model_id
        from order_video_match_preselect_model ovmpm
        JOIN (
        SELECT id,video_id,status,
        ROW_NUMBER() OVER (PARTITION BY video_id ORDER BY id DESC) AS rn
        FROM order_video_match
        ) ovm ON ovm.id = ovmpm.match_id AND ovm.rn = 1
        join order_video ov on ov.id = ovm.video_id
        <where>
            ov.status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getcode}
            AND ovmpm.add_type = ${@com.ruoyi.common.core.enums.PreselectModelAddTypeEnum@INTENTION_MODEL.getcode}
            AND ovmpm.status = ${@com.ruoyi.common.core.enums.PreselectStatusEnum@UN_JOINTED.getcode}
            <if test="matchStatus != null">
                and ovm.status = #{matchStatus}
            </if>
            <if test="modelIds != null and modelIds.size() > 0 ">
                AND ovmpm.model_id IN
                <foreach collection="modelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by ov.status_time desc
    </select>

    <select id="selectEnglishCloseList" resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoVO">
        select
        ov.video_code,
        ov.product_chinese,
        ov.product_english,
        ov.pic_count,
        ov.status_time,
        ov.create_order_user_id,ov.create_order_user_account,ov.create_order_user_name,ov.create_order_user_nick_name,
        ov.is_care,
        ov.shoot_model_id
        from order_video ov
        <where>
            ov.status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode}
             and ov.need_confirm_time is null
            <choose>
                <when test="userId != null">
                    AND ov.issue_id = #{userId}
                </when>
                <otherwise>
                    AND ov.issue_id is not null
                </otherwise>
            </choose>
        </where>
        order by ov.status_time desc
    </select>

    <select id="selectEnglishPauseMatchList" resultType="com.ruoyi.system.api.domain.vo.order.workbench.PauseMatchVO">
        select
        ov.video_code,
        ov.product_chinese,
        ov.product_english,
        max(ovmpm.oust_time) max_oust_time,
        ovm.pause_reason
        from order_video_match_preselect_model ovmpm
        JOIN (
            SELECT id ,video_id ,pause_reason ,status,
            ROW_NUMBER() OVER (PARTITION BY video_id ORDER BY id DESC) AS rn
            FROM order_video_match
        ) ovm ON ovm.id = ovmpm.match_id AND ovm.rn = 1
        join order_video ov on ov.id = ovm.video_id
        <where>
            ov.status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getcode}
            and ovm.status = ${@<EMAIL>}
            and ovmpm.is_pause_oust = ${@<EMAIL>}
            <if test="modelIds != null and modelIds.size() > 0 ">
                AND (
                ovmpm.model_id IN
                <foreach collection="modelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or ov.intention_model_id
                IN
                <foreach collection="modelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        </where>
        group by ov.video_code,
        ov.product_chinese,
        ov.product_english,
        ov.status_time,
        ovm.pause_reason
        order by max_oust_time desc
    </select>
    <select id="getWorkbenchStatisticsVO" resultType="com.ruoyi.system.api.domain.vo.order.WorkbenchVO">
        SELECT
        COUNT(CASE WHEN ov.STATUS = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_CONFIRM.getcode} THEN ov.order_num END) AS unConfirmCount,
        COUNT(CASE WHEN ov.STATUS = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getcode} and m.status = ${@<EMAIL>} THEN ov.order_num END) AS pauseMatchCount
        FROM order_video ov
        left JOIN (
            SELECT
                video_id,
                status,
                rollback_id,
            ROW_NUMBER() OVER (PARTITION BY video_id ORDER BY id DESC) AS rn
            FROM order_video_match
        ) m ON ov.id = m.video_id and (ov.rollback_id = m.rollback_id or ov.rollback_id is null ) AND m.rn = 1
        <where>
            <if test="dto.isAboutMe != null and dto.isAboutMe == 1">
                and (ov.contact_id = #{dto.userId} OR ov.issue_id = #{dto.userId})
            </if>
        </where>

    </select>

    <select id="getLogisticCount" resultType="java.lang.Integer">
        select
            count(*)
        from order_video ov
        <where>
            <if test="dto.videoIds != null and dto.videoIds.size() !=0">
                and ov.id in
                <foreach collection="dto.videoIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.videoStatus != null and dto.videoStatus.size() !=0">
                and ov.status in
                <foreach collection="dto.videoStatus" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.rollbackIds != null and dto.rollbackIds.size() !=0">
                and
                <foreach collection="dto.rollbackIds" item="item" open="(" separator="OR" close=")">
                    <if test="item == null ">
                        ov.rollback_id IS NULL
                    </if>
                    <if test="item != null ">
                        ov.rollback_id = #{item}
                    </if>
                </foreach>
            </if>
            <if test="dto.userId != null ">
                and (ov.contact_id = #{dto.userId} OR ov.issue_id = #{dto.userId})
            </if>
        </where>
    </select>

    <select id="selectOrderVideoListByCondition" resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoVO">
        <include refid="selectOrderVideoVo"/>
        <where>
            <if test="dto.care != null">
                and ov.is_care = #{dto.care}
            </if>
            <if test="dto.reminderStatus != null and dto.reminderStatus.size() != 0">
                AND (ov.`status` = 6
                <choose>
                    <when test="dto.reminderStatus.size() == 1 and dto.reminderStatus[0] == 2">
                        AND EXISTS (
                            SELECT 1
                            FROM order_video_reminder_record ovrr
                            WHERE ovrr.video_id = ov.id
                                AND NOT EXISTS (
                                    SELECT 1
                                    FROM order_video_reminder_record sub_ovrr
                                    WHERE sub_ovrr.video_id = ov.id
                                        AND sub_ovrr.`status` != #{dto.reminderStatus[0]}
                                )
                        )
                    </when>
                    <otherwise>
                        AND EXISTS (
                            SELECT 1
                            FROM order_video_reminder_record ovrr
                            WHERE ovrr.video_id = ov.id
                                AND ovrr.`status` IN
                                <foreach collection="dto.reminderStatus" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                        )
                    </otherwise>
                </choose>
                )
            </if>
            <if test="dto.keyword != null and dto.keyword != ''">
                and (
                ov.order_num like concat('%', #{dto.keyword}, '%')
                OR ov.video_code like concat('%', UCASE(#{dto.keyword}), '%')
                OR ov.product_chinese like concat('%', #{dto.keyword}, '%')
                OR ov.product_english like concat('%', #{dto.keyword}, '%')
                OR ov.product_link like concat('%', #{dto.keyword}, '%')
                OR ov.create_order_user_nick_name like concat('%', #{dto.keyword}, '%')
                OR ov.create_order_user_name like concat('%', #{dto.keyword}, '%')
                <if test="dto.modelIds != null and dto.modelIds.size() != 0">
                    OR ov.intention_model_id in
                    <foreach collection="dto.modelIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR ov.shoot_model_id in
                    <foreach collection="dto.modelIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>

                    <if test="dto.loginUserType == 0">
                        OR ov.id in (
                        select DISTINCT(ovm.video_id) from order_video_match ovm LEFT JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id where ovmpm.model_id in
                        <foreach collection="dto.modelIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                    </if>
                </if>

                <if test="dto.loginUserType == 0">
                    <if test="dto.platform != null and dto.platform.size() != 0">
                        OR ov.platform in
                        <foreach collection="dto.platform" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    OR ov.create_order_user_account like concat('%', #{dto.keyword}, '%')
                    OR ov.order_num in (select distinct order_num from order_table where merchant_code like concat('%', UCASE(#{dto.keyword}), '%'))
                    <if test="dto.searchBusinessIds != null and dto.searchBusinessIds.size() >0">
                    OR ov.order_num in (select distinct order_num from order_table where
                        merchant_id in
                        <foreach collection="dto.searchBusinessIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                    </if>
                </if>
                )
            </if>
            <if test="dto.orderNums != null and dto.orderNums.size() != 0">
                and ov.order_num in
                <foreach collection="dto.orderNums" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.orderNum != null and dto.orderNum != ''">
                and ov.order_num like concat('%', #{dto.orderNum}, '%')
            </if>

            <if test="dto.videoCode != null and dto.videoCode != ''">
                and ov.video_code like concat('%', UCASE(#{dto.videoCode}), '%')
            </if>

            <if test="dto.productName != null and dto.productName != ''">
                and (ov.product_chinese like concat('%', #{dto.productName}, '%') or ov.product_english like concat('%', #{dto.productName}, '%'))
            </if>

            <if test="dto.productLink != null and dto.productLink != ''">
                and ov.product_link like concat('%', #{dto.productLink}, '%')
            </if>

            <if test="dto.platform != null and dto.platform.size() != 0">
                and ov.platform in
                <foreach collection="dto.platform" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.shootModelId != null and dto.shootModelId.size() != 0">
                and ov.shoot_model_id in
                <foreach collection="dto.shootModelId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.contactId != null and dto.contactId.size() != 0">
                and ov.contact_id in
                <foreach collection="dto.contactId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.issueId != null and dto.issueId.size() != 0">
                and ov.issue_id in
                <foreach collection="dto.issueId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.status != null and dto.status.size() != 0">
                and ov.`status` in
                <foreach collection="dto.status" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.preselectAddUserId != null and dto.preselectAddUserId.size() != 0 ">
                and ov.id in (
                            select DISTINCT(ovm.video_id) from order_video_match ovm LEFT JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id where ovmpm.add_user_id in
                            <foreach collection="dto.preselectAddUserId" item="item" open="(" separator="," close=")">
                                 #{item}
                            </foreach>
                    )
            </if>

            <if test="dto.preselectAddUserName != null and dto.preselectAddUserName.size() != 0 ">
                and ov.id in (
                select DISTINCT(ovm.video_id) from order_video_match ovm LEFT JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id where ovmpm.model_person_name in
                <foreach collection="dto.preselectAddUserName" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test="dto.preselectModelId != null and dto.preselectModelId.size() != 0 ">
                and ov.id in (
                        select DISTINCT(ovm.video_id) from order_video_match ovm LEFT JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id where ovmpm.model_id in
                            <foreach collection="dto.preselectModelId" item="item" open="(" separator="," close=")">
                                 #{item}
                            </foreach>
                        AND ovmpm.status != 3
                    )
            </if>

            <if test="dto.preselectStatus != null and dto.preselectStatus.size() != 0 ">
                and ov.id in (
                        select DISTINCT(ovm.video_id) from order_video_match ovm LEFT JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id where ovmpm.status in
                            <foreach collection="dto.preselectStatus" item="item" open="(" separator="," close=")">
                                 #{item}
                            </foreach>
                    )
            </if>
--             兼容视频订单列表预选状态
            <if test="dto.newPreselectStatus != null and !dto.newPreselectStatus.isEmpty()">
                AND ov.id IN (
                SELECT DISTINCT ovm.video_id
                FROM order_video_match ovm
                LEFT JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
                <where>
                    <if test="dto.newPreselectStatus.contains(0) or dto.newPreselectStatus.contains(1) or dto.newPreselectStatus.contains(2)">
                        ovmpm.status IN
                        <foreach collection="dto.newPreselectStatus" item="item" open="(" separator="," close=")">
                            <if test="item == 0 or item == 1 or item == 2">
                                #{item}
                            </if>
                        </foreach>
                    </if>

                    <if test="dto.newPreselectStatus.contains(3) or dto.newPreselectStatus.contains(4) or dto.newPreselectStatus.contains(5) or dto.newPreselectStatus.contains(6) or dto.newPreselectStatus.contains(7) or dto.newPreselectStatus.contains(8)">
                        <if test="dto.newPreselectStatus.contains(0) or dto.newPreselectStatus.contains(1) or dto.newPreselectStatus.contains(2)">
                            OR
                        </if>
                        ovmpm.oust_type IN (
                        <foreach collection="dto.newPreselectStatus" item="item" separator="," >
                            <choose>
                                <when test="item == 3">1</when>
                                <when test="item == 4">2</when>
                                <when test="item == 5">3</when>
                                <when test="item == 6">4</when>
                                <when test="item == 7">5</when>
                                <when test="item == 8">6</when>
                            </choose>
                        </foreach>
                        )
                    </if>
                    <if test="dto.newPreselectStatus.contains(8)">
                       and ov.rollback_id IS NOT NULL
                    </if>
                </where>
                )
            </if>

            <if test="dto.replyContent !=null and dto.replyContent.size() != 0">
                and ov.id in (select DISTINCT(video_id) from order_video_case where reply_content in
                <foreach collection="dto.replyContent" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test="dto.videoIds != null and dto.videoIds.size() !=0">
                and ov.id in
                <foreach collection="dto.videoIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.statusDays != null">
                AND DATE(status_time) >= DATE_SUB(CURDATE(), INTERVAL #{dto.statusDays} DAY)
            </if>
            <if test="dto.newStatusDays != null">
                <choose>
                    <when test="dto.newStatusDays == 0">AND DATE(status_time) &gt;= DATE_SUB(CURDATE(), INTERVAL #{dto.newStatusDays} DAY)</when>
                    <when test="dto.newStatusDays == 3">
                        AND DATE(status_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL #{dto.newStatusDays} DAY)
                        AND DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                    </when>
                    <when test="dto.newStatusDays == 4">
                        AND DATE(status_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                        AND DATE_SUB(CURDATE(), INTERVAL #{dto.newStatusDays} DAY)
                    </when>
                    <otherwise>
                        AND DATE(status_time) &lt; DATE_SUB(CURDATE(), INTERVAL #{dto.newStatusDays} DAY)
                    </otherwise>
                </choose>
            </if>
            <if test="dto.refundVideoIds != null and dto.refundVideoIds.size() != 0">
                and ov.id not in
                <foreach collection="dto.refundVideoIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.modelType != null and dto.modelType.size() != 0">
                and ov.model_type in
                <foreach collection="dto.modelType" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.shootingCountry != null and dto.shootingCountry.size() >0">
                and ov.shooting_country in
                <foreach collection="dto.shootingCountry" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.videoId != null">
                and ov.videoId = #{dto.videoId}
            </if>

            <if test="dto.matchStartTime != null">
                AND date_format( ov.status_time, '%Y-%m-%d %H:%i:%s' ) BETWEEN #{dto.matchStartTimeBegin} AND #{dto.matchStartTimeEnd}
            </if>

            <if test="dto.createOrderUserName != null and dto.createOrderUserName.size() > 0 ">
                and ov.create_order_user_name in
                <foreach collection="dto.createOrderUserName" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.aboutMe == 'true' ">
                and (ov.contact_id = #{dto.backUserId} OR ov.issue_id = #{dto.backUserId})
            </if>

            <if test="dto.statusTimeBegin != null and dto.statusTimeEnd != null">
                AND ov.status_time BETWEEN #{dto.statusTimeBegin} AND #{dto.statusTimeEnd}
            </if>

            <if test="dto.matchStatus != null">
                AND ov.id IN (
                    SELECT
                        ovm.video_id
                    FROM
                        order_video_match ovm
                        JOIN ( SELECT video_id, MAX( start_time ) AS max_start_time FROM order_video_match GROUP BY video_id ) latest ON ovm.video_id = latest.video_id AND ovm.start_time = latest.max_start_time
                    WHERE ovm.`status` = #{dto.matchStatus}
                        <if test="dto.matchStatus == 1">
                            AND ovm.end_time IS NULL
                        </if>
                        <if test="dto.matchStatus == 2">
                            AND ovm.end_time IS NOT NULL
                        </if>
                )
            </if>
            <if test="dto.closeOrderStatus != null and dto.closeOrderStatus == 1">
                and ov.need_confirm_time is null and ov.issue_id is not null and ov.status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getCode}
            </if>

            <if test="dto.beforeStatusTime != null">
                and DATE ( ov.status_time ) &lt;= #{dto.beforeStatusTime}
            </if>
            <if test="dto.beforeStatusTimeStart != null and dto.beforeStatusTimeEnd != null ">
                AND ov.status_time BETWEEN #{dto.beforeStatusTimeStart} AND #{dto.beforeStatusTimeEnd}
            </if>

            <if test="dto.confirmReceiptTimeMap != null ">
                AND ov.id IN (
                        SELECT video_id
                        FROM (
                            SELECT video_id,sign_time as receipt_time,
                            ROW_NUMBER() OVER (PARTITION BY video_id ORDER BY create_time DESC) AS rn
                            FROM order_video_logistic_follow
                        ) AS ranked_logistic
                        WHERE rn = 1
                                <if test="dto.confirmReceiptTimeMap != null">
                                    AND (
                                    <foreach item="value" index="key" collection="dto.confirmReceiptTimeMap.entrySet()" separator=" or " >
                                        date_format(receipt_time, '%Y-%m-%d %H:%i:%s') BETWEEN #{key} AND #{value}
                                    </foreach>
                                    )
                                </if>
                                <if test="dto.beforeConfirmReceiptTime != null">
                                    AND DATE ( receipt_time ) &lt;= #{dto.beforeConfirmReceiptTime}
                                </if>
                )
            </if>

            <if test="dto.modelWaitOrder == true">
                AND (
                    ov.status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getCode}
                    OR (
                        ov.status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getCode}
                        AND EXISTS (
                            SELECT
                                1
                            FROM
                                order_video_task ovt
                                    JOIN order_video_task_detail ovtd ON ovtd.task_id = ovt.id
                            WHERE
                                ovt.video_id = ov.id
                                AND ovtd.`status` IN ( ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@UN_HANDLE.getCode},
                                                        ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@HANDLE_ING.getCode} )
                        )
                    )
                )
            </if>

            <if test="dto.hasPicCount != null ">
                <if test="dto.hasPicCount == true ">
                    AND ov.pic_count IS NOT NULL
                </if>
                <if test="dto.hasPicCount == false ">
                    AND ov.pic_count IS NULL
                </if>
            </if>

            <if test="dto.intentionModelIds != null and dto.intentionModelIds.size() > 0 ">
                AND ov.intention_model_id IN
                <foreach collection="dto.intentionModelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.isGunds != null and dto.isGunds.size() > 0 ">
                AND ov.is_gund IN
                <foreach collection="dto.isGunds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY
            <if test="dto.statusTimeSort != null and dto.statusTimeSort != ''">
                ov.status_time ${dto.statusTimeSort},
            </if>
            ov.create_time ASC,
            ov.id ASC
    </select>

    <select id="merchantOrderStatusCount" resultType="com.ruoyi.system.api.domain.vo.order.OrderStatusVO">
        SELECT
                COUNT(CASE WHEN ov.STATUS = #{statusMap.UN_PAY} THEN ov.order_num END) AS unPayCount,
                COUNT(CASE WHEN ov.STATUS = #{statusMap.UN_CHECK} THEN ov.order_num END) AS unCheckCount,
                COUNT(CASE WHEN ov.status = #{statusMap.UN_MATCH} OR ov.STATUS = #{statusMap.UN_CONFIRM} THEN ov.order_num END) AS unMatchCount,
                COUNT(CASE WHEN ov.STATUS = #{statusMap.NEED_FILLED} THEN ov.order_num END) AS unFilledCount,
                COUNT(CASE WHEN ov.STATUS = #{statusMap.UN_FINISHED} THEN ov.order_num END) AS unFinishedCount,
                COUNT(CASE WHEN ov.STATUS = #{statusMap.NEED_CONFIRM} THEN ov.order_num END) AS needConfirmCount,
                COUNT(CASE WHEN ov.STATUS = #{statusMap.FINISHED} THEN ov.order_num END) AS finishedCount
        FROM order_video ov
        WHERE ov.create_order_business_id = #{merchantId}
    </select>
    <select id="workbenchOrderStatusCount" resultType="com.ruoyi.system.api.domain.vo.order.OrderStatusVO">
        SELECT
                COUNT(CASE WHEN ov.STATUS = #{statusMap.UN_PAY} THEN ov.order_num END) AS unPayCount,
                COUNT(CASE WHEN ov.STATUS = #{statusMap.UN_CHECK} THEN ov.order_num END) AS unCheckCount,
                COUNT(CASE WHEN ov.status = #{statusMap.UN_MATCH} OR ov.STATUS = #{statusMap.UN_CONFIRM} THEN ov.order_num END) AS unMatchCount,
                COUNT(CASE WHEN ov.STATUS = #{statusMap.NEED_FILLED} THEN ov.order_num END) AS unFilledCount,
                COUNT(CASE WHEN ov.STATUS = #{statusMap.UN_FINISHED} THEN ov.order_num END) AS unFinishedCount,
                COUNT(CASE WHEN ov.STATUS = #{statusMap.NEED_CONFIRM} THEN ov.order_num END) AS needConfirmCount,
                COUNT(CASE WHEN ov.STATUS = #{statusMap.FINISHED} THEN ov.order_num END) AS finishedCount
        FROM order_video ov
        <where>
            ov.create_order_business_id = #{merchantId}
            <if test="createOrderUserAccount != null and createOrderUserAccount != ''">and ov.create_order_user_account = #{createOrderUserAccount}</if>
        </where>
    </select>

    <select id="orderVideoStatistics" resultType="com.ruoyi.system.api.domain.vo.OrderVideoStatisticsVO">
        SELECT
            count(ov.order_num) as 'orderVideoTotal',
            count(ov.status = 6 or null) as 'preFinishOrderTotal'
        from order_video ov
        left join order_table ot on ov.order_num = ot.order_num
        <where>
            ot.audit_status = ${@<EMAIL>}
            <if test="dto.statusList != null and dto.statusList.size() != 0">
                and ov.status in
                <foreach collection="dto.statusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.timeBegin != null and dto.timeEnd != null">
                and ov.create_time BETWEEN #{dto.timeBegin} AND #{dto.timeEnd}
            </if>
        </where>
    </select>

    <select id="orderVideoStatisticsDetail" resultType="com.ruoyi.system.api.domain.vo.OrderVideoStatisticsDetailVO">
        select
            ot.merchant_id as 'merchantId',
            count( DISTINCT ov.id ) as 'orderVideoTotal',
            count( DISTINCT CASE WHEN ov.STATUS = 6 OR ov.STATUS IS NULL THEN ov.id END ) as 'preFinishOrderTotal',
            ROUND( IFNULL( COUNT( DISTINCT CASE WHEN ( (ovt.id IS NOT NULL AND ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getcode} AND ovtd.status = ${@<EMAIL>} )
                                                           OR ( orf.id IS NOT NULL AND orf.refund_status = ${@com.ruoyi.common.core.enums.RefundStatusEnum@AFTER_SALE_FINISHED.getcode} )
                                                           OR ov.rollback_id IS NOT NULL )
                                                    AND ov.`status` != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode} THEN ov.id END )
            / NULLIF(COUNT(DISTINCT CASE
                    WHEN ovf.target_status IN (
                                                ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                                                ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                                                ${@<EMAIL>}
                                              )
                    AND ov.status != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode} THEN ov.id END), 0), 0 ), 4
            ) AS afterSaleRate
        from order_table ot
        inner join order_video ov on ot.order_num = ov.order_num
        LEFT JOIN order_video_task ovt ON ovt.video_id = ov.id
        LEFT JOIN order_video_task_detail ovtd on ovtd.task_id = ovt.id
        LEFT JOIN order_video_refund orf ON orf.video_id = ov.id
        LEFT JOIN order_video_flow ovf ON ovf.video_id = ov.id
        <where>
            ot.audit_status = ${@<EMAIL>}
            <if test="dto.statusList != null and dto.statusList.size() != 0">
                and ov.status in
                <foreach collection="dto.statusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.businessIds != null and dto.businessIds.size() != 0">
                and ot.merchant_id in
                <foreach collection="dto.businessIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.timeBegin != null and dto.timeEnd != null">
                and ov.create_time BETWEEN #{dto.timeBegin} AND #{dto.timeEnd}
            </if>
            <if test="dto.unConfirmTimeBegin != null and dto.unConfirmTimeEnd != null">
                and ov.un_confirm_time BETWEEN #{dto.unConfirmTimeBegin} AND #{dto.unConfirmTimeEnd}
            </if>
            <if test="dto.payTimeBegin != null and dto.payTimeEnd != null">
                and ot.pay_time BETWEEN #{dto.payTimeBegin} AND #{dto.payTimeEnd}
            </if>
        </where>
        GROUP BY ot.merchant_id
    </select>
    <select id="backOrderStatusCount" resultType="com.ruoyi.system.api.domain.vo.order.OrderStatusVO">
        SELECT
                COUNT(CASE WHEN ov.STATUS = #{statusMap.UN_CONFIRM} THEN ov.order_num END) AS unConfirmCount,
                COUNT(CASE WHEN ov.STATUS = #{statusMap.UN_MATCH} THEN ov.order_num END) AS unMatchCount,
                COUNT(CASE WHEN ov.STATUS = #{statusMap.NEED_FILLED} THEN ov.order_num END) AS unFilledCount,
                COUNT(CASE WHEN ov.STATUS = #{statusMap.UN_FINISHED} THEN ov.order_num END) AS unFinishedCount,
                COUNT(CASE WHEN ov.STATUS = #{statusMap.NEED_CONFIRM} THEN ov.order_num END) AS needConfirmCount,
                COUNT(CASE WHEN ov.STATUS = #{statusMap.FINISHED} THEN ov.order_num END) AS finishedCount
        FROM order_video ov
    </select>

    <update id="clearFlagOrder">
        UPDATE order_video
        SET
            schedule_type =null,
            commission_unit =null,
            commission =null,
            overstatement =null,
            carry_type =null,
            main_carry_count =null,
            main_carry_video_id =null,
            carry_ignore =0,
            shipping_remark =null,
            shoot_model_id =null,
            shipping_pic =null,
            logistic_flag = null,
            issue_id = null
        WHERE
            id IN
            <foreach item="item" collection="videoIds" open="(" separator="," close=")">
                #{item}
            </foreach>
    </update>

    <select id="getOrderVideoCount" resultType="java.lang.Integer">
        select
            count(1)
        from order_video
        where order_num in (
                        select order_num
                        from order_table
                        where merchant_code = UCASE(#{merchantCode})
                        )
        AND status not in
        <foreach item="item" collection="ignoreStatus" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND video_code IS NOT NULL
    </select>

    <select id="getOrderVideoCareCount" resultType="java.lang.Integer">
        select
            count(1)
        from order_video
        where order_num in (
            select order_num
            from order_table
            where merchant_code = UCASE(#{merchantCode})
        )
          AND is_care = ${@<EMAIL>}
    </select>
    <select id="checkManagerDataScope" resultType="int">
        SELECT
            count(1)
        FROM
            order_video
        WHERE
            id in
                <foreach item="item" collection="dto.videoIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
          <choose>
              <when test="dto.backUserType == null or dto.backUserType.name() == 'ALL' ">
                    AND ( contact_id != #{userid} OR contact_id IS NULL )
                    AND (issue_id != #{userid} OR issue_id IS NULL)
              </when>
              <when test="dto.backUserType.name() == 'CHINESE' ">
                    AND ( contact_id != #{userid} OR contact_id IS NULL )
                    <if test="dto.workOrderAssignee == true ">
                        AND NOT EXISTS (
                                        SELECT
                                            1
                                        FROM
                                            order_video_task_detail ovtd
                                                LEFT JOIN order_video_task ovt ON ovt.id = ovtd.task_id
                                        WHERE
                                            ovt.video_id IN
                                                <foreach item="item" collection="dto.videoIds" open="(" separator="," close=")">
                                                    #{item}
                                                </foreach>
                                            AND ovtd.work_order_type = ${@com.ruoyi.common.core.enums.OrderTaskWorkOrderTypeEnum@MODEL_DIDNT_GET_IT.getcode}
                                            AND ovtd.assignee_id = #{userid}
                                            AND ovtd.`status` = ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@UN_HANDLE.getcode}
                        )
                    </if>
              </when>
              <when test="dto.backUserType.name() == 'ENGLISH' ">
                    AND (issue_id != #{userid} OR issue_id IS NULL)
              </when>
              <otherwise>
                    AND ( contact_id != #{userid} OR contact_id IS NULL )
                    AND (issue_id != #{userid} OR issue_id IS NULL)
              </otherwise>
          </choose>
    </select>

    <select id="checkUserDataScope" resultType="int">
        SELECT
            count(1)
        FROM
            order_video ov
                RIGHT JOIN order_table ot ON ot.order_num = ov.order_num
        WHERE
            ov.id IN
            <foreach item="item" collection="dto.videoIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND (
                ot.merchant_id != #{businessAccount.businessId}
		        OR ot.merchant_id IS NULL
                <if test="businessAccount.isOwnerAccount != null and businessAccount.isOwnerAccount != 1 and dto.businessShare != true">
                    OR (ov.create_order_user_id != #{businessAccount.id}
                    OR ov.create_order_user_id IS NULL)
                </if>
                <if test="dto.submitter == true">
                    AND (ot.order_user_id != #{businessAccount.id} OR ot.order_user_id IS NULL )
                </if>
            )
    </select>

    <select id="getModelOrderCount" resultType="com.ruoyi.system.api.domain.vo.order.ModelOrderVO">
        -- 1) 先在派生表里预聚合“反馈素材”数据
        WITH ovfbm AS (
            SELECT
                video_id,
                rollback_id,
                COUNT(*) AS fb_cnt
            FROM order_video_feed_back_material
            GROUP BY video_id, rollback_id
        ),

        -- 2) 在派生表里预聚合“工单明细”的未完成/已完成数量
        ovtd AS (
        SELECT
        t.video_id,
        SUM(CASE WHEN d.after_sale_video_type IN
        (${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@RESHOOT_VIDEO.getCode},${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@RESHOT_VIDEO.getCode})
        AND d.status NOT IN
        (${@<EMAIL>},${@<EMAIL>},${@<EMAIL>})
        THEN 1 ELSE 0 END) AS incomp_cnt,
        SUM(CASE WHEN d.after_sale_video_type IN
        (${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@RESHOOT_VIDEO.getCode},${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@RESHOT_VIDEO.getCode})
        AND d.status IN
        (${@<EMAIL>},${@<EMAIL>},${@<EMAIL>})
        THEN 1 ELSE 0 END) AS comp_cnt
        FROM order_video_task t
        JOIN order_video_task_detail d ON d.task_id = t.id
        GROUP BY t.video_id
        )
        SELECT
            ov.shoot_model_id AS model_id,
        -- 待拍数：
            SUM(
                CASE
                    -- A: 状态=5 (待发货)
                    WHEN ov.status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode} THEN 1
                    -- B: 状态=6 且无任何反馈素材
                    WHEN ov.status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode} AND COALESCE(ovfbm.fb_cnt, 0) = 0 THEN 1
                    -- C: 状态∈(6,7) 且存在未完成的重拍/补拍工单
                    WHEN ov.status IN (${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode}) AND COALESCE(ovtd.incomp_cnt, 0) > 0 THEN 1
                ELSE 0
                END
            ) AS waits,
        -- 待确认数：
            SUM(
                CASE
                    -- 状态=7 且“没有任何未完成的重拍/补拍工单”
                    WHEN ov.status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode}  AND COALESCE(ovtd.incomp_cnt, 0) = 0 THEN 1
                ELSE 0
                END
            ) AS toBeConfirm,
            SUM( ov.STATUS = ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode}) AS unconfirmed,
            SUM( ov.STATUS = ${@<EMAIL>} ) AS finished
        FROM
            (
            SELECT id, shoot_model_id, status, rollback_id
            FROM order_video
            WHERE status IN (
                        ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_FILLED.getcode},
                        ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                        ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                        ${@<EMAIL>}
                        )
            ) AS ov
            LEFT JOIN ovfbm ON ovfbm.video_id = ov.id AND ovfbm.rollback_id &lt;=&gt; ov.rollback_id
            LEFT JOIN ovtd ON ovtd.video_id = ov.id
        <where>
            <if test="modelIds != null and modelIds.size() >0 ">
               and ov.shoot_model_id IN
                <foreach item="item" collection="modelIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND  ov.shoot_model_id IS NOT NULL
        </where>
        GROUP BY
            ov.shoot_model_id
    </select>
    <select id="getOrderUserId" resultType="java.lang.Long">
        select distinct(create_order_user_id) from order_video
    </select>
    <select id="getOrderVideoCurrentInfo" resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoCurrentInfoVO">
        <include refid="getOrderVideoCurrentInfo"/>
        where id = #{id}
    </select>
    <select id="selectListByProductPicIsNullAndProductLink"
            resultType="com.ruoyi.system.api.domain.entity.order.OrderVideo">
        SELECT
            id,product_link
        FROM
            order_video
        WHERE
            product_link in
            <foreach collection="productLinks" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND product_pic IS NULL
    </select>

    <select id="orderPayDetailVideoList" resultType="com.ruoyi.system.api.domain.vo.order.finace.OrderPayVideoDetailVO">
        select
            ov.id,
            ot.pay_time,
            ot.order_num,
            ot.pay_num,
            tpl.mchnt_order_no,
            ot.order_type,
            ot.pay_type,
            ot.pay_type_detail,
            ot.audit_time,
            ot.current_exchange_rate,
            ov.amount,
            ov.video_promotion_amount,
            ov.pay_amount,
            ov.pay_amount_dollar,
            ov.video_price,
            ov.pic_price,
            ov.difference_amount,
            ov.exchange_price,
            ov.service_price,
            ov.commission_pays_taxes,
            ov.product_chinese,
            ov.product_english,
            ov.video_code,
            ov.status,
            ov.shoot_model_id,
            ov.need_confirm_time,
            ov.last_model_submit_time,
            ov.pic_count
        from order_table ot
        inner join order_video ov on ot.order_num = ov.order_num
        LEFT JOIN order_pay_log tpl ON tpl.order_num = ot.order_num
        <where>
            ot.audit_status =${@<EMAIL>}
            <if test="keyword != null and keyword != ''">
                and (
                    ot.order_num like concat('%', #{keyword}, '%')
                    or ot.pay_num like concat('%', #{keyword}, '%')
                    or tpl.mchnt_order_no like concat('%', #{keyword}, '%')
                    or ov.video_code like concat('%', UCASE(#{keyword}), '%')
                    OR ov.product_chinese like concat('%', #{keyword}, '%')
                    OR ov.product_english like concat('%', #{keyword}, '%')
                    <if test="keywordModelIds!= null and keywordModelIds.size() != 0">
                        OR ov.shoot_model_id in
                        <foreach collection="keywordModelIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                )
            </if>
            <if test="businessId != null">and ot.merchant_id = #{businessId}</if>
            <if test="orderNum != null and orderNum != ''"> and ot.order_num = #{orderNum}</if>
            <if test="payTypes != null and payTypes.size() != 0">
                and ot.pay_type in
                <foreach collection="payTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="payTypeDetails != null and payTypeDetails.size() != 0">
                and ot.pay_type_detail in
                <foreach collection="payTypeDetails" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null and endTime != null">
                and ot.pay_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>

    <select id="financialVerificationAllExportList" resultType="com.ruoyi.system.api.domain.vo.order.finace.FinancialVerificationAllExportVO">
        select
            ov.id videoId,
            ot.merchant_code,
            ot.order_time,
            ot.pay_time,
            ot.order_num,
            ot.pay_num,
            tpl.mchnt_order_no,
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.shoot_model_id,
            ov.status,
            ov.need_confirm_time,
            ov.last_model_submit_time,
            ot.pay_type,
            ot.order_type,
            ot.audit_time,
            ov.video_price,
            ov.pic_price,
            ov.exchange_price,
            ov.commission_pays_taxes,
            ov.service_price,
            ot.current_exchange_rate,
            ov.amount,
            ov.video_promotion_amount,
            ov.pay_amount,
            ov.pay_amount_dollar,
            ov.difference_amount,
            ov.video_promotion_amount,
            ov.use_balance,
            ov.pic_count,
            ov.create_order_business_proxy_status as 'is_proxy'
        from order_table ot
                 inner join order_video ov on ot.order_num = ov.order_num
                 inner JOIN order_pay_log tpl ON tpl.order_num = ot.order_num
        where ot.pay_time BETWEEN #{startTime} AND #{endTime}
    </select>
    <select id="test" resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoModelChange">
        SELECT
            ovmc.*
        FROM
            order_video ov
                JOIN order_video_model_change ovmc ON ovmc.video_id = ov.id
        WHERE
            ovmc.schedule_type IS NULL
          AND ovmc.source = 2
          AND ov.STATUS IN (
                            5,
                            6,
                            7,
                            8)
    </select>
    <select id="test2" resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoMatch">
        SELECT
            ovm.*
        FROM
            order_video ov
                JOIN order_video_match ovm ON ovm.video_id = ov.id
                AND COALESCE ( ovm.rollback_id, 0 ) = COALESCE ( ov.rollback_id, 0 )
        WHERE
            ovm.schedule_type IS NULL
          AND ov.schedule_type IS NOT NULL
          AND ovm.STATUS = 1
          AND ovm.end_time IS NOT NULL
          AND ov.STATUS IN (
                            5,
                            6,
                            7,
                            8)
    </select>
    <select id="test3" resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoModelChange">
        SELECT
            ovmc.*
        FROM
            order_video_match ovm
                JOIN order_video_model_change ovmc ON ovmc.video_id = ovm.video_id AND COALESCE(ovm.rollback_id, 0) = COALESCE(ovmc.rollback_id, 0) AND ovm.shoot_model_id = ovmc.model_id and date_format(ovmc.selected_time,'%Y-%m-%d') =date_format(ovm.submit_time,'%Y-%m-%d')
                JOIN order_video ov on ov.id = ovm.video_id
        WHERE
            (ovmc.schedule_type != ovm.schedule_type or (ovmc.schedule_type is not null and ovm.schedule_type is  null)or (ovmc.schedule_type is not null and ovm.schedule_type is  null))or
            (ovmc.carry_type != ovm.carry_type or (ovmc.carry_type is not null and ovm.carry_type is  null)or (ovmc.carry_type is not null and ovm.carry_type is  null))OR
            (ovmc.commission_unit != ovm.commission_unit or (ovmc.commission_unit is not null and ovm.commission_unit is  null)or (ovmc.commission_unit is not null and ovm.commission_unit is  null))OR
            (ovmc.commission != ovm.commission or (ovmc.commission is not null and ovm.commission is  null)or (ovmc.commission is not null and ovm.commission is  null))OR
            (ovmc.overstatement != ovm.overstatement or (ovmc.overstatement is not null and ovm.overstatement is  null)or (ovmc.overstatement is not null and ovm.overstatement is  null))
    </select>
    <select id="hasValidVideoOrderByBusinessId" resultType="java.lang.Boolean">
        SELECT
            CASE WHEN COUNT(1) > 0 THEN TRUE ELSE FALSE END
        FROM
            order_video
        WHERE
            create_order_business_id = #{businessId}
            AND status != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getCode}
        <if test="startTime != null ">
            AND create_time &gt;= #{startTime}
        </if>
    </select>
    <select id="getModelOrderRanking"
            resultType="com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelOrderRankingInfo">
        SELECT
            shoot_model_id AS model_id,
            COUNT(*) AS order_count
        FROM
            order_video
        WHERE
            `status` IN (
                        ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getcode},
                        ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getcode},
                        ${@<EMAIL>}
                        )
            AND rollback_id IS NULL
            AND DATE_FORMAT( create_time, '%Y-%m' ) = #{date}
        GROUP BY
            shoot_model_id
    </select>
    <select id="getChineseCustomerServiceOrderCountByDate"
            resultType="com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedCompleteCountInfo">
        SELECT
            ov.contact_id AS customerServiceId,
            COUNT(CASE WHEN DATE(ovf.event_execute_time) = #{date} AND ov.status != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode} THEN 1 END) AS addedCount,
            COUNT(CASE WHEN DATE(ov.status_time) = #{date} AND ov.status = ${@<EMAIL>} THEN 1 END) AS finishCount
        FROM
            order_video ov
                LEFT JOIN order_video_flow ovf ON ovf.video_id = ov.id AND ovf.target_status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_CONFIRM.getcode}
        WHERE
            ov.rollback_id IS NULL
            AND ov.contact_id IS NOT NULL
        GROUP BY
            ov.contact_id
    </select>
    <select id="getEnglishCustomerServiceOrderCountByDate"
            resultType="com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedCompleteCountInfo">
        SELECT
            issue_id AS customerServiceId,
            COUNT(CASE WHEN DATE(last_model_submit_time) = #{date} AND status != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode} THEN 1 END) AS addedCount,
            COUNT(CASE WHEN DATE(status_time) = #{date} AND status = ${@<EMAIL>} THEN 1 END) AS finishCount
        FROM
            order_video
        WHERE
            rollback_id IS NULL
          AND issue_id IS NOT NULL
        GROUP BY
            issue_id
    </select>
    <select id="backCreateOrderUserNameSelect" resultType="com.ruoyi.system.api.domain.entity.order.OrderVideo">
        SELECT DISTINCT
            create_order_user_name,
            create_order_user_nick_name
        FROM
            order_video
        WHERE
            create_order_user_name IS NOT NULL AND create_order_user_name != '' AND create_order_user_nick_name IS NOT NULL AND create_order_user_nick_name != ''
        <if test="keyword != null and keyword != '' ">
             AND create_order_user_name LIKE CONCAT( '%', #{keyword}, '%' ) OR create_order_user_nick_name LIKE CONCAT( '%', #{keyword}, '%' )
        </if>
    </select>
    <select id="getOrderVideoDataStatisticsDay"
            resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoDataStatisticsDay">
        SELECT
            COUNT( DISTINCT CASE WHEN DATE ( ovf.event_execute_time )= #{date}
                            AND ov.`status` != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode} THEN ov.id END ) AS added_order_video_count,
            COUNT( DISTINCT CASE WHEN DATE ( ovt.create_time )= #{date} THEN ov.id END ) AS added_order_video_task_count
        FROM
            order_video ov
                LEFT JOIN order_video_flow ovf ON ovf.video_id = ov.id AND ovf.target_status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_CONFIRM.getcode}
                LEFT JOIN order_video_task ovt ON ovt.video_id = ov.id
    </select>
    <select id="getOrderVideoBaseBoard"
            resultType="com.ruoyi.system.api.domain.vo.order.datastatistics.OrderVideoBaseBoardVO">
        SELECT
            COUNT( DISTINCT CASE WHEN ov.un_confirm_time IS NOT NULL AND ov.`status` != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getCode} THEN ov.id END ) AS orderCount,
            COUNT( DISTINCT CASE WHEN ovt.id IS NOT NULL AND ovtd.`status` NOT IN ( ${@<EMAIL>},${@<EMAIL>} ) THEN ov.id END ) AS taskCount
        FROM
            order_video ov
                LEFT JOIN order_video_task ovt ON ovt.video_id = ov.id
                LEFT JOIN order_video_task_detail ovtd ON ovtd.task_id = ovt.id
    </select>
    <!--    更新视频订单-->
    <update id="updateOrderVideo">
        update order_video
        <trim prefix="SET" suffixOverrides=",">
            <if test="platform != null">platform = #{platform},</if>
            <if test="videoStyle != null">video_style = #{videoStyle},</if>
            <if test="isObject != null">is_object = #{isObject},</if>
            <if test="videoFormat != null">video_format = #{videoFormat},</if>
            <if test="shootingCountry != null">shooting_country = #{shootingCountry},</if>
            <if test="modelType != null">model_type = #{modelType},</if>
            <if test="productChinese != null and productChinese != ''">product_chinese = #{productChinese},</if>
            <if test="productEnglish != null and productEnglish != ''">product_english = #{productEnglish},</if>
            <if test="picCount != null">pic_count = #{picCount},</if>
            <if test="videoDuration != null">video_duration = #{videoDuration},</if>
            <if test="lastChangeTime != null">last_change_time = #{lastChangeTime},</if>
            <if test="isGund != null">is_gund = #{isGund},</if>
            particular_emphasis_pic_ids = #{particularEmphasisPicIds},
            cautions_pic = #{cautionsPicId},
            product_link = #{productLink},
            product_pic = #{productPic},
            reference_video_link = #{referenceVideoLink},
            intention_model_id = #{intentionModelId},
            reference_pic = #{referencePicId},
        </trim>
        where id = #{id}
    </update>

    <update id="updateIssueId">
        UPDATE order_video
        SET issue_id = #{dto.issueId}
        WHERE
                id IN (
                SELECT
                    id
                FROM
                    (
                        SELECT
                            id
                        FROM
                            order_video
                        WHERE
                            shoot_model_id IN
                            <foreach collection="dto.modelIds" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            AND STATUS != 9
                            AND (
                                STATUS != 8
                                    OR (
                                        STATUS = 8
                                        AND id IN (
                                            SELECT DISTINCT video_id
                                            FROM order_video_upload_link
                                            WHERE status = ${@com.ruoyi.common.core.enums.UploadLinkStatusEnum@HAVEN_T_UPLOADED.getCode}
                                        )
                                )
                            )
                    ) AS temp_table
            )
    </update>

    <update id="updateOrderVideoFieldNullToNull" parameterType="java.util.List">
            UPDATE order_video
                SET
                order_num = #{orderVideo.orderNum},
                video_code = #{orderVideo.videoCode},
                status = #{orderVideo.status},
                status_time = #{orderVideo.statusTime},
                platform = #{orderVideo.platform},
                is_care = #{orderVideo.isCare},
                model_phone_visible = #{orderVideo.modelPhoneVisible},
                cautions_pic = #{orderVideo.cautionsPicId},
                product_chinese = #{orderVideo.productChinese},
                product_english = #{orderVideo.productEnglish},
                product_link = #{orderVideo.productLink},
                product_pic = #{orderVideo.productPic},
                reference_video_link = #{orderVideo.referenceVideoLink},
                video_format = #{orderVideo.videoFormat},
                video_duration = #{orderVideo.videoDuration},
                shooting_country = #{orderVideo.shootingCountry},
                model_type = #{orderVideo.modelType},
                is_object = #{orderVideo.isObject},
                pic_count = #{orderVideo.picCount},
                reference_pic = #{orderVideo.referencePicId},
                intention_model_id = #{orderVideo.intentionModelId},
                shoot_model_id = #{orderVideo.shootModelId},
                logistic_flag = #{orderVideo.logisticFlag},
                shipping_remark = #{orderVideo.shippingRemark},
                shipping_pic = #{orderVideo.shippingPic},
                contact_id = #{orderVideo.contactId},
                issue_id = #{orderVideo.issueId},
                amount = #{orderVideo.amount},
                amount_dollar = #{orderVideo.amountDollar},
                pay_amount = #{orderVideo.payAmount},
                pay_amount_dollar = #{orderVideo.payAmountDollar},
                video_promotion_amount = #{orderVideo.videoPromotionAmount},
                video_price = #{orderVideo.videoPrice},
                pic_price = #{orderVideo.picPrice},
                commission_pays_taxes = #{orderVideo.commissionPaysTaxes},
                exchange_price = #{orderVideo.exchangePrice},
                service_price = #{orderVideo.servicePrice},
                release_time = #{orderVideo.releaseTime},
                release_flag = #{orderVideo.releaseFlag},
                rollback_id = #{orderVideo.rollbackId},
                un_confirm_time = #{orderVideo.unConfirmTime},
                un_finished_time = #{orderVideo.unFinishedTime},
                need_confirm_time = #{orderVideo.needConfirmTime},
                auto_complete_time = #{orderVideo.autoCompleteTime},
                last_change_time = #{orderVideo.lastChangeTime},
                last_model_submit_time = #{orderVideo.lastModelSubmitTime},
                create_order_business_id = #{orderVideo.createOrderBusinessId},
                create_order_biz_user_id = #{orderVideo.createOrderBizUserId},
                create_order_user_id = #{orderVideo.createOrderUserId},
                create_order_user_account = #{orderVideo.createOrderUserAccount},
                create_order_user_name = #{orderVideo.createOrderUserName},
                create_order_user_nick_name = #{orderVideo.createOrderUserNickName},
                create_time = #{orderVideo.createTime},
                update_time = #{orderVideo.updateTime}
            WHERE id = #{orderVideo.id}
    </update>

</mapper>