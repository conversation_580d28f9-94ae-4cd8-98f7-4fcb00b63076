<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.ModelDataTableMapper">

    <sql id="selectModelDataTableOrderScheduledRecordListSql">
            SELECT
                ov.id AS video_id,
                ovm.id AS match_id,
                ov.video_code,
                ov.product_chinese,
                ov.product_english,
                ovm.shoot_model_add_type,
                ovm.submit_time,
                ovm.schedule_type,
                ovm.commission_unit,
                ovm.commission,
                ROUND(
                        ovm.commission * (
                        CASE
                            WHEN ovm.commission_unit = '${@<EMAIL>}' THEN
                                ${@<EMAIL>}
                            WHEN ovm.commission_unit = '${@<EMAIL>}' THEN
                                ${@<EMAIL>}
                            WHEN ovm.commission_unit = '${@<EMAIL>}' THEN
                                ${@<EMAIL>} ELSE 1
                            END
                        ),
                    4
                 ) AS commissionSort,
                ovm.carry_type,
                TIMESTAMPDIFF( DAY, ovm.submit_time, MIN( ovfb.create_time ) ) AS feedbackDuration,
                ov.`status`,
                (
                    CASE
                        WHEN ov.`status` = 8
                            AND ovm.rollback_id &lt;=> ov.rollback_id
                            AND ovm.shoot_model_id = ov.shoot_model_id
                            THEN ov.status_time END
                    ) AS statusTime,
                CASE
                    WHEN EXISTS (
                        SELECT
                            1
                        FROM
                            order_video_task ovt
                                JOIN order_video_task_detail ovtd ON ovtd.task_id = ovt.id
                        WHERE
                            ovt.video_id = ovm.video_id
                          AND ovt.task_type = 2
                          AND ovtd.submit_time >= COALESCE(MIN( ovl.shipping_time ) ,MIN(ovrr.un_finished_time),CASE WHEN ovm.rollback_id &lt;=> ov.rollback_id AND ov.un_finished_time IS NOT NULL AND ovm.submit_time = ov.last_model_submit_time THEN ov.un_finished_time END)
                          AND ( ovtd.submit_time &lt; MIN( ovrr.operate_time ) OR MIN( ovrr.operate_time ) IS NULL )
                    ) THEN 1 END AS workOrderTag,
                CASE
                    WHEN EXISTS (
                        SELECT
                            1
                        FROM
                            order_video_task ovt
                                JOIN order_video_task_detail ovtd ON ovtd.task_id = ovt.id
                        WHERE
                            ovt.video_id = ovm.video_id
                          AND ovt.task_type = 1
                          AND ovtd.submit_time >= COALESCE(MIN( ovl.shipping_time ) ,MIN(ovrr.un_finished_time),CASE WHEN ovm.rollback_id &lt;=> ov.rollback_id AND ov.un_finished_time IS NOT NULL AND ovm.submit_time = ov.last_model_submit_time THEN ov.un_finished_time END)
                          AND ( ovtd.submit_time &lt; MIN( ovrr.operate_time ) OR MIN( ovrr.operate_time ) IS NULL )
                    ) THEN 2 END AS afterOrderTag,
                CASE
                    WHEN EXISTS (
                        SELECT
                            1
                        FROM
                            order_video_refund ovr
                        WHERE
                            ovr.video_id = ovm.video_id
                          AND ovr.refund_type = 1
                          AND ovr.refund_status = 4
                          AND ovr.apply_time >= COALESCE(MIN( ovl.shipping_time ) ,MIN(ovrr.un_finished_time),CASE WHEN ovm.rollback_id &lt;=> ov.rollback_id AND ov.un_finished_time IS NOT NULL AND ovm.submit_time = ov.last_model_submit_time THEN ov.un_finished_time END)
                          AND ( ovr.apply_time &lt; MIN( ovrr.operate_time ) OR MIN( ovrr.operate_time ) IS NULL )
                    ) THEN 3 END AS compensateTag,
                CASE
                    WHEN EXISTS (
                        SELECT
                            1
                        FROM
                            order_video_refund ovr
                        WHERE
                            ovr.video_id = ovm.video_id
                          AND ovr.refund_type = 2
                          AND ovr.refund_status = 4
                          AND ovr.apply_time >= COALESCE(MIN( ovl.shipping_time ) ,MIN(ovrr.un_finished_time),CASE WHEN ovm.rollback_id &lt;=> ov.rollback_id AND ov.un_finished_time IS NOT NULL AND ovm.submit_time = ov.last_model_submit_time THEN ov.un_finished_time END)
                          AND ( ovr.apply_time &lt; MIN( ovrr.operate_time ) OR MIN( ovrr.operate_time ) IS NULL )
                    ) THEN 4 END AS cancelOrderTag,
                CASE WHEN MIN( ovrr.operate_time ) IS NOT NULL THEN 5 END AS returnOrderTag
            FROM
                order_video_match ovm
                    JOIN order_video ov ON ov.id = ovm.video_id
                    LEFT JOIN order_video_feed_back ovfb ON ovfb.video_id = ov.id AND ovfb.rollback_id &lt;=> ov.rollback_id AND ov.last_model_submit_time = ovm.submit_time
                    LEFT JOIN order_video_model_shipping_address ovmsa ON ovmsa.video_id = ovm.video_id AND ovmsa.rollback_id &lt;=> ovm.rollback_id AND ovmsa.shoot_model_id = ovm.shoot_model_id AND ABS(TIMESTAMPDIFF(SECOND, ovmsa.create_time, ovm.submit_time)) &lt;= 3
                    LEFT JOIN order_video_logistic ovl ON ovl.shipping_address_id = ovmsa.id
                    LEFT JOIN (
                    SELECT
                        ovrr.id,
                        ovrrc.id AS cid,
                        ovrr.video_id,
                        ovrr.operate_time,
                        ovrrc.last_model_submit_time,
                        ovrrc.un_finished_time
                    FROM
                        order_video_rollback_record ovrr
                            JOIN order_video_rollback_record_change ovrrc ON ovrrc.rollback_id = ovrr.id
                ) ovrr ON ovrr.video_id = ovm.video_id AND ovrr.last_model_submit_time = ovm.submit_time
            WHERE
                ovm.shoot_model_id = #{modelId}
                AND ( ovl.id IS NOT NULL OR ovrr.un_finished_time IS NOT NULL OR (ovm.rollback_id &lt;=> ov.rollback_id AND ov.un_finished_time IS NOT NULL AND ovm.submit_time=ov.last_model_submit_time))
            GROUP BY
                ovm.id
    </sql>

    <select id="selectModelDataTableOrderScheduledRecordList"
            resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableOrderScheduledRecordVO">
        SELECT
            *,
            CONCAT_WS( ',', t.workOrderTag, t.afterOrderTag, t.compensateTag, t.cancelOrderTag, t.returnOrderTag ) AS tags
        FROM
            (
            <include refid="selectModelDataTableOrderScheduledRecordListSql"/>
            ) t
        <where>
            <if test="dto.tag != null ">
                <if test="dto.tag == 0">
                    AND workOrderTag IS NULL AND afterOrderTag IS NULL AND compensateTag IS NULL AND cancelOrderTag IS NULL AND returnOrderTag IS NULL
                </if>
                <if test="dto.tag == 1">
                    AND workOrderTag IS NOT NULL
                </if>
                <if test="dto.tag == 2">
                    AND afterOrderTag IS NOT NULL
                </if>
                <if test="dto.tag == 3">
                    AND compensateTag IS NOT NULL
                </if>
                <if test="dto.tag == 4">
                    AND cancelOrderTag IS NOT NULL
                </if>
                <if test="dto.tag == 5">
                    AND returnOrderTag IS NOT NULL
                </if>
            </if>
        </where>
        ORDER BY
            <choose>
                <when test="dto.sortColumnStr != null and dto.sortColumnStr != '' and dto.sortWay != null and dto.sortWay != '' ">
                    ${dto.sortColumnStr} ${dto.sortWay} ,match_id ASC
                </when>
                <otherwise>
                    submit_time DESC
                </otherwise>
            </choose>
    </select>
    <select id="getModelDataTableOrderScheduledTagCount"
            resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableOrderScheduledTagCountVO">
        SELECT
            COUNT(video_id) AS allCount,
            COUNT(CASE WHEN workOrderTag IS NULL AND afterOrderTag IS NULL AND compensateTag IS NULL AND cancelOrderTag IS NULL AND returnOrderTag IS NULL THEN 1 END) AS noExceptionCount,
            COUNT(workOrderTag) AS workOrderCount,
            COUNT(afterOrderTag) AS afterSaleOrderCount,
            COUNT(compensateTag) AS compensationOrderCount,
            COUNT(cancelOrderTag) AS cancelOrderCount,
            COUNT(returnOrderTag) AS rollbackOrderCount
        FROM
            (
            <include refid="selectModelDataTableOrderScheduledRecordListSql"/>
            ) t
    </select>
</mapper>