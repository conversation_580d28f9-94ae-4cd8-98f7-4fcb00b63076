<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoTagMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.order.OrderVideoTag" id="OrderVideoTagResult">
        <result property="id"    column="id"    />
        <result property="videoId"    column="video_id"    />
        <result property="tagId"    column="tag_id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOrderVideoTagVo">
        select id, video_id, tag_id, category_id, create_time, update_time from order_video_tag
    </sql>

    <select id="selectOrderVideoTagList" parameterType="com.ruoyi.system.api.domain.entity.order.OrderVideoTag" resultMap="OrderVideoTagResult">
        <include refid="selectOrderVideoTagVo"/>
        <where>
            <if test="videoId != null "> and video_id = #{videoId}</if>
            <if test="tagId != null "> and tag_id = #{tagId}</if>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
        </where>
    </select>

    <select id="selectOrderVideoTagById" parameterType="Long" resultMap="OrderVideoTagResult">
        <include refid="selectOrderVideoTagVo"/>
        where id = #{id}
    </select>

    <insert id="insertOrderVideoTag" parameterType="com.ruoyi.system.api.domain.entity.order.OrderVideoTag" useGeneratedKeys="true" keyProperty="id">
        insert into order_video_tag
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="videoId != null">video_id,</if>
            <if test="tagId != null">tag_id,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="videoId != null">#{videoId},</if>
            <if test="tagId != null">#{tagId},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateOrderVideoTag" parameterType="com.ruoyi.system.api.domain.entity.order.OrderVideoTag">
        update order_video_tag
        <trim prefix="SET" suffixOverrides=",">
            <if test="videoId != null">video_id = #{videoId},</if>
            <if test="tagId != null">tag_id = #{tagId},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOrderVideoTagById" parameterType="Long">
        delete from order_video_tag where id = #{id}
    </delete>

    <delete id="deleteOrderVideoTagByIds" parameterType="String">
        delete from order_video_tag where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>