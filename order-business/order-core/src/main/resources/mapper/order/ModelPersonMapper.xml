<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.ModelPersonMapper">

    <!--    我的订单-->
    <select id="myOrder" resultType="com.ruoyi.system.api.domain.vo.order.OrderModelMyOrderListVO">
        SELECT
            ov.id,
            ov.`status`,
            MAX( ofbm.`status` ) AS in_after_sale,
            ov.product_pic,
            ov.product_english,
            ov.video_code,
            ov.video_style,
            ov.video_duration,
            ov.platform,
            ov.pic_count,
            ov.refund_pic_count,
            ov.is_object,
            ll.number,
            ov.status_time
        FROM
            order_video_model ovm
            LEFT JOIN order_video ov ON ov.id = ovm.video_id
            LEFT JOIN order_video_feed_back_material ofbm ON ofbm.video_id = ovm.video_id
            LEFT JOIN (
                        SELECT
                            video_id,number
                        FROM
                            order_video_logistic
                        WHERE
                            ( video_id, reissue, create_time ) IN ( SELECT video_id, MIN( reissue ), MAX( create_time ) FROM order_video_logistic where is_cancel = ${@<EMAIL>} GROUP BY video_id )
                    ) ll ON ll.video_id = ovm.video_id
        WHERE
            ovm.model_id = #{dto.curModelId} AND ovm.accept_time IS NOT NULL
            <if test="dto.status != null and dto.status != 0">
               AND ov.`status` = #{dto.status}
            </if>
            AND ov.`status` != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getCode}
        GROUP BY
            ov.id,
            ov.`status`,
            ov.product_pic,
            ov.product_english,
            ov.video_duration,
            ov.platform,
            ov.pic_count,
            ov.is_object,
            ll.number,
            ovm.accept_time
    </select>

    <!--    我的订单徽标-->
    <select id="myOrderBadge" resultType="com.ruoyi.system.api.domain.vo.order.OrderModelMyOrderBadgeVO">
        SELECT
            SUM(sub.pendingCompletionCount) AS pendingCompletionCount,
            SUM(sub.afterSalesCount) AS afterSalesCount
        FROM
            (
                SELECT
                    ovm.video_id,
                    MAX(ov.STATUS = #{status.UN_FINISHED}) AS pendingCompletionCount,
                    MAX(ofbm.STATUS = #{afterSales} AND ofbm.reply_status = #{afterSales}) AS afterSalesCount
                FROM
                    order_video_model ovm
                        LEFT JOIN order_video ov ON ov.id = ovm.video_id
                        LEFT JOIN order_video_feed_back_material ofbm ON ofbm.video_id = ovm.video_id
                WHERE
                    ovm.model_id = #{dto.curModelId}
                    AND ovm.accept_time IS NOT NULL
                    AND ov.status != #{status.TRADE_CLOSE}
                GROUP BY
                    ovm.video_id
            ) sub
    </select>

    <!--    售后订单-->
    <select id="afterSales" resultType="com.ruoyi.system.api.domain.vo.order.OrderModelWorkbenchAfterSalesListVO">
        SELECT
            ofbm.id,
            ofbm.title,
            ofbm.update_time,
            ofbm.reply_status,
            ov.id as video_id,
            ov.product_pic,
            ov.product_english,
            ov.video_duration,
            ov.platform,
            ov.pic_count,
            ov.refund_pic_count
        FROM
            order_video_model ovm
            LEFT JOIN order_video_feed_back_material ofbm ON ofbm.video_id = ovm.video_id
            LEFT JOIN order_video ov ON ov.id = ovm.video_id
        WHERE
            ofbm.`status` = #{dto.rejectStatus}
            AND ov.`status` = #{dto.status}
            AND ovm.model_id = #{dto.curModelId}
            AND ovm.accept_time IS NOT NULL
            AND ov.`status` != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getCode}
          <if test="dto.replyStatus != null">
              AND ofbm.reply_status = #{dto.replyStatus}
          </if>
          <if test="dto.videoId != null">
              AND ofbm.video_id = #{dto.videoId}
          </if>
    </select>

    <!--    模特选择记录-->
    <select id="getModelSelectRecord" resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoModelSelectListVO">
        SELECT
            ovmpm.id,
            IF( ovmpm.add_type = ${@com.ruoyi.common.core.enums.PreselectModelAddTypeEnum@MODEL_OPTIONAL.getCode}
                    AND ovmpm.select_status= ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@IN_REVIEW.getCode}
                    AND NOT EXISTS (
                                SELECT 1
                                FROM order_video_match_preselect_model ovmpm2
                                WHERE ovmpm2.match_id = ovmpm.match_id
                                  AND ovmpm2.status = ${@<EMAIL>}
                            ), 1, 0 ) AS canCancel,
            ov.id AS video_id,
            ovmpm.model_id,
            ovmpm.select_status,
            ovmpm.select_time,
            ovmpm.add_type,
            ovmpm.add_time,
            ov.product_pic,
            ov.product_english,
            ov.video_code,
            ov.video_style,
            ov.video_duration,
            ov.platform,
            ov.pic_count,
            ov.refund_pic_count
        FROM
            order_video_match_preselect_model ovmpm
            LEFT JOIN order_video_match ovm on ovm.id = ovmpm.match_id
            LEFT JOIN order_video ov ON ov.id = ovm.video_id
        WHERE
            ovmpm.model_id = #{dto.curModelId}
            AND (
                ovmpm.select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@UN_HANDLE.getCode}
                OR ( ovmpm.add_time &lt;= NOW() - INTERVAL #{dto.overTime} HOUR AND ovmpm.status = ${@com.ruoyi.common.core.enums.PreselectStatusEnum@UN_JOINTED.getCode} )
            )
            AND (ovmpm.add_type != ${@<EMAIL>} OR (ovmpm.add_type = ${@<EMAIL>} AND ovmpm.distribution_result=${@<EMAIL>}))
    </select>
    <select id="checkModelDataScope" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            order_video ov
                LEFT JOIN order_video_model ovm ON ov.id = ovm.video_id
        WHERE
            ov.id IN
                <foreach item="item" collection="videoIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            AND (
                ovm.model_id != #{userid}
                OR ovm.model_id IS NULL
                OR ovm.accept_time IS NULL
                OR ov.`status` != ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getCode}
                )
    </select>
</mapper>