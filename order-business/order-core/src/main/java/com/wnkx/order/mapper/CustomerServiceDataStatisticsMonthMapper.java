package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceDataStatisticsMonth;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR> 
 * @Date 2025-05-16 09:34:10 
 */
@Mapper
public interface CustomerServiceDataStatisticsMonthMapper extends SuperMapper<CustomerServiceDataStatisticsMonth> {

    /**
     * 通过记录时间 年月格式 获取模特数据统计_每月记录表
     */
    default CustomerServiceDataStatisticsMonth getByWriteTimeMonth(String date) {
        return selectOne(new LambdaQueryWrapper<CustomerServiceDataStatisticsMonth>()
                .last("WHERE DATE_FORMAT(write_time_begin,'%Y-%m') = '" + date + "'")
        );
    }
}
