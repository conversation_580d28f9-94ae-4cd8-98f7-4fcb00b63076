package com.wnkx.order.mapper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.OrderVideoModel;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Mapper
public interface OrderVideoModelMapper extends SuperMapper<OrderVideoModel> {

    /**
     * 获取逾期未反馈素材的模特
     */
    List<Long> checkModelOverdueVideo(@Param("modelId") Collection<Long> modelId,
                                      @Param("orderOverdueMax") Integer orderOverdueMax,
                                      @Param("status") Map<String, Integer> orderStatus);

    /**
     * 通过模特id获取订单关联模特
     */
    default List<OrderVideoModel> listByModelId(Collection<Long> modelIds) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoModel>().in(CollUtil.isNotEmpty(modelIds), OrderVideoModel::getModelId, modelIds));
    }

    /**
     * 通过视频订单id获取订单关联模特
     */
    default List<OrderVideoModel> listByVideoId(List<Long> videoIds) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoModel>().in(OrderVideoModel::getVideoId, videoIds));
    }

    /**
     * 根据视频订单id查询接单模特
     */
    default OrderVideoModel getModelIdByVideoId(Long videoId) {
        return this.selectOne(new LambdaQueryWrapper<OrderVideoModel>()
                .eq(OrderVideoModel::getVideoId, videoId)
                .last("limit 1")
        );
    }

    /**
     * 获取模特已确认接单的订单 即[待完成、需确认、已完成]的订单
     */
    List<OrderVideoModel> getModelAllOrder();

    /**
     * 获取模特已接收未完成的订单 即[需发货、待完成、需确认]的订单
     */
    List<OrderVideoModel> getUnfinishedOrderModelByModelId(@Param("modelId") Collection<Long> modelIds,@Param("status")Map<String, Integer> codeMap);

    /**
     * 通过视频订单ID删除模特关联订单
     */
    default void deleteOrderVideoModelByVideoId(Long videoId) {
        delete(new LambdaQueryWrapper<OrderVideoModel>()
                .eq(OrderVideoModel::getVideoId, videoId)
        );
    }
}
