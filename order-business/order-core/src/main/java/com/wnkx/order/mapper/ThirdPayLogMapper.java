package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.ThirdPayLog;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 针对表【third_pay_log(支付回调记录表)】的数据库操作Mapper
 * @Entity
 */
@Mapper
public interface ThirdPayLogMapper extends SuperMapper<ThirdPayLog> {

    /**
     * 根据富友订单号查询是否不存在数据
     * @param mchntOrderNo
     * @return 如果存在数据则返回false
     */
    default boolean checkOrderNotExist(String mchntOrderNo) {
        return selectCount(new LambdaQueryWrapper<ThirdPayLog>().eq(ThirdPayLog::getMchnt_order_no, mchntOrderNo)) <= 0;
    }

    /**
     * 根据系统订单号查询是否不存在数据
     * @param orderNumber
     * @return  如果存在数据则返回false
     */
    default boolean checkOrderNumberNotExist(String orderNumber) {
        return selectCount(new LambdaQueryWrapper<ThirdPayLog>().eq(ThirdPayLog::getOrderNumber, orderNumber)) <= 0;
    }
}




