package com.wnkx.order.mapper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.dto.order.OrderVideoRoastListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRoast;
import com.ruoyi.system.api.domain.vo.order.OrderVideoRoastStatisticsVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoRoastVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/9 14:13
 */
@Mapper
public interface OrderVideoRoastMapper extends SuperMapper<OrderVideoRoast> {

    /**
     * 查询视频订单吐槽列表*
     *
     * @param dto
     * @return
     */
    List<OrderVideoRoastVO> getOrderVideoRoastVO(OrderVideoRoastListDTO dto);

    /**
     * 吐槽列表-统计
     *
     * @return
     */
    OrderVideoRoastStatisticsVO orderVideoRoastStatisticsVO();

    /**
     * 查询视频订单是否有存在吐槽
     */
    default boolean isRoast(Long videoId) {
        return exists(new LambdaQueryWrapper<OrderVideoRoast>()
                .eq(OrderVideoRoast::getVideoId, videoId)
        );
    }

    default List<Long> isRoast(List<Long> videoIds) {
        return selectList(new LambdaQueryWrapper<OrderVideoRoast>()
                .in(OrderVideoRoast::getVideoId, videoIds)
        ).stream().map(OrderVideoRoast::getVideoId).collect(Collectors.toList());
    }

    /**
     * 获取视频订单的出单人id
     *
     * @return
     */
    default Set<Long> getRoastIssueId() {
        List<OrderVideoRoast> orderVideoRoasts = this.selectList(new LambdaQueryWrapper<OrderVideoRoast>()
                .select(OrderVideoRoast::getIssueId)
                .isNotNull(OrderVideoRoast::getIssueId)
        );
        if (CollUtil.isEmpty(orderVideoRoasts)) {
            return Collections.emptySet();
        }
        return orderVideoRoasts.stream().map(OrderVideoRoast::getIssueId).collect(Collectors.toSet());
    }

    /**
     * 获取视频订单的出单人id
     *
     * @return
     */
    default Set<Long> getRoastContactId() {
        List<OrderVideoRoast> orderVideoRoasts = this.selectList(new LambdaQueryWrapper<OrderVideoRoast>()
                .select(OrderVideoRoast::getContactId)
                .isNotNull(OrderVideoRoast::getContactId)
        );
        if (CollUtil.isEmpty(orderVideoRoasts)) {
            return Collections.emptySet();
        }
        return orderVideoRoasts.stream().map(OrderVideoRoast::getContactId).collect(Collectors.toSet());
    }
}
