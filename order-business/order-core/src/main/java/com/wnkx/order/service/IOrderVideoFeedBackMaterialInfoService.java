package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.EditCloseReasonEnum;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBackMaterialInfo;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.order.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 订单反馈(模特)_详细信息Service接口
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface IOrderVideoFeedBackMaterialInfoService extends IService<OrderVideoFeedBackMaterialInfo> {

    /**
     * 通过素材id查询素材信息
     * @param materialIds   素材id
     * @return              素材信息
     */
    List<OrderFeedBackMaterialInfoVO> selectListByMaterialId(List<Long> materialIds);

    /**
     * 添加数据
     */
    void saveOrderFeedBackMaterialInfo(Long materialId, UploadLinkDTO dto);

    /**
     * 剪辑管理-待下载、待剪辑、待反馈、需确认 列表
     */
    List<MaterialInfoListVO> selectMaterialInfoListByCondition(MaterialInfoListDTO dto);

    /**
     * 剪辑部-待领取素材列表
     * @return
     */
    List<MaterialInfoListVO> selectUnGetMaterialList();

    /**
     * 剪辑管理-待下载列表统计
     */
    GetMaterialInfoStatisticsVO getMaterialInfoStatistics();

    /**
     * 剪辑管理-历史剪辑记录
     */
    List<SelectHistoryEditListVO> selectHistoryEditList(Long videoId);

    /**
     * 剪辑管理-领取
     */
    void getEdit(GetEditDTO dto);

    /**
     * 剪辑管理-标记下载-校验是否存在进行中的任务
     */
    Boolean checkDownload(Long videoId);

    /**
     * 剪辑管理-标记下载
     */
    void markDownload(Long id);

    /**
     * 剪辑管理-历史剪辑要求
     */
    HistoryClipRecordVO getHistoryClipRecord(Long videoId);

    /**
     * 剪辑管理-标记已剪辑
     */
    void markClip(MarkClipDTO dto);

    /**
     * 剪辑管理-不反馈给商家
     */
    void markNoFeedback(MarkNoFeedbackDTO dto);

    /**
     * 剪辑管理-待下载、待剪辑、待反馈、需确认 拍摄模特下拉框
     */
    List<ModelInfoVO> selectShootModel(Integer status);

    /**
     * 剪辑管理-待下载 领取人下拉框
     */
    List<UserVO> selectReceivePerson();

    /**
     * 剪辑管理-待反馈、需确认 剪辑人下拉框
     */
    List<UserVO> selectEditPerson(Integer status);

    /**
     * 校验能否评分
     */
    Boolean checkCanScore(Long videoId);

    /**
     * 剪辑管理-添加反馈素材给商家
     */
    void addFeedBack(OrderVideoFeedBackDTO dto);

    /**
     * 剪辑管理-需确认列表-导出
     */
    void exportNeedConfirmList(MaterialInfoListDTO dto, HttpServletResponse response);

    /**
     * 剪辑管理-已关闭 列表
     */
    List<ClosedListVO> selectClosedListByCondition(ClosedListDTO dto);

    /**
     * 通过模特反馈素材ID关闭模特反馈素材详情
     */
    void closeMaterialInfoByMaterialIds(List<Long> materialIds, EditCloseReasonEnum editCloseReasonEnum);

    /**
     * 获取存在剪辑任务为待下载、待反馈、待剪辑、需确认、待上传 中的视频订单ID
     */
    List<Long> getInTheEditingVideoIds(List<Long> videoIds);

    /**
     * 获取工作台剪辑统计数据
     * @return
     */
    WorkbenchVO getWorkbenchStatistics();
}
