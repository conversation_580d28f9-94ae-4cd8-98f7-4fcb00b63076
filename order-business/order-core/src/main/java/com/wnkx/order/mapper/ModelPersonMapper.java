package com.wnkx.order.mapper;

import com.ruoyi.system.api.domain.dto.order.ModelSelectRecordListDTO;
import com.ruoyi.system.api.domain.dto.order.OrderModelMyOrderListDTO;
import com.ruoyi.system.api.domain.vo.order.OrderModelMyOrderBadgeVO;
import com.ruoyi.system.api.domain.vo.order.OrderModelMyOrderListVO;
import com.ruoyi.system.api.domain.vo.order.OrderModelWorkbenchAfterSalesListVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoModelSelectListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ModelPersonMapper {

    /**
     * 我的订单
     */
    List<OrderModelMyOrderListVO> myOrder(@Param("dto") OrderModelMyOrderListDTO dto);

    /**
     * 我的订单徽标
     */
    OrderModelMyOrderBadgeVO myOrderBadge(@Param("dto") OrderModelMyOrderListDTO dto,
                                          @Param("status") Map<String, Integer> orderStatus,
                                          @Param("afterSales") Integer afterSales);

    /**
     * 售后订单
     */
    List<OrderModelWorkbenchAfterSalesListVO> afterSales(@Param("dto") OrderModelMyOrderListDTO dto);

    /**
     * 模特选择记录
     */
    List<OrderVideoModelSelectListVO> getModelSelectRecord(@Param("dto") ModelSelectRecordListDTO dto);

    /**
     * 校验模特数据权限
     */
    int checkModelDataScope(@Param("videoIds") List<Long> videoIds, @Param("userid") Long userid);
}
