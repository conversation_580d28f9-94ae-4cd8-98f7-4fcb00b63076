package com.wnkx.order.mapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.UploadLinkStatusEnum;
import com.ruoyi.system.api.domain.dto.order.UploadLinkListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoUploadLink;
import com.ruoyi.system.api.domain.vo.order.UploadLinkListVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/26 17:56
 */
@Mapper
public interface OrderVideoUploadLinkMapper extends SuperMapper<OrderVideoUploadLink> {
    /**
     * 根据视频id获取上传素材
     */
    default OrderVideoUploadLink getOneByVideoId(Long videoId) {
        return selectOne(new LambdaQueryWrapper<OrderVideoUploadLink>()
                .eq(OrderVideoUploadLink::getVideoId, videoId)
                .ne(OrderVideoUploadLink::getStatus, UploadLinkStatusEnum.NO_UPLOAD_REQUIRED.getCode())
                .last("limit 1")
        );
    }

    /**
     * 通过视频订单id查询上传素材列表
     */
    default List<OrderVideoUploadLink> selectListByVideoIds(List<Long> videoIds) {
        return selectList(new LambdaQueryWrapper<OrderVideoUploadLink>()
                .in(OrderVideoUploadLink::getVideoId, videoIds)
        );
    }

    /**
     * 查询订单_视频_上传平台素材表
     *
     * @param listDTO
     * @return
     */
    default List<OrderVideoUploadLink> selectListByDto(UploadLinkListDTO listDTO) {
        return selectList(new LambdaQueryWrapper<OrderVideoUploadLink>()
                .in(CollUtil.isNotEmpty(listDTO.getVideoIds()), OrderVideoUploadLink::getVideoId, listDTO.getVideoIds())
                .gt(ObjectUtil.isNotNull(listDTO.getUploadLinkTimeBegin()), OrderVideoUploadLink::getCreateTime, listDTO.getUploadLinkTimeBegin())
                .lt(ObjectUtil.isNotNull(listDTO.getUploadLinkTimeEnd()), OrderVideoUploadLink::getCreateTime, listDTO.getUploadLinkTimeEnd())
        );

    }

    /**
     * 剪辑管理-待上传、已完成 列表
     */
    List<UploadLinkListVO> selectUploadLinkListByCondition(@Param("dto") UploadLinkListDTO dto);

    /**
     * 剪辑管理-待上传、已完成 拍摄模特下拉框
     */
    List<Long> getShootModelId(@Param("status") List<Integer> status);

    /**
     * 待上传
     * @return
     */
    default Long getHavenTUploaded(){
        return selectCount(new LambdaQueryWrapper<OrderVideoUploadLink>()
                .eq(OrderVideoUploadLink::getStatus, UploadLinkStatusEnum.HAVEN_T_UPLOADED.getCode()));
    }

    /**
     * 通过ASIN获取上传账号
     */
    default List<String> getUploadAccountByASIN(String asin, Long id) {
        List<OrderVideoUploadLink> orderVideoUploadLinks = selectList(new LambdaQueryWrapper<OrderVideoUploadLink>()
                .eq(OrderVideoUploadLink::getAsin, asin)
                .ne(OrderVideoUploadLink::getId, id)
                .in(OrderVideoUploadLink::getStatus, UploadLinkStatusEnum.HAVE_ALREADY_UPLOADED.getCode(), UploadLinkStatusEnum.HAVEN_T_UPLOADED.getCode(), UploadLinkStatusEnum.UPLOAD_TO_BE_CONFIRMED.getCode())
        );

        return orderVideoUploadLinks.stream().map(OrderVideoUploadLink::getUploadAccount).collect(Collectors.toList());
    }

    /**
     * 剪辑管理-上传失败
     */
    default void uploadLinkFail(OrderVideoUploadLink orderVideoUploadLink) {
        update(orderVideoUploadLink, new LambdaUpdateWrapper<OrderVideoUploadLink>()
                .eq(OrderVideoUploadLink::getId, orderVideoUploadLink.getId())
                .set(OrderVideoUploadLink::getUploadAccount, null)
                .set(OrderVideoUploadLink::getUploadUserId, null)
                .set(OrderVideoUploadLink::getUploadUserName, null)
                .set(OrderVideoUploadLink::getUploadTime,null)
        );
    }

    /**
     * 剪辑管理-上传账号-下拉框
     */
    default Set<String> uploadAccountSelect() {
        List<OrderVideoUploadLink> orderVideoUploadLinks = selectList(new LambdaQueryWrapper<OrderVideoUploadLink>()
                .select(OrderVideoUploadLink::getUploadAccount)
                .isNotNull(OrderVideoUploadLink::getUploadAccount)
                .ne(OrderVideoUploadLink::getUploadAccount, "")
        );

        return orderVideoUploadLinks.stream().map(OrderVideoUploadLink::getUploadAccount).collect(Collectors.toSet());
    }
}
