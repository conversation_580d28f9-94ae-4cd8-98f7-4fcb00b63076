package com.wnkx.order.service.core.impl.logistic;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.enums.EventExecuteObjectEnum;
import com.ruoyi.common.core.enums.FollowStatusEnum;
import com.ruoyi.common.core.enums.ModelResultEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFlowDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollowRecord;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.wnkx.order.mapper.OrderVideoMapper;
import com.wnkx.order.service.OrderResourceService;
import com.wnkx.order.service.OrderVideoLogisticFollowRecordService;
import com.wnkx.order.service.OrderVideoLogisticFollowService;
import com.wnkx.order.service.core.OrderVideoLogisticFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :已结束
 * @create :2025-04-23 11:41
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoLogisticFlowClose implements OrderVideoLogisticFlowService {
    private final OrderResourceService orderResourceService;
    private final OrderVideoLogisticFollowService orderVideoLogisticFollowService;
    private final OrderVideoLogisticFollowRecordService orderVideoLogisticFollowRecordService;
    private final OrderVideoMapper orderVideoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void OrderVideoLogisticFlow(OrderVideoLogisticFlowDTO dto) {
        this.checkStatus(dto);
        LoginUserInfoVO loginUserInfoVo = dto.getLoginUserInfoVO();
        List<Long> resourceIds = orderResourceService.saveBatchOrderResource(dto.getResourceIds());
        String resourceId = StrUtil.join(StrUtil.COMMA, resourceIds);

        List<String> videoCodes = dto.getOrderVideoLogisticFollowList().stream().map(OrderVideoLogisticFollow::getVideoCode).collect(Collectors.toList());
        List<OrderVideo> orderVideoList = orderVideoMapper.selectListByVideoCodes(videoCodes);
        Assert.isTrue(CollUtil.isNotEmpty(orderVideoList), "订单视频不存在");
        Map<String, OrderVideo> orderVideoMap = orderVideoList.stream().collect(Collectors.toMap(OrderVideo::getVideoCode, a -> a));

        List<OrderVideoLogisticFollow> updateList = new ArrayList<>();
        Date date = new Date();
        for (OrderVideoLogisticFollow item : dto.getOrderVideoLogisticFollowList()) {
            OrderVideoLogisticFollow updateEntity = new OrderVideoLogisticFollow();
            updateEntity.setId(item.getId());
            updateEntity.setLogisticStatus(this.getFollowStatusType().getLogisticStatus());
            updateEntity.setFollowStatus(this.getFollowStatusType().getCode());
            updateEntity.setModelResult(dto.getModelResult());
            updateEntity.setLatestRemark(StrUtil.isBlank(dto.getRemark()) ? "" : dto.getRemark());
            updateEntity.setLatestResourceId(StrUtil.isBlank(resourceId) ? "" : resourceId);
            updateEntity.setSignTime(dto.getSignTime());
            if (EventExecuteObjectEnum.BACK.getCode().equals(loginUserInfoVo.getUserType())) {
                updateEntity.setUpdateById(loginUserInfoVo.getUserId());
                updateEntity.setUpdateBy(loginUserInfoVo.getName());
            }
            OrderVideo orderVideo = orderVideoMap.getOrDefault(item.getVideoCode(), new OrderVideo());
            updateEntity.setProductLink(orderVideo.getProductLink());
            updateEntity.setProductPic(orderVideo.getProductPic());
            updateEntity.setProductChinese(orderVideo.getProductChinese());
            updateEntity.setProductEnglish(orderVideo.getProductEnglish());
            updateEntity.setShootModelId(orderVideo.getShootModelId());
            updateEntity.setContactId(orderVideo.getContactId());
            updateEntity.setIssueId(orderVideo.getIssueId());
            updateEntity.setCreateOrderUserName(orderVideo.getCreateOrderUserName());
            updateEntity.setCreateOrderUserNickName(orderVideo.getCreateOrderUserNickName());
            updateEntity.setCreateOrderOperationUserName(orderVideo.getCreateOrderOperationUserName());
            updateEntity.setCreateOrderOperationUserNickName(orderVideo.getCreateOrderOperationUserNickName());
            updateEntity.setPlatform(orderVideo.getPlatform());
            updateEntity.setUpdateTime(date);
            updateList.add(updateEntity);
        }
        orderVideoLogisticFollowService.updateBatchById(updateList);

        List<OrderVideoLogisticFollowRecord> orderVideoLogisticFollowRecords = new ArrayList<>();
        for (OrderVideoLogisticFollow orderVideoLogisticFollow : updateList) {
            OrderVideoLogisticFollowRecord orderVideoLogisticFollowRecord = new OrderVideoLogisticFollowRecord();
            orderVideoLogisticFollowRecord.setFollowId(orderVideoLogisticFollow.getId());
            if (ModelResultEnum.ROLLBACK.getCode().equals(dto.getModelResult())) {

                orderVideoLogisticFollowRecord.setEventName("回退订单");
                orderVideoLogisticFollowRecord.setEventContent("订单回退");
            } else if (ModelResultEnum.NOTICE_SHOOTING.getCode().equals(dto.getModelResult())) {
                orderVideoLogisticFollowRecord.setEventName("更新模特反馈结果：已通知模特");
                orderVideoLogisticFollowRecord.setEventContent(loginUserInfoVo.getName() + "已通知模特可开始拍摄");
            } else {
                orderVideoLogisticFollowRecord.setResourceId(resourceId);

                orderVideoLogisticFollowRecord.setEventName("更新模特反馈结果：" + ModelResultEnum.getLabelByCode(dto.getModelResult()));
                String signTime = "";
                String remark = StrUtil.isNotBlank(dto.getRemark()) ? "，备注：" + dto.getRemark() : "";
                if (ModelResultEnum.RECEIVED.getCode().equals(orderVideoLogisticFollow.getModelResult())) {
                    signTime = "，" + DateUtils.parseDateToStr("yyyy年MM月dd日", orderVideoLogisticFollow.getSignTime()) + "模特已收货";
                }
                orderVideoLogisticFollowRecord.setEventContent(loginUserInfoVo.getName() + "更新模特反馈结果" + signTime + remark);
            }

            orderVideoLogisticFollowRecord.setEventExecuteObject(loginUserInfoVo.getUserType());
            orderVideoLogisticFollowRecord.setEventExecuteUserId(loginUserInfoVo.getUserId());
            orderVideoLogisticFollowRecord.setEventExecuteUserName(loginUserInfoVo.getName());
            orderVideoLogisticFollowRecord.setEventExecuteNickName(loginUserInfoVo.getNickName());
            orderVideoLogisticFollowRecord.setEventExecuteTime(new Date());
            orderVideoLogisticFollowRecord.setRemark(dto.getRemark());
            orderVideoLogisticFollowRecords.add(orderVideoLogisticFollowRecord);
        }
        orderVideoLogisticFollowRecordService.saveBatch(orderVideoLogisticFollowRecords);
    }

    @Override
    public void checkStatus(OrderVideoLogisticFlowDTO dto) {
        Assert.notNull(dto, "已结束：参数不能为空~");
        Assert.isTrue(CollUtil.isNotEmpty(dto.getOrderVideoLogisticFollowList()), "已结束：物流跟进数据不能为空~");
        Assert.notNull(dto.getModelResult(), "已结束：模特处理结果不能为空~");
        if (ModelResultEnum.RECEIVED.getCode().equals(dto.getModelResult())) {
            Assert.notNull(dto.getSignTime(), "已结束：签收时间不能为空~");
        } else if (ModelResultEnum.LOST.getCode().equals(dto.getModelResult())) {
            Assert.isTrue(StrUtil.isNotBlank(dto.getRemark()), "已结束：备注不能为空~");
        }  else if (ModelResultEnum.NOTICE_SHOOTING.getCode().equals(dto.getModelResult())) {
            for (OrderVideoLogisticFollow item : dto.getOrderVideoLogisticFollowList()) {
                Assert.isTrue(ModelResultEnum.WAIT_NOTICE_SHOOTING.getCode().equals(item.getModelResult()), "已结束：当前模特结果状态有误~");
            }
        } else if (ModelResultEnum.ROLLBACK.getCode().equals(dto.getModelResult())) {
            //回退状态 需要是已发货
            for (OrderVideoLogisticFollow item : dto.getOrderVideoLogisticFollowList()) {
                Assert.isTrue(StatusTypeEnum.YES.getCode().equals(item.getLogisticStatus()), "已结束：当前物流状态不在范围内~");
            }
            return;
        } else {
            throw new ServiceException("当前模特处理结果不在范围内~");
        }
        //只有模特待确认 才能更新为 已关闭
        for (OrderVideoLogisticFollow item : dto.getOrderVideoLogisticFollowList()) {
            Assert.isTrue(FollowStatusEnum.MODEL_CONFIRM_PEND.getCode().equals(item.getFollowStatus()), "已结束：当前物流跟进状态不在范围内~");
        }

    }

    @Override
    public FollowStatusEnum getFollowStatusType() {
        return FollowStatusEnum.CLOSE;
    }
}
