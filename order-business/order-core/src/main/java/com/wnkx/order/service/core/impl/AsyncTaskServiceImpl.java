package com.wnkx.order.service.core.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.request.AlipayTradeCloseRequest;
import com.alipay.api.response.AlipayTradeCloseResponse;
import com.ruoyi.common.core.constant.CommonConstant;
import com.ruoyi.common.core.constant.PayConstant;
import com.ruoyi.common.core.enums.ChangeLogTypeEnum;
import com.ruoyi.common.core.enums.PayStatusCodeEnum;
import com.ruoyi.common.core.utils.ObjectUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.domain.dto.order.AddOrderMemberFlowDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoChangeLogDTO;
import com.ruoyi.system.api.domain.dto.order.UpdateIssueIdDTO;
import com.ruoyi.system.api.domain.entity.order.AlipayOrderTable;
import com.ruoyi.system.api.domain.entity.order.OrderMemberFlow;
import com.ruoyi.system.api.domain.entity.order.OrderResource;
import com.ruoyi.system.api.domain.entity.order.WechatOrderTable;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoCurrentInfoVO;
import com.ruoyi.system.api.domain.vo.pay.CloseOrderRequestVo;
import com.ruoyi.system.api.domain.vo.pay.CloseOrderResponseVo;
import com.wnkx.order.config.PayConfig;
import com.wnkx.order.factory.PayClientFactory;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.AsyncTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.BeansException;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/8 11:39
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AsyncTaskServiceImpl implements AsyncTaskService {

    /**
     * 远程服务
     */
    private final RemoteService remoteService;

    private final PayConfig payConfig;
    private final OkHttpClient okHttpClient;
    private final OrderVideoChangeLogService orderVideoChangeLogService;
    private final OrderResourceService orderResourceService;
    private final IOrderMemberFlowService orderMemberFlowService;
    private final IWechatOrderTableService wechatOrderTableService;
    private final AlipayOrderTableService alipayOrderTableService;

    /**
     * 模特变更对接客服更新视频订单的出单人ID
     */
    @Override
    public void updateIssueId(UpdateIssueIdDTO dto) {
        SpringUtils.getBean(IOrderVideoService.class).updateIssueId(dto);
    }

    @Override
    public void closeAlipayOrder(String outTradeNo, String appId) {
        log.debug("调用支付宝关单服务{}", outTradeNo);
        AlipayTradeCloseRequest request = new AlipayTradeCloseRequest();
        Map<String, String> hashmap = new HashMap<>();
        hashmap.put("out_trade_no", outTradeNo);
        hashmap.put("operator_id", "system");
        request.setBizContent(JSON.toJSONString(hashmap));
        try {
            AlipayClient alipayClient = PayClientFactory.getAlipayProperties(appId).getAlipayClient();
            AlipayTradeCloseResponse response = alipayClient.execute(request);
            if (response.isSuccess()) {
                log.info("调用支付宝关单服务成功,{},{}", response.getSubCode(), response.getOutTradeNo());
            } else {
                log.info("调用支付宝关单服务失败,{},{}", response.getSubCode(), response.getOutTradeNo());
            }
        } catch (AlipayApiException e) {
            log.info("调用支付宝关单服务失败,api异常,{},{},{}", e.getErrCode(), e.getErrMsg(), outTradeNo);
        }
    }

    /**
     * 关闭所有订单订单*
     */
    @Override
    public void closeAllOrder(List<String> orderNums) {
        for (String orderNum : orderNums) {
            closeAllOrder(orderNum);
        }
    }

    @Override
    public void closeAllOrder(String orderNum) {
        AlipayOrderTable alipayOrderTable = alipayOrderTableService.getValidAlipayOrderTableByOrderNum(orderNum);
        if (ObjectUtil.isNotNull(alipayOrderTable)) {
            AlipayTradeCloseRequest request = new AlipayTradeCloseRequest();
            Map<String, String> hashmap = new HashMap<>();
            hashmap.put("out_trade_no", alipayOrderTable.getMchntOrderNo());
            hashmap.put("operator_id", "system");
            request.setBizContent(JSON.toJSONString(hashmap));
            try {
                AlipayClient alipayClient = PayClientFactory.getAlipayProperties(alipayOrderTable.getAppId()).getAlipayClient();
                AlipayTradeCloseResponse response = alipayClient.execute(request);
                if (response.isSuccess()) {
                    log.info("调用支付宝关单服务成功,{},{},{}", response.getSubCode(), alipayOrderTable.getMchntOrderNo(), response);
                } else {
                    log.info("调用支付宝关单服务失败,{},{},{}", response.getSubCode(), alipayOrderTable.getMchntOrderNo(), response);
                }
            } catch (AlipayApiException e) {
                log.info("调用支付宝关单服务失败,api异常,{},{},{}", e.getErrCode(), e.getErrMsg(), alipayOrderTable.getMchntOrderNo());
            }
        }

        try {
            WechatOrderTable validWechatOrderTable = wechatOrderTableService.getValidWechatOrderTable(orderNum);
            if (ObjectUtil.isNotNull(validWechatOrderTable)) {
                SpringUtils.getBean(WeChatService.class).closeOrder(validWechatOrderTable.getMchntOrderNo(), validWechatOrderTable.getAppId());
            }
        } catch (BeansException e) {
            log.error("关闭微信订单服务失败：{}", e);
        }
    }

    /**
     * 生成订单创建变更记录
     */
    @Override
    public void addVideoInitChangeLog(List<OrderVideoCurrentInfoVO> orderVideoCurrentInfoVOS) {
        if (CollUtil.isEmpty(orderVideoCurrentInfoVOS)) {
            return;
        }
        Set<Long> intentionModelIds = orderVideoCurrentInfoVOS.stream()
                .map(OrderVideoCurrentInfoVO::getIntentionModelId)
                .filter(ObjectUtil::isNotNull)
                .collect(Collectors.toSet());

        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(intentionModelIds);

        List<String> referencePicIds = orderVideoCurrentInfoVOS.stream()
                .map(OrderVideoCurrentInfoVO::getReferencePicId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(StringUtils.splitToLong(referencePicIds, StrUtil.COMMA));

        List<OrderVideoChangeLogDTO> orderVideoChangeLogDTOS = new ArrayList<>();
        for (OrderVideoCurrentInfoVO orderVideoCurrentInfoVO : orderVideoCurrentInfoVOS) {
            orderVideoCurrentInfoVO.setIntentionModelName(modelMap.getOrDefault(orderVideoCurrentInfoVO.getIntentionModelId(), new ModelInfoVO()).getName());

            for (Long referencePicId : StringUtils.splitToLong(orderVideoCurrentInfoVO.getReferencePicId(), StrUtil.COMMA)) {
                orderVideoCurrentInfoVO.getReferencePic().add(resourceMap.getOrDefault(referencePicId, new OrderResource()).getObjectKey());
            }

            OrderVideoChangeLogDTO orderVideoChangeLogDTO = new OrderVideoChangeLogDTO();
            orderVideoChangeLogDTO.setVideoId(orderVideoCurrentInfoVO.getId());
            orderVideoChangeLogDTO.setLogType(ChangeLogTypeEnum.INIT_LOG.getCode());
            orderVideoChangeLogDTO.setData(ObjectUtils.convertObjectToMap(orderVideoCurrentInfoVO));
            orderVideoChangeLogDTOS.add(orderVideoChangeLogDTO);
        }

        orderVideoChangeLogService.addVideoChangeLog(orderVideoChangeLogDTOS);
    }


    @Override
    public void closeOrder(String payPlatformType, String outTradeNo) {
        CloseOrderRequestVo closeOrderRequestVo = new CloseOrderRequestVo(payConfig.getMchntId(), payPlatformType, outTradeNo, payConfig.getMchntKey());
        RequestBody requestBody = RequestBody.Companion.create(closeOrderRequestVo.getUnderlinePreOrderJsonStr(), MediaType.parse(CommonConstant.JSON_MEDIATYPE));
        String url = payConfig.getBaseUrl() + PayConstant.CLOSE_ORDER;
        final Request request = new Request.Builder().url(url).post(requestBody).build();
        Call call = okHttpClient.newCall(request);
        try (Response response = call.execute()) {
            if (response.body() != null && response.isSuccessful()) {
                CloseOrderResponseVo closeOrderResponseVo = JSON.parseObject(response.body().string(), CloseOrderResponseVo.class);
                log.info("http关闭订单closeOrder成功：{}", closeOrderResponseVo.toString());
                Assert.isTrue(closeOrderResponseVo.getResultCode().equals(PayStatusCodeEnum.SUCCESS.getCode()), closeOrderResponseVo.getResultMsg());
            } else {
                log.error("请求失败，http关闭订单closeOrder失败！");
            }
        } catch (IOException e) {
            log.error("io异常，http关闭订单closeOrder失败：{}", e);
        }
    }

    @Override
    public void addOrderMemberFlow(AddOrderMemberFlowDTO dto, LoginUserInfoVO loginUserInfoVo) {
        try {
            OrderMemberFlow orderMemberFlow = new OrderMemberFlow();
            orderMemberFlow.setUserId(loginUserInfoVo.getUserId());
            orderMemberFlow.setEventExecuteObject(loginUserInfoVo.getUserType());
            orderMemberFlow.setEventExecuteUser(loginUserInfoVo.getName());
            orderMemberFlow.setEventExecuteNickName(loginUserInfoVo.getNickName());
            orderMemberFlow.setEventExecutePhone(loginUserInfoVo.getPhone());
            orderMemberFlow.setEventExecuteTime(DateUtil.date());

            orderMemberFlow.setOrderNum(dto.getOrderNum());
            orderMemberFlow.setEventName(dto.getEventName());
            orderMemberFlow.setOriginStatus(dto.getOriginStatus());
            orderMemberFlow.setTargetStatus(dto.getTargetStatus());

            orderMemberFlowService.save(orderMemberFlow);
        } catch (Exception e) {
            log.warn("请求参数：{}", dto.toString());
            log.error("添加流水失败：{}", e);
        }
    }
}
