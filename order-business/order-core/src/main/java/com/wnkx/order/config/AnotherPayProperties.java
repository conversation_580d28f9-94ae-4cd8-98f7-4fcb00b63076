package com.wnkx.order.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/6 14:05
 */
@Component
@ConfigurationProperties(prefix = "pay.anotherpay")
@Data
@RefreshScope
public class AnotherPayProperties {

    /**
     * 代付链接前缀
     */
    private String linkPrefix;

    /**
     * 代付链接有效时间（单位：小时 24小时制）
     */
    private Integer timeLimit;
}
