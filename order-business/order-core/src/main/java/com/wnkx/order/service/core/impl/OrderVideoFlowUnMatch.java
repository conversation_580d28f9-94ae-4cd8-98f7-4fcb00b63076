package com.wnkx.order.service.core.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.system.api.domain.dto.order.OrderVideoFlowDTO;
import com.ruoyi.system.api.domain.dto.order.SaveBatchOrderVideoMatchDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.wnkx.order.config.OrderVideoProperties;
import com.wnkx.order.service.OrderVideoFlowRecordService;
import com.wnkx.order.service.OrderVideoMatchService;
import com.wnkx.order.service.core.OrderVideoFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 视频订单流转至待匹配
 *
 * <AUTHOR>
 * @date 2024/6/29 10:40
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoFlowUnMatch implements OrderVideoFlowService {
    private final OrderVideoFlowRecordService orderVideoFlowRecordService;
    private final OrderVideoMatchService orderVideoMatchService;
    @Override
    public void orderVideoFlow(List<OrderVideo> orderVideos, String eventName) {
        //  ->待匹配
        //  a:订单状态修改
        //  b:设置对接人id
        DateTime date = DateUtil.date();

        List<OrderVideoFlowDTO> orderVideoFlowDTOS = new ArrayList<>();
        List<SaveBatchOrderVideoMatchDTO> saveBatchOrderVideoMatchDTOS = new ArrayList<>();
        for (OrderVideo orderVideo : orderVideos) {
            orderVideoFlowDTOS.add(OrderVideoFlowDTO.builder().videoId(orderVideo.getId()).originStatus(orderVideo.getStatus()).targetStatus(OrderStatusEnum.UN_MATCH.getCode()).build());

            orderVideo.setStatus(OrderStatusEnum.UN_MATCH.getCode());
            orderVideo.setStatusTime(date);
            //  流转到待匹配 设置释放时间
            orderVideo.setReleaseTime(date);
            if (ObjectUtil.isNull(orderVideo.getFirstMatchTime())){
                //  流转到待匹配 设置首次匹配时间，
                orderVideo.setFirstMatchTime(date);
            }
            saveBatchOrderVideoMatchDTOS.add(SaveBatchOrderVideoMatchDTO.builder().videoId(orderVideo.getId()).rollbackId(orderVideo.getRollbackId()).isRollback(orderVideo.getIsRollback()).modelId(orderVideo.getIntentionModelId()).build());
        }
        orderVideoFlowRecordService.createOrderVideoFlow(eventName, orderVideoFlowDTOS);
        orderVideoMatchService.saveBatchOrderVideoMatch(saveBatchOrderVideoMatchDTOS);
    }

    @Override
    public OrderStatusEnum getOrderStatusType() {
        return OrderStatusEnum.UN_MATCH;
    }
}
