package com.wnkx.order.service.core.impl.logistic;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.EventExecuteObjectEnum;
import com.ruoyi.common.core.enums.FollowStatusEnum;
import com.ruoyi.common.core.enums.ReissueEnum;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFlowDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFollowDTO;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.wnkx.order.service.OrderVideoLogisticFollowService;
import com.wnkx.order.service.core.OrderVideoLogisticFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :已发货
 * @create :2025-04-23 11:38
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoLogisticFlowShip implements OrderVideoLogisticFlowService {

    private final OrderVideoLogisticFollowService orderVideoLogisticFollowService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void OrderVideoLogisticFlow(OrderVideoLogisticFlowDTO dto) {
        this.checkStatus(dto);
        LoginUserInfoVO loginUserInfoVo = dto.getLoginUserInfoVO();
        //初始化数据
        OrderVideoLogisticFollow orderVideoLogisticFollow = dto.getOrderVideoLogisticFollow();
        if (ObjectUtil.isNull(orderVideoLogisticFollow.getId())) {
            orderVideoLogisticFollow.setCreateBy(loginUserInfoVo.getName());
            orderVideoLogisticFollow.setCreateById(loginUserInfoVo.getUserId());
            orderVideoLogisticFollow.setCreateTime(new Date());
            orderVideoLogisticFollow.setUpdateBy(loginUserInfoVo.getName());
            orderVideoLogisticFollow.setUpdateById(loginUserInfoVo.getUserId());
            orderVideoLogisticFollow.setUpdateTime(new Date());
            orderVideoLogisticFollow.setFollowStatus(this.getFollowStatusType().getCode());
            orderVideoLogisticFollowService.save(orderVideoLogisticFollow);
        } else {
            OrderVideoLogisticFollow updateEntity = new OrderVideoLogisticFollow();
            updateEntity.setId(orderVideoLogisticFollow.getId());
            updateEntity.setNumber(dto.getLogisticNum());
            updateEntity.setOrderVideoLogisticId(dto.getOrderVideoLogisticId());
            updateEntity.setLogisticStatus(this.getFollowStatusType().getLogisticStatus());
            updateEntity.setFollowStatus(this.getFollowStatusType().getCode());

            if (EventExecuteObjectEnum.BACK.getCode().equals(loginUserInfoVo.getUserType())) {
                updateEntity.setUpdateById(loginUserInfoVo.getUserId());
                updateEntity.setUpdateBy(loginUserInfoVo.getName());
            }
            updateEntity.setUpdateTime(new Date());
            orderVideoLogisticFollowService.updateById(updateEntity);
        }
    }

    @Override
    public void checkStatus(OrderVideoLogisticFlowDTO dto) {
        Assert.notNull(dto, "已发货：参数不能为空~");
        Assert.notNull(dto.getVideoId(), "已发货：视频订单id不能为空~");
        Assert.notNull(dto.getReissue(), "已发货：是否补发不能为空~");
        Assert.notNull(dto.getVideoCode(), "已发货：视频订单编码不能为空~");
        Assert.notNull(dto.getBusinessId(), "已发货：商家id不能为空~");
        Assert.notNull(dto.getMemberCode(), "已发货：会员编码不能为空~");
        Assert.notNull(dto.getOrderVideoLogisticId(), "已发货：物流关联表id不能为空~");
        Assert.notNull(dto.getLogisticNum(), "已发货：物流单号不能为空~");
        if (ReissueEnum.REISSUE.getCode().equals(dto.getReissue())) {
            //补发 直接初始化数据
            initFollowEntity(dto);
        } else if (ObjectUtil.isNull(dto.getOrderVideoLogisticFollow())) {
            List<OrderVideoLogisticFollow> orderVideoLogisticFollows = orderVideoLogisticFollowService.queryListBase(OrderVideoLogisticFollowDTO.builder().videoId(dto.getVideoId()).build());
            //传入数据未空 数据库需要也为空 否则就是没传跟进数据
            Assert.isTrue(CollUtil.isEmpty(orderVideoLogisticFollows), "已发货：物流跟进数据不能为空~");
            initFollowEntity(dto);
        }
    }

    private void initFollowEntity(OrderVideoLogisticFlowDTO dto) {
        OrderVideoLogisticFollow orderVideoLogisticFollow = new OrderVideoLogisticFollow();
        orderVideoLogisticFollow.setOrderVideoLogisticId(dto.getOrderVideoLogisticId());
        orderVideoLogisticFollow.setBusinessId(dto.getBusinessId());
        orderVideoLogisticFollow.setMemberCode(dto.getMemberCode());
        orderVideoLogisticFollow.setVideoCode(dto.getVideoCode());
        orderVideoLogisticFollow.setVideoId(dto.getVideoId());
        orderVideoLogisticFollow.setNumber(dto.getLogisticNum());
        orderVideoLogisticFollow.setLogisticStatus(this.getFollowStatusType().getLogisticStatus());
        orderVideoLogisticFollow.setFollowStatus(this.getFollowStatusType().getCode());
        dto.setOrderVideoLogisticFollow(orderVideoLogisticFollow);
    }

    @Override
    public FollowStatusEnum getFollowStatusType() {
        return FollowStatusEnum.SHIP;
    }
}
