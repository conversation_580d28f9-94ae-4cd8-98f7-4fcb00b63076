package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.ReminderStatusEnum;
import com.ruoyi.system.api.domain.dto.order.OrderVideoReminderCountVO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoReminderRecordListDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoReminderRecordListVO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoReminderRecord;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/14 16:15
 */
@Mapper
public interface OrderVideoReminderRecordMapper extends SuperMapper<OrderVideoReminderRecord> {
    default List<OrderVideoReminderRecord> selectListByVideoIds(List<Long> videoIds) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoReminderRecord>()
                .in(OrderVideoReminderRecord::getVideoId, videoIds)
        );
    }

    /**
     * 催单列表
     */
    List<OrderVideoReminderRecordListVO> selectListByCondition(@Param("dto") OrderVideoReminderRecordListDTO dto);

    /**
     * 总催单次数统计
     */
    OrderVideoReminderCountVO reminderCount(@Param("status") Map<String, Integer> codeMap);

    /**
     * 更新催单记录为已完成
     */
    default void updateReminderStatusToFinished(List<Long> videoIds) {
        this.update(null, new LambdaUpdateWrapper<OrderVideoReminderRecord>()
                .in(OrderVideoReminderRecord::getVideoId, videoIds)
                .ne(OrderVideoReminderRecord::getStatus, ReminderStatusEnum.FINISHED.getCode())
                .set(OrderVideoReminderRecord::getStatus, ReminderStatusEnum.FINISHED.getCode())
        );
    }

    /**
     * 运营已催单
     */
    default void urged(Long videoId) {
        this.update(null, new LambdaUpdateWrapper<OrderVideoReminderRecord>()
                .eq(OrderVideoReminderRecord::getVideoId, videoId)
                .eq(OrderVideoReminderRecord::getStatus, ReminderStatusEnum.UNTREATED.getCode())
                .set(OrderVideoReminderRecord::getStatus, ReminderStatusEnum.CONFIRM.getCode())
        );

    }

    /**
     * 获取视频订单有几条催单记录
     *
     * @param videoId 视频订单id
     * @return 视频订单关联数量
     */
    default Long selectByVideoCount(Long videoId) {
        return this.selectCount(new LambdaQueryWrapper<OrderVideoReminderRecord>()
                .eq(OrderVideoReminderRecord::getVideoId, videoId)
        );
    }
}
