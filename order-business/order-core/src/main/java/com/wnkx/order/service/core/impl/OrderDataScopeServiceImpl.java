package com.wnkx.order.service.core.impl;

import cn.hutool.core.lang.Assert;
import com.ruoyi.common.core.constant.ErrorConstants;
import com.ruoyi.common.core.exception.PreAuthorizeException;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.CheckDataScopeDTO;
import com.ruoyi.system.api.model.LoginBusiness;
import com.ruoyi.system.api.model.LoginModel;
import com.ruoyi.system.api.model.LoginUser;
import com.wnkx.order.service.IOrderVideoService;
import com.wnkx.order.service.ModelPersonService;
import com.wnkx.order.service.core.OrderDataScopeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/7/19 17:11
 */
@Component
@RequiredArgsConstructor
public class OrderDataScopeServiceImpl implements OrderDataScopeService {
    private final String NO_MANAGER_ERROR_TIPS = "视频订单没有关联中文部客服或者英文部客服，不允许操作";

    /**
     * 校验数据权限
     */
    @Override
    public void checkDataScope(CheckDataScopeDTO dto) {
        LoginBaseEntity loginUser = SecurityUtils.getLoginUser();

        if (SecurityUtils.isAdmin(loginUser.getUserid())) {
            Boolean check = SpringUtils.getBean(IOrderVideoService.class).checkAdminDataScope(dto.getVideoIds());
            Assert.isTrue(check, () -> new PreAuthorizeException(ErrorConstants.NO_MANAGER_ERROR_TIPS));
            return;
        }

        if (loginUser instanceof LoginUser) {
            Boolean check = SpringUtils.getBean(IOrderVideoService.class).checkManagerDataScope(dto, loginUser.getUserid());
            Assert.isFalse(check, () -> new PreAuthorizeException(ErrorConstants.ERROR_TIPS));
        } else if (loginUser instanceof LoginBusiness) {
            Boolean check = SpringUtils.getBean(IOrderVideoService.class).checkUserDataScope(dto, ((LoginBusiness) loginUser).getBusinessAccountVO());
            Assert.isFalse(check, () -> new PreAuthorizeException(ErrorConstants.ERROR_TIPS));
        } else if (loginUser instanceof LoginModel) {
            Boolean check = SpringUtils.getBean(ModelPersonService.class).checkModelDataScope(dto.getVideoIds(), loginUser.getUserid());
            Assert.isFalse(check, () -> new PreAuthorizeException(ErrorConstants.ERROR_TIPS_ENGLISH));
        } else {
            throw new PreAuthorizeException(ErrorConstants.ERROR_TIPS);
        }
    }

    // @Override
    // public void checkDataScope(Long videoId, boolean businessShare) {
    //     checkDataScope(List.of(videoId), businessShare);
    // }
}
