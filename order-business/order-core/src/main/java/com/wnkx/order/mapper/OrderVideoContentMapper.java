package com.wnkx.order.mapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.system.api.domain.entity.order.OrderVideoContent;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 视频_关联内容Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Mapper
public interface OrderVideoContentMapper extends SuperMapper<OrderVideoContent> {

    /**
     * 根据视频id查询关联内容
     *
     * @param id 视频订单id
     * @return 关联内容
     */
    default List<OrderVideoContent> selectListByVideoIdOrType(Long id, List<Integer> contentTypes) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoContent>()
                .eq(ObjectUtil.isNotNull(id), OrderVideoContent::getVideoId, id)
                .in(CollUtil.isNotEmpty(contentTypes), OrderVideoContent::getType, contentTypes)
                .isNotNull(OrderVideoContent::getContent)
                .orderByAsc(OrderVideoContent::getSort)
                .orderByDesc(OrderVideoContent::getCreateTime)
        );
    }

    /**
     * 根据视频id删除关联内容
     */
    default void removeByVideoId(Collection<Long> videoIds, Collection<Integer> types) {
        delete(new LambdaQueryWrapper<OrderVideoContent>()
                .in(OrderVideoContent::getVideoId, videoIds)
                .in(CollUtil.isNotEmpty(types), OrderVideoContent::getType, types)
        );
    }

    /**
     * 根据视频id查询关联内容
     *
     * @param videoIds 视频订单id
     * @return 关联内容
     */
    default List<OrderVideoContent> selectListByVideoIds(List<Long> videoIds) {
        return selectList(new LambdaQueryWrapper<OrderVideoContent>()
                .in(OrderVideoContent::getVideoId, videoIds)
                .isNotNull(OrderVideoContent::getContent)
        );
    }
    /**
     * 根据视频id、类型查询关联内容
     *
     * @param videoIds 视频订单id
     * @param types 类型
     * @return 关联内容
     */
    default List<OrderVideoContent> selectListByVideoIdsAndTypesAsc(Collection<Long> videoIds, Collection<Integer> types) {
        return selectList(new LambdaQueryWrapper<OrderVideoContent>()
                .in(OrderVideoContent::getVideoId, videoIds)
                .in(CollUtil.isNotEmpty(types), OrderVideoContent::getType, types)
                .orderByAsc(OrderVideoContent::getVideoId)
                .orderByAsc(OrderVideoContent::getType)
                .orderByAsc(OrderVideoContent::getSort)
        );
    }

    /**
     * 变更调字内容
     * @param videoId
     */
    default void rollbackOrderVideoContent(Long videoId) {
        List<OrderVideoContent> list = this.selectList(Wrappers.lambdaQuery(OrderVideoContent.class).eq(OrderVideoContent::getVideoId, videoId));
        for (OrderVideoContent item : list) {
            item.setFirstEdit(StatusTypeEnum.NO.getCode());
            item.setFirstContent(item.getContent());
        }
        this.updateBatchById(list);
    }
}
