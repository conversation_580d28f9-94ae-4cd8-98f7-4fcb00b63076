package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderVideoModelSearch;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订单_视频_模特搜索记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-02
 */
@Mapper
public interface OrderVideoModelSearchMapper extends SuperMapper<OrderVideoModelSearch> {
    /**
     * 通过模特id查询搜索记录
     *
     * @param modelId 模特id
     * @return 搜索记录
     */
    default List<OrderVideoModelSearch> selectOrderVideoModelSearchListByModelId(Long modelId) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoModelSearch>()
                .eq(OrderVideoModelSearch::getModelId, modelId)
                .orderByDesc(OrderVideoModelSearch::getCreateTime));
    }

    /**
     * 通过搜索内容查询搜索记录
     *
     * @param content 搜索内容
     * @return        搜索记录
     */
    default OrderVideoModelSearch getOneByContent(String content) {
        return this.selectOne(new LambdaQueryWrapper<OrderVideoModelSearch>()
                .eq(OrderVideoModelSearch::getContent, content)
                .last("limit 1"));
    }
}
