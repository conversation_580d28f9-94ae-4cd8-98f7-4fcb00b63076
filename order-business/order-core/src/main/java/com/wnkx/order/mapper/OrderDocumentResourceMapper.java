package com.wnkx.order.mapper;

import com.ruoyi.system.api.domain.entity.order.OrderDocumentResource;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订单支付凭证关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-21
 */
@Mapper
public interface OrderDocumentResourceMapper extends SuperMapper<OrderDocumentResource>
{
    /**
     * 查询订单支付凭证关联
     * 
     * @param id 订单支付凭证关联主键
     * @return 订单支付凭证关联
     */
    public OrderDocumentResource selectOrderDocumentResourceById(Long id);

    /**
     * 查询订单支付凭证关联列表
     * 
     * @param orderDocumentResource 订单支付凭证关联
     * @return 订单支付凭证关联集合
     */
    public List<OrderDocumentResource> selectOrderDocumentResourceList(OrderDocumentResource orderDocumentResource);

    /**
     * 根据keyword获取支付凭证
     * @param keyword 支付单号、订单号
     * @return
     */
    public List<OrderDocumentResource> selectOrderDocumentResourceListByKeyword(String keyword);

    /**
     * 新增订单支付凭证关联
     * 
     * @param orderDocumentResource 订单支付凭证关联
     * @return 结果
     */
    public int insertOrderDocumentResource(OrderDocumentResource orderDocumentResource);

    /**
     * 修改订单支付凭证关联
     * 
     * @param orderDocumentResource 订单支付凭证关联
     * @return 结果
     */
    public int updateOrderDocumentResource(OrderDocumentResource orderDocumentResource);

    /**
     * 删除订单支付凭证关联
     * 
     * @param id 订单支付凭证关联主键
     * @return 结果
     */
    public int deleteOrderDocumentResourceById(Long id);

    /**
     * 批量删除订单支付凭证关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrderDocumentResourceByIds(Long[] ids);
}
