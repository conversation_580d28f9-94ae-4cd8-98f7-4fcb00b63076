package com.wnkx.order.mapper;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderAuditFlow;
import com.wnkx.db.mapper.SuperMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_audit_flow(订单财务审核流水)】的数据库操作Mapper
* @createDate 2024-12-18 18:09:39
* @Entity com.ruoyi.system.api.domain.entity.order.OrderAuditFlow
*/
public interface OrderAuditFlowMapper extends SuperMapper<OrderAuditFlow> {

    /**
     * 根据订单号获取审核流水数据
     * @param orderNum
     * @param payNum
     * @return
     */
    default List<OrderAuditFlow> getListByOrderNumOrPayNum(String orderNum, String payNum){
        return this.selectList(new LambdaQueryWrapper<OrderAuditFlow>()
                .eq(StrUtil.isNotBlank(orderNum), OrderAuditFlow::getOrderNum, orderNum)
                .eq(StrUtil.isNotBlank(payNum), OrderAuditFlow::getPayNum, payNum)
        );

    }
}




