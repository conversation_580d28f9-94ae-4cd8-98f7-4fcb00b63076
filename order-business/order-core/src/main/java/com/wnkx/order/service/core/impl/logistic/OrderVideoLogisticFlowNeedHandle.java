package com.wnkx.order.service.core.impl.logistic;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.enums.EventExecuteObjectEnum;
import com.ruoyi.common.core.enums.FollowStatusEnum;
import com.ruoyi.common.core.enums.HandleStatusEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFlowDTO;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollowRecord;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.wnkx.order.service.OrderResourceService;
import com.wnkx.order.service.OrderVideoLogisticFollowRecordService;
import com.wnkx.order.service.OrderVideoLogisticFollowService;
import com.wnkx.order.service.core.OrderVideoLogisticFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :需处理
 * @create :2025-04-23 11:34
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoLogisticFlowNeedHandle implements OrderVideoLogisticFlowService {
    private final OrderResourceService orderResourceService;
    private final OrderVideoLogisticFollowService orderVideoLogisticFollowService;
    private final OrderVideoLogisticFollowRecordService orderVideoLogisticFollowRecordService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void OrderVideoLogisticFlow(OrderVideoLogisticFlowDTO dto) {
        //初始化数据
        this.checkStatus(dto);
        LoginUserInfoVO loginUserInfoVo = dto.getLoginUserInfoVO();
        OrderVideoLogisticFollow orderVideoLogisticFollow = dto.getOrderVideoLogisticFollow();
        if (List.of(HandleStatusEnum.UN_NOTIFIED.getCode(), HandleStatusEnum.UN_NOTIFIED_CONFIRM_MODEL.getCode()).contains(dto.getHandleStatus().getCode())) {
            orderVideoLogisticFollow = new OrderVideoLogisticFollow();
            orderVideoLogisticFollow.setBusinessId(dto.getBusinessId());
            orderVideoLogisticFollow.setMemberCode(dto.getMemberCode());
            orderVideoLogisticFollow.setVideoCode(dto.getVideoCode());
            orderVideoLogisticFollow.setVideoId(dto.getVideoId());
            orderVideoLogisticFollow.setHandleStatus(dto.getHandleStatus().getCode());

            orderVideoLogisticFollow.setLogisticStatus(this.getFollowStatusType().getLogisticStatus());
            orderVideoLogisticFollow.setFollowStatus(this.getFollowStatusType().getCode());

            orderVideoLogisticFollow.setCreateBy(loginUserInfoVo.getName());
            orderVideoLogisticFollow.setCreateById(loginUserInfoVo.getUserId());
            orderVideoLogisticFollow.setCreateTime(new Date());
            orderVideoLogisticFollow.setUpdateBy(loginUserInfoVo.getName());
            orderVideoLogisticFollow.setUpdateById(loginUserInfoVo.getUserId());
            orderVideoLogisticFollow.setUpdateTime(new Date());
            orderVideoLogisticFollowService.resetEntity(orderVideoLogisticFollow);
        } else {
            OrderVideoLogisticFollow updateEntity = new OrderVideoLogisticFollow();
            updateEntity.setId(orderVideoLogisticFollow.getId());
            updateEntity.setHandleStatus(dto.getHandleStatus().getCode());

            updateEntity.setLogisticStatus(this.getFollowStatusType().getLogisticStatus());
            updateEntity.setFollowStatus(this.getFollowStatusType().getCode());
            updateEntity.setIsDefaultLogisticStartTime(StatusTypeEnum.NO.getCode());

            if (EventExecuteObjectEnum.BACK.getCode().equals(loginUserInfoVo.getUserType())) {
                updateEntity.setUpdateById(loginUserInfoVo.getUserId());
                updateEntity.setUpdateBy(loginUserInfoVo.getName());
            }
            updateEntity.setUpdateTime(new Date());
            orderVideoLogisticFollowService.updateById(updateEntity);
        }
        //添加跟进记录数据
        OrderVideoLogisticFollowRecord orderVideoLogisticFollowRecord = new OrderVideoLogisticFollowRecord();
        orderVideoLogisticFollowRecord.setFollowId(orderVideoLogisticFollow.getId());
//        List<Long> resourceIds = orderResourceService.saveBatchOrderResource(dto.getResourceIds());
//        orderVideoLogisticFollowRecord.setResourceId(StrUtil.join(StrUtil.COMMA, resourceIds));
        orderVideoLogisticFollowRecord.setEventName(dto.getHandleStatus().getLabel());
        if (HandleStatusEnum.URGE_SHIPPING_REMINDER == dto.getHandleStatus()) {
            orderVideoLogisticFollowRecord.setEventContent(StrUtil.format(dto.getHandleStatus().getDescription(), HandleStatusEnum.NOTIFIED.getCode().equals(orderVideoLogisticFollow.getHandleStatus()) ? "5" : "3"));
        } else if (HandleStatusEnum.ADDRESS_CHANGE_NOTICE == dto.getHandleStatus()) {
            orderVideoLogisticFollowRecord.setEventContent(StrUtil.format(dto.getHandleStatus().getDescription(), dto.getModelName()));
        } else if (HandleStatusEnum.DELAY_REMINDER_OTHER == dto.getHandleStatus()) {
            //流转延迟发货提醒 如果是 延迟发货状态 则是5天后 不是则代表是 延迟发货已提醒  是3天后
            orderVideoLogisticFollowRecord.setEventContent(StrUtil.format(dto.getHandleStatus().getDescription(), HandleStatusEnum.DELAY_SHIPPING.getCode().equals(orderVideoLogisticFollow.getHandleStatus()) ? "5" : "3"));
        }  else if (HandleStatusEnum.URGE_CONFIRM_MODEL_UN_NOTIFIED == dto.getHandleStatus()) {
            //流转催确认模特提醒  如果是 已通知确认模特  则是5天后  不是则代表是 已通知催确认模特  是3天后
            orderVideoLogisticFollowRecord.setEventContent(StrUtil.format(dto.getHandleStatus().getDescription(), HandleStatusEnum.NOTIFIED_CONFIRM_MODEL.getCode().equals(orderVideoLogisticFollow.getHandleStatus()) ? "5" : "3"));
        }  else if (HandleStatusEnum.NOTIFIED_SHIPPING_REMINDER == dto.getHandleStatus()) {
            //流转标记发货提醒  如果是 标记发货  则是5天后  不是则代表是 标记发货已提醒  是3天后
            orderVideoLogisticFollowRecord.setEventContent(StrUtil.format(dto.getHandleStatus().getDescription(), HandleStatusEnum.NOTIFIED_SHIPPING.getCode().equals(orderVideoLogisticFollow.getHandleStatus()) ? "5" : "3"));
        } else {
            orderVideoLogisticFollowRecord.setEventContent(dto.getHandleStatus().getDescription());
        }
        orderVideoLogisticFollowRecord.setEventExecuteObject(loginUserInfoVo.getUserType());
        orderVideoLogisticFollowRecord.setEventExecuteUserId(loginUserInfoVo.getUserId());
        orderVideoLogisticFollowRecord.setEventExecuteUserName(loginUserInfoVo.getName());
        orderVideoLogisticFollowRecord.setEventExecuteNickName(loginUserInfoVo.getNickName());
        orderVideoLogisticFollowRecord.setEventExecuteTime(new Date());
        orderVideoLogisticFollowRecord.setRemark(dto.getRemark());
        orderVideoLogisticFollowRecordService.save(orderVideoLogisticFollowRecord);
    }

    @Override
    public void checkStatus(OrderVideoLogisticFlowDTO dto) {
        Assert.notNull(dto, "需处理：参数不能为空~");
        Assert.notNull(dto.getHandleStatus(), "需处理：处理状态不能为空~");
        Assert.isTrue(HandleStatusEnum.UN_NOTIFIED == dto.getHandleStatus()
                        || HandleStatusEnum.UN_NOTIFIED_CONFIRM_MODEL == dto.getHandleStatus()
                        || dto.getHandleStatus().getFollowStatusEnum().getLogisticStatus().equals(this.getFollowStatusType().getLogisticStatus()),
                "需处理-{}：物流跟进状态不正确~", dto.getHandleStatus().getLabel());
        if (HandleStatusEnum.UN_NOTIFIED == dto.getHandleStatus()) {
            Assert.notNull(dto.getVideoId(), "需处理-未通知：视频订单id不能为空~");
            Assert.notNull(dto.getVideoCode(), "需处理-未通知：视频订单编码不能为空~");
            Assert.notNull(dto.getBusinessId(), "需处理-未通知：商家id不能为空~");
            Assert.notNull(dto.getMemberCode(), "需处理-未通知：会员编码不能为空~");
            Assert.isNull(dto.getOrderVideoLogisticFollow(), "需处理-未通知：物流跟进数据不应存在数据~");
        } else if (HandleStatusEnum.UN_NOTIFIED_CONFIRM_MODEL == dto.getHandleStatus()) {
            Assert.notNull(dto.getVideoId(), "需处理-通知确认模特：视频订单id不能为空~");
            Assert.notNull(dto.getVideoCode(), "需处理-通知确认模特：视频订单编码不能为空~");
            Assert.notNull(dto.getBusinessId(), "需处理-通知确认模特：商家id不能为空~");
            Assert.notNull(dto.getMemberCode(), "需处理-通知确认模特：会员编码不能为空~");
            Assert.isNull(dto.getOrderVideoLogisticFollow(), "需处理-通知确认模特：物流跟进数据不应存在数据~");
        } else if (HandleStatusEnum.DELAY_REMINDER == dto.getHandleStatus()) {
            Assert.notNull(dto.getOrderVideoLogisticFollow(), "需处理-延迟发货提醒：物流跟进数据不能为空~");
            Assert.isTrue(this.getFollowStatusType().getLogisticStatus().equals(dto.getOrderVideoLogisticFollow().getLogisticStatus()), "需处理-延迟发货提醒：发货状态有误~");
            orderVideoLogisticFollowService.checkHandleStatus(Arrays.asList(dto.getOrderVideoLogisticFollow()), HandleStatusEnum.DELAY_SHIPPING, HandleStatusEnum.DELAY_NOTIFIED);
        } else if (HandleStatusEnum.DELAY_REMINDER_OTHER == dto.getHandleStatus()) {
            Assert.notNull(dto.getOrderVideoLogisticFollow(), "需处理-延迟发货提醒：物流跟进数据不能为空~");
            Assert.isTrue(this.getFollowStatusType().getLogisticStatus().equals(dto.getOrderVideoLogisticFollow().getLogisticStatus()), "需处理-延迟发货提醒：发货状态有误~");
            orderVideoLogisticFollowService.checkHandleStatus(Arrays.asList(dto.getOrderVideoLogisticFollow()), HandleStatusEnum.DELAY_SHIPPING, HandleStatusEnum.DELAY_NOTIFIED);
        } else if (HandleStatusEnum.URGE_SHIPPING_REMINDER == dto.getHandleStatus()) {
            Assert.notNull(dto.getOrderVideoLogisticFollow(), "需处理-催发货提醒：物流跟进数据不能为空~");
            Assert.isTrue(this.getFollowStatusType().getLogisticStatus().equals(dto.getOrderVideoLogisticFollow().getLogisticStatus()), "需处理-催发货提醒：发货状态有误~");
            orderVideoLogisticFollowService.checkHandleStatus(Arrays.asList(dto.getOrderVideoLogisticFollow()), HandleStatusEnum.NOTIFIED, HandleStatusEnum.URGE_SHIPPING_NOTIFIED, HandleStatusEnum.CHANGE_NOTIFIED);
        } else if (HandleStatusEnum.ADDRESS_CHANGE_NOTICE == dto.getHandleStatus()) {
            Assert.notNull(dto.getOrderVideoLogisticFollow(), "需处理-地址变更通知：物流跟进数据不能为空~");
            Assert.isTrue(this.getFollowStatusType().getLogisticStatus().equals(dto.getOrderVideoLogisticFollow().getLogisticStatus()), "需处理-地址变更通知：发货状态有误~");
            Assert.notNull(dto.getModelName(), "需处理-地址变更通知：模特名称不能为空~");
        } else if (HandleStatusEnum.NOTIFIED_SHIPPING_REMINDER == dto.getHandleStatus()) {
            Assert.notNull(dto.getOrderVideoLogisticFollow(), "需处理-标记发货提醒：物流跟进数据不能为空~");
            Assert.isTrue(this.getFollowStatusType().getLogisticStatus().equals(dto.getOrderVideoLogisticFollow().getLogisticStatus()), "需处理-标记发货提醒：发货状态有误~");
            orderVideoLogisticFollowService.checkHandleStatus(Arrays.asList(dto.getOrderVideoLogisticFollow()), HandleStatusEnum.NOTIFIED_SHIPPING, HandleStatusEnum.NOTIFIED_SHIPPING_NOTIFIED);
        }  else if (HandleStatusEnum.URGE_CONFIRM_MODEL_UN_NOTIFIED == dto.getHandleStatus()) {
            Assert.notNull(dto.getOrderVideoLogisticFollow(), "需处理-催发货提醒：物流跟进数据不能为空~");
            Assert.isTrue(this.getFollowStatusType().getLogisticStatus().equals(dto.getOrderVideoLogisticFollow().getLogisticStatus()), "需处理-催发货提醒：发货状态有误~");
            orderVideoLogisticFollowService.checkHandleStatus(Arrays.asList(dto.getOrderVideoLogisticFollow()), HandleStatusEnum.NOTIFIED_CONFIRM_MODEL, HandleStatusEnum.URGE_CONFIRM_MODEL_NOTIFIED);
        } else {
            throw new RuntimeException("需处理：处理状态不正确~");
        }
    }

    @Override
    public FollowStatusEnum getFollowStatusType() {
        return FollowStatusEnum.NEED_HANDLE;
    }
}
