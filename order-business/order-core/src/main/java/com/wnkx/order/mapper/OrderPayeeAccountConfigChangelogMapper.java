package com.wnkx.order.mapper;
import com.ruoyi.common.core.enums.PayeeAccountChangeLogEnum;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigChangelogDTO;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigInfoDTO;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfigChangelog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_payee_account_config_changelog(收款人账号关联表)】的数据库操作Mapper
 * @createDate 2024-12-17 16:04:06
 * @Entity com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfigChangelog
 */
public interface OrderPayeeAccountConfigChangelogMapper extends BaseMapper<OrderPayeeAccountConfigChangelog> {

    default void saveNewChangeLog(OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO) {
        OrderPayeeAccountConfigChangelog orderPayeeAccountConfigChangelog = new OrderPayeeAccountConfigChangelog();
        orderPayeeAccountConfigChangelog.setType(PayeeAccountChangeLogEnum.NEW.getCode());
        orderPayeeAccountConfigChangelog.setComments(String.format(PayeeAccountChangeLogEnum.NEW.getComment(), orderPayeeAccountConfigInfoDTO.getAccountName()));
        orderPayeeAccountConfigChangelog.setCreateBy(SecurityUtils.getUsername());
        orderPayeeAccountConfigChangelog.setCreateById(SecurityUtils.getUserId());
        orderPayeeAccountConfigChangelog.setUpdateBy(SecurityUtils.getUsername());
        orderPayeeAccountConfigChangelog.setUpdateById(SecurityUtils.getUserId());
        orderPayeeAccountConfigChangelog.setConfigType(orderPayeeAccountConfigInfoDTO.getType());
        insert(orderPayeeAccountConfigChangelog);
    }

    List<OrderPayeeAccountConfigChangelogDTO> historyLog(Long type);

    default void saveUpdateChangeLog(String oldCompanyName, String companyName,Integer type) {
        OrderPayeeAccountConfigChangelog orderPayeeAccountConfigChangelog = new OrderPayeeAccountConfigChangelog();
        orderPayeeAccountConfigChangelog.setType(PayeeAccountChangeLogEnum.DEL.getCode());
        orderPayeeAccountConfigChangelog.setComments(String.format(PayeeAccountChangeLogEnum.DEL.getComment(), oldCompanyName, companyName));
        orderPayeeAccountConfigChangelog.setCreateBy(SecurityUtils.getUsername());
        orderPayeeAccountConfigChangelog.setCreateById(SecurityUtils.getUserId());
        orderPayeeAccountConfigChangelog.setUpdateBy(SecurityUtils.getUsername());
        orderPayeeAccountConfigChangelog.setUpdateById(SecurityUtils.getUserId());
        orderPayeeAccountConfigChangelog.setConfigType(type);
        insert(orderPayeeAccountConfigChangelog);
    }

    default void saveModifyChangeLog(String name,Integer type){
        OrderPayeeAccountConfigChangelog orderPayeeAccountConfigChangelog = new OrderPayeeAccountConfigChangelog();
        orderPayeeAccountConfigChangelog.setType(PayeeAccountChangeLogEnum.MODIFY.getCode());
        orderPayeeAccountConfigChangelog.setComments(String.format(PayeeAccountChangeLogEnum.MODIFY.getComment(), name));
        orderPayeeAccountConfigChangelog.setCreateBy(SecurityUtils.getUsername());
        orderPayeeAccountConfigChangelog.setCreateById(SecurityUtils.getUserId());
        orderPayeeAccountConfigChangelog.setUpdateBy(SecurityUtils.getUsername());
        orderPayeeAccountConfigChangelog.setUpdateById(SecurityUtils.getUserId());
        orderPayeeAccountConfigChangelog.setConfigType(type);
        insert(orderPayeeAccountConfigChangelog);
    }
}




