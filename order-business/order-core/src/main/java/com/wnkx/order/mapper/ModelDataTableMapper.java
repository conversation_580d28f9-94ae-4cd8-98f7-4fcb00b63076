package com.wnkx.order.mapper;

import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableOrderScheduledRecordDTO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableOrderScheduledRecordVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableOrderScheduledTagCountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/9 15:04
 */
@Mapper
public interface ModelDataTableMapper {

    /**
     * 排单记录
     */
    List<ModelDataTableOrderScheduledRecordVO> selectModelDataTableOrderScheduledRecordList(@Param("modelId") Long modelId, @Param("dto") ModelDataTableOrderScheduledRecordDTO dto);

    /**
     * 排单记录-标签统计
     */
    ModelDataTableOrderScheduledTagCountVO getModelDataTableOrderScheduledTagCount(@Param("modelId") Long modelId);
}
