package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceRecord;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:27
 */
@Mapper
public interface OrderInvoiceRecordMapper extends SuperMapper<OrderInvoiceRecord> {


    /**
     * 运营端-发票管理-开票记录
     */
    default List<OrderInvoiceRecord> getInvoiceRecord(Long invoiceId) {
        return selectList(new LambdaQueryWrapper<OrderInvoiceRecord>()
                .eq(OrderInvoiceRecord::getInvoiceId, invoiceId)
                .orderByDesc(OrderInvoiceRecord::getInvoicingTime)
        );
    }
}
