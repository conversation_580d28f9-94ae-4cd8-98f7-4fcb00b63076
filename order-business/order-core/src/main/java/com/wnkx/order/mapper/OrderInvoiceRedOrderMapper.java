package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceRedOrder;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:28
 */
@Mapper
public interface OrderInvoiceRedOrderMapper extends SuperMapper<OrderInvoiceRedOrder> {


    /**
     * 通过发票红冲ID获取发票红冲视频订单
     */
    default List<OrderInvoiceRedOrder> selectListByInvoiceRedIds(List<Long> invoiceRedIds) {
        return selectList(new LambdaQueryWrapper<OrderInvoiceRedOrder>()
                .in(OrderInvoiceRedOrder::getInvoiceRedId, invoiceRedIds)
        );
    }
}
