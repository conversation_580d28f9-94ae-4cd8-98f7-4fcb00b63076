package com.wnkx.order.mapper;

import com.ruoyi.system.api.domain.bo.order.datastatistics.OrderVideoDurationDataBO;
import com.ruoyi.system.api.domain.bo.order.datastatistics.OrderVideoTrendBO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.OrderVideoAfterSaleTypeAnalysisDetailVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.OrderVideoAverageDurationDataVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.OrderVideoBaseBoardVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.OrderVideoServiceCountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/5 13:55
 */
@Mapper
public interface OrderVideoDataStatisticsMapper {

    /**
     * 视频订单数据-基础看板
     */
    OrderVideoBaseBoardVO getOrderVideoBaseBoard();

    /**
     * 视频订单数据-服务中订单数
     */
    OrderVideoServiceCountVO getOrderVideoServiceCount();

    /**
     * 视频订单数据-订单趋势-新增订单数
     */
    List<OrderVideoTrendBO> getOrderNewCountByTimeBetween(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime, @Param("dateFormat") String dateFormat);

    /**
     * 视频订单数据-订单趋势-取消订单数
     */
    List<OrderVideoTrendBO> getOrderCancelCountByTimeBetween(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime, @Param("dateFormat") String dateFormat);

    /**
     * 视频订单数据-订单趋势-排单数
     */
    List<OrderVideoTrendBO> getOrderScheduledCountByTimeBetween(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime, @Param("dateFormat") String dateFormat);

    /**
     * 视频订单数据-订单匹配时长数据分析
     */
    List<OrderVideoDurationDataBO> selectOrderVideoMatchDurationDataList(@Param("date") String date);

    /**
     * 视频订单数据-订单发货时长数据分析
     */
    List<OrderVideoDurationDataBO> selectOrderVideoDeliveryDurationDataList(@Param("date") String date);

    /**
     * 获取平均发货时长（天）
     *
     * @return 平均发货时长（天）
     */
    BigDecimal getAverageDeliveryDuration(@Param("date") String date);

    /**
     * 视频订单数据-订单素材反馈时长数据分析
     */
    List<OrderVideoDurationDataBO> selectOrderVideoFeedbackDurationDataList(@Param("date") String date);

    /**
     * 获取平均素材反馈时长（天）
     *
     * @return 平均素材反馈时长（天）
     */
    BigDecimal getAverageFeedbackDuration(@Param("date") String date);

    /**
     * 视频订单数据-平均审单/服务时长、任务单数及占比、拖单数及占比、烂单数及占比
     */
    OrderVideoAverageDurationDataVO getOrderVideoAverageDurationData();

    /**
     * 视频订单数据-售后类型分析
     */
    List<OrderVideoAfterSaleTypeAnalysisDetailVO> getOrderVideoAfterSaleTypeAnalysis(@Param("date") String date);

    /**
     * 视频订单数据-补偿订单情况
     */
    List<BigDecimal> selectRefundAmountListByDate(@Param("date") String date);
}
