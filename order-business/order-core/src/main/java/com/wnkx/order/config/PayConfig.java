package com.wnkx.order.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/6/1
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "pay.fuyou")
public class PayConfig {
    /**
     * 订单前缀
     */
    private String orderPrefix;
    /**
     * 商户号
     */
    private String mchntId;
    /**
     * 密钥
     */
    private String mchntKey;
    /**
     * 服务器ip
     */
    private String serverIp;
    /**
     * 通知回调地址
     */
    private String notifyUrl;
    /**
     * 服务器地址
     */
    private String baseUrl;

}
