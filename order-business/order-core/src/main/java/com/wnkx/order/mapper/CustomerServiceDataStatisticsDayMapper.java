package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceDataStatisticsDay;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR> 
 * @Date 2025-05-16 09:34:05 
 */
@Mapper
public interface CustomerServiceDataStatisticsDayMapper extends SuperMapper<CustomerServiceDataStatisticsDay> {

    /**
     * 通过日期查询客服数据
     */
    default CustomerServiceDataStatisticsDay getByWriteTime(String date) {
        return selectOne(new LambdaQueryWrapper<CustomerServiceDataStatisticsDay>()
                .last("WHERE DATE_FORMAT(write_time_begin,'%Y-%m-%d') = '" + date + "'")
        );
    }
}
