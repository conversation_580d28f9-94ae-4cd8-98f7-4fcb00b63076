package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.ChangeLogTypeEnum;
import com.ruoyi.system.api.domain.entity.order.OrderVideoChangeLog;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:14
 */
@Mapper
public interface OrderVideoChangeLogMapper extends SuperMapper<OrderVideoChangeLog> {
    /**
     * 根据视频订单id查询变更记录
     */
    default List<OrderVideoChangeLog> selectVideoChangeListByVideoId(Long videoId) {
        return selectList(new LambdaQueryWrapper<OrderVideoChangeLog>()
                .eq(OrderVideoChangeLog::getVideoId, videoId)
                .orderByDesc(OrderVideoChangeLog::getChangeTime)
                .orderByDesc(OrderVideoChangeLog::getLogType)
        );
    }

    /**
     * 查询视频订单是否有变更记录
     */
    default boolean videoHasChangeLog(Long videoId) {
        return exists(new LambdaQueryWrapper<OrderVideoChangeLog>()
                .eq(OrderVideoChangeLog::getVideoId, videoId)
                .ne(OrderVideoChangeLog::getLogType, ChangeLogTypeEnum.INIT_LOG.getCode())
        );
    }
}
