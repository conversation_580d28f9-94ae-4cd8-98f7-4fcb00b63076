package com.wnkx.order.utlis;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.system.api.domain.dto.order.pay.PayInfoExportDTO;
import com.ruoyi.system.api.domain.dto.order.pay.PayInfoOrderExportDTO;
import com.ruoyi.system.api.domain.dto.order.pay.PayInfoVideoExportDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDType0Font;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class PayInfoExportUtil {

    /**
     * PDF页面高度
     */
    private final static int PDF_PAGE_HEIGHT = 800;

    /**
     * PDF页面宽度
     */
    private final static int PDF_PAGE_WIDTH = 800;

    /**
     * 达到需要分页最低的y值，低于这个值则分页
     */
    private final static int PAGE_MIN_Y = 100;

    /**
     * 新建页面的top y值，从这个值开始写入数据
     */
    private final static int NEW_PAGE_TOP_Y = 720;

    /**
     * 行间距
     */
    private final static int LINE_SPACING = 15;

    /**
     * 订单信息首行Y轴
     */
    private final static int ORDER_INFO_TOP_Y = 720;

    /**
     * 订单信息行间距
     */
    private final static int ORDER_INFO_LINE_SPACING = 20;

    /**
     * 订单信息左侧X轴
     */
    private final static int ORDER_INFO_LEFT_X = 50;

    /**
     * 订单信息右侧X轴
     */
    private final static int ORDER_INFO_RIGHT_X = 400;

    /**
     * 订单费用明细Y轴
     */
    private final static int ORDER_FEE_DETAIL_TOP_Y = 620;

    /**
     * 页面空白
     */
    private final static float MARGIN = 50f;

    /**
     * 表格数据初始Y值
     */
    private final static int TABLE_BASE_Y = 620;

    /**
     * 表格行高
     */
    private final static float TABLE_ROW_HEIGHT = 20f;
    private final static float CELL_MARGIN = 5f;

    /**
     * 表格线条宽度
     */
    private final static float TABLE_LINE_WIDTH = 0.5f;

    /**
     * 标题字体大小
     */
    private final static float TITLE_FONT_SIZE = 12f;

    /**
     * 标题名称
     */
    private final static String TITLE_NAME = "蜗牛海拍视频订单费用清单";

    /**
     * PDF文件名称
     */
    private final static String PDF_FILE_NAME = URLEncoder.encode("《蜗牛海拍视频订单费用清单》", StandardCharsets.UTF_8) + ".pdf";

    /**
     * 信息字体大小
     */
    private final static float INFO_FONT_SIZE = 8f;

    /**
     * 单元格字体大小
     */
    private final static float CELL_FONT_SIZE = 5f;

    /**
     * 表格表头字段
     */
    private final static String[] TABLE_HEADER = new String[]{"产品名称", "下单用户", "视频佣金", "照片佣金", "佣金代缴税费", "paypal代付手续费", "蜗牛服务费", "实时百度汇率", "小计 (USD)", "小计 (CNY)"};

    /**
     * 替换后的文本
     */
    private static final Map<String, String> REPLACE_TEXT = new HashMap<>();

    /**
     * 基础字体
     */
    private static byte[] baseFontBytes;

    /**
     * 加粗字体
     */
    private static byte[] boldFontBytes;

    /**
     * 加载字体
     */
    static {
        try (InputStream baseStream = PayInfoExportUtil.class.getResourceAsStream("/fonts/OPPOSans/OPPOSans-Regular.ttf");
             InputStream boldStream = PayInfoExportUtil.class.getResourceAsStream("/fonts/MiSans/MiSans-Demibold.ttf")) {
            baseFontBytes = baseStream.readAllBytes();
            boldFontBytes = boldStream.readAllBytes();
        } catch (Exception e) {
            log.error("加载字体失败", e);
            throw new ServiceException("加载字体失败");
        }
    }

    public static void exportPayInfo(PayInfoExportDTO dto, HttpServletResponse response) throws IOException {
        // 创建一个新的PDF文档
        try (PDDocument document = new PDDocument();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            // 创建一页页面
            PDPage page = new PDPage(PDRectangle.A4);
            document.addPage(page);

            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                // 重新创建 InputStream 每次加载字体
                try (InputStream baseFontStream = new ByteArrayInputStream(baseFontBytes);
                     InputStream boldFontStream = new ByteArrayInputStream(boldFontBytes)) {
                    PDType0Font baseFont = PDType0Font.load(document, baseFontStream);
                    PDType0Font boldFont = PDType0Font.load(document, boldFontStream);

                    contentStream.setFont(boldFont, TITLE_FONT_SIZE);
                    // 1. **标题居中对齐**：
                    float titleWidth = boldFont.getStringWidth(TITLE_NAME) / 1000 * 12;  // 计算标题的宽度
                    float pageWidth = page.getMediaBox().getWidth();  // 获取页面宽度
                    float titleX = (pageWidth - titleWidth) / 2;  // 计算居中位置
                    // 开始写标题
                    contentStream.beginText();
                    contentStream.newLineAtOffset(titleX, 750);  // 设置标题位置
                    contentStream.showText(TITLE_NAME);
                    contentStream.endText();
                    int titleYCount = 1;
                    int tableBaseYChange = TABLE_BASE_Y;

                    // 写入订单号等信息
                    writeKeyValue(contentStream, "会员编码: ", dto.getMerchantCode(), baseFont, boldFont, ORDER_INFO_LEFT_X, ORDER_INFO_TOP_Y);
                    writeKeyValue(contentStream, "订单合计: ", dto.getMergePayAmount() + " CNY", baseFont, boldFont, ORDER_INFO_LEFT_X, ORDER_INFO_TOP_Y - ORDER_INFO_LINE_SPACING * titleYCount++);
                    if (CharSequenceUtil.isNotBlank(dto.getMergePromotionAmountSum()) && !"0.00".equals(dto.getMergePromotionAmountSum())){
                        writeKeyValue(contentStream, "优惠金额: ", dto.getMergePromotionAmountSum() + " CNY", baseFont, boldFont, ORDER_INFO_LEFT_X, ORDER_INFO_TOP_Y - ORDER_INFO_LINE_SPACING * titleYCount++);
                        tableBaseYChange = TABLE_BASE_Y - ORDER_INFO_LINE_SPACING;
                    }
                    writeKeyValue(contentStream, "余额抵扣: ", dto.getMergeUseBalance() + " CNY", baseFont, boldFont, ORDER_INFO_LEFT_X, ORDER_INFO_TOP_Y - ORDER_INFO_LINE_SPACING * titleYCount++);
                    writeKeyValue(contentStream, "还需支付: ", dto.getMergeNeedPayAmount() + " CNY", baseFont, boldFont, ORDER_INFO_LEFT_X, ORDER_INFO_TOP_Y - ORDER_INFO_LINE_SPACING * titleYCount++);

                    // 表格数据
                    for (PayInfoOrderExportDTO payInfoOrderExportDTO : dto.getPayInfoOrderExportDTOS()) {
                        String[][] tableData = new String[payInfoOrderExportDTO.getPayInfoVideoExportDTOS().size() + 2][10];
                        tableData[0] = TABLE_HEADER;
                        for (int i = 0; i < payInfoOrderExportDTO.getPayInfoVideoExportDTOS().size(); i++) {
                            PayInfoVideoExportDTO videoExportDTO = payInfoOrderExportDTO.getPayInfoVideoExportDTOS().get(i);
                            tableData[i + 1] = new String[]{
                                    videoExportDTO.getProductChinese()
                                    , videoExportDTO.getOrderUserName()
                                    , "$" + videoExportDTO.getVideoPrice()
                                    , CharSequenceUtil.isNotBlank(videoExportDTO.getPicPrice()) && !"0.00".equals(videoExportDTO.getPicPrice()) ? "$" + videoExportDTO.getPicPrice() : StrPool.DASHED
                                    , "$" + videoExportDTO.getCommissionPaysTaxes()
                                    , "$" + videoExportDTO.getExchangePrice()
                                    , "$" + videoExportDTO.getServicePrice()
                                    , videoExportDTO.getCurrentExchangeRate()
                                    , "$" + videoExportDTO.getSubtotalUSD()
                                    , "￥" + videoExportDTO.getSubtotalCNY()
                            };
                        }
                        tableData[payInfoOrderExportDTO.getPayInfoVideoExportDTOS().size() + 1] = new String[]{
                                "合计"
                                , StrPool.DASHED
                                , StrPool.DASHED
                                , StrPool.DASHED
                                , StrPool.DASHED
                                , StrPool.DASHED
                                , StrPool.DASHED
                                , StrPool.DASHED
                                , "$" + payInfoOrderExportDTO.getTotalUSD()
                                , "￥" + payInfoOrderExportDTO.getTotalCNY()
                        };
                        payInfoOrderExportDTO.setTableData(tableData);
                    }

                    drawTableStructure(document, page, contentStream, tableBaseYChange, dto.getPayInfoOrderExportDTOS(), baseFont, boldFont);
                }
            }

            // 添加页码
            addPageNumbers(document);

            // 保存 PDF 到字节流
            document.save(outputStream);

            // 将 PDF 数据写入 HttpServletResponse
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=" + PDF_FILE_NAME);
            response.getOutputStream().write(outputStream.toByteArray());
            response.getOutputStream().flush();
        }
    }

    /**
     * 绘制表格结构
     *
     * @param document      PDF对象
     * @param page          PDF页
     * @param contentStream 内容流
     * @param tableTopY     表格顶部Y轴
     * @param orderListInfo 订单数据
     * @param baseFont      基础字体
     * @param boldFont      加粗字体
     * @throws IOException 异常
     */
    public static void drawTableStructure(PDDocument document, PDPage page, PDPageContentStream contentStream,
                                          float tableTopY, List<PayInfoOrderExportDTO> orderListInfo,
                                          PDType0Font baseFont, PDType0Font boldFont) throws IOException {
        //  表格宽度
        final float tableWidth = page.getMediaBox().getWidth() - (2 * MARGIN);
        int partingLine = 0;
        for (PayInfoOrderExportDTO orderInfo : orderListInfo) {
            if (partingLine != 0) {
                //  绘制单元格横线
                contentStream.setLineWidth(TABLE_LINE_WIDTH);
                contentStream.drawLine(MARGIN, tableTopY, MARGIN + tableWidth, tableTopY);
                tableTopY = tableTopY - ORDER_INFO_LINE_SPACING;
            }
            // 判断是否需要换页
            if ((tableTopY - ORDER_INFO_LINE_SPACING * 2 - 5) < PAGE_MIN_Y) {
                // 如果剩余空间不足100，换页
                PDPage newPage = new PDPage(PDRectangle.A4);
                document.addPage(newPage);

                // 关闭当前的 contentStream
                contentStream.close();
                // 新建流
                contentStream = new PDPageContentStream(document, newPage);

                tableTopY = NEW_PAGE_TOP_Y; // 重置
            }

            int tableTopYCount = 1;
            //  绘制每组表格数据表格行
            writeKeyValue(contentStream, "订单编号: ", orderInfo.getOrderNum(), baseFont, boldFont, ORDER_INFO_LEFT_X, tableTopY);
            writeKeyValue(contentStream, "下单时间: ", orderInfo.getOrderTime(), baseFont, boldFont, ORDER_INFO_LEFT_X, tableTopY - ORDER_INFO_LINE_SPACING * tableTopYCount ++);
            if (CharSequenceUtil.isNotBlank(orderInfo.getOrderPromotionAmount()) && !"0.00".equals(orderInfo.getOrderPromotionAmount())){
                writeKeyValue(contentStream, "优惠金额: ", orderInfo.getOrderPromotionAmount() + " CNY", baseFont, boldFont, ORDER_INFO_LEFT_X, tableTopY - ORDER_INFO_LINE_SPACING * tableTopYCount ++);
                writeKeyValue(contentStream, "还需支付: ", orderInfo.getPayAmount() + " CNY", baseFont, boldFont, ORDER_INFO_LEFT_X, tableTopY - ORDER_INFO_LINE_SPACING * tableTopYCount);
            }else {
                writeKeyValue(contentStream, "订单合计: ", orderInfo.getPayAmount() + " CNY", baseFont, boldFont, ORDER_INFO_LEFT_X, tableTopY - ORDER_INFO_LINE_SPACING * tableTopYCount);
            }
            tableTopY = tableTopY - ORDER_INFO_LINE_SPACING * tableTopYCount - 5;

            partingLine++;
            String[][] content = orderInfo.getTableData();
            //  行数
            int rows = content.length;
            //  列数
            final int cols = content[0].length;
            //  单元格宽度
            final float colWidth = tableWidth / (float) cols;

            //  每个单元格需要保留多高 key: 第几列，value: 行高
            Map<Integer, Integer> colRetainsHeight = new HashMap<>();

            //  表格数据
            String[][] data = content;
            //  当前页表格行数
            int currentPageRows = 0;
            //  已处理条数
            int processedRows = 0;
            // 绘制行
            float firstLine = tableTopY;
            float nexty = tableTopY;
            for (int i = 0; i <= rows; i++) {
                if (i <= content.length) {
                    currentPageRows++;
                }

                //  本行数据在PDF的线条是否画了的标记
                boolean flag = false;
                if (i > 0 && i <= content.length) {
                    processedRows++;
                    //  该行文本增高倍数
                    int textRiseMultiple = 0;
                    //  该行文本增高行数
                    int textRiseRows = 0;
                    //  文本溢出标记
                    boolean overflowFlag = false;

                    //  本行内容若有超出单元格 则进行加高
                    for (String text : content[i - 1]) {
                        if (ObjectUtil.isNotNull(REPLACE_TEXT.get(text))) {
                            text = REPLACE_TEXT.get(text);
                        } else {
                            text = replaceUnsupportedChars(baseFont, text);
                        }

                        //  获取文本宽度
                        float wordWidth = baseFont.getStringWidth(text) / 1000 * CELL_FONT_SIZE;
                        //  能承受最大的内容宽度
                        float maxLineWidth = colWidth - 2 * CELL_MARGIN;
                        if (wordWidth > maxLineWidth) {
                            overflowFlag = true;
                            int tempTextRiseMultiple = 0;
                            int tempTextRiseRows = 0;
                            //  根据该单元格内容字符长度 设置需要加高的高度 nextynew为倍数 10 * nextynew 即加高10的倍数
                            StringBuilder sb = new StringBuilder();
                            char[] chars = text.toCharArray();
                            for (char c : chars) {
                                sb.append(c);
                                if (baseFont.getStringWidth(sb.toString()) / 1000 * CELL_FONT_SIZE > maxLineWidth) {
                                    tempTextRiseMultiple++;
                                    tempTextRiseRows++;
                                    sb.delete(0, sb.length());
                                }
                            }
                            if (textRiseMultiple < tempTextRiseMultiple) {
                                textRiseMultiple = tempTextRiseMultiple;
                            }

                            if (textRiseRows < tempTextRiseRows) {
                                textRiseRows = tempTextRiseRows;
                            }
                        }
                    }

                    if (overflowFlag) {
                        currentPageRows = currentPageRows + textRiseRows - 1;

                        //  绘制单元格横线
                        contentStream.setLineWidth(TABLE_LINE_WIDTH);
                        contentStream.drawLine(MARGIN, nexty - (10 * textRiseMultiple), MARGIN + tableWidth, nexty - (10 * textRiseMultiple));
                        //  标记已绘制
                        flag = nexty >= PAGE_MIN_Y;
                        //  绘制完之后Y轴递减
                        nexty = nexty - (10 * textRiseMultiple);
                        //  存储该内容需要扩展的高度
                        colRetainsHeight.put(processedRows - 1, 10 * textRiseMultiple);
                    } else if ("合计".equals(content[i - 1][0])) {
                        //  绘制单元格横线
                        contentStream.setLineWidth(TABLE_LINE_WIDTH);
                        contentStream.drawLine(MARGIN, nexty, MARGIN + tableWidth, nexty);
                        //  标记已绘制
                        flag = nexty >= PAGE_MIN_Y;
                    }
                }

                if (!flag && i < content.length) {
                    //  绘制单元格横线
                    contentStream.setLineWidth(TABLE_LINE_WIDTH);
                    contentStream.drawLine(MARGIN, nexty, MARGIN + tableWidth, nexty);
                    //  绘制完之后Y轴递减
                    firstLine -= TABLE_ROW_HEIGHT;
                    nexty -= TABLE_ROW_HEIGHT;
                } else if (i <= content.length) {
                    //  绘制完之后Y轴递减
                    nexty -= TABLE_ROW_HEIGHT;
                }

                // 判断是否需要换页
                if (nexty < PAGE_MIN_Y && content.length > i) {
                    // 如果剩余空间不足100，换页
                    PDPage newPage = new PDPage(PDRectangle.A4);
                    document.addPage(newPage);

                    //  先将当前页面绘制表格内容
                    data = drawTableData(contentStream, tableTopY, data, baseFont, boldFont, cols, colWidth, colRetainsHeight, nexty + 20);

                    // 关闭当前的 contentStream
                    contentStream.close();
                    // 新建流
                    contentStream = new PDPageContentStream(document, newPage);

                    nexty = NEW_PAGE_TOP_Y; // 重置y坐标
                    tableTopY = NEW_PAGE_TOP_Y; // 重置
                    firstLine = NEW_PAGE_TOP_Y; // 重置
                    currentPageRows = 0; // 重置
                    processedRows = 1; // 重置
                    //  创建新页面 rows加2 一个是表格表头 一个是底部横线
                    rows = rows + 2;

                    for (int i1 = 0; i1 < 2; i1++) {
                        //  绘制单元格横线
                        contentStream.setLineWidth(TABLE_LINE_WIDTH);
                        contentStream.drawLine(MARGIN, firstLine, MARGIN + tableWidth, firstLine);
                        //  绘制完之后Y轴递减
                        firstLine -= TABLE_ROW_HEIGHT;
                        nexty -= TABLE_ROW_HEIGHT;
                    }
                }
            }
            drawTableData(contentStream, tableTopY, data, baseFont, boldFont, cols, colWidth, colRetainsHeight, nexty + 20);
            tableTopY = nexty;
        }
        //  关流
        contentStream.close();
    }

    /**
     * 绘制表格数据
     *
     * @param contentStream    内容流
     * @param tableTopY        表格顶部Y轴
     * @param content          表格数据
     * @param baseFont         基础字体
     * @param boldFont         加粗字体
     * @param cols             表格列数
     * @param colWidth         单元格宽度
     * @param colRetainsHeight 每个单元格需要保留多高 key: 第几列，value: 行高
     * @return 未处理的表格数据
     * @throws IOException 异常
     */
    private static String[][] drawTableData(PDPageContentStream contentStream, float tableTopY,
                                            String[][] content, PDType0Font baseFont, PDType0Font boldFont,
                                            int cols, float colWidth, Map<Integer, Integer> colRetainsHeight,
                                            float bottomY) throws IOException {
        // 绘制列
        float nextx = MARGIN;
        for (int i = 0; i <= cols; i++) {
            contentStream.setLineWidth(TABLE_LINE_WIDTH);
            //  处理当前页面列的线条长度
            contentStream.drawLine(nextx, tableTopY, nextx, bottomY);
            nextx += colWidth;
        }

        //  当前数据已处理到第几列
        int processedDataColumns = 0;
        // 现在添加文本，进行换行
        float textx = MARGIN + CELL_MARGIN;
        float texty = tableTopY - LINE_SPACING;
        for (int i = 0; i < content.length; i++) {
            //  超出表格底部 结束遍历数据
            if (texty <= bottomY) {
                break;
            }

            for (int j = 0; j < content[i].length; j++) {
                String text = content[i][j];
                if (ObjectUtil.isNotNull(REPLACE_TEXT.get(text))) {
                    text = REPLACE_TEXT.get(text);
                } else {
                    text = replaceUnsupportedChars(baseFont, text);
                }
                if (null == text) {
                    continue;
                }
                if (i == 0 || "合计".equals(content[i][0])) {
                    contentStream.setFont(boldFont, CELL_FONT_SIZE);
                } else {
                    contentStream.setFont(baseFont, CELL_FONT_SIZE);
                }

                // 计算每个单元格的内容宽度并自动换行
                float maxLineWidth = colWidth - 2 * CELL_MARGIN;

                float wordWidth = baseFont.getStringWidth(text) / 1000 * CELL_FONT_SIZE;
                if (wordWidth > maxLineWidth) {
                    List<String> strings = new ArrayList<>();

                    StringBuilder sb = new StringBuilder();
                    char[] chars = text.toCharArray();
                    for (char c : chars) {
                        sb.append(c);
                        if (baseFont.getStringWidth(sb.toString()) / 1000 * CELL_FONT_SIZE > maxLineWidth) {
                            strings.add(sb + "\n");
                            sb.delete(0, sb.length());
                        }
                    }
                    if (CharSequenceUtil.isNotBlank(sb.toString())) {
                        strings.add(sb.toString());
                    }

                    float textynew = texty;
                    for (String s : strings) {
                        if ("\n".equals(s.substring(s.length() - 1))) {
                            s = s.substring(0, s.length() - 1);
                        }
                        contentStream.beginText();
                        contentStream.newLineAtOffset(textx, textynew);
                        contentStream.showText(s);
                        contentStream.endText();
                        textynew -= 10;
                    }
                } else {
                    contentStream.beginText();
                    // 计算水平居中的 x 坐标
                    float wordWidthForCenter = baseFont.getStringWidth(text) / 1000 * CELL_FONT_SIZE;
                    float centerX = textx + (colWidth - wordWidthForCenter) / 2 - 4;  // 水平居中

                    float centerY = texty;
                    Integer integer = colRetainsHeight.get(i);
                    if (ObjectUtil.isNotNull(integer)) {
                        int errand = 0;
                        if (integer.equals(20)) {
                            errand = 12;
                        } else if (integer.equals(30)) {
                            errand = 18;
                        } else if (integer.equals(10)) {
                            errand = 6;
                        }
                        centerY = texty - integer + errand;
                    }
                    // 绘制文本并居中
                    contentStream.newLineAtOffset(centerX, centerY);
                    contentStream.showText(text);
                    contentStream.endText();
                }

                textx += colWidth;
            }
            Integer integer = colRetainsHeight.get(i);
            if (ObjectUtil.isNotNull(integer)) {
                texty = texty - TABLE_ROW_HEIGHT - integer;
            } else {
                texty = texty - TABLE_ROW_HEIGHT;
            }

            textx = MARGIN + CELL_MARGIN;
            processedDataColumns = i;
            colRetainsHeight.remove(i);
        }

        //  将未处理的数据进行返回
        String[][] data = new String[content.length - processedDataColumns + 1][10];
        data[0] = TABLE_HEADER;
        int subscript = 1;
        for (int i = processedDataColumns + 1; i < content.length; i++) {
            data[subscript] = content[i];
            subscript++;
        }
        return data;
    }

    private static void writeKeyValue(PDPageContentStream contentStream, String key, String value, PDType0Font baseFont, PDType0Font boldFont, float tx, float ty) throws IOException {
        contentStream.beginText();
        contentStream.setFont(baseFont, INFO_FONT_SIZE);
        contentStream.newLineAtOffset(tx, ty);
        contentStream.showText(key);
        contentStream.setFont(boldFont, INFO_FONT_SIZE);
        contentStream.showText(value);
        contentStream.endText();
    }

    /**
     * 添加页码
     */
    public static void addPageNumbers(PDDocument document) throws IOException {
        int totalPages = document.getNumberOfPages();
        float pageWidth = PDRectangle.A4.getWidth(); // 页面宽度（可根据页面大小调整）

        PDType0Font font = PDType0Font.load(document, new ByteArrayInputStream(baseFontBytes)); // 加载字体

        for (int i = 0; i < totalPages; i++) {
            PDPage page = document.getPage(i);
            try (PDPageContentStream contentStream = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, true, true)) {
                contentStream.setFont(font, 10); // 设置字体和大小

                String pageNumber = String.format("%d / %d", i + 1, totalPages); // 页码格式
                float textWidth = font.getStringWidth(pageNumber) / 1000 * 10; // 页码文本宽度
                float textX = (pageWidth - textWidth) / 2; // 页码居中
                float textY = 20; // 距离底部 20 像素

                // 绘制页码
                contentStream.beginText();
                contentStream.newLineAtOffset(textX, textY);
                contentStream.showText(pageNumber);
                contentStream.endText();
            }
        }
    }

    /**
     * 删除不被字体支持的字符
     *
     * @param font 字体对象
     * @param text 原始文本
     * @return 替换后的文本
     */
    public static String replaceUnsupportedChars(PDType0Font font, String text) throws IOException {
        if (CharSequenceUtil.isBlank(text)) {
            return text;
        }

        StringBuilder result = new StringBuilder();
        for (char c : text.toCharArray()) {
            try {
                font.getStringWidth(String.valueOf(c));
                result.append(c);
            } catch (IllegalArgumentException e) {

            }
        }
        REPLACE_TEXT.put(text, result.toString());
        return result.toString();
    }
}
