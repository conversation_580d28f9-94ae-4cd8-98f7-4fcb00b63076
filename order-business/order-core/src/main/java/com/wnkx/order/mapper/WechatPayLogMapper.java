package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wnkx.db.mapper.SuperMapper;
import com.ruoyi.system.api.domain.entity.order.WechatPayLog;

/**
 * <AUTHOR>
 * @description 针对表【wechat_pay_log(微信回调记录表)】的数据库操作Mapper
 * @createDate 2024-10-25 09:28:00
 * @Entity com.ruoyi.system.api.domain.entity.order.WechatPayLog
 */
public interface WechatPayLogMapper extends SuperMapper<WechatPayLog> {
    /**
     * 根据商户订单号查询是否不存在数据
     *
     * @param outTradeNo
     * @return 如果存在数据则返回false
     */
    default boolean checkOrderNotExist(String outTradeNo) {
        return selectCount(new LambdaQueryWrapper<WechatPayLog>().eq(WechatPayLog::getOutTradeNo, outTradeNo)) <= 0;
    }

    /**
     * 根据系统订单号查询是否不存在数据
     *
     * @param orderNum
     * @return 如果存在数据则返回false
     */
    default boolean checkOrderNumberNotExist(String orderNum) {
        return selectCount(new LambdaQueryWrapper<WechatPayLog>().eq(WechatPayLog::getOrderNum, orderNum)) <= 0;
    }
}




