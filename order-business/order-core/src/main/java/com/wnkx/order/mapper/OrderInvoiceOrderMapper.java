package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceOrder;
import com.ruoyi.system.api.domain.vo.order.OrderInvoiceVideoBackVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:10
 */
@Mapper
public interface OrderInvoiceOrderMapper extends SuperMapper<OrderInvoiceOrder> {


    /**
     * 根据发票id查询发票关联的视频订单
     */
    default List<OrderInvoiceOrder> selectListByInvoiceIds(List<Long> invoiceIds) {
        return selectList(new LambdaQueryWrapper<OrderInvoiceOrder>()
                .in(OrderInvoiceOrder::getInvoiceId, invoiceIds)
        );
    }

    /**
     * 根据订单号获取 发票关联的订单
     * @param orderNums
     * @return
     */
    default List<OrderInvoiceOrder> selectListByOrderNums(List<String> orderNums) {
        return selectList(new LambdaQueryWrapper<OrderInvoiceOrder>()
                .in(OrderInvoiceOrder::getOrderNum, orderNums)
        );
    }

    /**
     * 根据发票id查询发票关联的视频订单
     */
    default List<OrderInvoiceOrder> selectListByInvoiceId(Long invoiceId) {
        return selectList(new LambdaQueryWrapper<OrderInvoiceOrder>()
                .eq(OrderInvoiceOrder::getInvoiceId, invoiceId)
        );
    }

    /**
     * 运营端-发票管理-发票关联订单
     */
    List<OrderInvoiceVideoBackVO> getInvoiceVideoBackList(@Param("invoiceId") Long invoiceId);
}
