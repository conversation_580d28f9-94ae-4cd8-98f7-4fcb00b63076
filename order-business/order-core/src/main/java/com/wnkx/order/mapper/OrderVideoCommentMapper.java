package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.OrderCommentDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoComment;
import com.wnkx.db.mapper.SuperMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_video_comment(订单备注表)】的数据库操作Mapper
 * @createDate 2024-06-17 19:32:10
 * @Entity
 */
public interface OrderVideoCommentMapper extends SuperMapper<OrderVideoComment> {
    default void addOrderComment(OrderCommentDTO orderCommentDTO) {
        OrderVideoComment orderVideoComment = new OrderVideoComment();
        orderVideoComment.setCommentContent(orderCommentDTO.getCommentContent());
        orderVideoComment.setVideoId(orderCommentDTO.getVideoId());
        final LoginBaseEntity loginUser = SecurityUtils.getLoginUser();
        if (loginUser != null) {
            orderVideoComment.setCreateUserId(loginUser.getUserid());
            orderVideoComment.setCreateBy(loginUser.getUsername());
        }
        insert(orderVideoComment);
    }

    default List<OrderVideoComment> getOrderCommentList(Long videoId) {
        return selectList(new LambdaQueryWrapper<OrderVideoComment>()
                .eq(OrderVideoComment::getVideoId, videoId)
        );
    }

    List<Long> getVideoIds();

    default List<OrderVideoComment> orderCommentListByVideoIds(List<Long> videoIds) {
        return selectList(new LambdaQueryWrapper<OrderVideoComment>()
                .in(OrderVideoComment::getVideoId, videoIds)
        );
    }
}




