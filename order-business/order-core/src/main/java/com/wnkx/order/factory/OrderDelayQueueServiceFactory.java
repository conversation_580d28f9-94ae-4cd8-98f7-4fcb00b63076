package com.wnkx.order.factory;

import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.redis.service.RedisService;
import com.wnkx.order.service.delay.OrderQueueHandleService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class OrderDelayQueueServiceFactory {

    private final Map<String, OrderQueueHandleService> orderDelayQueueServiceMap = new HashMap<>();

    private final ApplicationContext applicationContext;
    private final RedisService redisService;


    @PostConstruct
    public void init() {
        Map<String, OrderQueueHandleService> services = applicationContext.getBeansOfType(OrderQueueHandleService.class);
        for (OrderQueueHandleService service : services.values()) {
                orderDelayQueueServiceMap.put(service.getQueueName(), service);
                redisService.setCacheSet(CacheConstants.DELAY_QUEUE_RUNNING_KEY, Collections.singleton(service.getQueueName()));
                redisService.expire(CacheConstants.DELAY_QUEUE_RUNNING_KEY, 30);
        }
    }

    public OrderQueueHandleService getOrderDelayQueueService(String queueName) {
        OrderQueueHandleService orderQueueHandleService = orderDelayQueueServiceMap.get(queueName);
        if (orderQueueHandleService == null) {
            throw new IllegalArgumentException("无效的队列： " + queueName);
        }
        return orderQueueHandleService;
    }
}
