package com.wnkx.order.mapper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.ReplyEnum;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.ReplyVideoCaseDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoCase;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订单_视频_匹配情况反馈Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
@Mapper
public interface OrderVideoCaseMapper extends SuperMapper<OrderVideoCase>
{
    /**
     * 查询订单_视频_匹配情况反馈
     * 
     * @param id 订单_视频_匹配情况反馈主键
     * @return 订单_视频_匹配情况反馈
     */
    public OrderVideoCase selectOrderVideoCaseById(Long id);

    /**
     * 查询订单_视频_匹配情况反馈列表
     * 
     * @param orderVideoCase 订单_视频_匹配情况反馈
     * @return 订单_视频_匹配情况反馈集合
     */
    public List<OrderVideoCase> selectOrderVideoCaseList(OrderVideoCase orderVideoCase);

    /**
     * 新增订单_视频_匹配情况反馈
     * 
     * @param orderVideoCase 订单_视频_匹配情况反馈
     * @return 结果
     */
    public int insertOrderVideoCase(OrderVideoCase orderVideoCase);

    /**
     * 修改订单_视频_匹配情况反馈
     * 
     * @param orderVideoCase 订单_视频_匹配情况反馈
     * @return 结果
     */
    public int updateOrderVideoCase(OrderVideoCase orderVideoCase);

    /**
     * 删除订单_视频_匹配情况反馈
     * 
     * @param id 订单_视频_匹配情况反馈主键
     * @return 结果
     */
    public int deleteOrderVideoCaseById(Long id);

    /**
     * 批量删除订单_视频_匹配情况反馈
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrderVideoCaseByIds(Long[] ids);

    /**
     * 通过视频订单id获取未回复的匹配情况
     *
     * @return 未回复的匹配情况
     */
    default List<OrderVideoCase> selectNoReplyListByVideoId(List<Long> videoIds) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoCase>()
                .in(OrderVideoCase::getVideoId, videoIds)
                .eq(OrderVideoCase::getReplyContent, ReplyEnum.NO_FEEDBACK.getCode())
        );
    }

    /**
     * 通过视频订单id查询视频订单匹配情况反馈
     */
    default List<OrderVideoCase> selectListByVideoId(Long videoId) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoCase>()
                .eq(OrderVideoCase::getVideoId, videoId)
                .orderByDesc(OrderVideoCase::getSendTime));
    }

    /**
     * 商家回复匹配情况反馈
     */
    default void replyVideoCase(ReplyVideoCaseDTO replyVideoCaseDTO) {
        OrderVideoCase orderVideoCase = BeanUtil.copyProperties(replyVideoCaseDTO, OrderVideoCase.class);
        orderVideoCase.setReplyId(SecurityUtils.getUserId());
        orderVideoCase.setReplyTime(DateUtil.date());
        orderVideoCase.setReason(replyVideoCaseDTO.getReason());

        this.updateById(orderVideoCase);
    }

    /**
     * 通过主键和视频id查询反馈情况
     */
    default OrderVideoCase getByIdAndVideoId(Long id, Long videoId) {
        return this.selectOne(new LambdaQueryWrapper<OrderVideoCase>()
                .eq(OrderVideoCase::getId, id)
                .eq(OrderVideoCase::getVideoId, videoId)
                .last("limit 1")
        );
    }
}
