package com.wnkx.order.utlis;

import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.wnkx.order.remote.RemoteService;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2024/8/20 11:02
 */
@Component
@RequiredArgsConstructor
public class CompletableFutureUtil {
    private final ThreadPoolTaskExecutor asyncPoolTaskExecutor;
    private final RemoteService remoteService;

    public CompletableFuture<List<BusinessAccountDetailVO>> queryMerchantByBusinessIdsAndAccountIds(Set<Long> businessIds, Set<Long> accountIds) {
        return CompletableFuture.supplyAsync(() -> remoteService.queryMerchantByBusinessIdsAndAccountIds(businessIds, accountIds), asyncPoolTaskExecutor);
    }
}
