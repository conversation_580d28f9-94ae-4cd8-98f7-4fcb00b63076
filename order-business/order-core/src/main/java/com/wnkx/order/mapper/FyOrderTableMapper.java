package com.wnkx.order.mapper;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.system.api.domain.dto.order.fy.FyOrderTableDTO;
import com.ruoyi.system.api.domain.entity.order.FyOrderTable;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
* <AUTHOR>
* @description 针对表【fy_order_table(富友订单表)】的数据库操作Mapper
* @createDate 2024-08-14 14:01:11
* @Entity com.ruoyi.system.api.domain.entity.biz.business.FyOrderTable
*/
@Mapper
public interface FyOrderTableMapper extends SuperMapper<FyOrderTable> {

    /**
     * 获取有效订单数据*
     * @param dto
     * @return
     */
    default FyOrderTable getValidFyOrderTable(FyOrderTableDTO dto){
        Assert.notNull(dto.getOrderNumber(), "内部订单号不能为空");
        Assert.notNull(dto.getPlatform(), "平台类型不能为空");
        return this.selectOne(new LambdaQueryWrapper<FyOrderTable>()
                .eq(FyOrderTable::getOrderNumber, dto.getOrderNumber())
                .eq(FyOrderTable::getStatus, StatusTypeEnum.YES.getCode())
                .eq(FyOrderTable::getPlatform, dto.getPlatform())
        );
    }

    /**
     * 获取有效订单列表*
     * @param orderNum
     * @return
     */
    default List<FyOrderTable> getValidFyOrderTableListByOrderNum(String orderNum){
        return this.selectList(new LambdaQueryWrapper<FyOrderTable>()
                .eq(FyOrderTable::getOrderNumber, orderNum)
                .eq(FyOrderTable::getStatus, StatusTypeEnum.YES.getCode())
                );
    }
}
