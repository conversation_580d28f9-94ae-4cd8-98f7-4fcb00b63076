package com.wnkx.order.service.core.impl.logistic;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.enums.EventExecuteObjectEnum;
import com.ruoyi.common.core.enums.FollowStatusEnum;
import com.ruoyi.common.core.enums.LogisticMainStatus;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFlowDTO;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollowRecord;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.wnkx.order.service.OrderResourceService;
import com.wnkx.order.service.OrderVideoLogisticFollowRecordService;
import com.wnkx.order.service.OrderVideoLogisticFollowService;
import com.wnkx.order.service.core.OrderVideoLogisticFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :需跟进
 * @create :2025-04-23 11:39
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoLogisticFlowNeedFollowUp implements OrderVideoLogisticFlowService {
    private final OrderResourceService orderResourceService;
    private final OrderVideoLogisticFollowService orderVideoLogisticFollowService;
    private final OrderVideoLogisticFollowRecordService orderVideoLogisticFollowRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void OrderVideoLogisticFlow(OrderVideoLogisticFlowDTO dto) {
        //17track返回数据 只有在跟进状态为无需跟进 且物流状态为 查询不到、运输过久、可能异常的时候才能继续执行
        if (StatusTypeEnum.YES.getCode().equals(dto.getIsCallback()) &&
                StatusTypeEnum.NO.getCode().equals(dto.getOrderVideoLogisticFollow().getIsCallBack())
        ){
            //如果本次调用是回调、系统物流回调记录非回调 则物流流转失效
            return;
        }
        if (StatusTypeEnum.YES.getCode().equals(dto.getIsCallback())
                && !List.of(LogisticMainStatus.NOT_FOUND.getLabel(), LogisticMainStatus.EXPIRED.getLabel(), LogisticMainStatus.EXCEPTION.getLabel()).contains(dto.getLatestMainStatus())
        ) {
            log.error("无需跟进状态下，回调数据状态不为：查询不到、运输过久、可能异常 无法流转为需跟进");
            return;
        }
        this.checkStatus(dto);
        List<Long> resourceIds = orderResourceService.saveBatchOrderResource(dto.getResourceIds());
        String resourceId = StrUtil.join(StrUtil.COMMA, resourceIds);

        LoginUserInfoVO loginUserInfoVo = dto.getLoginUserInfoVO();
        OrderVideoLogisticFollow updateEntity = new OrderVideoLogisticFollow();
        updateEntity.setId(dto.getOrderVideoLogisticFollow().getId());
        updateEntity.setLogisticStatus(this.getFollowStatusType().getLogisticStatus());
        updateEntity.setFollowStatus(this.getFollowStatusType().getCode());
        updateEntity.setLatestMainStatus(dto.getLatestMainStatus());
        updateEntity.setLogisticUpdateTime(dto.getLogisticUpdateTime());
        updateEntity.setIsCallBack(dto.getIsCallback());
        updateEntity.setLatestRemark(StrUtil.isBlank(dto.getRemark()) ? "" : dto.getRemark());
        updateEntity.setLatestResourceId(StrUtil.isBlank(resourceId) ? "" : resourceId);

        if (EventExecuteObjectEnum.BACK.getCode().equals(loginUserInfoVo.getUserType())) {
            updateEntity.setUpdateById(loginUserInfoVo.getUserId());
            updateEntity.setUpdateBy(loginUserInfoVo.getName());
        }
        updateEntity.setUpdateTime(new Date());
        orderVideoLogisticFollowService.updateById(updateEntity);


        OrderVideoLogisticFollow orderVideoLogisticFollow = dto.getOrderVideoLogisticFollow();

        OrderVideoLogisticFollowRecord orderVideoLogisticFollowRecord = new OrderVideoLogisticFollowRecord();
        orderVideoLogisticFollowRecord.setFollowId(orderVideoLogisticFollow.getId());
        orderVideoLogisticFollowRecord.setResourceId(resourceId);
        if (StatusTypeEnum.YES.getCode().equals(dto.getIsCallback())) {
            orderVideoLogisticFollowRecord.setEventName("新物流跟进：" + LogisticMainStatus.getSketchByLabel(dto.getLatestMainStatus()));
            orderVideoLogisticFollowRecord.setEventContent("生成新物流跟进记录");
        } else {
            String remark = StrUtil.isNotBlank(dto.getRemark()) ? ",备注：" + dto.getRemark() : "";
            String logisticUpdateTime = "，物流时间" + DateUtils.parseDateToStr("yyyy年MM月dd日 HH:mm", dto.getLogisticUpdateTime());
            orderVideoLogisticFollowRecord.setEventName("更新物流状态：" + LogisticMainStatus.getSketchByLabel(dto.getLatestMainStatus()));
            orderVideoLogisticFollowRecord.setEventContent(loginUserInfoVo.getName() + "更新物流" + logisticUpdateTime + remark);
        }
        orderVideoLogisticFollowRecord.setEventExecuteObject(loginUserInfoVo.getUserType());
        orderVideoLogisticFollowRecord.setEventExecuteUserId(loginUserInfoVo.getUserId());
        orderVideoLogisticFollowRecord.setEventExecuteUserName(loginUserInfoVo.getName());
        orderVideoLogisticFollowRecord.setEventExecuteNickName(loginUserInfoVo.getNickName());
        orderVideoLogisticFollowRecord.setEventExecuteTime(new Date());
        orderVideoLogisticFollowRecord.setRemark(dto.getRemark());
        orderVideoLogisticFollowRecordService.save(orderVideoLogisticFollowRecord);
    }

    @Override
    public void checkStatus(OrderVideoLogisticFlowDTO dto) {
        Assert.notNull(dto, "需跟进：参数不能为空~");
        Assert.notNull(dto.getOrderVideoLogisticFollow(), "需跟进：物流跟进数据不能为空~");
        Assert.isTrue(List.of(FollowStatusEnum.SHIP.getCode(),
                        FollowStatusEnum.NO_FOLLOW_NEED.getCode(),
                        FollowStatusEnum.NEED_FOLLOW_UP.getCode(),
                        FollowStatusEnum.MODEL_CONFIRM_PEND.getCode()).contains(dto.getOrderVideoLogisticFollow().getFollowStatus()),
                "需跟进：已结束、已删除数据无法流转为无需跟进~");
        Assert.isTrue(StatusTypeEnum.YES.getCode().equals(dto.getOrderVideoLogisticFollow().getLogisticStatus()), "无需跟进：只有已发货数据才能无需跟进~");
        Assert.notNull(dto.getLatestMainStatus(), "需跟进：最新物流状态不能为空~");
        Assert.notNull(dto.getLogisticUpdateTime(), "需跟进：物流系统同步时间不能为空~");
        Assert.notNull(dto.getIsCallback(), "需跟进：是否回调不能为空~");


    }

    @Override
    public FollowStatusEnum getFollowStatusType() {
        return FollowStatusEnum.NEED_FOLLOW_UP;
    }
}
