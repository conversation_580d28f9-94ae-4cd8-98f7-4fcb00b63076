package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.system.api.domain.entity.OrderPayLog;
import com.wnkx.db.mapper.SuperMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_pay_log(订单支付记录表)】的数据库操作Mapper
* @createDate 2024-12-18 09:10:26
* @Entity com.ruoyi.system.api.domain.entity.OrderPayLog
*/
public interface OrderPayLogMapper extends SuperMapper<OrderPayLog> {

    /**
     * 初始化商家后填充商家ID数据
     * @param orderNum
     * @param businessId
     */
    default void setBusinessId(String orderNum, Long businessId){
        this.update(null, new LambdaUpdateWrapper<OrderPayLog>()
                .set(OrderPayLog::getBusinessId, businessId)
                .eq(OrderPayLog::getOrderNum, orderNum)
                .eq(OrderPayLog::getBusinessId, 0L)
        );
    }

    /**
     * 根据订单号列表获取支付流水数据
     * @param orderNums
     * @return
     */
    default List<OrderPayLog> getOrderPayLogListByOrderNums(List<String> orderNums){
        return this.selectList(new LambdaUpdateWrapper<OrderPayLog>()
                .in(OrderPayLog::getOrderNum, orderNums));
    }
}




