package com.wnkx.order.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderVideoModelShippingAddress;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/9 18:33
 */
@Mapper
public interface OrderVideoModelShippingAddressMapper extends SuperMapper<OrderVideoModelShippingAddress> {

    /**
     * 通过视频订单ID获取发货信息列表
     */
    default List<OrderVideoModelShippingAddress> selectListByVideoId(Long videoId) {
        return selectList(new LambdaQueryWrapper<OrderVideoModelShippingAddress>()
                .eq(OrderVideoModelShippingAddress::getVideoId, videoId)
                .orderByDesc(OrderVideoModelShippingAddress::getCreateTime)
        );
    }

    /**
     * 获取视频订单最新的发货信息
     */
    default OrderVideoModelShippingAddress getLastOrderVideoModelShippingAddressByVideoIdAndRollbackId(Long videoId, Long rollbackId) {
        return selectOne(new LambdaQueryWrapper<OrderVideoModelShippingAddress>()
                .eq(OrderVideoModelShippingAddress::getVideoId, videoId)
                .eq(ObjectUtil.isNotNull(rollbackId), OrderVideoModelShippingAddress::getRollbackId, rollbackId)
                .orderByDesc(OrderVideoModelShippingAddress::getCreateTime)
                .last("limit 1")
        );
    }


    /**
     * 获取视频订单最新的发货信息
     */
    List<OrderVideoModelShippingAddress> selectLastOrderVideoModelShippingAddressListByVideoId(@Param("videoIds") List<Long> videoIds);

    /**
     * 删除模特收件地址
     */
    default void removeOrderVideoModelShippingAddressByVideoIdAndRollbackId(Long videoId, Long rollbackId) {
        delete(new LambdaQueryWrapper<OrderVideoModelShippingAddress>()
                .eq(OrderVideoModelShippingAddress::getVideoId, videoId)
                .eq(ObjectUtil.isNotNull(rollbackId), OrderVideoModelShippingAddress::getRollbackId, rollbackId)
        );
    }

    /**
     * 通过物流单号查询订单关联物流的视频id
     */
    List<OrderVideoModelShippingAddress> selectModelShippingAddressByLogisticNumber(@Param("numbers") Collection<String> numbers);
}
