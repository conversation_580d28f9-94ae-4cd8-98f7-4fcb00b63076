package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.task.AfterSaleFlowDTO;
import com.ruoyi.system.api.domain.dto.order.task.AfterSaleOrderTaskListDTO;
import com.ruoyi.system.api.domain.dto.order.task.ConfirmAfterOrderDTO;
import com.ruoyi.system.api.domain.entity.order.*;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.domain.vo.order.task.AfterSaleListVO;
import com.ruoyi.system.api.domain.vo.order.task.AfterSaleTaskDetailInfoVO;
import com.ruoyi.system.api.domain.vo.order.task.AfterSaleTaskDetailListVO;
import com.wnkx.order.mapper.OrderVideoTaskMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.OrderVideoTaskFlowCore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/9 15:24
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoTaskServiceImpl extends ServiceImpl<OrderVideoTaskMapper, OrderVideoTask> implements OrderVideoTaskService {
    private final IOrderVideoService orderVideoService;
    private final IOrderVideoTaskDetailService orderVideoTaskDetailService;
    private final OrderResourceService orderResourceService;
    private final OrderVideoOperateService orderVideoOperateService;
    private final RemoteService remoteService;
    private final OrderVideoTaskDetailProcessRecordService orderVideoTaskDetailProcessRecordService;
    private final OrderVideoTaskWorkAssigneeHistoryService orderVideoTaskWorkAssigneeHistoryService;
    private final OrderVideoFeedBackMaterialInfoTaskDetailService orderVideoFeedBackMaterialInfoTaskDetailService;
    private final OrderVideoFeedBackMaterialService orderVideoFeedBackMaterialService;

    /**
     * 通过视频订单ID关闭任务单
     */
    @Override
    public void closeTaskByVideoIds(List<Long> videoIds, OrderTaskDetailFlowOperateTypeEnum operateTypeEnum) {
        List<OrderVideoTask> taskList = baseMapper.selectOrderVideoTaskListByVideoIds(videoIds);
        if (CollUtil.isEmpty(taskList)) {
            return;
        }

        orderVideoTaskDetailService.closeTaskByVideoIds(taskList.stream().map(OrderVideoTask::getId).collect(Collectors.toList()), operateTypeEnum);
    }

    @Override
    public OrderTaskStatisticsVO getOrderTaskStatistics(Long userId, Long assigneeId) {
        Assert.isTrue(UserTypeConstants.MANAGER.equals(SecurityUtils.getLoginUserType()), "只有运营端才能获取售后工作台数据！");
        OrderTaskStatisticsVO orderTaskStatistics = baseMapper.getOrderTaskStatistics(userId, assigneeId);
        if (ObjectUtil.isNull(orderTaskStatistics)) {
            orderTaskStatistics = new OrderTaskStatisticsVO();
            orderTaskStatistics.setWorkOrderTotalCount(0);
            orderTaskStatistics.setHandlingWorkOrderCount(0);
            orderTaskStatistics.setUnHandleWorkOrderCount(0);
            orderTaskStatistics.setAfterSaleTotalCount(0);
            orderTaskStatistics.setUnHandleAfterSaleCount(0);
            orderTaskStatistics.setHandlingAfterSaleCount(0);
        }
        return orderTaskStatistics;
    }

    /**
     * 工单-历史处理人下拉框
     */
    @Override
    public List<UserVO> getHistoryAssigneeUserList() {
        return orderVideoTaskWorkAssigneeHistoryService.getHistoryAssigneeUserList();
    }

    /**
     * 通过视频订单ID获取任务单
     */
    @Override
    public List<VideoTaskOrderVO> selectVideoTaskOrderVOListByVideoIds(List<Long> videoIds) {
        return baseMapper.selectVideoTaskOrderVOListByVideoIds(videoIds);
    }

    /**
     * 工单-查询处理记录
     */
    @Override
    public List<OrderVideoTaskDetailProcessRecordVO> selectWorkOrderProcessRecordList(String taskNum) {
        return orderVideoTaskDetailProcessRecordService.selectWorkOrderProcessRecordList(taskNum);
    }

    /**
     * 工单-新增处理记录
     */
    @Override
    public void addWorkOrderProcessRecord(OrderVideoTaskDetailProcessRecordDTO dto) {
        orderVideoTaskDetailService.addWorkOrderProcessRecord(dto);
    }

    /**
     * 工单-重新打开
     */
    @Override
    public void reopenWorkOrder(TaskDetailOperateDTO dto) {
        orderVideoTaskDetailService.reopenWorkOrder(dto);
    }

    /**
     * 工单-关闭工单
     */
    @Override
    public void closeWorkOrder(Long taskDetailId) {
        orderVideoTaskDetailService.closeWorkOrder(taskDetailId);
    }

    /**
     * 工单-拒绝工单
     */
    @Override
    public void rejectWorkOrder(TaskDetailOperateDTO dto) {
        orderVideoTaskDetailService.rejectWorkOrder(dto);
    }

    /**
     * 工单-指派处理人
     */
    @Override
    public void assignHandler(AssignHandlerDTO dto) {
        orderVideoTaskDetailService.assignHandler(dto);
    }

    /**
     * 完结工单
     */
    @Override
    public void finishWorkOrder(Long taskDetailId) {
        orderVideoTaskDetailService.finishWorkOrder(taskDetailId);
    }

    /**
     * 校验 反馈素材给商家 售后单/工单 是否与视频订单关联 然后完结订单
     */
    @Override
    public void checkFeedbackMaterialPendingTaskThenFinishWorkOrder(Long videoId, List<Long> taskDetailId, List<Integer> afterSaleClass) {
        Assert.isTrue(orderVideoTaskDetailService.checkWorkOrderAssigneePermission(taskDetailId), "对不起，您没有权限操作该任务单");
        List<OrderVideoTaskDetailVO> feedbackMaterialPendingTask = getFeedbackMaterialPendingTask(videoId, taskDetailId, afterSaleClass, false);
        if (CollUtil.isEmpty(feedbackMaterialPendingTask)) {
            return;
        }
        taskDetailId = feedbackMaterialPendingTask.stream().map(OrderVideoTaskDetailVO::getId).collect(Collectors.toList());
        orderVideoTaskDetailService.finishWorkOrders(taskDetailId, OrderTaskDetailFlowCompletionModeEnum.FEEDBACK_MATERIAL_TO_THE_BUSINESS, TaskDetailFlowRecordOperateByTypeEnum.ASSIGNEE,Collections.emptyList());
    }

    /**
     * 反馈素材给商家 _获取视频订单相关联的任务单信息
     */
    @Override
    public List<OrderVideoTaskDetailVO> getFeedbackMaterialPendingTask(Long videoId, List<Long> taskDetailId, List<Integer> afterSaleClass, Boolean isToBeEdited) {
        OrderVideo orderVideo = orderVideoService.getById(videoId);
        Assert.notNull(orderVideo, "视频订单不存在");

        List<OrderVideoTask> orderVideoTasks = baseMapper.selectOrderVideoTaskListByVideoIds(List.of(videoId));
        if (CollUtil.isEmpty(orderVideoTasks)) {
            return Collections.emptyList();
        }
        List<Long> taskIds = orderVideoTasks.stream().map(OrderVideoTask::getId).collect(Collectors.toList());

        List<OrderVideoFeedBackMaterialInfoTaskDetail> materialInfoTaskDetails = orderVideoFeedBackMaterialInfoTaskDetailService.selectListByMaterialInfoIdAndVideoIdAndRollbackId(null, orderVideo.getId(), orderVideo.getRollbackId());
        List<Long> existingTaskDetailIds = materialInfoTaskDetails.stream().map(OrderVideoFeedBackMaterialInfoTaskDetail::getTaskDetailId).collect(Collectors.toList());

        return orderVideoTaskDetailService.getFeedbackMaterialPendingTask(taskIds, taskDetailId, afterSaleClass, existingTaskDetailIds, isToBeEdited);
    }

    /**
     * 校验 帮模特反馈素材 售后单/工单 是否与视频订单关联 然后完结订单
     */
    @Override
    public void checkBackHelpModelUploadMaterialPendingTaskThenFinishWorkOrder(Long videoId, List<Long> taskDetailId) {
        Assert.isTrue(orderVideoTaskDetailService.checkWorkOrderAssigneePermission(taskDetailId), "对不起，您没有权限操作该任务单");
        List<OrderVideoTaskDetailVO> backHelpModelUploadMaterialPendingTask = getBackHelpModelUploadMaterialPendingTask(videoId, taskDetailId);
        if (CollUtil.isEmpty(backHelpModelUploadMaterialPendingTask)) {
            return;
        }
        taskDetailId = backHelpModelUploadMaterialPendingTask.stream().map(OrderVideoTaskDetailVO::getId).collect(Collectors.toList());
        orderVideoTaskDetailService.finishWorkOrders(taskDetailId, OrderTaskDetailFlowCompletionModeEnum.MODEL_FEEDBACK_MATERIAL, TaskDetailFlowRecordOperateByTypeEnum.ASSIGNEE,Collections.emptyList());
    }

    /**
     * 帮模特反馈素材 _获取视频订单相关联的任务单信息
     */
    @Override
    public List<OrderVideoTaskDetailVO> getBackHelpModelUploadMaterialPendingTask(Long videoId, List<Long> taskDetailIds) {
        OrderVideoTask orderVideoTask = baseMapper.getOrderVideoTaskByVideoIdAndTaskType(videoId, OrderTaskTypeEnum.WORK_ORDER.getCode());
        if (ObjectUtil.isNull(orderVideoTask)) {
            return Collections.emptyList();
        }
        return orderVideoTaskDetailService.getBackHelpModelUploadMaterialPendingTask(orderVideoTask.getId(), taskDetailIds);
    }

    /**
     * 校验 申请补偿退款 OR 补发物流 售后单/工单 是否与视频订单关联 然后要处理的任务单ID
     */
    @Override
    public void checkRefundPendingTaskThenRecord(Long videoId, List<Long> taskDetailId, OrderVideoRefund orderVideoRefund) {
        Assert.isTrue(orderVideoTaskDetailService.checkWorkOrderAssigneePermission(taskDetailId), "对不起，您没有权限操作该任务单");
        List<OrderVideoTaskDetailVO> refundPendingTask = getRefundPendingTask(videoId, taskDetailId);
        if (CollUtil.isEmpty(refundPendingTask)) {
            return;
        }

        taskDetailId = refundPendingTask.stream().map(OrderVideoTaskDetailVO::getId).collect(Collectors.toList());
        orderVideoTaskDetailService.addTaskDetailFlow(taskDetailId, OrderVideoTaskDetailFlowRecordDTO.builder().operateType(OrderTaskDetailFlowOperateTypeEnum.COMPENSATION.getCode()).build());
        orderVideoRefund.setTaskDetailId(CharSequenceUtil.join(StrPool.COMMA, taskDetailId));
    }

    /**
     * 校验 申请补偿退款 OR 补发物流_ 售后单/工单 是否与视频订单关联 然后完结订单
     */
    @Override
    public void checkRefundPendingTaskThenFinishWorkOrder(Long videoId, List<Long> taskDetailId) {
        Assert.isTrue(orderVideoTaskDetailService.checkWorkOrderAssigneePermission(taskDetailId), "对不起，您没有权限操作该工单");
        List<OrderVideoTaskDetailVO> refundPendingTask = getRefundPendingTask(videoId, taskDetailId);
        if (CollUtil.isEmpty(refundPendingTask)) {
            return;
        }
        taskDetailId = refundPendingTask.stream().map(OrderVideoTaskDetailVO::getId).collect(Collectors.toList());
        orderVideoTaskDetailService.finishWorkOrders(taskDetailId, OrderTaskDetailFlowCompletionModeEnum.REISSUE, TaskDetailFlowRecordOperateByTypeEnum.ASSIGNEE,Collections.emptyList());
    }

    /**
     * 申请补偿退款 OR 补发物流 _获取视频订单相关联的任务单信息
     */
    @Override
    public List<OrderVideoTaskDetailVO> getRefundPendingTask(Long videoId, List<Long> taskDetailId) {
        OrderVideoTask orderVideoTask = baseMapper.getOrderVideoTaskByVideoIdAndTaskType(videoId, OrderTaskTypeEnum.WORK_ORDER.getCode());
        if (ObjectUtil.isNull(orderVideoTask)) {
            return Collections.emptyList();
        }
        return orderVideoTaskDetailService.getRefundPendingTask(orderVideoTask.getId(), taskDetailId);
    }

    /**
     * 查看工单详情
     */
    @Override
    public WorkOrderTaskInfoVO getWorkOrderInfo(String taskNum) {
        return orderVideoTaskDetailService.getWorkOrderInfo(taskNum);
    }

    /**
     * 工单列表
     */
    @Override
    public List<WorkOrderTaskListVO> selectWorkOrderListByCondition(WorkOrderTaskListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ovt.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        if (CharSequenceUtil.isNotBlank(dto.getLastReplyTimeLogicalSymbol())) {
            dto.setLastReplyTimeLogicalSymbol(LogicalSymbolEnum.getLogicalSymbol(Integer.valueOf(dto.getLastReplyTimeLogicalSymbol())));
        }

        List<WorkOrderTaskListVO> list = baseMapper.selectWorkOrderListByCondition(dto);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        Set<Long> taskIds = list.stream().map(WorkOrderTaskListVO::getId).collect(Collectors.toSet());
        dto.setTaskIds(taskIds);

        List<WorkOrderTaskDetailListVO> workOrderTaskDetailListVOS = orderVideoTaskDetailService.selectWorkOrderTaskDetailListByCondition(dto);
        if (CollUtil.isEmpty(workOrderTaskDetailListVOS)) {
            return Collections.emptyList();
        }
        Map<Long, List<WorkOrderTaskDetailListVO>> workOrderTaskDetailListVOMap = workOrderTaskDetailListVOS.stream().collect(Collectors.groupingBy(WorkOrderTaskDetailListVO::getTaskId));

        Set<Long> shootModelIds = list.stream().map(WorkOrderTaskListVO::getShootModelId).collect(Collectors.toSet());
        Map<Long, ModelOrderSimpleVO> modelSimpleMap = remoteService.getModelSimpleMap(shootModelIds);

        for (WorkOrderTaskListVO workOrderTaskListVO : list) {
            workOrderTaskListVO.setShootModel(modelSimpleMap.get(workOrderTaskListVO.getShootModelId()));
            workOrderTaskListVO.setWorkOrderTaskDetailListVOS(workOrderTaskDetailListVOMap.get(workOrderTaskListVO.getId()));
        }

        List<String> taskNums = workOrderTaskDetailListVOS.stream()
                .map(WorkOrderTaskDetailListVO::getTaskNum)
                .distinct()
                .collect(Collectors.toList());

        List<OrderVideoTaskDetailProcessRecordVO> resList = orderVideoTaskDetailProcessRecordService
                .selectWorkOrderProcessRecordListByTaskNums(taskNums);

        Map<String, OrderVideoTaskDetailProcessRecordVO> processRecordMap = resList.stream()
                .collect(Collectors.toMap(OrderVideoTaskDetailProcessRecordVO::getTaskNum, record -> record, (e, r) -> e));

        Map<String, String> isNewProcessMap = processRecordMap.entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> {
                    OrderVideoTaskDetailProcessRecordVO record = entry.getValue();
                    String operateName = (record.getOperate() != null && StrUtil.isNotBlank(record.getOperate().getName())) ?
                            String.format("%s: ", record.getOperate().getName()) : "";
                    String content = StrUtil.isNotBlank(record.getContent()) ? record.getContent() :
                            record.getOperateType().equals(OrderTaskDetailFlowOperateTypeEnum.CLOSE_WORK_ORDER.getCode()) ?
                                    OrderTaskDetailFlowOperateTypeEnum.CLOSE_WORK_ORDER.getLabel() :
                                    OrderTaskDetailFlowOperateTypeEnum.FINISHED_WORK_ORDER.getLabel();
                    return DateFormatUtils.format(record.getTime(), DatePattern.NORM_DATETIME_PATTERN) + "," + operateName + content;
                }));

        list.forEach(workOrderTaskListVO -> {
            workOrderTaskListVO.setShootModel(modelSimpleMap.get(workOrderTaskListVO.getShootModelId()));
            List<WorkOrderTaskDetailListVO> taskDetails = workOrderTaskDetailListVOMap.get(workOrderTaskListVO.getId());
            if (CollUtil.isNotEmpty(taskDetails)) {
                taskDetails.forEach(taskDetail -> taskDetail.setIsNewProcess(isNewProcessMap.get(taskDetail.getTaskNum())));
            }
            workOrderTaskListVO.setWorkOrderTaskDetailListVOS(taskDetails);
        });
        return list;
    }

    /**
     * 校验任务单是否已存在
     */
    @Override
    public Boolean checkTaskExist(OrderTaskDTO dto) {
        OrderVideoTask orderVideoTask = baseMapper.getOrderVideoTaskByVideoIdAndTaskType(dto.getVideoId(), dto.getTaskType());
        if (ObjectUtil.isNull(orderVideoTask)) {
            return false;
        }
        dto.setTaskId(orderVideoTask.getId());
        return orderVideoTaskDetailService.checkTaskExist(dto);
    }

    @Override
    public OrderTaskStatusVO getOrderTaskStatus(Integer taskType) {
        Assert.notNull(taskType, "任务单类型不能为空~");
        OrderTaskStatusVO orderTaskStatus = baseMapper.getOrderTaskStatus(taskType);
        orderTaskStatus.setUnHandleCount(Optional.ofNullable(orderTaskStatus.getUnHandleCount()).orElse(0));
        orderTaskStatus.setHandleIngCount(Optional.ofNullable(orderTaskStatus.getHandleIngCount()).orElse(0));
        orderTaskStatus.setApplicationForCancellationCount(Optional.ofNullable(orderTaskStatus.getApplicationForCancellationCount()).orElse(0));
        orderTaskStatus.setHandleCount(Optional.ofNullable(orderTaskStatus.getHandleCount()).orElse(0));
        orderTaskStatus.setRejectCount(Optional.ofNullable(orderTaskStatus.getRejectCount()).orElse(0));
        orderTaskStatus.setCloseCount(Optional.ofNullable(orderTaskStatus.getCloseCount()).orElse(0));
        return orderTaskStatus;
    }

    /**
     * 创建任务单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTask(OrderTaskDTO dto) {
        OrderVideo orderVideo = orderVideoService.getById(dto.getVideoId());
        Assert.notNull(orderVideo, "视频订单不存在");
        orderVideoService.checkVideoStatus(orderVideo, OrderStatusEnum.UN_FINISHED, OrderStatusEnum.NEED_CONFIRM);

        OrderVideoTask orderVideoTask = baseMapper.getOrderVideoTaskByVideoIdAndTaskType(dto.getVideoId(), dto.getTaskType());
        if (ObjectUtil.isNull(orderVideoTask)) {
            OrderVideoTask saveOrderVideoTask = BeanUtil.copyProperties(dto, OrderVideoTask.class);
            baseMapper.insert(saveOrderVideoTask);
            orderVideoTask = saveOrderVideoTask;
        }

        OrderVideoTaskDetail orderVideoTaskDetail = BeanUtil.copyProperties(dto, OrderVideoTaskDetail.class);
        orderVideoTaskDetail.setTaskId(orderVideoTask.getId());
        orderVideoTaskDetail.setRollbackId(orderVideo.getRollbackId());
        if (CollUtil.isNotEmpty(dto.getIssuePic())) {
            List<Long> resourceIds = orderResourceService.saveBatchOrderResource(dto.getIssuePic());
            orderVideoTaskDetail.setIssuePicId(StrUtil.join(StrUtil.COMMA, resourceIds));
        }

        orderVideoTaskDetailService.createTaskDetail(orderVideoTaskDetail, OrderTaskTypeEnum.AFTER_SALE.getCode().equals(dto.getTaskType()) ? OrderTaskDetailFlowOperateTypeEnum.CREATE_AFTER_SALES : OrderTaskDetailFlowOperateTypeEnum.CREATE_WORK_ORDER);

        if (OrderTaskWorkOrderTypeEnum.NEED_CLIPS.getCode().equals(dto.getWorkOrderType())) {
            Assert.isTrue(OrderStatusEnum.NEED_CONFIRM.getCode().equals(orderVideo.getStatus()), "未反馈素材给商家，不支持发起需剪辑工单~");
            UploadLinkDTO uploadLinkDTO = new UploadLinkDTO();
            uploadLinkDTO.setVideoId(orderVideo.getId());
            uploadLinkDTO.setTaskDetailId(orderVideoTaskDetail.getId());
            uploadLinkDTO.setMaterialInfoStatusEnum(MaterialInfoStatusEnum.TO_BE_EDITED);
            SpringUtils.getBean(OrderVideoFeedBackMaterialService.class).uploadLink(uploadLinkDTO);
        } else if (OrderTaskAfterSaleVideoTypeEnum.RESHOOT_VIDEO.getCode().equals(dto.getAfterSaleVideoType())
                || OrderTaskAfterSaleVideoTypeEnum.RESHOT_VIDEO.getCode().equals(dto.getAfterSaleVideoType())
                || OrderTaskAfterSaleVideoTypeEnum.RE_UPLOAD.getCode().equals(dto.getAfterSaleVideoType())
                || OrderTaskAfterSalePicTypeEnum.RESHOOT_PIC.getCode().equals(dto.getAfterSalePicType())
                || OrderTaskAfterSalePicTypeEnum.RESHOT_PIC.getCode().equals(dto.getAfterSalePicType())
        ) {
            Assert.isTrue(orderVideoFeedBackMaterialService.checkExistByVideoIdAndRollbackId(dto.getVideoId(), orderVideo.getRollbackId()), "模特还未反馈素材时，不支持发起售后单~");
        }
        // orderVideoService.removeAutoCompleteTime(dto.getVideoId());

        OrderVideoOperateTypeEnum operateTypeEnum = OrderTaskTypeEnum.AFTER_SALE.getCode().equals(dto.getTaskType()) ? OrderVideoOperateTypeEnum.INITIATING_AFTER_SALE : OrderVideoOperateTypeEnum.INITIATING_WORK_ORDER;
        orderVideoOperateService.createOrderVideoOperate(operateTypeEnum.getEventName(),
                operateTypeEnum.getIsPublic(),
                OrderVideoOperateDTO.builder()
                        .videoId(dto.getVideoId())
                        .eventContent(operateTypeEnum.getEventContent())
                        .build());
    }

    @Override
    public List<AfterSaleListVO> queryAfterSaleList(AfterSaleOrderTaskListDTO dto) {
        loadAfterSaleType(dto);
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ovt.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<AfterSaleListVO> afterSaleListVOS = baseMapper.selectAfterSaleListByCondition(dto);
        if (CollUtil.isEmpty(afterSaleListVOS)){
            return Collections.emptyList();
        }
        Set<Long> taskIds = afterSaleListVOS.stream().map(AfterSaleListVO::getId).collect(Collectors.toSet());
        dto.setTaskIds(taskIds);

        List<AfterSaleTaskDetailListVO> afterSaleTaskDetailListVOS = orderVideoTaskDetailService.selectAfterSaleOrderTaskDetailListByCondition(dto);
        if (CollUtil.isEmpty(afterSaleTaskDetailListVOS)) {
            return Collections.emptyList();
        }
        Map<Long, List<AfterSaleTaskDetailListVO>> afterSaleOrderTaskDetailListVOMap = afterSaleTaskDetailListVOS.stream().collect(Collectors.groupingBy(AfterSaleTaskDetailListVO::getTaskId));

        Set<Long> shootModelIds = afterSaleListVOS.stream().map(AfterSaleListVO::getShootModelId).collect(Collectors.toSet());
        Map<Long, ModelOrderSimpleVO> modelSimpleMap = remoteService.getModelSimpleMap(shootModelIds);

        for (AfterSaleListVO item : afterSaleListVOS) {
            item.setShootModel(modelSimpleMap.get(item.getShootModelId()));
            List<AfterSaleTaskDetailListVO> resultAfterSaleTaskDetailList = afterSaleOrderTaskDetailListVOMap.get(item.getId());
            item.setOrderVideoTaskDetailList(resultAfterSaleTaskDetailList);
        }
        return afterSaleListVOS;
    }

    @Override
    public AfterSaleTaskDetailInfoVO getAfterSaleTaskInfo(String taskNum) {
        return orderVideoTaskDetailService.getAfterSaleTaskInfo(taskNum);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void afterSaleFlow(AfterSaleFlowDTO dto) {
        OrderVideoTaskDetail orderVideoTaskDetail = orderVideoTaskDetailService.getById(dto.getId());
        Assert.notNull(orderVideoTaskDetail, "售后单不能为空~");

        OrderTaskDetailFlowOperateTypeEnum orderTaskDetailFlowOperateTypeEnum = OrderTaskDetailFlowOperateTypeEnum.getByCode(dto.getOperateType());
        Assert.notNull(orderTaskDetailFlowOperateTypeEnum, "不存在操作~");
        Assert.notNull(orderTaskDetailFlowOperateTypeEnum.getOrderTaskStatus(), "不存在工单状态");
        OrderVideoTaskFlowDTO orderVideoTaskFlow = new OrderVideoTaskFlowDTO();
        orderVideoTaskFlow.setOrderTaskStatus(orderTaskDetailFlowOperateTypeEnum.getOrderTaskStatus());
        orderVideoTaskFlow.setOrderVideoTaskDetail(orderVideoTaskDetail);
        //提交人：取消售后、重新打开
        if (OrderTaskDetailFlowOperateTypeEnum.CANCEL_AFTER_SALE == orderTaskDetailFlowOperateTypeEnum || OrderTaskDetailFlowOperateTypeEnum.AFTER_SALE_REOPEN == orderTaskDetailFlowOperateTypeEnum){
            Assert.isTrue(orderVideoTaskDetailService.checkWorkOrderSubmitPermission(orderVideoTaskDetail.getId()), "对不起，您无权操作新增处理记录");
        }
        //处理人权限：确认售后、拒绝售后、完结工单、申请取消
        if (OrderTaskDetailFlowOperateTypeEnum.CONFIRM_AFTER_SALE == orderTaskDetailFlowOperateTypeEnum
                || OrderTaskDetailFlowOperateTypeEnum.REFUSE_AFTER_SALE == orderTaskDetailFlowOperateTypeEnum
                || OrderTaskDetailFlowOperateTypeEnum.AFTER_SALE_FEEDBACK_MATERIAL_TO_THE_BUSINESS == orderTaskDetailFlowOperateTypeEnum
                || OrderTaskDetailFlowOperateTypeEnum.AFTER_SALE_FINISHED == orderTaskDetailFlowOperateTypeEnum
                || OrderTaskDetailFlowOperateTypeEnum.APPLY_FOR_CANCELLATION == orderTaskDetailFlowOperateTypeEnum
        ){
            Assert.isTrue(orderVideoTaskDetailService.checkWorkOrderAssigneePermission(orderVideoTaskDetail.getId()), "对不起，您无权操作新增处理记录");
        }


        SpringUtils.getBean(OrderVideoTaskFlowCore.class).flowVideoTask(orderVideoTaskFlow);

        if (OrderTaskDetailFlowOperateTypeEnum.CONFIRM_AFTER_SALE.getCode().equals(dto.getOperateType())){
            orderVideoTaskDetailService.updateById(OrderVideoTaskDetail.builder().id(dto.getId()).contentEnglish(dto.getRemark()).build());
            orderVideoTaskDetailService.taskDetailOperate(new TaskDetailOperateDTO(), orderVideoTaskDetail, orderTaskDetailFlowOperateTypeEnum);
            return;
        }
        orderVideoTaskDetailService.taskDetailOperate(dto, orderVideoTaskDetail, orderTaskDetailFlowOperateTypeEnum);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmAfterOrder(ConfirmAfterOrderDTO dto) {
        AfterSaleFlowDTO afterSaleFlowDTO = BeanUtil.copyProperties(dto, AfterSaleFlowDTO.class);
        afterSaleFlowDTO.setOperateType(OrderTaskDetailFlowOperateTypeEnum.CONFIRM_AFTER_SALE.getCode());
        afterSaleFlow(afterSaleFlowDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishAfterSale(Long taskDetailId) {
        orderVideoTaskDetailService.finishAfterSaleOrder(taskDetailId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelApplicationAfterOrder(Long taskDetailId) {
        OrderVideoTaskDetail orderVideoTaskDetail = orderVideoTaskDetailService.getById(taskDetailId);
        Assert.notNull(orderVideoTaskDetail, "售后单不能为空~");
        Assert.isTrue(orderVideoTaskDetailService.checkWorkOrderAssigneePermission(orderVideoTaskDetail.getId()), "对不起，您无权操作新增处理记录");
        Assert.isTrue(OrderTaskStatusEnum.APPLICATION_FOR_CANCELLATION.getCode().equals(orderVideoTaskDetail.getStatus()), "任务单状态异常，需要是[申请取消中]");

        OrderVideoTaskFlowDTO orderVideoTaskFlow = new OrderVideoTaskFlowDTO();
        orderVideoTaskFlow.setOrderTaskStatus(OrderTaskStatusEnum.HANDLE_ING);
        orderVideoTaskFlow.setOrderVideoTaskDetail(orderVideoTaskDetail);

        SpringUtils.getBean(OrderVideoTaskFlowCore.class).flowVideoTask(orderVideoTaskFlow);
        orderVideoTaskDetailService.taskDetailOperate(new TaskDetailOperateDTO(), orderVideoTaskDetail, OrderTaskDetailFlowOperateTypeEnum.WITHDRAW_THE_APPLICATION);
    }

    @Override
    public void agreeCancelAfterOrder(Long taskDetailId) {
        OrderVideoTaskDetail orderVideoTaskDetail = orderVideoTaskDetailService.getById(taskDetailId);
        Assert.notNull(orderVideoTaskDetail, "售后单不能为空~");
        Assert.isTrue(orderVideoTaskDetailService.checkWorkOrderSubmitPermission(orderVideoTaskDetail.getId()), "对不起，您无权操作新增处理记录");
        Assert.isTrue(OrderTaskStatusEnum.APPLICATION_FOR_CANCELLATION.getCode().equals(orderVideoTaskDetail.getStatus()), "任务单状态异常，需要是[申请取消中]");
        OrderVideoTaskFlowDTO orderVideoTaskFlow = new OrderVideoTaskFlowDTO();
        orderVideoTaskFlow.setOrderTaskStatus(OrderTaskStatusEnum.CLOSE);
        orderVideoTaskFlow.setOrderVideoTaskDetail(orderVideoTaskDetail);
        SpringUtils.getBean(OrderVideoTaskFlowCore.class).flowVideoTask(orderVideoTaskFlow);
        orderVideoTaskDetailService.taskDetailOperate(new TaskDetailOperateDTO(), orderVideoTaskDetail, OrderTaskDetailFlowOperateTypeEnum.AGREE_SALE_FINISHED);
    }

    @Override
    public void refuseCancelAfterOrder(Long taskDetailId) {
        OrderVideoTaskDetail orderVideoTaskDetail = orderVideoTaskDetailService.getById(taskDetailId);
        Assert.notNull(orderVideoTaskDetail, "售后单不能为空~");
        Assert.isTrue(orderVideoTaskDetailService.checkWorkOrderSubmitPermission(orderVideoTaskDetail.getId()), "对不起，您无权操作新增处理记录");
        Assert.isTrue(OrderTaskStatusEnum.APPLICATION_FOR_CANCELLATION.getCode().equals(orderVideoTaskDetail.getStatus()), "任务单状态异常，需要是[申请取消中]");
        OrderVideoTaskFlowDTO orderVideoTaskFlow = new OrderVideoTaskFlowDTO();
        orderVideoTaskFlow.setOrderTaskStatus(OrderTaskStatusEnum.HANDLE_ING);
        orderVideoTaskFlow.setOrderVideoTaskDetail(orderVideoTaskDetail);
        SpringUtils.getBean(OrderVideoTaskFlowCore.class).flowVideoTask(orderVideoTaskFlow);
        orderVideoTaskDetailService.taskDetailOperate(new TaskDetailOperateDTO(), orderVideoTaskDetail, OrderTaskDetailFlowOperateTypeEnum.REFUSE_TO_CANCEL);
    }


    @Override
    public List<ModelOrderSimpleVO> taskModelList(Integer type) {
        List<Long> modelIdList =  baseMapper.shootModelIdList(type);
        return new ArrayList<>(remoteService.getModelSimpleMap(modelIdList).values());
    }

    @Override
    public List<UserVO> getAssigneeUserList(Integer type) {
        List<Long> userIdList = baseMapper.getAssigneeUserList(type);
        return remoteService.getUserList(userIdList);
    }

    @Override
    public List<UserVO> getSubmitUserList(Integer type) {
        List<Long> userIdList = baseMapper.getSubmitUserList(type);
        return remoteService.getUserList(userIdList);
    }

    @Override
    public List<UserVO> getRelevanceList(Integer type) {
        List<Long> submitUserList = baseMapper.getSubmitUserList(type);
        List<Long> assigneeUserList = baseMapper.getAssigneeUserList(type);
        List<Long> userIdList = new ArrayList<>();
        if (CollUtil.isNotEmpty(submitUserList)){
            userIdList.addAll(submitUserList);
        }
        if (CollUtil.isNotEmpty(assigneeUserList)){
            userIdList.addAll(assigneeUserList);
        }
        if (CollUtil.isNotEmpty(userIdList)){
            userIdList = userIdList.stream().distinct().collect(Collectors.toList());
        }
        return remoteService.getUserList(userIdList);
    }

    private void loadAfterSaleTypes(AfterSaleListVO item, List<AfterSaleTaskDetailListVO> resultAfterSaleTaskDetailList) {
        if (CollUtil.isNotEmpty(resultAfterSaleTaskDetailList)) {
            List<Integer> afterSaleTypes = new ArrayList<>();
            for (AfterSaleTaskDetailListVO itemSon : resultAfterSaleTaskDetailList) {
                if (OrderTaskAfterSaleClassTypeEnum.VIDEO.getCode().equals(itemSon.getAfterSaleClass())){
                    afterSaleTypes.add(OrderTaskAfterSaleAllTypeEnum.getTaskAfterSaleVideoByCode(itemSon.getAfterSaleVideoType()));
                }
                if (OrderTaskAfterSaleClassTypeEnum.PIC.getCode().equals(itemSon.getAfterSaleClass())){
//                    if (TaskAfterSalePicTypeEnum.SOURCE_MATERIAL.getCode().equals(itemSon.getAfterSalePicType())){
//                        afterSaleTypes.add(TaskAfterSaleAllTypeEnum.getTaskAfterSaleVideoByCode(itemSon.getAfterSaleVideoType()));
//                        continue;
//                    }
                    afterSaleTypes.add(OrderTaskAfterSaleAllTypeEnum.getTaskAfterSalePicByCode(itemSon.getAfterSalePicType()));
                }
            }
            if (CollUtil.isNotEmpty(afterSaleTypes)){
                item.setAfterSaleTypes(afterSaleTypes.stream().distinct().collect(Collectors.toList()));
            }
        }
    }

    private void loadAfterSaleType(AfterSaleOrderTaskListDTO dto) {
        if (CollUtil.isNotEmpty(dto.getAfterSaleType())){
            for (Integer item : dto.getAfterSaleType()){
                OrderTaskAfterSaleAllTypeEnum orderTaskAfterSaleAllTypeEnum = OrderTaskAfterSaleAllTypeEnum.getTaskAfterSaleAllTypeEnumByCode(item);
                if (ObjectUtil.isNotNull(orderTaskAfterSaleAllTypeEnum)){
                    if (OrderTaskAfterSaleClassTypeEnum.VIDEO.getCode().equals(orderTaskAfterSaleAllTypeEnum.getType())){
                        if (CollUtil.isEmpty(dto.getAfterSaleVideoTypes())){
                            dto.setAfterSaleVideoTypes(new ArrayList<>());
                        }
                        dto.getAfterSaleVideoTypes().add(orderTaskAfterSaleAllTypeEnum.getValue());
                    }else if (OrderTaskAfterSaleClassTypeEnum.PIC.getCode().equals(orderTaskAfterSaleAllTypeEnum.getType())){
                        if (CollUtil.isEmpty(dto.getAfterSalePicTypes())){
                            dto.setAfterSalePicTypes(new ArrayList<>());
                        }
                        dto.getAfterSalePicTypes().add(orderTaskAfterSaleAllTypeEnum.getValue());
                    }
                }
            }
        }
    }
}
