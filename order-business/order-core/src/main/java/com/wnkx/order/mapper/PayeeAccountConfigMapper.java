package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.system.api.domain.entity.order.PayeeAccountConfig;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【payee_account_config(收款人账号配置表)】的数据库操作Mapper
* @createDate 2024-09-13 11:08:38
* @Entity generator.domain.PayeeAccountConfig
*/
@Mapper
public interface PayeeAccountConfigMapper extends SuperMapper<PayeeAccountConfig> {

    /**
     * 根据账号类型获取收款人账号配置表
     * @param type
     * @return
     */
    default PayeeAccountConfig getValidConfigByAccountType(Integer type){
        return this.selectOne(new LambdaQueryWrapper<PayeeAccountConfig>()
                .eq(PayeeAccountConfig::getStatus, StatusTypeEnum.YES.getCode())
                .eq(PayeeAccountConfig::getAccountType, type)
                .last("limit 1")
        );
    }
}




