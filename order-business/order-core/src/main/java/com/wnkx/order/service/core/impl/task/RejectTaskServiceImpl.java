package com.wnkx.order.service.core.impl.task;

import cn.hutool.core.lang.Assert;
import com.ruoyi.common.core.enums.OrderTaskStatusEnum;
import com.ruoyi.system.api.domain.dto.order.OrderVideoTaskFlowDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTask;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetail;
import com.wnkx.order.service.OrderVideoTaskService;
import com.wnkx.order.service.core.OrderVideoTaskFlowService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 已拒绝
 * @create :2024-12-11 16:53
 **/
@Service
@RequiredArgsConstructor
public class RejectTaskServiceImpl implements OrderVideoTaskFlowService {

    private final OrderVideoTaskService orderVideoTaskService;
    @Override
    public void flowVideoTask(OrderVideoTaskFlowDTO dto) {
        OrderVideoTaskDetail orderVideoTaskDetail = dto.getOrderVideoTaskDetail();
        OrderVideoTask orderVideoTask = orderVideoTaskService.getById(orderVideoTaskDetail.getTaskId());
        Assert.notNull(orderVideoTask, "工单不存在");
        Assert.isTrue(OrderTaskStatusEnum.UN_HANDLE.getCode().equals(orderVideoTaskDetail.getStatus()), "任务单状态异常，需要是[待处理]");

        orderVideoTaskDetail.setStatus(OrderTaskStatusEnum.REJECT.getCode());

    }

    @Override
    public OrderTaskStatusEnum getOrderTaskStatusEnumType() {
        return OrderTaskStatusEnum.REJECT;
    }
}
