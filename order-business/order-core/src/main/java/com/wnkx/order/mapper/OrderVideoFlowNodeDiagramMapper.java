package com.wnkx.order.mapper;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.OrderVideoFlowNodeEnum;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFlowNodeDiagram;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/14 17:20
 */
@Mapper
public interface OrderVideoFlowNodeDiagramMapper extends SuperMapper<OrderVideoFlowNodeDiagram> {

    /**
     * 设置节点完成时间
     */
    default void setNodeCompleteTime(List<Long> videoIds, OrderVideoFlowNodeEnum... nodeEnum) {
        update(null, new LambdaUpdateWrapper<OrderVideoFlowNodeDiagram>()
                .in(OrderVideoFlowNodeDiagram::getVideoId, videoIds)
                .in(OrderVideoFlowNodeDiagram::getNode, Arrays.stream(nodeEnum).map(OrderVideoFlowNodeEnum::getCode).collect(Collectors.toList()) )
                .set(OrderVideoFlowNodeDiagram::getTime, DateUtil.date())
        );
    }

    /**
     * 删除完成时间为空的节点
     */
    default void deleteNullTime(List<Long> videoIds) {
        delete(new LambdaUpdateWrapper<OrderVideoFlowNodeDiagram>()
                .in(OrderVideoFlowNodeDiagram::getVideoId, videoIds)
                .isNull(OrderVideoFlowNodeDiagram::getTime)
        );
    }

    /**
     * 删除取消订单节点
     * @param videoIds
     */
    default void deleteClose(List<Long> videoIds) {
        delete(new LambdaUpdateWrapper<OrderVideoFlowNodeDiagram>()
                .in(OrderVideoFlowNodeDiagram::getVideoId, videoIds)
                .eq(OrderVideoFlowNodeDiagram::getNode, OrderVideoFlowNodeEnum.CANCEL_ORDER.getCode())
        );
    }

    /**
     * 设置某一节点的完成时间为null
     */
    default void setNodeCompleteTimeNull(Long videoId, OrderVideoFlowNodeEnum nodeEnum) {
        update(null, new LambdaUpdateWrapper<OrderVideoFlowNodeDiagram>()
                .eq(OrderVideoFlowNodeDiagram::getVideoId, videoId)
                .eq(OrderVideoFlowNodeDiagram::getNode, nodeEnum.getCode())
                .set(OrderVideoFlowNodeDiagram::getTime, null)
        );
    }

    /**
     * 查询视频订单节点图
     */
    default List<OrderVideoFlowNodeDiagram> selectOrderVideoFlowNodeDiagramListByVideoId(Long videoId) {
        return selectList(new LambdaQueryWrapper<OrderVideoFlowNodeDiagram>()
                .eq(OrderVideoFlowNodeDiagram::getVideoId, videoId)
                .orderByAsc(OrderVideoFlowNodeDiagram::getSort)
        );
    }

    /**
     * 订单回退设置除下单支付外节点完成时间为null
     */
    default void setNodeCompleteTimeNullExceptOrderPay(Long videoId) {
        update(null, new LambdaUpdateWrapper<OrderVideoFlowNodeDiagram>()
                .eq(OrderVideoFlowNodeDiagram::getVideoId, videoId)
                .ne(OrderVideoFlowNodeDiagram::getNode, OrderVideoFlowNodeEnum.ORDER_PAYMENT.getCode())
                .set(OrderVideoFlowNodeDiagram::getTime, null)
        );
    }
}
