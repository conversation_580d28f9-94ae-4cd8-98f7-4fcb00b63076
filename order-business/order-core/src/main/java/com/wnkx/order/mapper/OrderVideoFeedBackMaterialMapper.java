package com.wnkx.order.mapper;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.MaterialStatusEnum;
import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.system.api.domain.dto.order.MaterialRejectDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoFeedBackMaterialDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBackMaterial;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_video_feed_back_material(订单反馈表(模特))】的数据库操作Mapper
 * @createDate 2024-06-18 17:43:06
 * @Entity
 */
public interface OrderVideoFeedBackMaterialMapper extends SuperMapper<OrderVideoFeedBackMaterial> {

    default List<OrderVideoFeedBackMaterial> modelFeedBackListByVideoIds(List<Long> videoIds) {
        return selectList(new LambdaQueryWrapper<OrderVideoFeedBackMaterial>()
                .in(OrderVideoFeedBackMaterial::getVideoId, videoIds)
                .orderByDesc(OrderVideoFeedBackMaterial::getCreateTime)
        );
    }

    default List<OrderVideoFeedBackMaterial> modelFeedBackList(Long videoId) {
        return selectList(new LambdaQueryWrapper<OrderVideoFeedBackMaterial>()
                .eq(OrderVideoFeedBackMaterial::getVideoId, videoId)
                .orderByDesc(OrderVideoFeedBackMaterial::getCreateTime)
        );
    }

    default void rejectModelMaterial(MaterialRejectDTO materialRejectDTO) {
        Assert.isTrue(
                selectCount(new LambdaQueryWrapper<OrderVideoFeedBackMaterial>().eq(
                        OrderVideoFeedBackMaterial::getId, materialRejectDTO.getMaterialId())) > 0, "素材不存在");
        update(null, new LambdaUpdateWrapper<OrderVideoFeedBackMaterial>()
                .eq(OrderVideoFeedBackMaterial::getId, materialRejectDTO.getMaterialId())
                .set(OrderVideoFeedBackMaterial::getStatus, MaterialStatusEnum.REJECT.getCode())
                .set(OrderVideoFeedBackMaterial::getRemark, materialRejectDTO.getRemark())
                .set(OrderVideoFeedBackMaterial::getTitle, materialRejectDTO.getTitle())
                .set(OrderVideoFeedBackMaterial::getReplyStatus, StatusEnum.UN_ENABLED.getCode())
        );
    }


    /**
     * 通过视频订单id新增一条
     *
     * @param videoId
     * @return
     */
    default OrderVideoFeedBackMaterial addOrderFeedBackMaterialByVideoId(Long videoId, Long rollbackId) {
        OrderVideoFeedBackMaterial orderVideoFeedBackMaterial = new OrderVideoFeedBackMaterial();
        orderVideoFeedBackMaterial.setVideoId(videoId);
        orderVideoFeedBackMaterial.setRollbackId(rollbackId);
        this.insert(orderVideoFeedBackMaterial);
        return orderVideoFeedBackMaterial;
    }

    /**
     * 设置模特反馈素材状态为已完成
     */
    default void finishModelMaterial(List<Long> videoIds) {
        this.update(null, new LambdaUpdateWrapper<OrderVideoFeedBackMaterial>()
                .in(OrderVideoFeedBackMaterial::getVideoId, videoIds)
                .eq(OrderVideoFeedBackMaterial::getStatus, MaterialStatusEnum.UN_CONFIRM.getCode())
                .set(OrderVideoFeedBackMaterial::getStatus, MaterialStatusEnum.FINISHED.getCode())
        );
    }

    /**
     * 查询模特有反馈素材的视频订单id
     */
    List<Long> selectHasFeedBackMaterialByVideoIds(@Param("videoIds") List<Long> videoIds);

    /**
     * 查询模特有反馈
     * @param dto
     * @return
     */
    List<Long> selectHasFeedBackMaterialByDto(OrderVideoFeedBackMaterialDTO dto);

    /**
     * 获取视频订单最新一条的模特反馈素材
     */
    default OrderVideoFeedBackMaterial getLastModelFeedBack(Long videoId, Long rollbackId) {
        return selectOne(new LambdaQueryWrapper<OrderVideoFeedBackMaterial>()
                .eq(OrderVideoFeedBackMaterial::getVideoId, videoId)
                .eq(ObjectUtil.isNotNull(rollbackId), OrderVideoFeedBackMaterial::getRollbackId, rollbackId)
                .orderByDesc(OrderVideoFeedBackMaterial::getCreateTime)
                .last("limit 1")
        );
    }

    /**
     * 标记下载状态
      * @param id
     */
    void markDownload(Long id);

    /**
     * 通过视频订单ID和回退ID查询数据     * @param videoId
     */
    default List<OrderVideoFeedBackMaterial> selectListByVideoIdAndRollbackId(Long videoId, Long rollbackId) {
        return selectList(new LambdaQueryWrapper<OrderVideoFeedBackMaterial>()
                .eq(OrderVideoFeedBackMaterial::getVideoId, videoId)
                .eq(ObjectUtil.isNotNull(rollbackId), OrderVideoFeedBackMaterial::getRollbackId, rollbackId)
        );
    }

    /**
     * 通过视频订单ID和回退ID查询是否有模特反馈素材
     */
    default Boolean checkExistByVideoIdAndRollbackId(Long videoId, Long rollbackId) {
        return exists(new LambdaQueryWrapper<OrderVideoFeedBackMaterial>()
                .eq(OrderVideoFeedBackMaterial::getVideoId, videoId)
                .eq(ObjectUtil.isNotNull(rollbackId), OrderVideoFeedBackMaterial::getRollbackId, rollbackId)
        );
    }

    /**
     * 通过视频订单ID查询数据
     */
    List<OrderVideoFeedBackMaterial> selectListByVideoIds(@Param("videoIds") List<Long> videoIds);
}




