package com.wnkx.order.mapper;

import com.ruoyi.system.api.domain.entity.order.OrderInvoiceRedOrderVideo;
import com.ruoyi.system.api.domain.vo.order.OrderInvoiceRedOrderVideoVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/13 10:46
 */
@Mapper
public interface OrderInvoiceRedOrderVideoMapper extends SuperMapper<OrderInvoiceRedOrderVideo> {


    /**
     * 根据发票红冲id查询发票红冲视频订单
     */
    List<OrderInvoiceRedOrderVideoVO> selectRedOrderVideoVOListByRedOrderIds(@Param("invoiceRedOrderIds") List<Long> invoiceRedOrderIds);
}
