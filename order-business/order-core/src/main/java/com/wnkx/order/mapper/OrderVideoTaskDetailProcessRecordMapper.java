package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetailProcessRecord;
import com.ruoyi.system.api.domain.vo.order.OrderVideoTaskDetailProcessRecordVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/11 18:02
 */
@Mapper
public interface OrderVideoTaskDetailProcessRecordMapper extends SuperMapper<OrderVideoTaskDetailProcessRecord> {

    /**
     * 通过任务单查询处理记录
     */
    default List<OrderVideoTaskDetailProcessRecord> selectWorkOrderProcessRecordListByTaskNum(String taskNum) {
        return selectList(new LambdaQueryWrapper<OrderVideoTaskDetailProcessRecord>()
                .eq(OrderVideoTaskDetailProcessRecord::getTaskNum, taskNum)
                .orderByDesc(OrderVideoTaskDetailProcessRecord::getTime)
        );
    }

    default List<OrderVideoTaskDetailProcessRecord> selectListByTaskNums(List<String> taskNum) {
        return selectList(Wrappers.lambdaQuery(OrderVideoTaskDetailProcessRecord.class)
                .in(OrderVideoTaskDetailProcessRecord::getTaskNum, taskNum));
    }

}
