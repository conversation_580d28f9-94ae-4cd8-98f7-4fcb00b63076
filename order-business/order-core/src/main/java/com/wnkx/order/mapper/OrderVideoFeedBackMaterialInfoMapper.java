package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.dto.order.ClosedListDTO;
import com.ruoyi.system.api.domain.dto.order.MaterialInfoListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBackMaterialInfo;
import com.ruoyi.system.api.domain.vo.order.*;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 订单反馈(模特)_详细信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Mapper
public interface OrderVideoFeedBackMaterialInfoMapper extends SuperMapper<OrderVideoFeedBackMaterialInfo> {

    /**
     * 通过素材id查询素材信息
     *
     * @param materialIds 素材id
     * @return 素材信息
     */
    default List<OrderVideoFeedBackMaterialInfo> selectListByMaterialId(List<Long> materialIds) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoFeedBackMaterialInfo>()
                .in(OrderVideoFeedBackMaterialInfo::getMaterialId, materialIds)
                .orderByDesc(OrderVideoFeedBackMaterialInfo::getUploadTime)
        );
    }

    /**
     * 剪辑管理-待下载、待剪辑、待反馈、需确认 列表
     */
    List<MaterialInfoListVO> selectMaterialInfoListByCondition(@Param("dto") MaterialInfoListDTO dto, @Param("oldDataEndTime") String oldDataEndTime);

    /**
     * 待领取素材列表
     * @param oldDataEndTime
     * @return
     */
    List<MaterialInfoListVO> selectUnGetMaterialList(@Param("oldDataEndTime") String oldDataEndTime);

    /**
     * 剪辑管理-待下载列表统计
     */
    GetMaterialInfoStatisticsVO getMaterialInfoStatistics(@Param("oldDataEndTime") String oldDataEndTime);

    /**
     * 剪辑管理-历史剪辑记录
     */
    List<SelectHistoryEditListVO> selectHistoryEditList(@Param("videoId") Long videoId);

    /**
     * 获取今日领取编码最大值
     */
    Integer getTodayMaxGetCode();

    /**
     * 剪辑管理-标记下载-校验是否存在进行中的任务
     */
    Boolean checkDownload(@Param("videoId") Long videoId);

    /**
     * 剪辑管理-历史剪辑要求
     */
    List<HistoryClipRecordListVO> getHistoryClipRecord(@Param("videoId") Long videoId);

    /**
     * 检查视频订单是否已评分
     */
    Boolean checkScored(@Param("videoId") Long videoId, @Param("rollbackId") Long rollbackId);

    /**
     * 获取有剪辑要求的视频订单id
     */
    List<Long> getExistClipRequireByVideoIds(@Param("videoIds") List<Long> videoIds);

    /**
     * 剪辑管理-待下载、待剪辑、待反馈、需确认 拍摄模特下拉框
     */
    List<Long> getShootModelId(@Param("status") Integer status, @Param("oldDataEndTime") String oldDataEndTime);

    /**
     * 剪辑管理-待下载 领取人下拉框
     */
    List<Long> getReceivePerson();

    /**
     * 剪辑管理-待反馈、需确认 剪辑人下拉框
     */
    List<Long> getEditPerson(@Param("status") Integer status);

    /**
     * 剪辑管理-已关闭 列表
     */
    List<ClosedListVO> selectClosedListByCondition(@Param("dto") ClosedListDTO dto);

    /**
     * 获取存在剪辑任务为待下载、待反馈、待剪辑、需确认、待上传 中的视频订单ID
     */
    List<Long> getInTheEditingVideoIds(@Param("videoIds") List<Long> videoIds);

    /**
     * 获取工作台剪辑统计数据
     * @param oldDataEndTime
     * @return
     */
    WorkbenchVO getWorkbenchStatistics(@Param("oldDataEndTime") String oldDataEndTime);

    /**
     * 剪辑管理-需确认列表-导出
     */
    List<ExportNeedConfirmListVO> selectExportNeedConfirmList(@Param("feedbackTimeBegin") Date feedbackTimeBegin, @Param("feedbackTimeEnd") Date feedbackTimeEnd);
}
