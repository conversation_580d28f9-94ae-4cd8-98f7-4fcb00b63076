package com.wnkx.order.service.core;

import com.ruoyi.system.api.domain.dto.order.AddOrderMemberFlowDTO;
import com.ruoyi.system.api.domain.dto.order.UpdateIssueIdDTO;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoCurrentInfoVO;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

public interface AsyncTaskService {

    /**
     * 富友关闭订单*
     *
     * @param payPlatformType
     * @param outTradeNo
     */
    @Async
    void closeOrder(String payPlatformType, String outTradeNo);

    /**
     * 生成订单创建变更记录
     */
    @Async
    void addVideoInitChangeLog(List<OrderVideoCurrentInfoVO> orderVideoCurrentInfoVOS);

    /**
     * 添加会员订单流水*
     *
     * @param dto
     * @param loginUserInfoVo
     */
    @Async
    void addOrderMemberFlow(AddOrderMemberFlowDTO dto, LoginUserInfoVO loginUserInfoVo);

    /**
     * 支付宝关闭订单*
     *
     * @param outTradeNo
     * @param appId
     */
    @Async
    void closeAlipayOrder(String outTradeNo, String appId);

    /**
     * 关闭所有订单订单*
     *
     * @param orderNum
     */
    @Async
    void closeAllOrder(String orderNum);

    /**
     * 关闭所有订单订单*
     */
    @Async
    void closeAllOrder(List<String> orderNums);

    /**
     * 模特变更对接客服更新视频订单的出单人ID
     */
    @Async
    void updateIssueId(UpdateIssueIdDTO dto);
}
