package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFollowDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFollowListDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.entity.order.OrderResource;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import com.ruoyi.system.api.domain.vo.LogisticVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import com.ruoyi.system.api.domain.vo.order.logistic.*;
import com.wnkx.order.mapper.OrderVideoLogisticFollowMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.OrderResourceService;
import com.wnkx.order.service.OrderVideoLogisticFollowRecordService;
import com.wnkx.order.service.OrderVideoLogisticFollowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【order_video_logistic_follow(物流跟进表)】的数据库操作Service实现
 * @createDate 2025-04-22 16:26:39
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderVideoLogisticFollowServiceImpl extends ServiceImpl<OrderVideoLogisticFollowMapper, OrderVideoLogisticFollow>
        implements OrderVideoLogisticFollowService {
    private final RemoteService remoteService;
    private final ThreadPoolTaskExecutor asyncPoolTaskExecutor;
    private final OrderVideoLogisticFollowRecordService orderVideoLogisticFollowRecordService;
    private final OrderResourceService orderResourceService;

    @Override
    public List<OrderVideoLogisticFollowVO> selectOrderVideoLogisticFollowList(OrderVideoLogisticFollowListDTO dto) {
        if (StrUtil.isNotBlank(dto.getKeyword())) {
            ModelListDTO modelListDTO = new ModelListDTO();
            modelListDTO.setName(dto.getKeyword());
            modelListDTO.setAccount(dto.getKeyword());
            List<ModelInfoVO> modelList = remoteService.queryLikeModelList(modelListDTO);
            if (CollUtil.isNotEmpty(modelList)) {
                List<Long> modelIds = modelList.stream().map(ModelInfoVO::getId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
                dto.setShootModelIdsByKeyword(modelIds);
            }
            SysUserListDTO sysUserListDTO = new SysUserListDTO();
            sysUserListDTO.setUserName(dto.getKeyword());
            List<SysUser> userList = remoteService.getUserList(sysUserListDTO);
            if (CollUtil.isNotEmpty(userList)) {
                List<Long> userIds = userList.stream().map(SysUser::getUserId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
                dto.setContactIdsByKeyword(userIds);
            }
        }
        if (ObjectUtil.isNotNull(dto.getLogisticUpdateTimeStart())) {
            dto.setLogisticUpdateTimeStart(DateUtils.getStartOfDay(dto.getLogisticUpdateTimeStart()));
        }
        if (ObjectUtil.isNotNull(dto.getLogisticUpdateTimeEnd())) {
            dto.setLogisticUpdateTimeEnd(DateUtils.getEndOfDay(dto.getLogisticUpdateTimeEnd()));
        }
        if (ObjectUtil.isNotNull(dto.getShootModelId())){
            if (StatusTypeEnum.YES.getCode().equals(dto.getIncludeFamily())){
                //查询模特家庭数据
                ModelListDTO modelListDTO = new ModelListDTO();
                modelListDTO.setId(Arrays.asList(dto.getShootModelId()));
                modelListDTO.setIncludeFamily(StatusTypeEnum.YES.getCode());
                List<ModelOrderSimpleVO> modelOrderSimpleVOS = remoteService.queryModelSimpleList(modelListDTO);
                if (CollUtil.isNotEmpty(modelOrderSimpleVOS)){
                    dto.setShootModelIdList(modelOrderSimpleVOS.stream().map(ModelOrderSimpleVO::getId).collect(Collectors.toList()));
                }else {
                    dto.setShootModelIdList(Arrays.asList(dto.getShootModelId()));
                }
            }else {
                dto.setShootModelIdList(Arrays.asList(dto.getShootModelId()));
            }
        }
        OrderByDto orderByDto = new OrderByDto();
        if (ObjectUtil.isNotNull(dto.getOrderByType()) && ObjectUtil.isNotNull(LogisticFollowOrderByTypeEnum.getValue(dto.getOrderByType()))) {
            orderByDto.setField(LogisticFollowOrderByTypeEnum.getValue(dto.getOrderByType()), StatusTypeEnum.YES.getCode().equals(dto.getIsAsc()) ? OrderByDto.DIRECTION.DESC : OrderByDto.DIRECTION.ASC);
        } else {
            orderByDto.setField("ovlf.update_time", OrderByDto.DIRECTION.ASC);
        }

        PageUtils.startPage(orderByDto);

        if (SecurityUtils.getLoginUserType().equals(UserTypeConstants.MANAGER_TYPE)) {
            dto.setBackUserId(SecurityUtils.getUserId());
        }
        if (CollUtil.isNotEmpty(dto.getLogisticMainStatus())){
            List<String> mainStatusLabel = new ArrayList<>();
            for (Integer status : dto.getLogisticMainStatus()) {
                mainStatusLabel.add(LogisticMainStatus.getLabelByCode(status));
            }
            dto.setMainStatusLabels(mainStatusLabel);
        }
        List<OrderVideoLogisticFollowVO> orderVideoLogisticFollowVOS = baseMapper.selectOrderVideoLogisticFollowList(dto);
        if (CollUtil.isEmpty(orderVideoLogisticFollowVOS)){
            return Collections.emptyList();
        }
        load(orderVideoLogisticFollowVOS);
        return orderVideoLogisticFollowVOS;
    }

    @Override
    public OrderVideoLogisticFollowDetailVO getDetailById(Long id) {
        OrderVideoLogisticFollowVO orderVideoLogisticFollowVO = baseMapper.getDetailById(id);
        if (ObjectUtil.isNull(orderVideoLogisticFollowVO)){
            return null;
        }
        load(Arrays.asList(orderVideoLogisticFollowVO));
        //获取
        OrderVideoLogisticFollowDetailVO orderVideoLogisticFollowDetailVO = BeanUtil.copyProperties(orderVideoLogisticFollowVO, OrderVideoLogisticFollowDetailVO.class);
        orderVideoLogisticFollowDetailVO.setOrderVideoLogisticFollowRecords(orderVideoLogisticFollowRecordService.getListByFollowId(orderVideoLogisticFollowDetailVO.getId()));
        return orderVideoLogisticFollowDetailVO;
    }

    private void load(List<OrderVideoLogisticFollowVO> orderVideoLogisticFollowVOS) {
        if (CollUtil.isEmpty(orderVideoLogisticFollowVOS)){
            return;
        }
        try {
            orderVideoLogisticFollowVOS.forEach(item -> item.init());
            CompletableFuture<Map<Long, UserVO>> userMapFuture = CompletableFuture.supplyAsync(() -> {
                Set<Long> contactIds = orderVideoLogisticFollowVOS.stream().map(OrderVideoLogisticFollowVO::getContactId).collect(Collectors.toSet());
                Set<Long> issueIds = orderVideoLogisticFollowVOS.stream().map(OrderVideoLogisticFollowVO::getIssueId).collect(Collectors.toSet());
                if (CollUtil.isNotEmpty(issueIds)){
                    contactIds.addAll(issueIds);
                }
                SysUserListDTO sysUserListDTO = new SysUserListDTO();
                sysUserListDTO.setUserId(contactIds);
                return remoteService.getUserMap(sysUserListDTO);
            }, asyncPoolTaskExecutor);
            CompletableFuture<Map<Long, ModelOrderSimpleVO>> modelIdsFuture = CompletableFuture.supplyAsync(() -> {
                List<Long> modelIds = new ArrayList<>();
                modelIds.addAll(orderVideoLogisticFollowVOS.stream().map(OrderVideoLogisticFollowVO::getShootModelId).collect(Collectors.toList()));
                return remoteService.getModelSimpleMap(modelIds);
            }, asyncPoolTaskExecutor);

            CompletableFuture<Map<String, LogisticVO>> logisticFuture = CompletableFuture.supplyAsync(() -> {
                List<String> numbers = new ArrayList<>();
                numbers.addAll(orderVideoLogisticFollowVOS.stream().map(OrderVideoLogisticFollowVO::getNumber).filter(StrUtil::isNotBlank).collect(Collectors.toList()));
                return remoteService.getLogisticMap(numbers);
            }, asyncPoolTaskExecutor);

            CompletableFuture<Map<Long, OrderResource>> resourceFuture = CompletableFuture.supplyAsync(() -> {
                List<Long> resourceIds = new ArrayList<>();
                for (OrderVideoLogisticFollowVO item:orderVideoLogisticFollowVOS){
                    List<Long> ids = StringUtils.splitToLong(item.getLatestResourceId(), StrUtil.COMMA);
                    resourceIds.addAll(ids);
                }
                return orderResourceService.getResourceMapByIds(resourceIds);
            }, asyncPoolTaskExecutor);

            CompletableFuture.allOf(
                    userMapFuture, modelIdsFuture, logisticFuture, resourceFuture
            ).join();

            Map<Long, UserVO> userVOMap = userMapFuture.get();
            Map<Long, ModelOrderSimpleVO> modelOrderSimpleVOMap = modelIdsFuture.get();
            Map<Long, OrderResource> resourceMap = resourceFuture.get();
            Map<String, LogisticVO> logisticMap = logisticFuture.get();

            for (OrderVideoLogisticFollowVO item : orderVideoLogisticFollowVOS) {
                item.setContact(userVOMap.get(item.getContactId()));
                item.setIssue(userVOMap.get(item.getIssueId()));
                item.setShootModel(modelOrderSimpleVOMap.get(item.getShootModelId()));
                item.setLogisticInfo(logisticMap.getOrDefault(item.getNumber(), new LogisticVO()).getLogisticInfo());
                item.setLatestMainStatusSketch(LogisticMainStatus.getSketchByLabel(item.getLatestMainStatus()));
                List<Long> ids = StringUtils.splitToLong(item.getLatestResourceId(), StrUtil.COMMA);
                List<String> urls = new ArrayList<>();
                for (Long id : ids) {
                    OrderResource orderResource = resourceMap.get(id);
                    if (ObjectUtil.isNull(orderResource)){
                        item.setResources(urls);
                        continue;
                    }
                    urls.add(orderResource.getObjectKey());
                }
                item.setResources(urls);
            }
        } catch (Exception e) {
            log.error("查询物流跟进列表错误！", e);
            throw new ServiceException("查询物流跟进列表错误！");
        }
    }

    @Override
    public void checkHandleStatus(List<OrderVideoLogisticFollow> orderVideoLogisticFollows, HandleStatusEnum... statusEnums) {
        List<Integer> status = Arrays.stream(statusEnums).map(HandleStatusEnum::getCode).collect(Collectors.toList());
        for (OrderVideoLogisticFollow orderVideo : orderVideoLogisticFollows) {
            if (!status.contains(orderVideo.getHandleStatus())) {
                log.error(StrUtil.format("处理状态异常，需要是：{}，当前是：[{}]，请刷新页面",
                        Arrays.stream(statusEnums).map(HandleStatusEnum::getLabel).collect(Collectors.toList()),
                        HandleStatusEnum.getHandleStatusEnumByCode(orderVideo.getHandleStatus(), null).getLabel()));
                throw new ServiceException("订单状态发生变化，请刷新页面重试~");
            }
        }
    }

    @Override
    public List<OrderVideoLogisticFollow> queryListBase(OrderVideoLogisticFollowDTO orderVideoLogisticFollowDTO) {
        return baseMapper.queryListBase(orderVideoLogisticFollowDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetEntity(OrderVideoLogisticFollow entity) {
        deleteByVideoIds(Arrays.asList(entity.getVideoId()));
        baseMapper.insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByVideoIds(List<Long> videoIds) {
        List<OrderVideoLogisticFollow> orderVideoLogisticFollows = baseMapper.selectList(new LambdaQueryWrapper<OrderVideoLogisticFollow>()
                .in(OrderVideoLogisticFollow::getVideoId, videoIds)
                .in(OrderVideoLogisticFollow::getFollowStatus, List.of(FollowStatusEnum.NEED_HANDLE.getCode(),FollowStatusEnum.TEMP_HOLD.getCode() )));
        if (CollUtil.isNotEmpty(orderVideoLogisticFollows)) {
            //删除需处理、暂不处理的数据
            List<Long> orderVideoLogisticFollowIds = orderVideoLogisticFollows.stream().map(OrderVideoLogisticFollow::getId).collect(Collectors.toList());
            baseMapper.deleteBatchIds(orderVideoLogisticFollowIds);
            orderVideoLogisticFollowRecordService.deleteByFollowIds(orderVideoLogisticFollowIds);
        }
    }

    @Override
    public OrderVideoLogisticFollowStatisticsVO getStatisticsVO() {
        OrderVideoLogisticFollowStatisticsVO orderVideoLogisticFollowStatisticsVO = Optional.ofNullable(baseMapper.getStatisticsVO()).orElse(new OrderVideoLogisticFollowStatisticsVO(0L, 0L, 0L, 0L, 0L, 0L));
        return orderVideoLogisticFollowStatisticsVO;
    }

    @Override
    public List<UserVO> orderContactSelect(String keyword, Integer followStatus) {
        Set<Long> orderContactId = baseMapper.getOrderContactId(followStatus);
        if (CollUtil.isEmpty(orderContactId)) {
            return Collections.emptyList();
        }

        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(orderContactId);
        dto.setUserName(keyword);
        List<SysUser> userList = remoteService.getUserList(dto);

        List<UserVO> userVOS = new ArrayList<>();

        userList.forEach(user -> {
            UserVO userVO = new UserVO();
            userVO.setId(user.getUserId());
            userVO.setName(user.getUserName());
            userVO.setPhonenumber(user.getPhonenumber());
            userVOS.add(userVO);

        });

        return userVOS;
    }

    @Override
    public List<UserVO> orderIssueSelect(String keyword, Integer followStatus) {
        Set<Long> orderIssueId = baseMapper.getOrderIssueId(followStatus);
        if (CollUtil.isEmpty(orderIssueId)) {
            return Collections.emptyList();
        }

        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(orderIssueId);
        dto.setUserName(keyword);
        List<SysUser> userList = remoteService.getUserList(dto);

        List<UserVO> userVOS = new ArrayList<>();

        userList.forEach(user -> {
            UserVO userVO = new UserVO();
            userVO.setId(user.getUserId());
            userVO.setName(user.getUserName());
            userVO.setPhonenumber(user.getPhonenumber());
            userVOS.add(userVO);

        });

        return userVOS;
    }

    @Override
    public List<MemberCodeListVO> memberCodeList(OrderVideoLogisticFollowListDTO dto) {
        return baseMapper.memberCodeList(dto);
    }

    @Override
    public List<LogisticFollowModelListVO> modelListSelect(OrderVideoLogisticFollowListDTO dto) {
        List<LogisticFollowModelListVO> logisticFollowModelListVOS = baseMapper.modelListSelect(dto);
        if (CollUtil.isNotEmpty(logisticFollowModelListVOS)) {
            List<Long> modelIdList = logisticFollowModelListVOS.stream().map(LogisticFollowModelListVO::getModelId).filter(Objects::nonNull).collect(Collectors.toList());
            //远程查询 模特数据
            Map<Long, ModelOrderSimpleVO> modelSimpleMap = remoteService.getModelSimpleMap(modelIdList);
            if (CollUtil.isEmpty(modelSimpleMap)){
                return logisticFollowModelListVOS;
            }
            for (LogisticFollowModelListVO item : logisticFollowModelListVOS) {
                item.setModelName(modelSimpleMap.getOrDefault(item.getModelId(), new ModelOrderSimpleVO()).getName());
            }
        }
        return logisticFollowModelListVOS;
    }
}




