package com.wnkx.order.mapper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.OrderInvoiceStatusEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.system.api.domain.dto.order.InvoiceFinishListDTO;
import com.ruoyi.system.api.domain.dto.order.ToBeInvoicedListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderInvoice;
import com.ruoyi.system.api.domain.vo.order.*;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单_发票Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
@Mapper
public interface OrderInvoiceMapper extends SuperMapper<OrderInvoice> {

    /**
     * 设置商家*
     * @param orderNums
     * @param merchantId
     */
    default void setMerchant(List<String> orderNums, Long merchantId) {
        this.update(null, new LambdaUpdateWrapper<OrderInvoice>()
                .in(OrderInvoice::getOrderNum, orderNums)
                .eq(OrderInvoice::getMerchantId, 0L)
                .set(OrderInvoice::getMerchantId, merchantId)
        );
    }

    /**
     * 获取发票数量
     */
    default Long getInvoiceCount() {
        return selectCount(new LambdaQueryWrapper<OrderInvoice>()
                .isNotNull(OrderInvoice::getTicketCode)
        );
    }

    /**
     * 商家/运营端-发票管理-待开票列表
     */
    List<ToBeInvoicedListVO> selectToBeInvoicedListByCondition(@Param("dto") ToBeInvoicedListDTO dto);

    /**
     * 商家/运营端-发票管理-已完成列表
     */
    List<InvoiceFinishListVO> selectInvoiceFinishListByCondition(@Param("dto") InvoiceFinishListDTO dto);

    /**
     * 商家端-发票管理-去除发票新标记
     */
    default void removeInvoiceNewFlag(Long invoiceId) {
        update(null, new LambdaUpdateWrapper<OrderInvoice>()
                .eq(OrderInvoice::getId, invoiceId)
                .set(OrderInvoice::getIsNew, StatusTypeEnum.NO.getCode())
        );
    }

    /**
     * 商家端-发票管理-数量统计
     */
    CompanyInvoiceStatisticsVO companyInvoiceStatistics(@Param("currentBusinessId") Long currentBusinessId);

    /**
     * 运营端-发票管理-数量统计
     */
    BackInvoiceStatisticsVO backInvoiceStatistics();

    /**
     * 运营端-发票管理-开票金额统计
     */
    InvoiceAmountStatisticsVO invoiceAmountStatistics();

    /**
     * 查询状态为已投递的发票
     */
    default List<OrderInvoice> selectDeliverListByIds(List<Long> invoiceIds) {
        return selectList(new LambdaQueryWrapper<OrderInvoice>()
                .in(OrderInvoice::getId, invoiceIds)
                .eq(OrderInvoice::getStatus, OrderInvoiceStatusEnum.DELIVER.getCode())
        );
    }

    /**
     * 通过发票ID和状态查询发票
     */
    default List<OrderInvoice> selectOrderInvoiceListByIdsAndStatus(List<Long> invoiceIds, List<Integer> status) {
        return selectList(new LambdaQueryWrapper<OrderInvoice>()
                .in(CollUtil.isNotEmpty(invoiceIds), OrderInvoice::getId, invoiceIds)
                .in(CollUtil.isNotEmpty(status), OrderInvoice::getStatus, status)
        );
    }

    /**
     * 通过订单号、视频ID获取已投递或者已红冲的发票
     * @param orderNums
     * @param videoIds
     * @return
     */
    List<OrderVideoInvoiceVO> selectDeliverOrRedPushListByOrderNums(@Param("orderNums") List<String> orderNums, @Param("videoIds") List<Long> videoIds);

    /**
     * 通过订单号获取最新开票信息
     */
    OrderInvoice getLastInvoiceByOrderNum(@Param("orderNum") String orderNum);
}
