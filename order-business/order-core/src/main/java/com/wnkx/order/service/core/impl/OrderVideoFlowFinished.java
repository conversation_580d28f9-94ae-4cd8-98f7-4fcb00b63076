package com.wnkx.order.service.core.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.system.api.domain.dto.order.OrderVideoFlowDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.IOrderVideoService;
import com.wnkx.order.service.OrderVideoFeedBackMaterialService;
import com.wnkx.order.service.OrderVideoFlowRecordService;
import com.wnkx.order.service.OrderVideoReminderRecordService;
import com.wnkx.order.service.core.OrderVideoFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 视频订单流转至已完成
 *
 * <AUTHOR>
 * @date 2024/6/29 10:40
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoFlowFinished implements OrderVideoFlowService {
    /**
     * 订单视频服务
     */
    private final IOrderVideoService orderVideoService;
    private final OrderVideoReminderRecordService orderVideoReminderRecordService;
    private final OrderVideoFeedBackMaterialService orderVideoFeedBackMaterialService;
    private final OrderVideoFlowRecordService orderVideoFlowRecordService;
    private final RemoteService remoteService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderVideoFlow(List<OrderVideo> orderVideos, String eventName) {
        //  ->已完成
        //  a:订单状态修改
        orderVideoService.checkVideoStatus(orderVideos, OrderStatusEnum.NEED_CONFIRM);

        Set<Long> createOrderUserIds = orderVideos.stream().map(OrderVideo::getCreateOrderUserId).collect(Collectors.toSet());
        List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchantByBusinessIdsAndAccountIds(null, createOrderUserIds);
        Map<Long, BusinessAccountDetailVO> accountMap = RemoteService.RemoteUtil.getAccountMap(businessAccountDetailVOS);

        List<OrderVideoFlowDTO> orderVideoFlowDTOS = new ArrayList<>();
        for (OrderVideo orderVideo : orderVideos) {
            orderVideoFlowDTOS.add(OrderVideoFlowDTO.builder().videoId(orderVideo.getId()).originStatus(orderVideo.getStatus()).targetStatus(OrderStatusEnum.FINISHED.getCode()).build());

            orderVideo.setStatus(OrderStatusEnum.FINISHED.getCode());
            orderVideo.setStatusTime(DateUtil.date());

            //  存储下单运营快照数据
            orderVideo.setCreateOrderUserName(accountMap.getOrDefault(orderVideo.getCreateOrderUserId(), new BusinessAccountDetailVO()).getName());
            orderVideo.setCreateOrderUserNickName(accountMap.getOrDefault(orderVideo.getCreateOrderUserId(), new BusinessAccountDetailVO()).getNickName());
        }

        List<Long> videoIds = orderVideos.stream().map(OrderVideo::getId).collect(Collectors.toList());
        //  更新视频订单催单记录为 已完成
        orderVideoReminderRecordService.updateReminderStatusToFinished(videoIds);
        //  设置模特反馈素材状态为已完成
        orderVideoFeedBackMaterialService.finishModelMaterial(videoIds);
        orderVideoFlowRecordService.createOrderVideoFlow(eventName, orderVideoFlowDTOS);
    }

    @Override
    public OrderStatusEnum getOrderStatusType() {
        return OrderStatusEnum.FINISHED;
    }
}
