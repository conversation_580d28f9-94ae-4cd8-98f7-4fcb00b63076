package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigDTO;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigInfoDTO;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_payee_account_config(收款人账号关联表)】的数据库操作Mapper
 * @createDate 2024-12-17 16:04:06
 * @Entity com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfig
 */
public interface OrderPayeeAccountConfigMapper extends BaseMapper<OrderPayeeAccountConfig> {

    List<OrderPayeeAccountConfigDTO> dataList();

    List<OrderPayeeAccountConfigDTO> typeList(Integer type);

    OrderPayeeAccountConfigInfoDTO typeInfo(Integer type);

    OrderPayeeAccountConfigInfoDTO getHistoryInfo(Long detailId);

    default Long saveInfo(OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO) {
        OrderPayeeAccountConfig orderPayeeAccountConfig = new OrderPayeeAccountConfig();
        orderPayeeAccountConfig.setAccountName(orderPayeeAccountConfigInfoDTO.getAccountName());
        orderPayeeAccountConfig.setStatus(orderPayeeAccountConfigInfoDTO.getStatus());
        orderPayeeAccountConfig.setType(orderPayeeAccountConfigInfoDTO.getType());
        orderPayeeAccountConfig.setCreateBy(SecurityUtils.getUsername());
        orderPayeeAccountConfig.setCreateById(SecurityUtils.getUserId());
        orderPayeeAccountConfig.setUpdateBy(SecurityUtils.getUsername());
        orderPayeeAccountConfig.setUpdateById(SecurityUtils.getUserId());
        insert(orderPayeeAccountConfig);
        return orderPayeeAccountConfig.getId();
    }

    default void updateInfo(OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO,Long id) {
        update(null, new LambdaUpdateWrapper<>(OrderPayeeAccountConfig.class)
                .eq(OrderPayeeAccountConfig::getId, orderPayeeAccountConfigInfoDTO.getId())
                .set(OrderPayeeAccountConfig::getDetailId, id)
                .set(OrderPayeeAccountConfig::getAccountName, orderPayeeAccountConfigInfoDTO.getAccountName())
        );
    }

    default void updateOldStatus(Integer type){
        update(null, new LambdaUpdateWrapper<>(OrderPayeeAccountConfig.class)
                .eq(OrderPayeeAccountConfig::getType, type)
                .set(OrderPayeeAccountConfig::getStatus, 0)
                .set(OrderPayeeAccountConfig::getUpdateBy, SecurityUtils.getUsername())
                .set(OrderPayeeAccountConfig::getUpdateById, SecurityUtils.getUserId())
                .set(OrderPayeeAccountConfig::getUpdateTime, new Date())
        );
    }

    default void saveDetailId(Long id, Long detailId){
        update(null, new LambdaUpdateWrapper<>(OrderPayeeAccountConfig.class)
                .eq(OrderPayeeAccountConfig::getId, id)
                .set(OrderPayeeAccountConfig::getDetailId, detailId)
                .set(OrderPayeeAccountConfig::getUpdateBy, SecurityUtils.getUsername())
                .set(OrderPayeeAccountConfig::getUpdateById, SecurityUtils.getUserId())
                .set(OrderPayeeAccountConfig::getUpdateTime, new Date())
        );
    }
}




