package com.wnkx.order.service.core.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionChannelOrderDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoFlowDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoModelChangeDTO;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessVO;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.OrderVideoFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 视频订单流转至待确认
 *
 * <AUTHOR>
 * @date 2024/6/29 10:40
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoFlowUnConfirm implements OrderVideoFlowService {

    /**
     * 订单视频服务
     */
    private final IOrderVideoService orderVideoService;
    private final RemoteService remoteService;
    private final OrderVideoFlowRecordService orderVideoFlowRecordService;
    private final OrderVideoFlowNodeDiagramService orderVideoFlowNodeDiagramService;
    private final OrderVideoModelChangeService orderVideoModelChangeService;

    @Override
    public void orderVideoFlow(List<OrderVideo> orderVideos, String eventName) {
        //  ->待确认
        //  a:生成视频编码
        //  b:订单状态修改

        Order order = SpringUtils.getBean(IOrderService.class).getOrderByOrderNum(orderVideos.get(0).getOrderNum());
        Assert.notBlank(order.getMerchantCode(), "商家编码不能为空");
        //获取商家数据
        int orderVideoCount = orderVideoService.getOrderVideoCount(order.getMerchantCode());
        //商家重要订单数量
        int orderVideoCareCount = orderVideoService.getOrderVideoCareCount(order.getMerchantCode());

        //获取商家数据
        BusinessDTO businessDTO = new BusinessDTO();
        businessDTO.setId(order.getMerchantId());
        BusinessVO businessVo = remoteService.getBusinessVo(businessDTO);

        try {
            if (ObjectUtil.isNotNull(businessVo) && StrUtil.isNotBlank(businessVo.getSeedCode())){
                remoteService.saveDistributionChannelOrder(DistributionChannelOrderDTO.builder()
                        .orderAmount(order.getOrderAmount())
                        .realPayAmount(order.getRealPayAmount())
                        .payTime(order.getPayTime())
                        .orderNum(order.getOrderNum())
                        .seedCode(businessVo.getSeedCode())
                        .businessId(businessVo.getId())
                        .build());
            }
        } catch (Exception e) {
            log.error("保存渠道订单信息失败:", e);
        }
        //剩余标记订单数量
        AtomicInteger atomicInteger = new AtomicInteger();
        Map<Integer, CustomerTypeEnum> customerTypeEnumMap = Map.of(
                CustomerTypeEnum.NORMAL.getCode(), CustomerTypeEnum.NORMAL,
                CustomerTypeEnum.VIP.getCode(), CustomerTypeEnum.VIP
        );
        if (orderVideoCareCount >= customerTypeEnumMap.get(businessVo.getCustomerType()).getNum()) {
            atomicInteger.set(0);
        } else {
            atomicInteger.set(customerTypeEnumMap.get(businessVo.getCustomerType()).getNum() - orderVideoCareCount);
        }
        List<OrderVideoFlowDTO> orderVideoFlowDTOS = new ArrayList<>();
        List<OrderVideoModelChangeDTO> orderVideoModelChangeDTOS = new ArrayList<>();
        for (OrderVideo orderVideo : orderVideos) {
            if (atomicInteger.get() > 0) {
                orderVideo.setIsCare(StatusTypeEnum.YES.getCode());
                atomicInteger.set(atomicInteger.get() - 1);
            } else {
                orderVideo.setIsCare(StatusTypeEnum.NO.getCode());
            }
            orderVideoFlowDTOS.add(OrderVideoFlowDTO.builder().videoId(orderVideo.getId()).originStatus(orderVideo.getStatus()).targetStatus(OrderStatusEnum.UN_CONFIRM.getCode()).build());

            orderVideo.setVideoCode(order.getMerchantCode() + orderVideoCount++);
            orderVideo.setStatus(OrderStatusEnum.UN_CONFIRM.getCode());
            orderVideo.setStatusTime(DateUtil.date());
            orderVideo.setUnConfirmTime(DateUtil.date());

            if (ObjectUtil.isNotNull(orderVideo.getIntentionModelId())) {
                orderVideoModelChangeDTOS.add(OrderVideoModelChangeDTO.builder().videoId(orderVideo.getId()).modelId(orderVideo.getIntentionModelId()).source(OrderVideoModelChangeSourceEnum.INTENTION_MODEL.getCode()).build());
            }
        }
        orderVideoFlowRecordService.createOrderVideoFlow(eventName, orderVideoFlowDTOS);
        orderVideoFlowNodeDiagramService.setNodeCompleteTime(orderVideos.stream().map(OrderVideo::getId).collect(Collectors.toList()), OrderVideoFlowNodeEnum.ORDER_PAYMENT);
        orderVideoModelChangeService.saveOrderVideoModelChange(orderVideoModelChangeDTOS);
        //  商家排单时 更新最近排单时间 以及删除未排单事件
        remoteService.updateRecentOrderTime(order.getMerchantId());
    }

    @Override
    public OrderStatusEnum getOrderStatusType() {
        return OrderStatusEnum.UN_CONFIRM;
    }
}
