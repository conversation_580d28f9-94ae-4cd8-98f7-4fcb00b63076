package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.OrderInvoiceRedStatusEnum;
import com.ruoyi.system.api.domain.dto.order.ToBeRedInvoiceListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceRed;
import com.ruoyi.system.api.domain.vo.order.ToBeRedInvoiceListVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:28
 */
@Mapper
public interface OrderInvoiceRedMapper extends SuperMapper<OrderInvoiceRed> {


    /**
     * 运营端-发票管理-待红冲列表
     */
    List<ToBeRedInvoiceListVO> selectToBeRedInvoiceListByCondition(@Param("dto") ToBeRedInvoiceListDTO dto);

    /**
     * 通过发票ID获取发票已红冲记录
     */
    default OrderInvoiceRed getRedPushInvoiceByInvoiceId(Long invoiceId) {
        return selectOne(new LambdaQueryWrapper<OrderInvoiceRed>()
                .eq(OrderInvoiceRed::getInvoiceId, invoiceId)
                .eq(OrderInvoiceRed::getInvoiceRedStatus, OrderInvoiceRedStatusEnum.RED_STAMP.getCode())
                .orderByDesc(OrderInvoiceRed::getCreateTime)
                .last("limit 1")
        );
    }

    /**
     * 通过发票ID获取发票非不红冲记录
     */
    default OrderInvoiceRed getNoRedPushInvoiceByInvoiceId(Long invoiceId) {
        return selectOne(new LambdaQueryWrapper<OrderInvoiceRed>()
                .eq(OrderInvoiceRed::getInvoiceId, invoiceId)
                .ne(OrderInvoiceRed::getInvoiceRedStatus, OrderInvoiceRedStatusEnum.NO_RED_PUNCH.getCode())
                .last("limit 1")
        );
    }

    /**
     * 获取待红冲数量
     */
    Long getToBeRedInvoiceCount();

    /**
     * 获取待红冲金额
     */
    BigDecimal getToBeRedInvoiceAmount();

    /**
     * 通过发票ID获取发票待红冲记录
     */
    default List<OrderInvoiceRed> selectNeedRedPushListByInvoiceIds(Set<Long> invoiceIds) {
        return selectList(new LambdaQueryWrapper<OrderInvoiceRed>()
                .in(OrderInvoiceRed::getInvoiceId, invoiceIds)
                .eq(OrderInvoiceRed::getInvoiceRedStatus, OrderInvoiceRedStatusEnum.WAITING_TO_BE_FLUSHED.getCode())
                // .eq(OrderInvoiceRed::getInvoiceRedCause, OrderInvoiceRedCauseEnum.MERCHANT_WITHDRAWAL.getCode())
        );
    }
}
