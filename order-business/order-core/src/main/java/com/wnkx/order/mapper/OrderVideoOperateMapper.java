package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.enums.OrderVideoOperateTypeEnum;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.UpdateCartToOrderVideoOperateDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoOperate;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/14 9:34
 */
@Mapper
public interface OrderVideoOperateMapper extends SuperMapper<OrderVideoOperate> {

    /**
     * 将购物车操作记录更新为视频订单操作记录
     */
    default void updateCartToOrderVideoOperate(UpdateCartToOrderVideoOperateDTO updateCartToOrderVideoOperateDTO) {
        update(null, new LambdaUpdateWrapper<OrderVideoOperate>()
                .eq(OrderVideoOperate::getVideoId, updateCartToOrderVideoOperateDTO.getCartId())
                .eq(OrderVideoOperate::getIsCart, 1)
                .set(OrderVideoOperate::getVideoId, updateCartToOrderVideoOperateDTO.getVideoId())
                .set(OrderVideoOperate::getIsCart, 0)
        );
    }

    /**
     * 查询视频订单操作记录
     */
    default List<OrderVideoOperate> selectOrderVideoOperateListByVideoId(Long videoId) {
        return selectList(new LambdaQueryWrapper<OrderVideoOperate>()
                .eq(OrderVideoOperate::getVideoId, videoId)
                .eq(UserTypeConstants.USER_TYPE == SecurityUtils.getLoginUserType(), OrderVideoOperate::getIsPublic, 0)
                .eq(OrderVideoOperate::getIsCart, 0)
                .orderByDesc(OrderVideoOperate::getEventExecuteTime)
        );
    }

    /**
     * 查询视频订单操作记录
     */
    default List<OrderVideoOperate> selectOrderVideoOperateListByVideoIds(List<Long> videoIds) {
        return selectList(new LambdaQueryWrapper<OrderVideoOperate>()
                .in(OrderVideoOperate::getVideoId, videoIds)
        );
    }
}
