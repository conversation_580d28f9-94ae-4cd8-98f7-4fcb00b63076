package com.wnkx.order.factory;

import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.ruoyi.common.core.exception.ServiceException;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wnkx.order.config.AlipayProperties;
import com.wnkx.order.config.WeChatPayConfig;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付Client工厂
 *
 * <AUTHOR>
 * @Date 2025/02/20
 */
@Component
@Slf4j
public class PayClientFactory {

    private static final Map<String, AlipayProperties> ALIPAY_PROPERTIES_MAP = new HashMap<>();

    private static final Map<String, WeChatPayConfig> WE_CHAT_PAY_CONFIG_MAP = new HashMap<>();

    public static Collection<AlipayProperties> getAllAlipayProperties() {
        return ALIPAY_PROPERTIES_MAP.values();
    }

    public static AlipayProperties getAlipayProperties(String appId) {
        return ALIPAY_PROPERTIES_MAP.get(appId);
    }

    public static Collection<WeChatPayConfig> getAllWechatPayConfig() {
        return WE_CHAT_PAY_CONFIG_MAP.values();
    }

    public static WeChatPayConfig getWeChatPayConfig(String appId) {
        return WE_CHAT_PAY_CONFIG_MAP.get(appId);
    }

    private static String alipayConfigPath = "/alipay/config.json";

    private static String wechatConfigPath = "/wechat/config.json";

    @Value("${url.api}")
    private String apiUrl;


    @PostConstruct
    public void initConfig() {
        initAlipayConfig();
        initWechatPayConfig();
    }

    @SneakyThrows
    private void initAlipayConfig() {
        log.info("init AlipayConfig");
        String jsonConfig = readKey(alipayConfigPath);
        List<AlipayProperties> alipayProperties = JSON.parseArray(jsonConfig, AlipayProperties.class);
        for (AlipayProperties alipayProperty : alipayProperties) {
            alipayProperty.setNotifyUrl(apiUrl + alipayProperty.getNotifyUrl());
            AlipayConfig alipayConfig = new AlipayConfig();
            alipayConfig.setAppId(alipayProperty.getAppId());
            alipayConfig.setServerUrl(alipayProperty.getServerUrl());
            alipayConfig.setAlipayPublicKey(alipayProperty.getAlipayPublicKey());
            alipayConfig.setPrivateKey(alipayProperty.getPrivateKey());
            alipayConfig.setCharset("UTF-8");
            alipayConfig.setSignType("RSA2");
            alipayConfig.setFormat("json");
            //设置连接池中的最大可缓存的空闲连接数
            alipayConfig.setMaxIdleConnections(5);
            //连接超时，单位：毫秒，默认3000
            alipayConfig.setConnectTimeout(3000);
            //读取超时，单位：毫秒，默认15000
            alipayConfig.setReadTimeout(15000);
            //空闲连接存活时间，单位：毫秒，默认10000L
            alipayConfig.setKeepAliveDuration(10000L);
            try {
                DefaultAlipayClient defaultAlipayClient = new DefaultAlipayClient(alipayConfig);
                alipayProperty.setAlipayClient(defaultAlipayClient);
                alipayProperty.setAlipayConfig(alipayConfig);
                ALIPAY_PROPERTIES_MAP.put(alipayProperty.getAppId(), alipayProperty);
            } catch (AlipayApiException e) {
                throw new ServiceException("初始化alipayClient错误");
            }
        }
        log.info("初始化成功");

    }

    @SneakyThrows
    public void initWechatPayConfig() {
        log.info("init WechatPayConfig");
        String jsonConfig = readKey(wechatConfigPath);
        List<WeChatPayConfig> weChatPayConfigList = JSON.parseArray(jsonConfig, WeChatPayConfig.class);
        for (WeChatPayConfig config : weChatPayConfigList) {
            config.setNotifyUrl(apiUrl+config.getNotifyUrl());
            RSAAutoCertificateConfig rsaAutoCertificateConfig = new RSAAutoCertificateConfig.Builder()
                    .merchantId(config.getMerchantId())
                    .privateKey(config.getPrivateKey())
                    .merchantSerialNumber(config.getMerchantSerialNumber())
                    .apiV3Key(config.getApiV3Key())
                    .build();
            config.setRsaAutoCertificateConfig(rsaAutoCertificateConfig);
            config.setNativePayService(new NativePayService.Builder().config(rsaAutoCertificateConfig).build());
            config.setNotificationParser(new NotificationParser(rsaAutoCertificateConfig));
            WE_CHAT_PAY_CONFIG_MAP.put(config.getMerchantId(), config);
        }
        log.info("微信初始化成功");
    }

    private String readKey(String path) throws IOException {
        InputStream inputStream = AlipayProperties.class.getResourceAsStream(path);
        if (inputStream == null) {
            log.error("读取支付宝私钥失败");
            return "";
        }
        StringBuilder contentBuilder = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                contentBuilder.append(line).append("\n");
            }
        }
        return contentBuilder.toString();
    }
}
