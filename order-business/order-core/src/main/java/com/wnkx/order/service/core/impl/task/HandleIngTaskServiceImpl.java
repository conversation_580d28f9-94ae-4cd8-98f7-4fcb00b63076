package com.wnkx.order.service.core.impl.task;

import cn.hutool.core.lang.Assert;
import com.ruoyi.common.core.enums.OrderTaskStatusEnum;
import com.ruoyi.common.core.enums.OrderTaskTypeEnum;
import com.ruoyi.system.api.domain.dto.order.OrderVideoTaskFlowDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTask;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetail;
import com.wnkx.order.service.OrderVideoTaskService;
import com.wnkx.order.service.core.OrderVideoTaskFlowService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :  处理中
 * @create :2024-12-11 16:53
 **/
@Service
@RequiredArgsConstructor
public class HandleIngTaskServiceImpl implements OrderVideoTaskFlowService {

    private final OrderVideoTaskService orderVideoTaskService;
    @Override
    public void flowVideoTask(OrderVideoTaskFlowDTO dto) {
        OrderVideoTaskDetail orderVideoTaskDetail = dto.getOrderVideoTaskDetail();
        OrderVideoTask orderVideoTask = orderVideoTaskService.getById(orderVideoTaskDetail.getTaskId());
        Assert.notNull(orderVideoTask, "工单不存在");
        Assert.isTrue(OrderTaskTypeEnum.AFTER_SALE.getCode().equals(orderVideoTask.getTaskType()), "工单类型异常，需要是[售后]");
        Assert.isTrue(OrderTaskStatusEnum.UN_HANDLE.getCode().equals(orderVideoTaskDetail.getStatus()) || OrderTaskStatusEnum.APPLICATION_FOR_CANCELLATION.getCode().equals(orderVideoTaskDetail.getStatus()), "任务单状态异常，需要是[待处理]或者[申请取消中]");
        orderVideoTaskDetail.setStatus(OrderTaskStatusEnum.HANDLE_ING.getCode());
        orderVideoTaskDetail.setConfirmTime(new Date());
    }

    @Override
    public OrderTaskStatusEnum getOrderTaskStatusEnumType() {
        return OrderTaskStatusEnum.HANDLE_ING;
    }
}
