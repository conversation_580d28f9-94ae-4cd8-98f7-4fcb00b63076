package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceOrderVideo;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/13 10:46
 */
@Mapper
public interface OrderInvoiceOrderVideoMapper extends SuperMapper<OrderInvoiceOrderVideo> {


    /**
     * 通过发票订单ID获取关联视频订单
     */
    default List<OrderInvoiceOrderVideo> selectListByOrderInvoiceOrderIds(List<Long> orderInvoiceOrderIds) {
        return selectList(new LambdaQueryWrapper<OrderInvoiceOrderVideo>()
                .in(OrderInvoiceOrderVideo::getInvoiceOrderId, orderInvoiceOrderIds)
        );
    }

    /**
     * 根据视频编码查询发票关联的视频订单
     */
    default List<OrderInvoiceOrderVideo> selectListByVideoCodes(List<String> videoCodes) {
        return selectList(new LambdaQueryWrapper<OrderInvoiceOrderVideo>()
                .in(OrderInvoiceOrderVideo::getVideoCode, videoCodes)
        );
    }

}
