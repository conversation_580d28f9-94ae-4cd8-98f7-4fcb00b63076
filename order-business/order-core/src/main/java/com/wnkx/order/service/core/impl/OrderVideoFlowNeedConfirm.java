package com.wnkx.order.service.core.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.common.core.enums.OrderVideoFlowNodeEnum;
import com.ruoyi.system.api.domain.dto.order.OrderVideoFlowDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.wnkx.order.service.IOrderVideoService;
import com.wnkx.order.service.OrderVideoFlowNodeDiagramService;
import com.wnkx.order.service.OrderVideoFlowRecordService;
import com.wnkx.order.service.core.OrderVideoFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 视频订单流转至需确认
 *
 * <AUTHOR>
 * @date 2024/8/9
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoFlowNeedConfirm implements OrderVideoFlowService {
    /**
     * 订单视频服务
     */
    private final IOrderVideoService orderVideoService;
    private final OrderVideoFlowRecordService orderVideoFlowRecordService;
    private final OrderVideoFlowNodeDiagramService orderVideoFlowNodeDiagramService;

    @Override
    public void orderVideoFlow(List<OrderVideo> orderVideos, String eventName) {
        //  ->需确认
        //  a:订单状态修改
        orderVideoService.checkVideoStatus(orderVideos, OrderStatusEnum.UN_FINISHED);

        List<OrderVideoFlowDTO> orderVideoFlowDTOS = new ArrayList<>();
        DateTime date = DateUtil.date();
        for (OrderVideo orderVideo : orderVideos) {
            orderVideoFlowDTOS.add(OrderVideoFlowDTO.builder().videoId(orderVideo.getId()).originStatus(orderVideo.getStatus()).targetStatus(OrderStatusEnum.NEED_CONFIRM.getCode()).build());

            orderVideo.setStatus(OrderStatusEnum.NEED_CONFIRM.getCode());
            orderVideo.setStatusTime(date);
            orderVideo.setNeedConfirmTime(date);
        }
        orderVideoFlowRecordService.createOrderVideoFlow(eventName, orderVideoFlowDTOS);
        orderVideoFlowNodeDiagramService.setNodeCompleteTime(orderVideos.stream().map(OrderVideo::getId).collect(Collectors.toList()), OrderVideoFlowNodeEnum.FINISH_SHOOTING);
    }

    @Override
    public OrderStatusEnum getOrderStatusType() {
        return OrderStatusEnum.NEED_CONFIRM;
    }
}
