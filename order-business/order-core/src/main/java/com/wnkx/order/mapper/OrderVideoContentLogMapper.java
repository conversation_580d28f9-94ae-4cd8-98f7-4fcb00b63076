package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.enums.OtherStatusEnum;
import com.ruoyi.system.api.domain.entity.order.OrderVideoContentLog;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

@Mapper
public interface OrderVideoContentLogMapper extends SuperMapper<OrderVideoContentLog> {

    default List<OrderVideoContentLog> selectLogByVideoId(Collection<Long> videoIds) {
        return this.selectList(Wrappers.lambdaQuery(OrderVideoContentLog.class).in(OrderVideoContentLog::getVideoId, videoIds)
                .eq(OrderVideoContentLog::getType, 2));
    }

    /**
     * 变更调字内容
     */
    default void updateVideoContentLog(Long videoId) {
        List<OrderVideoContentLog> list = this.selectList(Wrappers.lambdaQuery(OrderVideoContentLog.class).eq(OrderVideoContentLog::getVideoId, videoId));
        for (OrderVideoContentLog item : list) {
            item.setFirstEdit(OtherStatusEnum.FIRST_BUY.getCode());
            item.setFirstContent(item.getContent());
        }
        this.updateBatchById(list);
    }
}
