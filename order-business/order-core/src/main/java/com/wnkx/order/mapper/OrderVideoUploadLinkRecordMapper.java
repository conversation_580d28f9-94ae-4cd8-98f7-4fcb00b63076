package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.UploadLinkStatusEnum;
import com.ruoyi.system.api.domain.entity.order.OrderVideoUploadLinkRecord;
import com.ruoyi.system.api.domain.vo.order.HistoryUploadRecordVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 
 * @Date 2025-03-20 15:22:30 
 */
@Mapper
public interface OrderVideoUploadLinkRecordMapper extends SuperMapper<OrderVideoUploadLinkRecord> {

    /**
     * 剪辑管理-历史上传记录
     */
    List<HistoryUploadRecordVO> getHistoryUploadRecord(@Param("uploadLinkId") Long uploadLinkId);

    /**
     * 获取上传次数
     */
    default Long getCountByUploadLinkId(Long uploadLinkId) {
        return selectCount(new LambdaQueryWrapper<OrderVideoUploadLinkRecord>()
                .eq(OrderVideoUploadLinkRecord::getUploadLinkId, uploadLinkId)
        );
    }

    /**
     * 获取最新的上传记录
     */
    default OrderVideoUploadLinkRecord getLatestUploadLinkRecordByUploadLinkId(Long uploadLinkId) {
        return selectOne(new LambdaQueryWrapper<OrderVideoUploadLinkRecord>()
                .eq(OrderVideoUploadLinkRecord::getUploadLinkId, uploadLinkId)
                .orderByDesc(OrderVideoUploadLinkRecord::getCreateTime)
                .last("limit 1")
        );
    }

    /**
     * 通过上传链接ID获取未上传的记录
     */
    default OrderVideoUploadLinkRecord getHavenTUploadedByUploadLinkId(Long uploadLinkId) {
        return selectOne(new LambdaQueryWrapper<OrderVideoUploadLinkRecord>()
                .eq(OrderVideoUploadLinkRecord::getUploadLinkId, uploadLinkId)
                .eq(OrderVideoUploadLinkRecord::getStatus, UploadLinkStatusEnum.HAVEN_T_UPLOADED.getCode())
        );
    }
}
