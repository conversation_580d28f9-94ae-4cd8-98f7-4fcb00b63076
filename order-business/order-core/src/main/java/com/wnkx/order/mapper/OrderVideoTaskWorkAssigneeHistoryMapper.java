package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTaskWorkAssigneeHistory;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/10 14:03
 */
@Mapper
public interface OrderVideoTaskWorkAssigneeHistoryMapper extends SuperMapper<OrderVideoTaskWorkAssigneeHistory> {

    /**
     * 工单-历史处理人下拉框
     */
    default Set<Long> selectHistoryAssigneeList() {
        List<OrderVideoTaskWorkAssigneeHistory> orderVideoTaskWorkAssigneeHistories = selectList(new LambdaQueryWrapper<OrderVideoTaskWorkAssigneeHistory>()
                .select(OrderVideoTaskWorkAssigneeHistory::getOriginalAssigneeId)
        );
        return orderVideoTaskWorkAssigneeHistories.stream().map(OrderVideoTaskWorkAssigneeHistory::getOriginalAssigneeId).collect(Collectors.toSet());
    }
}
