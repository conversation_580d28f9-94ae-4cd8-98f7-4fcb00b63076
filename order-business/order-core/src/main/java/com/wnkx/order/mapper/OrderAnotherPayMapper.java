package com.wnkx.order.mapper;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.system.api.domain.entity.order.OrderAnotherPay;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2024/12/6 13:34
 */
@Mapper
public interface OrderAnotherPayMapper extends SuperMapper<OrderAnotherPay> {

    /**
     * 通过 订单号或合并单ID 获取有效代付链接
     */
    default OrderAnotherPay getValidLinkByOrderNumOrMergeId(String orderNum, Long mergeId) {
        return selectOne(new LambdaQueryWrapper<OrderAnotherPay>()
                .eq(ObjectUtil.isNotNull(mergeId), OrderAnotherPay::getMergeId, mergeId)
                .eq(CharSequenceUtil.isNotBlank(orderNum), OrderAnotherPay::getOrderNum, orderNum)
                .eq(OrderAnotherPay::getStatus, StatusTypeEnum.YES.getCode())
        );
    }

    /**
     * 取消代付
     */
    default void cancel(String uuid) {
        update(null, new LambdaUpdateWrapper<OrderAnotherPay>()
                .in(OrderAnotherPay::getUuid, uuid)
                .set(OrderAnotherPay::getStatus, StatusTypeEnum.NO.getCode())
        );
    }

    /**
     * 通过 订单号或者合并单ID 获取代付链接
     */
    default OrderAnotherPay getOrderAnotherPayByOrderNumOrMergeId(String orderNum, Long mergeId) {
        return selectOne(new LambdaQueryWrapper<OrderAnotherPay>()
                .eq(CharSequenceUtil.isNotBlank(orderNum), OrderAnotherPay::getOrderNum, orderNum)
                .eq(ObjectUtil.isNotNull(mergeId), OrderAnotherPay::getMergeId, mergeId)
                .orderByDesc(OrderAnotherPay::getCreateTime)
                .last("limit 1")
        );
    }

    /**
     * 通过code获取有效代付链接
     */
    default OrderAnotherPay getValidLinkByUUID(String code) {
        return selectOne(new LambdaQueryWrapper<OrderAnotherPay>()
                .eq(OrderAnotherPay::getUuid, code)
                .eq(OrderAnotherPay::getStatus, StatusTypeEnum.YES.getCode())
        );
    }

    /**
     * 通过code获取代付链接
     */
    default OrderAnotherPay getOrderAnotherPayByUUID(String code) {
        return selectOne(new LambdaQueryWrapper<OrderAnotherPay>()
                .eq(OrderAnotherPay::getUuid, code)
        );
    }

    /**
     * 支付后关闭代付链接
     */
    default void payCancel(String uuid) {
        update(null, new LambdaUpdateWrapper<OrderAnotherPay>()
                .eq(OrderAnotherPay::getUuid, uuid)
                .set(OrderAnotherPay::getStatus, StatusTypeEnum.NO.getCode())
                .set(OrderAnotherPay::getIsPay, StatusTypeEnum.YES.getCode())
        );
    }
}
