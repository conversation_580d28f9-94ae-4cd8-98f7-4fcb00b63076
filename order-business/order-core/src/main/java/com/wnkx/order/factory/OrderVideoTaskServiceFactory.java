package com.wnkx.order.factory;

import com.ruoyi.common.core.enums.OrderTaskStatusEnum;
import com.wnkx.order.service.core.OrderVideoTaskFlowService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.EnumMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
@DependsOn("unHandelTaskServiceImpl")
public class OrderVideoTaskServiceFactory {

    private final EnumMap<OrderTaskStatusEnum, OrderVideoTaskFlowService> OrderVideoTaskFlowServiceMap = new EnumMap<>(OrderTaskStatusEnum.class);

    private final ApplicationContext applicationContext;


    @PostConstruct
    public void init() {
        Map<String, OrderVideoTaskFlowService> services = applicationContext.getBeansOfType(OrderVideoTaskFlowService.class);
        for (OrderVideoTaskFlowService service : services.values()) {
            OrderVideoTaskFlowServiceMap.put(service.getOrderTaskStatusEnumType(), service);
        }
    }

    public OrderVideoTaskFlowService getOrderVideoFlowService(OrderTaskStatusEnum orderTaskStatus) {
        OrderVideoTaskFlowService orderVideoTaskFlowService = OrderVideoTaskFlowServiceMap.get(orderTaskStatus);
        if (orderVideoTaskFlowService == null) {
            throw new IllegalArgumentException("无效任务单状态： " + orderTaskStatus);
        }
        return orderVideoTaskFlowService;
    }
}
