package com.wnkx.order.service.core;

import com.ruoyi.common.core.constant.ConfigConstants;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.vo.MemberConfigVo;
import com.wnkx.order.remote.RemoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-02 17:33
 **/
@Component
@RequiredArgsConstructor
@Slf4j
public class SystemCore {
    private final RemoteService remoteService;
    private final RedisService redisService;

    //todo 暂时copy 后续与biz统一使用
    public MemberConfigVo getMemberConfig() {
        String time = Convert.toStr(redisService.getCacheObject(getConfigCacheKey(ConfigConstants.SYS_MEMBER_PRESENTED_TIME)));
        String date = Convert.toStr(redisService.getCacheObject(getConfigCacheKey(ConfigConstants.SYS_MEMBER_PRESENTED_DATE)));
        if (StringUtils.isBlank(time)) {
            time = remoteService.getConfigKey(ConfigConstants.SYS_MEMBER_PRESENTED_TIME);
        }
        if (StringUtils.isBlank(date)) {
            date = remoteService.getConfigKey(ConfigConstants.SYS_MEMBER_PRESENTED_DATE);
        }
        MemberConfigVo vo = new MemberConfigVo();
        if (StringUtils.isBlank(time)) {
            vo.setStatus(StatusTypeEnum.NO.getCode());
            return vo;
        }
        vo.setPresentedTime(Convert.toInt(time));
        if (vo.getPresentedTime().compareTo(0) <= 0) {
            vo.setStatus(StatusTypeEnum.NO.getCode());
            return vo;
        }
        if (StringUtils.isBlank(date)) {
            vo.setStatus(StatusTypeEnum.NO.getCode());
            return vo;
        }
        String[] split = date.split("-");
        String startDate = split[0];
        String endDate = split[1];
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            log.info(">>>>>>>>>>>>>>>>>>>>>>>>: 会员加赠日期配置错误！");
            vo.setStatus(StatusTypeEnum.NO.getCode());
            return vo;
        }
        Integer start = Convert.toInt(startDate);
        Integer end = Convert.toInt(endDate);
        if (start.compareTo(end) > 0) {
            log.info(">>>>>>>>>>>>>>>>>>>>>>>>: 会员加赠日期配置开始时间大于结束时间！");
            vo.setStatus(StatusTypeEnum.NO.getCode());
            return vo;

        }
        //获取本月一共有几天
        int maxDayOfMonth = DateUtils.getMaxDaysOfMonth(new Date());
        if (end.compareTo(maxDayOfMonth) > 0) {
            end = maxDayOfMonth;
        }

        //获取今天属于本月多少天
        int day = DateUtils.getDaysOfMonth(new Date());

        if (start.compareTo(day) > 0 || end.compareTo(day) < 0) {
            log.info(">>>>>>>>>>>>>>>>>>>>>>>>: 会员加赠日期配置开始时间大于今日或配置结束时间小于今日！");
            vo.setStatus(StatusTypeEnum.NO.getCode());
            return vo;
        }

        Date startOfToday = DateUtils.getStartOfToday();
        Date endOfToday = DateUtils.getEndOfToday();
        vo.setPresentedStartDate(DateUtils.matchDateDay(startOfToday, start));
        vo.setPresentedEndDate(DateUtils.matchDateDay(endOfToday, end));
        vo.setStatus(StatusTypeEnum.YES.getCode());
        return vo;
    }

    private String getConfigCacheKey(String configKey) {
        return Constants.SYS_CONFIG_KEY + configKey;
    }
}
