package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderVideoChangeLogInfo;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:15
 */
@Mapper
public interface OrderVideoChangeLogInfoMapper extends SuperMapper<OrderVideoChangeLogInfo> {

    /**
     * 通过变更记录id查询变更记录详情
     */
    default List<OrderVideoChangeLogInfo> selectChangeLogInfoListByChangeLogIds(List<Long> changeLogIds) {
        return selectList(new LambdaQueryWrapper<OrderVideoChangeLogInfo>()
                .in(OrderVideoChangeLogInfo::getChangeLogId, changeLogIds)
        );
    }
}
