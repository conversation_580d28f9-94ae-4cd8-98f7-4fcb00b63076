package com.wnkx.order.service.core.impl.logistic;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.enums.EventExecuteObjectEnum;
import com.ruoyi.common.core.enums.FollowStatusEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFlowDTO;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollowRecord;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.wnkx.order.service.OrderVideoLogisticFollowRecordService;
import com.wnkx.order.service.OrderVideoLogisticFollowService;
import com.wnkx.order.service.core.OrderVideoLogisticFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :已删除
 * @create :2025-04-23 11:41
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoLogisticFlowDelete implements OrderVideoLogisticFlowService {
    private final OrderVideoLogisticFollowService orderVideoLogisticFollowService;
    private final OrderVideoLogisticFollowRecordService orderVideoLogisticFollowRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void OrderVideoLogisticFlow(OrderVideoLogisticFlowDTO dto) {
        this.checkStatus(dto);
        LoginUserInfoVO loginUserInfoVo = dto.getLoginUserInfoVO();
        List<OrderVideoLogisticFollow> orderVideoLogisticFollowList = dto.getOrderVideoLogisticFollowList();
        List<OrderVideoLogisticFollow> updateList = new ArrayList<>();

        Date date = new Date();
        for (OrderVideoLogisticFollow item : orderVideoLogisticFollowList) {
            OrderVideoLogisticFollow updateEntity = new OrderVideoLogisticFollow();
            updateEntity.setId(item.getId());
            if (StatusTypeEnum.YES.getCode().equals(dto.getIsCancel())){
                updateEntity.setLogisticStatus(StatusTypeEnum.YES.getCode());
            }else {
                updateEntity.setLogisticStatus(this.getFollowStatusType().getLogisticStatus());
            }
            updateEntity.setFollowStatus(this.getFollowStatusType().getCode());
            updateEntity.setLatestRemark(StrUtil.isBlank(dto.getRemark()) ? "" : dto.getRemark());
            if (EventExecuteObjectEnum.BACK.getCode().equals(loginUserInfoVo.getUserType())) {
                updateEntity.setUpdateById(loginUserInfoVo.getUserId());
                updateEntity.setUpdateBy(loginUserInfoVo.getName());
            }
            updateEntity.setUpdateTime(date);
            updateList.add(updateEntity);
        }
        orderVideoLogisticFollowService.updateBatchById(updateList);

        List<OrderVideoLogisticFollowRecord> orderVideoLogisticFollowRecords = new ArrayList<>();
        for (OrderVideoLogisticFollow item : updateList) {
            OrderVideoLogisticFollowRecord orderVideoLogisticFollowRecord = new OrderVideoLogisticFollowRecord();
            orderVideoLogisticFollowRecord.setFollowId(item.getId());
            if (StatusTypeEnum.YES.getCode().equals(dto.getIsCancel())){
                orderVideoLogisticFollowRecord.setEventName("作废物流单");
                orderVideoLogisticFollowRecord.setEventContent(loginUserInfoVo.getName() + "作废物流，作废原因：" + dto.getRemark());
            }else {
                orderVideoLogisticFollowRecord.setEventName("回退订单");
                orderVideoLogisticFollowRecord.setEventContent("回退订单");
            }


            orderVideoLogisticFollowRecord.setEventExecuteObject(loginUserInfoVo.getUserType());
            orderVideoLogisticFollowRecord.setEventExecuteUserId(loginUserInfoVo.getUserId());
            orderVideoLogisticFollowRecord.setEventExecuteUserName(loginUserInfoVo.getName());
            orderVideoLogisticFollowRecord.setEventExecuteNickName(loginUserInfoVo.getNickName());
            orderVideoLogisticFollowRecord.setEventExecuteTime(new Date());
            orderVideoLogisticFollowRecord.setRemark(dto.getRemark());

            orderVideoLogisticFollowRecords.add(orderVideoLogisticFollowRecord);
        }

        orderVideoLogisticFollowRecordService.saveBatch(orderVideoLogisticFollowRecords);
    }

    @Override
    public void checkStatus(OrderVideoLogisticFlowDTO dto) {
        Assert.notNull(dto, "已删除：参数不能为空~");
        Assert.isTrue(CollUtil.isNotEmpty(dto.getOrderVideoLogisticFollowList()), "已删除：物流跟进数据不能为空~");
        for (OrderVideoLogisticFollow item : dto.getOrderVideoLogisticFollowList()) {
            if (StatusTypeEnum.YES.getCode().equals(dto.getIsCancel())){
                Assert.isTrue( StatusTypeEnum.YES.getCode().equals(item.getLogisticStatus()), "已删除：当前物流状态不在范围内~");
            }else {
                Assert.isTrue( StatusTypeEnum.NO.getCode().equals(item.getLogisticStatus()), "已删除：当前物流状态不在范围内~");
            }
        }

    }

    @Override
    public FollowStatusEnum getFollowStatusType() {
        return FollowStatusEnum.DELETE;
    }
}
