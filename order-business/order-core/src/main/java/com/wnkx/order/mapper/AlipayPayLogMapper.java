package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.AlipayPayLog;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2024/10/24 18:16
 */
@Mapper
public interface AlipayPayLogMapper extends SuperMapper<AlipayPayLog> {

    /**
     * 通过notify_id查询是否已收到过支付宝的回调信息
     */
    default Boolean checkExistByNotifyId(String notify_id) {
        return exists(new LambdaQueryWrapper<AlipayPayLog>()
                .eq(AlipayPayLog::getNotify_id, notify_id)
        );
    }

    /**
     * 通过内部订单号查询是否已存在订单的数据
     */
    default Boolean checkExistByOutTradeNo(String out_trade_no) {
        return exists(new LambdaQueryWrapper<AlipayPayLog>()
                .eq(AlipayPayLog::getOut_trade_no, out_trade_no)
        );
    }
}
