package com.wnkx.order.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBackMaterialInfoTaskDetail;
import com.ruoyi.system.api.domain.vo.order.OrderVideoTaskDetailSimpleVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 
 * @Date 2025-03-20 15:02:11 
 */
@Mapper
public interface OrderVideoFeedBackMaterialInfoTaskDetailMapper extends SuperMapper<OrderVideoFeedBackMaterialInfoTaskDetail> {

    /**
     * 通过模特反馈素材ID和视频订单ID和回退ID查询视频订单关联的模特反馈素材详情任务
     */
    default List<OrderVideoFeedBackMaterialInfoTaskDetail> selectListByMaterialInfoIdAndVideoIdAndRollbackId(Long materialInfoId, Long videoId, Long rollbackId) {
        return selectList(new LambdaQueryWrapper<OrderVideoFeedBackMaterialInfoTaskDetail>()
                .eq(OrderVideoFeedBackMaterialInfoTaskDetail::getVideoId, videoId)
                .eq(ObjectUtil.isNotNull(rollbackId), OrderVideoFeedBackMaterialInfoTaskDetail::getRollbackId, rollbackId)
                .eq(ObjectUtil.isNotNull(materialInfoId), OrderVideoFeedBackMaterialInfoTaskDetail::getMaterialInfoId, materialInfoId)
        );
    }

    /**
     * 通过反馈ID查询视频订单关联的模特反馈素材详情任务
     */
    List<OrderVideoTaskDetailSimpleVO> selectListByFeedBackIds(@Param("feedBackIds") List<Long> feedBackIds);

    /**
     * 通过视频订单ID查询视频订单关联的模特反馈素材详情任务
     */
    List<OrderVideoFeedBackMaterialInfoTaskDetail> selectListByVideoIds(@Param("videoIds") List<Long> videoIds);
}
