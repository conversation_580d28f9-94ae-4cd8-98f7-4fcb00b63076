package com.wnkx.order.mapper;

import com.ruoyi.system.api.domain.vo.order.datastatistics.ChineseCustomerServiceDataVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.CustomerServiceBaseBoardVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.EnglishCustomerServiceDataVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 13:56
 */
@Mapper
public interface CustomerServiceDataStatisticsMapper {

    /**
     * 客服数据-中文部客服数据
     */
    List<ChineseCustomerServiceDataVO> selectChineseCustomerServiceData(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 客服数据-基础看板
     */
    CustomerServiceBaseBoardVO getCustomerServiceBaseBoardByDate(@Param("date") String date);

    /**
     * 客服数据-英文部客服数据
     */
    List<EnglishCustomerServiceDataVO> selectEnglishCustomerServiceData(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 获取客服关联任务单数量
     */
    List<ChineseCustomerServiceDataVO> selectChineseCustomerServiceTaskData(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 客服数据-英文部客服匹配单数据
     */
    List<EnglishCustomerServiceDataVO> selectEnglishCustomerServiceMatchData(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime);
}
