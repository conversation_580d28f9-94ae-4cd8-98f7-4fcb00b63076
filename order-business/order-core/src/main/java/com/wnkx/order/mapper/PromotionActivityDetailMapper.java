package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivityDetail;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR> 
 * @Date 2025-05-21 14:34:08 
 */
@Mapper
public interface PromotionActivityDetailMapper extends SuperMapper<PromotionActivityDetail> {

    /**
     * 通过活动ID查询单条数据
     */
    default PromotionActivityDetail getByActivityId(Long activityId) {
        return selectOne(new LambdaQueryWrapper<PromotionActivityDetail>()
                .eq(PromotionActivityDetail::getActivityId, activityId)
        );
    }
}
