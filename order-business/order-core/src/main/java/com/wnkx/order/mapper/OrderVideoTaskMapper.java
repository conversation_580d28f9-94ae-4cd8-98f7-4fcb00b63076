package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.dto.order.WorkOrderTaskListDTO;
import com.ruoyi.system.api.domain.dto.order.task.AfterSaleOrderTaskListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTask;
import com.ruoyi.system.api.domain.vo.order.OrderTaskStatisticsVO;
import com.ruoyi.system.api.domain.vo.order.OrderTaskStatusVO;
import com.ruoyi.system.api.domain.vo.order.VideoTaskOrderVO;
import com.ruoyi.system.api.domain.vo.order.WorkOrderTaskListVO;
import com.ruoyi.system.api.domain.vo.order.task.AfterSaleListVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/9 15:23
 */
@Mapper
public interface OrderVideoTaskMapper extends SuperMapper<OrderVideoTask> {

    /**
     * 通过视频订单ID获取任务单
     */
    default OrderVideoTask getOrderVideoTaskByVideoId(Long videoId) {
        return selectOne(new LambdaQueryWrapper<OrderVideoTask>()
                .eq(OrderVideoTask::getVideoId, videoId)
                .orderByDesc(OrderVideoTask::getCreateTime)
                .last("limit 1")
        );
    }

    /**
     * 通过视频订单ID和任务单类型获取任务单
     */
    default OrderVideoTask getOrderVideoTaskByVideoIdAndTaskType(Long videoId, Integer taskType) {
        return selectOne(new LambdaQueryWrapper<OrderVideoTask>()
                .eq(OrderVideoTask::getVideoId, videoId)
                .eq(OrderVideoTask::getTaskType, taskType)
        );
    }

    /**
     * 任务单-工单列表
     */
    List<WorkOrderTaskListVO> selectWorkOrderListByCondition(@Param("dto") WorkOrderTaskListDTO dto);


    /**
     * 任务单 - 售后单列表
     * * @param dto
     *
     * @return
     */
    List<AfterSaleListVO> selectAfterSaleListByCondition(@Param("dto") AfterSaleOrderTaskListDTO dto);

    /**
     * 通过视频订单ID获取任务单列表
     */
    default List<OrderVideoTask> selectOrderVideoTaskListByVideoIds(List<Long> videoIds) {
        return selectList(new LambdaQueryWrapper<OrderVideoTask>()
                .in(OrderVideoTask::getVideoId, videoIds)
        );
    }

    /**
     * 获取统计数据
     *
     * @param taskType
     * @return
     */
    OrderTaskStatusVO getOrderTaskStatus(Integer taskType);

    /**
     * 通过视频订单ID获取任务单
     */
    List<VideoTaskOrderVO> selectVideoTaskOrderVOListByVideoIds(@Param("videoIds") List<Long> videoIds);

    List<Long> shootModelIdList(@Param("taskType") Integer taskType);

    List<Long> getAssigneeUserList(@Param("taskType") Integer taskType);

    List<Long> getSubmitUserList(@Param("taskType") Integer taskType);

    /**
     * 获取工单任务统计
     * @param userId
     * @param assigneeId
     * @return
     */
    OrderTaskStatisticsVO getOrderTaskStatistics(@Param("userId") Long userId, @Param("assigneeId") Long assigneeId);
}
