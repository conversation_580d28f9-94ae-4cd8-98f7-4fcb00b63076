package com.wnkx.order.mapper;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.OrderMergeStatusEnum;
import com.ruoyi.system.api.domain.dto.order.OrderListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderMerge;
import com.ruoyi.system.api.domain.vo.order.OrderMergeListVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> 
 * @Date 2025-03-04 10:00:27 
 */
@Mapper
public interface OrderMergeMapper extends SuperMapper<OrderMerge> {

    /**
     * 通过 合并单ID或者支付单号 获取合并订单下的订单号
     */
    List<String> getOrderNumsByMergeIdOrPayNum(@Param("mergeId") Long mergeId, @Param("payNum") String payNum);

    /**
     * 校验订单是否已合并
     */
    Boolean checkOrderMerge(@Param("orderNums") List<String> orderNums);

    /**
     * 通过 合并单ID或者支付单号 获取合并单
     */
    default OrderMerge getOrderMergeNormalByIdOrPayNum(Long mergeId, String payNum) {
        return selectOne(new LambdaQueryWrapper<OrderMerge>()
                .eq(ObjectUtil.isNotNull(mergeId), OrderMerge::getId, mergeId)
                .eq(CharSequenceUtil.isNotBlank(payNum), OrderMerge::getPayNum, payNum)
                .eq(OrderMerge::getStatus, OrderMergeStatusEnum.NORMAL.getCode())
        );
    }

    /**
     * 通过 合并单ID或者支付单号 完成合并单
     */
    default void completeOrderMergeByMergeIdOrPayNum(Long mergeId, String payNum) {
        update(null, new LambdaUpdateWrapper<OrderMerge>()
                .eq(ObjectUtil.isNotNull(mergeId), OrderMerge::getId, mergeId)
                .eq(CharSequenceUtil.isNotBlank(payNum), OrderMerge::getPayNum, payNum)
                .set(OrderMerge::getStatus, OrderMergeStatusEnum.COMPLETE.getCode())
                .set(OrderMerge::getCompleteTime, DateUtil.date())
        );
    }

    /**
     * 商家端-需支付订单列表
     */
    List<OrderMergeListVO> selectNeedPayOrderListByCondition(@Param("dto") OrderListDTO orderListDTO);

    /**
     *
     * 根据支付单号获取合并单
     * @param payNum
     * @return
     */
    default OrderMerge getOrderMergeByPayNum(String payNum) {
        return selectOne(new LambdaQueryWrapper<OrderMerge>()
                .eq(OrderMerge::getPayNum, payNum)
                .in(OrderMerge::getStatus, Arrays.asList(OrderMergeStatusEnum.NORMAL.getCode(), OrderMergeStatusEnum.COMPLETE.getCode()))
        );
    }

    /**
     * 通过订单号获取正常状态下的合并单
     */
    List<OrderMergeListVO> selectOrderMergeNormalListByOrderNums(@Param("orderNums") List<String> orderNums);

    /**
     * 通过 合并单ID或者支付单号 获取合并单
     */
    default OrderMerge getOrderMergeByIdOrPayNum(Long mergeId, String payNum) {
        return selectOne(new LambdaQueryWrapper<OrderMerge>()
                .eq(ObjectUtil.isNotNull(mergeId), OrderMerge::getId, mergeId)
                .eq(CharSequenceUtil.isNotBlank(payNum), OrderMerge::getPayNum, payNum)
        );
    }

    /**
     * 通过订单号获取同个合并单下的订单号
     */
    List<String> getMergeOrderNumsByOrderNum(@Param("orderNum") String orderNum);
}
