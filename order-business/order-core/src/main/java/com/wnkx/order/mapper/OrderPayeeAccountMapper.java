package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccount;
import com.wnkx.db.mapper.SuperMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_payee_account(订单收款账号表)】的数据库操作Mapper
 * @createDate 2024-10-29 11:12:22
 * @Entity com.ruoyi.system.api.domain.entity.order.OrderPayeeAccount
 */
public interface OrderPayeeAccountMapper extends SuperMapper<OrderPayeeAccount> {

    /**
     * 根据订单列表获取  订单收款账号列表
     *
     * @param orderNums
     * @return
     */
    default List<OrderPayeeAccount> queryListByOrderNums(List<String> orderNums) {
        return this.selectList(new LambdaQueryWrapper<OrderPayeeAccount>()
                .in(OrderPayeeAccount::getOrderNum, orderNums));

    }

    default void saveOrUpdateDataByOrderNumber(OrderPayeeAccount orderPayeeAccount) {
        if (selectCount(new LambdaQueryWrapper<OrderPayeeAccount>()
                .eq(OrderPayeeAccount::getOrderNum, orderPayeeAccount.getOrderNum())) > 0L) {
            update(orderPayeeAccount, new LambdaUpdateWrapper<OrderPayeeAccount>()
                    .eq(OrderPayeeAccount::getOrderNum, orderPayeeAccount.getOrderNum())
            );
            return;
        }
        insert(orderPayeeAccount);
    }

    /**
     * 根据订单号修改
     * @param orderPayeeAccount
     */
    default void updateByOrderNum(OrderPayeeAccount orderPayeeAccount){
        this.update(orderPayeeAccount, new LambdaQueryWrapper<OrderPayeeAccount>()
                .eq(OrderPayeeAccount::getOrderNum, orderPayeeAccount.getOrderNum()));
    }

    /**
     * 根据订单号修改
     *
     * @param orderPayeeAccount
     * @param orderNums
     */
    default void updateByOrderNums(OrderPayeeAccount orderPayeeAccount, List<String> orderNums) {
        this.update(orderPayeeAccount, new LambdaQueryWrapper<OrderPayeeAccount>()
                .in(OrderPayeeAccount::getOrderNum, orderNums));
    }
}




