package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.entity.order.VideoCart;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountSimpleVO;
import com.ruoyi.system.api.domain.vo.order.CreateOrderVO;
import com.ruoyi.system.api.domain.vo.order.VideoCartVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/1 14:44
 */
public interface VideoCartService extends IService<VideoCart> {
    /**
     * 添加购物车
     */

    void addCart(OrderVideoDTO orderVideoDTO);

    /**
     * 购物车列表
     */
    List<VideoCartVO> selectCartList(CartListDTO cartListDTO);

    /**
     * 删除购物车订单
     */
    void deleteCart(List<Long> cartIds);

    /**
     * 查看购物车订单
     */
    VideoCartVO getCartInfo(Long cartId);

    /**
     * 编辑购物车
     */
    void editCart(OrderVideoDTO orderVideoDTO);

    /**
     * 购物车结算
     */
    CreateOrderVO cartSettleAccounts(List<CartSettleAccountsDTO> cartSettleAccountsDTOS);

    /**
     * 当前商户购物车数量统计
     */
    Long getCartCount();

    /**
     * 复制购物车
     */
    VideoCartVO copyCart(Long cartId);

    /**
     * 购物车列表-下单运营下拉框
     */
    List<BusinessAccountSimpleVO> cartCreateUserSelect(String keyword);

    /**
     * 接收抓取亚马逊图片更新购物车订单
     */
    void updateBatchOrderCartProductPic(List<UpdateBatchOrderVideoProductPicDTO> dto);

    /**
     * 重新加入购物车
     */
    void rejoinCart(Long videoId);

    /**
     * 更改购物车意向模特
     */
    CreateOrderVO updateCartIntentionModel(UpdateCartIntentionModelDTO dto);

    /**
     * 清楚购物车意向模特
     * @param dto
     */
    void clearVideoCartIntentionModelId(ClearVideoCartIntentionModelDTO dto);
}
