package com.wnkx.order.service.core.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.system.api.domain.dto.order.OrderVideoFlowDTO;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.IOrderService;
import com.wnkx.order.service.IOrderVideoService;
import com.wnkx.order.service.OrderVideoFlowRecordService;
import com.wnkx.order.service.core.OrderVideoFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 视频订单流转至待审核
 *
 * <AUTHOR>
 * @date 2024/6/29 10:40
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoFlowUnCheck implements OrderVideoFlowService {

    /**
     * 订单视频服务
     */
    private final IOrderVideoService orderVideoService;
    private final OrderVideoFlowRecordService orderVideoFlowRecordService;
    private final RemoteService remoteService;
    @Override
    public void orderVideoFlow(List<OrderVideo> orderVideos, String eventName) {
        //  ->待审核
        //  a:订单状态修改
        orderVideoService.checkVideoStatus(orderVideos, OrderStatusEnum.UN_PAY);

        Order order = SpringUtils.getBean(IOrderService.class).getOrderByOrderNum(orderVideos.get(0).getOrderNum());
        Assert.notBlank(order.getMerchantCode(), "商家编码不能为空");

        List<OrderVideoFlowDTO> orderVideoFlowDTOS = new ArrayList<>();
        for (OrderVideo orderVideo : orderVideos) {
            orderVideoFlowDTOS.add(OrderVideoFlowDTO.builder().videoId(orderVideo.getId()).originStatus(orderVideo.getStatus()).targetStatus(OrderStatusEnum.UN_CHECK.getCode()).build());

            orderVideo.setStatus(OrderStatusEnum.UN_CHECK.getCode());
            orderVideo.setStatusTime(DateUtil.date());
        }
        orderVideoFlowRecordService.createOrderVideoFlow(eventName, orderVideoFlowDTOS);
        remoteService.updatePaySucceed(order.getMerchantId());
    }

    @Override
    public OrderStatusEnum getOrderStatusType() {
        return OrderStatusEnum.UN_CHECK;
    }
}
