package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.CartListDTO;
import com.ruoyi.system.api.domain.dto.order.ClearVideoCartIntentionModelDTO;
import com.ruoyi.system.api.domain.dto.order.UpdateCartIntentionModelDTO;
import com.ruoyi.system.api.domain.entity.order.VideoCart;
import com.ruoyi.system.api.domain.vo.order.VideoCartVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/1 14:43
 */
public interface VideoCartMapper extends SuperMapper<VideoCart> {
    /**
     * 同一商家下的用户添加了多少购物车
     *
     * @param businessId 商家id
     */
    default Long getBusinessCartCount(Long businessId) {
        return this.selectCount(new LambdaQueryWrapper<VideoCart>()
                .eq(VideoCart::getCreateOrderBusinessId, businessId)
        );
    }

    /**
     * 购物车列表
     */
    List<VideoCartVO> selectCartList(@Param("dto") CartListDTO cartListDTO,
                                     @Param("businessId") Long businessId);

    /**
     * 删除购物车订单
     */
    default void deleteCart(List<Long> cartIds) {
        this.deleteBatchIds(cartIds);
    }

    /**
     * 编辑购物车
     */
    void updateCart(VideoCart videoCart);

    /**
     * 获取下单运营id
     */
    default Set<Long> getCreateUserId(Long businessId) {
        List<VideoCart> orders = this.selectList(new LambdaQueryWrapper<VideoCart>()
                .eq(VideoCart::getCreateOrderBusinessId, businessId)
                .select(VideoCart::getCreateOrderUserId)
                .isNotNull(VideoCart::getCreateOrderUserId)
        );
        return orders.stream().map(VideoCart::getCreateOrderUserId).collect(Collectors.toSet());
    }

    /**
     * 根据商品链接获取购物车信息
     */
    List<VideoCart> selectListByProductPicIsNullAndProductLink(@Param("productLinks") Set<String> productLinks);

    /**
     * 更改购物车意向模特
     */
    void updateCartIntentionModel(UpdateCartIntentionModelDTO dto);

    /**
     * 清楚购物车意向模特
     * @param dto
     */
    default void clearVideoCartIntentionModelId(ClearVideoCartIntentionModelDTO dto){
        this.update(null,new LambdaUpdateWrapper<VideoCart>()
                .set(VideoCart::getIntentionModelId,null)
                .eq(VideoCart::getIntentionModelId, dto.getIntentionModelId())
                .eq(VideoCart::getCreateOrderBizUserId, dto.getBizUserId())
        );
    }

    /**
     * 根据购物车ID获取数量
     */
    default Long selectCountByIds(List<Long> cartIds) {
        return selectCount(new LambdaQueryWrapper<VideoCart>()
                .in(VideoCart::getId, cartIds)
                .in(VideoCart::getCreateOrderBusinessId, SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId())
        );
    }
}
