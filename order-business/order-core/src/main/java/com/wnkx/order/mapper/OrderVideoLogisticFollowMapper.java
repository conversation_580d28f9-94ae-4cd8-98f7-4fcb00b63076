package com.wnkx.order.mapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.FollowStatusEnum;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFollowDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFollowListDTO;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import com.ruoyi.system.api.domain.vo.order.logistic.LogisticFollowModelListVO;
import com.ruoyi.system.api.domain.vo.order.logistic.MemberCodeListVO;
import com.ruoyi.system.api.domain.vo.order.logistic.OrderVideoLogisticFollowStatisticsVO;
import com.ruoyi.system.api.domain.vo.order.logistic.OrderVideoLogisticFollowVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【order_video_logistic_follow(物流跟进表)】的数据库操作Mapper
* @createDate 2025-04-22 16:26:39
* @Entity com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow
*/
@Mapper
public interface OrderVideoLogisticFollowMapper extends SuperMapper<OrderVideoLogisticFollow> {

    /**
     * 获取物流跟进列表
     * @param dto
     * @return
     */
    List<OrderVideoLogisticFollowVO> selectOrderVideoLogisticFollowList(@Param("dto") OrderVideoLogisticFollowListDTO dto);

    /**
     * 根据id获取物流跟进详情
     * @param id
     * @return
     */
    OrderVideoLogisticFollowVO getDetailById(@Param("id")Long id);

    /**
     * 获取物流跟进统计信息
     * @return
     */
    OrderVideoLogisticFollowStatisticsVO getStatisticsVO();

    /**
     * 获取物流跟进出单人id列表（未删除数据）
     * @return
     */
    Set<Long> getOrderIssueId(@Param("followStatus") Integer followStatus);


    /**
     * 获取商家编码及数量
     * @param dto
     * @return
     */
    List<MemberCodeListVO> memberCodeList(@Param("dto") OrderVideoLogisticFollowListDTO dto);


    /**
     * 查询模特及跟进数量
     * @param dto
     * @return
     */
    List<LogisticFollowModelListVO> modelListSelect(@Param("dto") OrderVideoLogisticFollowListDTO dto);

    /**
     * 获取物流跟进联系人id列表（未删除数据）
     * @return
     */
    Set<Long> getOrderContactId(@Param("followStatus") Integer followStatus);

    /**
     * 根据物流单Id获取 跟进记录
     * @param orderVideoLogisticId
     * @return
     */
    default OrderVideoLogisticFollow getOneByOrderVideoLogisticId(Long orderVideoLogisticId){
        return this.selectOne(new LambdaQueryWrapper<OrderVideoLogisticFollow>()
                .eq(OrderVideoLogisticFollow::getOrderVideoLogisticId, orderVideoLogisticId));
    }

    /**
     * 查询物流跟进数据列表（未删除数据）
     * @param dto
     * @return
     */
    default List<OrderVideoLogisticFollow> queryListBase(OrderVideoLogisticFollowDTO dto) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoLogisticFollow>()
                .eq(ObjectUtil.isNotEmpty(dto.getVideoId()), OrderVideoLogisticFollow::getVideoId, dto.getVideoId())
                .ne(OrderVideoLogisticFollow::getFollowStatus, FollowStatusEnum.DELETE.getCode())
                .in(CollUtil.isNotEmpty(dto.getFollowStatusList()), OrderVideoLogisticFollow::getFollowStatus, dto.getFollowStatusList())
                .in(CollUtil.isNotEmpty(dto.getVideoIds()), OrderVideoLogisticFollow::getVideoId, dto.getVideoIds())
                .eq(ObjectUtil.isNotEmpty(dto.getFollowStatus()), OrderVideoLogisticFollow::getFollowStatus, dto.getFollowStatus())
                .eq(ObjectUtil.isNotEmpty(dto.getNumber()), OrderVideoLogisticFollow::getNumber, dto.getNumber())
                .eq(ObjectUtil.isNotEmpty(dto.getLogisticStatus()), OrderVideoLogisticFollow::getLogisticStatus, dto.getLogisticStatus())
                .eq(ObjectUtil.isNotEmpty(dto.getHandleStatus()), OrderVideoLogisticFollow::getHandleStatus, dto.getHandleStatus())
        );
    }



}




