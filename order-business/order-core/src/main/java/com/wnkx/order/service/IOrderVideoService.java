package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.ChangeLogTypeEnum;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.system.api.domain.dto.OrderVideoStatisticsDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.finance.FinancialVerificationExportDTO;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelOrderRankingInfo;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoDataStatisticsDay;
import com.ruoyi.system.api.domain.entity.order.OrderVideoMatch;
import com.ruoyi.system.api.domain.entity.order.OrderVideoModelChange;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedCompleteCountInfo;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsDetailVO;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountSimpleVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.domain.vo.order.datastatistics.OrderVideoBaseBoardVO;
import com.ruoyi.system.api.domain.vo.order.finace.FinancialVerificationAllExportVO;
import com.ruoyi.system.api.domain.vo.order.finace.OrderPayVideoDetailVO;
import com.ruoyi.system.api.domain.vo.order.workbench.PauseMatchVO;

import java.math.BigDecimal;
import java.util.*;

/**
 * 订单_视频Service接口
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
public interface IOrderVideoService extends IService<OrderVideo> {
    /**
     * 创建视频订单
     *
     * @param orderVideoDTOS 视频订单
     * @param orderNum       订单编号
     */
    void createOrderVideo(List<OrderVideoDTO> orderVideoDTOS, String orderNum);

    /**
     * 修改订单_视频
     *
     * @param orderVideoDTO 订单_视频
     * @return 结果
     */
    public void updateOrderVideo(OrderVideoDTO orderVideoDTO);

    /**
     * 查询订单_视频列表
     *
     * @param orderListDTO 订单_视频
     * @return 订单_视频集合
     */
    public List<OrderVideoVO> selectOrderVideoListByCondition(OrderListDTO orderListDTO);

    /**
     * 获取账号订单列表
     * @return
     */
    List<OrderVideoVO> selectAccountOrderList();

    /**
     * 当
     * 1、选定的模特无法接单
     * 2、更改了拍摄模特
     * 3、更改了选定模特
     * 时 清除视频订单的[标记订单]的相关字段 以及 清除 视频订单的携带订单相关
     */
    void clearFlagOrder(List<ClearFlagOrder> clearFlagOrders);

    /**
     * 更换模特
     */
    CreateOrderVO changeModel(Long videoId, Long newModelId, String reason);

    /**
     * 商家端-订单各个状态统计
     */
    OrderStatusVO merchantOrderStatusCount();

    /**
     * 工作太统计
     * @return
     */
    OrderStatusVO workbenchOrderStatusCount();

    /**
     * 运营端-订单各个状态统计
     */
    OrderStatusVO backOrderStatusCount();

    /**
     * 工作台统计
     * @param workbenchRoleType
     * @return
     */
    WorkbenchVO getWorkbenchStatisticsVO(Integer workbenchRoleType);

    /**
     * 获取中文部待审核订单列表（5条）
     * @return
     */
    List<OrderVideoVO> chineseUnConfirmList();

    /**
     * 获取英文部-待匹配订单列表（5条）
     * @return
     */
    List<OrderVideoVO> englishUnMatchList();

    /**
     * 英文部
     * @return
     */
    List<OrderVideoVO> englishCloseList();

    /**
     * 获取英文部-暂停匹配订单列表（5条）
     * @return
     */
    List<PauseMatchVO> englishPauseMatchList();

    /**
     * 修改订单费用
     */
    void updateOrderVideoPrice(UpdateOrderVideoPriceDTO dto);

    /**
     * 审核订单_获取视频详细信息
     *
     * @param id 视频订单的主键
     */
    OrderOperationVideoVO getOperationOrderVideoInfo(Long id, OrderListDTO dto);

    /**
     * 审核订单
     */
    CreateOrderVO editOperationOrderVideoInfo(OrderOperationVideoDTO orderOperationVideoDTO);

    /**
     * 通过订单编号批量更新视频订单状态
     *
     * @param orderNums 订单编号
     * @param status    更新的状态
     */
    void updateStatusByOrderNum(List<String> orderNums, Integer status);

    /**
     * 修改视频订单_获取视频详细信息
     *
     * @param id 视频订单的主键
     */
    OrderVideoVO getOrderVideoInfo(Long id);

    /**
     * 获取可退款金额
     */
    BigDecimal getRefundAmount(Long videoId);

    /**
     * 上传产品图
     */
    void uploadProductImage(Long videoId, String productPic);

    /**
     * 获取订单统计
     *
     * @param orderVideoStatisticsDTO
     * @return
     */
    OrderVideoStatisticsVO orderVideoStatistics(OrderVideoStatisticsDTO orderVideoStatisticsDTO);

    /**
     * 获取订单详情统计
     * @param orderVideoStatisticsDTO
     * @return
     */
    List<OrderVideoStatisticsDetailVO> orderVideoStatisticsDetail(OrderVideoStatisticsDTO orderVideoStatisticsDTO);

    /**
     * 通过订单编号获取视频订单
     */
    List<OrderVideo> selectByOrderNum(String orderNum);

    /**
     * * 获取 订单收支明细 - 视频明细
     * @param dto
     * @return
     */
    List<OrderPayVideoDetailVO> orderPayDetailVideoList(OrderPayDetailVideoListDTO dto);

    /**
     * 获取财务对账-视频明细列表数据
     * @param dto
     * @return
     */
    List<FinancialVerificationAllExportVO> financialVerificationAllExportList(FinancialVerificationExportDTO dto);

    /**
     * 通过订单编号列表获取视频订单
     * @param orderNums
     * @return
     */
    List<OrderVideo> selectByOrderNums(Collection<String> orderNums);

    /**
     * 通过订单编号获取 非交易关闭状态数据
     */
    List<OrderVideo> selectValidByOrderNum(String orderNum);

    /**
     * 通过订单编号获取 非交易关闭状态数据
     */
    List<OrderVideo> selectValidByOrderNums(List<String> orderNums);

    /**
     * 通过订单编号获取 非交易关闭状态数据
     */
    List<OrderVideo> selectValidByOrderNumsAsc(List<String> orderNums);

    /**
     * 检查视频订单状态
     *
     * @param orderVideos 视频订单
     * @param statusEnum  应该是什么状态
     */
    void checkVideoStatus(List<OrderVideo> orderVideos, OrderStatusEnum... statusEnum);

    /**
     * 检查视频订单状态
     *
     * @param orderVideo 视频订单
     * @param statusEnum 应该是什么状态
     */
    void checkVideoStatus(OrderVideo orderVideo, OrderStatusEnum... statusEnum);


    /**
     * 获取商户视频订单数量
     */
    int getOrderVideoCount(String merchantCode);

    /**
     * 获取商家照顾单数量
     * @param merchantCode
     * @return
     */
    int getOrderVideoCareCount(String merchantCode);

    /**
     * 根据条件查询视频订单列表
     */
    List<OrderVideo> selectOrderVideoListByConditionV2(OrderVideoListDTO orderVideoListDTO);

    /**
     * 订单列表-获取拍摄模特下拉框
     */
    List<ModelInfoVO> orderShootModelSelect(String keyword);

    /**
     * 订单列表-获取对接人下拉框
     */
    List<UserVO> orderContactSelect(String keyword);

    /**
     * 订单列表-获取出单人下拉框
     */
    List<UserVO> orderIssueSelect(String keyword);

    /**
     * 运营根据匹配情况修改订单
     */
    void operationVideoCase(OrderOperationVideoCaseDTO dto);

    /**
     * 校验admin数据权限
     * @return true 校验通过 false 校验不通过
     */
    Boolean checkAdminDataScope(List<Long> videoIds);

    /**
     * 校验运营数据权限
     * @return true 校验不通过 false 校验通过
     */
    Boolean checkManagerDataScope(CheckDataScopeDTO dto, Long userid);

    /**
     * 校验商家数据权限
     *
     * @return true 校验不通过 false 校验通过
     */
    Boolean checkUserDataScope(CheckDataScopeDTO dto, BusinessAccountVO businessAccount);

    /**
     * 批量更新视频订单（非完成、关闭状态的）的对接人
     *
     * @param orderNums 订单编号
     * @param contactId 对接人客服id
     */
    void updateOrderVideoContact(List<String> orderNums, Long contactId);

    /**
     * 获取模特待拍数、已完成订单数、超时订单
     */
    List<ModelOrderVO> getModelOrderCount(Collection<Long> modelIds);

    /**
     * 订单列表-获取下单用户下拉框（运营端）
     */
    List<BusinessAccountSimpleVO> backOrderUserSelect(String keyword);

    /**
     * 接收抓取亚马逊图片更新视频订单
     */
    void updateBatchOrderVideoProductPic(List<UpdateBatchOrderVideoProductPicDTO> dto);

    /**
     * 视频订单发货信息
     */
    ShippingVO shippingInfo(Long videoId);

    /**
     * 释放视频订单到模特公池
     */
    @Deprecated(since = "于模特端v1.0.1弃用 订单进入待匹配即为释放 前24小时（配置时间）依然优先给优质模特", forRemoval = true)
    void releaseOrderVideo(Long videoId);

    /**
     * 释放视频订单到模特公池
     */
    @Deprecated(since = "于模特端v1.0.1弃用 订单进入待匹配即为释放 前24小时（配置时间）依然优先给优质模特", forRemoval = true)
    void releaseOrderVideo(Collection<Long> videoIds);

    /**
     * 设置视频订单自动完成时间
     */
    void setAutoCompleteTime(Long videoId);

    /**
     * 删除视频订单自动完成时间
     */
    void removeAutoCompleteTime(Long videoId);

    /**
     * 运营修改视频订单具体逻辑
     */
    void backUserEditOrderVideo(OrderOperationVideoDTO orderOperationVideoDTO, OrderVideo orderVideo, ChangeLogTypeEnum changeLog);

    /**
     * 获取视频订单当前信息（用于比较变更后的数据）
     */
    OrderVideoCurrentInfoVO getOrderVideoCurrentInfo(Long id);

    List<String> selectOrderUserNicknameList(String name);

    /**
     * 获取待完成、需确认状态下的订单的出单人信息
     */
    List<UnFinishedAndNeedConfirmOrderIssueSelectVO> unFinishedAndNeedConfirmOrderIssueSelect();


    /**
     * 订单列表-获取下单用户下拉框（商家端）
     */
    Set<String> companyOrderUserSelect(String keyword);

    /**
     * 运营手动获取产品图
     */
    void crawlProductPic(Long videoId);

    /**
     * 模特变更对接客服更新视频订单的出单人ID
     */
    void updateIssueId(UpdateIssueIdDTO dto);

    /**
     * 更新视频订单 若字段为null 更新为null
     * PS:请注意字段值
     */
    void updateOrderVideoFieldNullToNull(OrderVideo orderVideo);

    /**
     * 更新视频订单 若字段为null 更新为null
     * PS:请注意字段值
     */
    void updateOrderVideoBatchFieldNullToNull(List<OrderVideo> orderVideos);

    /**
     * 设置订单号下所有使用余额为0
     */
    void setBalanceZeroByOrderNum(List<String> orderNums);

    /**
     * 通过视频编码获取视频订单列表
     */
    List<OrderVideo> selectListByVideoCodes(List<String> videoCodes);

    /**
     * 通过视频编码获取视频订单
     * @param videoCode
     * @return
     */
    OrderVideo selectOneByVideoCode(String videoCode);
    /**
     * （一次性）补全order_video_model_change的选定信息
     */
    List<OrderVideoModelChange> test();

    /**
     * （一次性）补全order_video_match的选定信息
     */
    List<OrderVideoMatch> test2();

    /**
     * (一次性)更新order_video_model_change数据
     */
    List<OrderVideoModelChange> test3();

    /**
     * 通过商家ID查询商家是否有进行中或者交易完成的视频订单
     */
    Boolean hasValidVideoOrderByBusinessId(Long businessId, String startTime);

    /**
     * 模特数据统计-模特接单排行榜
     */
    List<ModelOrderRankingInfo> getModelOrderRanking(String date);

    /**
     * （一次性）初始化模特数据-模特接单排行榜
     */
    Map<String, List<ModelOrderRankingInfo>> getModelOrderRankings(List<String> collect);

    /**
     * 通过日期 获取中文部客服新增/完成订单数量
     */
    List<CustomerServiceAddedCompleteCountInfo> getChineseCustomerServiceOrderCountByDate(String date);

    /**
     * 通过日期 获取英文部客服新增/完成订单数量
     */
    List<CustomerServiceAddedCompleteCountInfo> getEnglishCustomerServiceOrderCountByDate(String date);

    /**
     * 订单列表-获取订单运营下拉框（运营端）
     */
    List<OrderVideo> backCreateOrderUserNameSelect(String keyword);

    /**
     * 视频订单数据统计-按天统计视频订单数据
     */
    OrderVideoDataStatisticsDay getOrderVideoDataStatisticsDay(String date);


    /**
     * 视频订单数据-基础看板
     */
    OrderVideoBaseBoardVO getOrderVideoBaseBoard();

    /**
     * 视频订单列表-获取意向模特下拉框
     */
    List<ModelInfoVO> orderIntentionModelSelect(String keyword);

    /**
     * 模特数据-模特排单情况
     */
    List<PieChartVO> getModelOrderScheduledData(Collection<Long> modelIds);

    Date getOrderFirstMatchTime(Long orderId);
}
