package com.wnkx.order.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/7/9 15:13
 */
@Component
@ConfigurationProperties(prefix = "order.video")
@Data
@RefreshScope
public class OrderVideoProperties {
    /**
     * 运营反馈素材给商家后且无未完结任务单，订单自动完成时间（单位：小时，24小时制）（测试调整，原1080）
     */
    private Integer orderAutoFinishedTime;
    /**
     * 视频订单释放到模特端订单列表 前几个小时优先推送给优质模特（单位：小时，24小时制）
     */
    private Integer releaseOverTime;
    /**
     * 预选模特未对接超时时间（单位：小时，24小时制）
     */
    private Integer preselectModelOverTime;
    /**
     * 订单进入待确认状态 商家在几天后可以申请取消退款（单位：小时，24小时制）
     */
    private Integer merchantApplyRefundOverTime;
    /**
     * 订单进入待完成状态，商家在几天后可以催一催（单位：小时，24小时制）
     */
    private Integer reminderTime;
    /**
     * 订单进入待完成状态，模特逾期未反馈素材时间（单位：小时，24小时制）
     */
    private Integer orderFeedbackOverdueTime;

    /**
     * 自动关闭订单时间
     */
    private Integer closeOrderHours;
    /**
     *  展示开启订单时间（单位：天）
     */
    private Integer showReopenOrder;

    /**
     *  预选模特坑位数
     */
    private Integer preselectModelNumberOfPits;

    /**
     * 视频订单审核通过后，几个小时内运营先处理后释放到模特池（单位：小时，24小时制）
     */
    private Integer preselectModelReserveTime;

    /**
     * 模特端新版48小时逻辑旧数据结束时间
     */
    private String modelTerminalAllListOldDataEndTime;
}
