package com.wnkx.order.factory;

import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.wnkx.order.service.core.OrderVideoFlowService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.EnumMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class OrderVideoFlowServiceFactory {

    private final EnumMap<OrderStatusEnum, OrderVideoFlowService> orderVideoFlowServiceMap = new EnumMap<>(OrderStatusEnum.class);

    private final ApplicationContext applicationContext;


    @PostConstruct
    public void init() {
        Map<String, OrderVideoFlowService> services = applicationContext.getBeansOfType(OrderVideoFlowService.class);
        for (OrderVideoFlowService service : services.values()) {
            orderVideoFlowServiceMap.put(service.getOrderStatusType(), service);
        }
    }

    public OrderVideoFlowService getOrderVideoFlowService(OrderStatusEnum orderStatus) {
        OrderVideoFlowService orderVideoFlowService = orderVideoFlowServiceMap.get(orderStatus);
        if (orderVideoFlowService == null) {
            throw new IllegalArgumentException("无效的订单状态： " + orderStatus);
        }
        return orderVideoFlowService;
    }
}
