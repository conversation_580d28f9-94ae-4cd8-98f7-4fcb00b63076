package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceReopen;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2025/1/14 19:09
 */
@Mapper
public interface OrderInvoiceReopenMapper extends SuperMapper<OrderInvoiceReopen> {

    /**
     * 通过新发票ID获取旧发票ID
     */
    default OrderInvoiceReopen getOldInvoiceReopenByNewInvoiceId(Long newInvoiceId) {
        return selectOne(new LambdaQueryWrapper<OrderInvoiceReopen>()
                .eq(OrderInvoiceReopen::getNewInvoiceId, newInvoiceId)
        );
    }
}
