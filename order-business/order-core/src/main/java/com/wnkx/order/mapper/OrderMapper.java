package com.wnkx.order.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.enums.AuditStatusEnum;
import com.ruoyi.common.core.enums.OrderTypeEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.CompanyNotInvoicedListDTO;
import com.ruoyi.system.api.domain.dto.order.OrderListDTO;
import com.ruoyi.system.api.domain.dto.order.OrderPayDetailDTO;
import com.ruoyi.system.api.domain.dto.order.finance.FinancialVerificationExportDTO;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.domain.vo.order.finace.FinancialVerificationExportVO;
import com.ruoyi.system.api.domain.vo.order.finace.OrderAuditStatusStatisticsVO;
import com.ruoyi.system.api.domain.vo.order.finace.OrderPayDetailVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 订单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Mapper
public interface OrderMapper extends SuperMapper<Order> {

    /**
     * 获取订单审核数据统计
     * @return
     */
    public WorkbenchVO getAuditOrderStatistics();
    /**
     * 查询订单
     *
     * @param id 订单主键
     * @return 订单
     */
    public Order selectOrderById(Long id);

    /**
     * 查询订单列表
     *
     * @param orderListDTO 订单
     * @return 订单集合
     */
    List<OrderListVO> selectOrderListByCondition(@Param("dto") OrderListDTO orderListDTO, @Param("orderType") Integer orderType);


    /**
     * 查询视频订单审核数据
     *
     * @param orderListDTO
     * @return
     */
    List<OrderVideoAuditVO> selectOrderAuditList(@Param("dto") OrderListDTO orderListDTO);

    /**
     * 应收审批列表
     *
     * @param orderListDTO
     * @return
     */
    List<OrderListVO> selectOrderListV1(@Param("dto") OrderListDTO orderListDTO);

    /**
     * 获取工作台-财务部-视频订单待审核
     * @return
     */
    List<OrderVideoAuditVO> workbenchFinanceVideoList();

    /**
     * 获取订单明细列表
     *
     * @param dto
     * @return
     */
    List<OrderPayDetailVO> orderPayDetailList(@Param("dto") OrderPayDetailDTO dto);

    /**
     * 获取 财务对账导出数据
     *
     * @param dto
     * @return
     */
    List<FinancialVerificationExportVO> getFinancialVerificationExports(@Param("dto") FinancialVerificationExportDTO dto);

    /**
     * 新增订单
     *
     * @param order 订单
     * @return 结果
     */
    public int insertOrder(Order order);

    /**
     * 修改订单
     *
     * @param order 订单
     * @return 结果
     */
    public int updateOrder(Order order);

    /**
     * 回填订单商家数据
     *
     * @param merchantCode      会员编码
     * @param merchantId        商家ID
     * @param bizUserId         登录账号ID
     * @param businessAccountId 商家账号ID
     */
    void setMerchant(@Param("merchantCode") String merchantCode, @Param("merchantId") Long merchantId, @Param("bizUserId") Long bizUserId, @Param("businessAccountId") Long businessAccountId);

    /**
     * 视频应收审批统计
     *
     * @return
     */
    OrderAuditStatusStatisticsVO orderAuditStatusStatistics();

    /**
     * 会员应收审核统计
     *
     * @return
     */
    OrderAuditStatusStatisticsVO orderMemberAuditStatusStatistics();

    /**
     * 订单余额锁定数据
     *
     * @param businessId
     * @return
     */
    List<BalanceLockRecordVO> getBalanceLockRecord(Long businessId);

    /**
     *  根据商家Id获取未支付订单（大订单、合并订单）
     * @param businessId
     * @return
     */
    List<Order> getUnPayListByBusinessId(Long businessId);

    /**
     * 检测订单是否支付
     *
     * @param orderNums
     * @return
     */
    default boolean checkOrderStatus(List<String> orderNums) {
        return selectCount(new LambdaUpdateWrapper<Order>()
                .in(Order::getOrderNum, orderNums)
                .isNull(Order::getPayTime)
        ) == orderNums.size();
    }

    /**
     * 禁用会员发票
     *
     * @param businessId
     */
    default void banMemberInvoice(Long businessId) {
        this.update(null, new LambdaUpdateWrapper<Order>()
                .set(Order::getBanInvoice, StatusTypeEnum.YES.getCode())
                .eq(Order::getOrderType, OrderTypeEnum.VIP_ORDER.getCode())
                .eq(Order::getMerchantId, businessId));
    }

    /**
     * 根据支付单号获取订单列表
     * @param payNum
     * @return
     */
    default List<Order> getListByPayNum(String payNum){
        return this.selectList(new LambdaQueryWrapper<Order>()
                .eq(Order::getPayNum, payNum));
    }

    /**
     * 根据支付单号获取订单列表
     * @param payNums
     * @return
     */
    default List<Order> getListByPayNums(List<String> payNums){
        return this.selectList(new LambdaQueryWrapper<Order>()
                .in(Order::getPayNum, payNums));
    }
    default Order getOrderByOrderNum(String orderNum) {
        return selectOne(new LambdaUpdateWrapper<Order>()
                .eq(Order::getOrderNum, orderNum)
                .last("limit 1")
        );
    }

    /**
     * 获取下单用户id
     *
     * @return
     */
    default Set<Long> getOrderUserId(Long businessId) {
        List<Order> orders = this.selectList(new LambdaQueryWrapper<Order>()
                .eq(Order::getMerchantId, businessId)
                .select(Order::getOrderUserId)
                .isNotNull(Order::getOrderUserId)
        );
        return orders.stream().map(Order::getOrderUserId).collect(Collectors.toSet());
    }

    /**
     * 获取订单总数
     */
    default Long getOrderCount(Long businessId) {
        return this.selectCount(new LambdaQueryWrapper<Order>()
                .eq(Order::getMerchantId, businessId)
                .eq(Order::getOrderType, OrderTypeEnum.VIDEO_ORDER.getCode())
        );
    }

    /**
     * 根据登录账号id获取有效会员订单数量
     *
     * @param bizUserId
     * @return
     */
    default Long getValidOrderMemberCount(Long bizUserId) {
        return this.selectCount(new LambdaQueryWrapper<Order>()
                .eq(Order::getBizUserId, bizUserId)
                .eq(Order::getOrderType, OrderTypeEnum.VIP_ORDER.getCode())
                .notIn(Order::getAuditStatus, AuditStatusEnum.CLOSE.getCode(), AuditStatusEnum.APPROVE.getCode())
        );
    }

    /**
     * 根据登录账号获取未取消订单
     *
     * @param businessId
     * @return
     */
    default Long getUnCancelOrderCount(Long businessId) {
        return this.selectCount(new LambdaQueryWrapper<Order>()
                .eq(Order::getMerchantId, businessId)
                .eq(Order::getOrderType, OrderTypeEnum.VIP_ORDER.getCode())
                .in(Order::getAuditStatus, List.of(AuditStatusEnum.UN_CHECK.getCode(), AuditStatusEnum.EXCEPTION.getCode()))
        );

    }

    /**
     * 开启订单需要清除原数据
     *
     * @param orderNum
     * @param payAmount
     * @param payAmountDollar
     * @param reopenCount
     */
    default void clearCloseInfo(String orderNum, BigDecimal payAmount, BigDecimal payAmountDollar, Integer reopenCount) {
        this.update(null, new LambdaUpdateWrapper<Order>()
                .set(Order::getPayeeId, null)
                .set(Order::getPayType, null)
                .set(Order::getUseBalance, BigDecimal.ZERO)
                .set(Order::getPayAccount, null)
                .set(Order::getPayUserId, null)
                .set(Order::getPayAmount, payAmount)
                .set(Order::getPayAmountDollar, payAmountDollar)
                .set(Order::getReopenCount, reopenCount + 1)
                .set(Order::getOrderTimeSign, new Date())
                .set(Order::getSubmitCredentialTime, null)
                .set(Order::getCloseOrderTime, null)
                .set(Order::getAuditStatus, AuditStatusEnum.UN_CHECK.getCode())
                .eq(Order::getOrderNum, orderNum));
    }

    /**
     * 通过商家id获取大订单列表
     */
    default List<Order> selectListByBusinessId(List<Long> businessIds) {
        return this.selectList(new LambdaQueryWrapper<Order>()
                .in(Order::getMerchantId, businessIds)
        );
    }

    /**
     * * 通过账号id获取大订单列表
     *
     * @param userIds
     * @return
     */
    default List<Order> selectListByBizUserIds(List<Long> userIds) {
        return this.selectList(new LambdaQueryWrapper<Order>()
                .in(Order::getBizUserId, userIds)
        );
    }

    /**
     * 根据id修改订单数据 若支付类型是null 则设置支付类型为null
     *
     * @param order
     */
    default void updateByIdFullNullPayType(Order order) {
        this.update(order, new LambdaUpdateWrapper<Order>()
                .set(ObjectUtil.isNull(order.getPayType()), Order::getPayType, null)
                .eq(Order::getId, order.getId())
        );
    }

    /**
     * 余额支付更新订单信息
     *
     * @param order
     */
    default void balancePay(Order order) {
        update(order, new LambdaUpdateWrapper<Order>()
                .eq(Order::getId, order.getId())
                .set(Order::getPayeeId, null)
        );
    }

    /**
     * 商家端-发票管理-未开票列表
     */
    List<CompanyNotInvoicedListVO> selectCompanyNotInvoicedListByCondition(@Param("dto") CompanyNotInvoicedListDTO dto, @Param("oldDataEndTime") String oldDataEndTime);

    /**
     * 通过订单号查询订单
     */
    default List<Order> selectListByOrderNums(Collection<String> orderNums) {
        return selectList(new LambdaQueryWrapper<Order>()
                .in(Order::getOrderNum, orderNums)
                .orderByAsc(Order::getCreateTime)
                .orderByAsc(Order::getId)
        );
    }

    default List<Order> getLogByOrderNumber(List<String> orderNums) {
        return selectList(new LambdaQueryWrapper<Order>()
                .select(Order::getWechatPayAppId, Order::getAlipayPayAppId)
                .in(Order::getOrderNum, orderNums)
                .and(wrapper -> wrapper.isNotNull(Order::getWechatPayAppId).or().isNotNull(Order::getAlipayPayAppId))

        );
    }

    default void updateWechatPayAppId(List<String> orderNums, String bankAccount) {
        update(null, new LambdaUpdateWrapper<Order>()
                .set(Order::getWechatPayAppId, bankAccount)
                .in(Order::getOrderNum, orderNums));
    }

    default void updateAliPayAppId(List<String> orderNums, String bankAccount) {
        update(null, new LambdaUpdateWrapper<Order>()
                .set(Order::getAlipayPayAppId, bankAccount)
                .in(Order::getOrderNum, orderNums));
    }

    /**
     * 清除支付类型
     */
    default void clearPayType(List<String> orderNums) {
        update(null, new LambdaUpdateWrapper<Order>()
                .in(Order::getOrderNum, orderNums)
                .set(Order::getPayType, null)
        );
    }

    /**
     * 获取使用余额的订单
     */
    default List<Order> selectUseBalanceListByOrderNums(List<String> orderNums) {
        return selectList(new LambdaQueryWrapper<Order>()
                .in(Order::getOrderNum, orderNums)
                .ne(Order::getUseBalance, BigDecimal.ZERO)
                .isNotNull(Order::getUseBalance)
        );
    }

    /**
     * 更新大订单 若字段为null 更新为null
     * PS:请注意字段值
     */
    void updateOrderFieldNullToNull(@Param("orderTable") Order order);

    /**
     * 合并时设置订单支付号
     */
    default void setPayNum(List<String> orderNums, String payNum) {
        update(null, new LambdaUpdateWrapper<Order>()
                .in(Order::getOrderNum, orderNums)
                .set(Order::getPayNum, payNum)
                .set(Order::getIsMergeOrder, StatusTypeEnum.YES.getCode())
        );
    }

    /**
     * 提交支付时设置订单支付号
     */
    default void setPayNum(String orderNum) {
        update(null, new LambdaUpdateWrapper<Order>()
                .eq(Order::getOrderNum, orderNum)
                .set(Order::getPayNum, IdUtils.createOrderNum(OrderConstant.NORMAL_ORDER_PAY_NUM_PREFIX_ZFDD, 4))
        );
    }


    /**
     * 取消合并时清空订单支付号
     */
    default void emptyPayNum(List<String> orderNums) {
        update(null, new LambdaUpdateWrapper<Order>()
                .in(Order::getOrderNum, orderNums)
                .set(Order::getIsMergeOrder, StatusTypeEnum.NO.getCode())
                .set(Order::getPayNum, null)
        );
    }

    /**
     * 获取用户账号创建订单数量
     */
    default Long getUserAccountCreateOrderCount() {
        return selectCount(new LambdaQueryWrapper<Order>()
                .eq(Order::getBizUserId, SecurityUtils.getBizUserId())
        );
    }

    /**
     * 设置支付金额美元
     * @param orderNum
     * @param payAmountDollar
     */
    default void setPayAmountDollar(String orderNum, BigDecimal payAmountDollar) {
        update(null, new LambdaUpdateWrapper<Order>()
                .eq(Order::getOrderNum, orderNum)
                .set(Order::getPayAmountDollar, payAmountDollar)
        );
    }

    /**
     * 通过BizUserId查询订单
     */
    default List<Order> selectListByBizUserId(Long bizUserId) {
        return selectList(new LambdaQueryWrapper<Order>()
                .eq(Order::getBizUserId, bizUserId)
        );
    }

    /**
     * 更新支付类型详情为null
     */
    default void updatePayTypeDetailToNull(List<String> orderNums) {
        update(null, new LambdaUpdateWrapper<Order>()
                .in(Order::getOrderNum, orderNums)
                .set(Order::getPayTypeDetail, null)
        );
    }
}
