package com.wnkx.order.mapper;

import com.ruoyi.system.api.domain.entity.order.promotion.OrderPromotionDetail;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.member.MemberDiscountVO;
import com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_promotion_detail】的数据库操作Mapper
 * @createDate 2025-03-19 15:50:19
 * @Entity com.ruoyi.system.api.domain.entity.order.promotion.OrderPromotionDetail
 */
public interface OrderPromotionDetailMapper extends SuperMapper<OrderPromotionDetail> {

    /**
     * 通过订单号获取订单参与活动以及优惠金额明细
     */
    List<OrderDiscountDetailVO> selectOrderDiscountDetailsByOrderNums(@Param("orderNums") List<String> orderNums);

    /**
     * 通过视频订单ID获取订单参与活动以及优惠金额明细
     */
    List<OrderDiscountDetailVO> selectOrderDiscountDetailsByVideoIds(@Param("videoIds") List<Long> videoIds);

    /**
     * 会员优惠分析
     * @param from
     * @param to
     * @return
     */
    List<MemberDiscountVO> getBusinessMemberDiscountAnalysis(@Param("from") Date from, @Param("to") Date to);
}




