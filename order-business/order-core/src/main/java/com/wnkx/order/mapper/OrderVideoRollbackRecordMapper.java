package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRollbackRecord;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2024/12/25 17:46
 */
@Mapper
public interface OrderVideoRollbackRecordMapper extends SuperMapper<OrderVideoRollbackRecord> {

    /**
     * 回退订单
     */
    default Long getCountByVideoId(Long videoId) {
        return selectCount(new LambdaQueryWrapper<OrderVideoRollbackRecord>()
                .eq(OrderVideoRollbackRecord::getVideoId, videoId)
        );
    }
}
