package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.MarkUploadAccountDTO;
import com.ruoyi.system.api.domain.dto.order.OrderUploadLinkDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoUploadLinkDTO;
import com.ruoyi.system.api.domain.dto.order.UploadLinkListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoUploadLink;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.order.*;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/8/26 17:57
 */
@Validated
public interface OrderVideoUploadLinkService extends IService<OrderVideoUploadLink> {

    /**
     * 新增上传素材
     */
    void saveOrderVideoUploadLink(@Validated OrderVideoUploadLinkDTO dto);

    /**
     * 上传素材
     */
    OrderVideoUploadLinkVO getUploadMaterial(Long videoId);

    /**
     * 查询视频订单上传素材
     */
    OrderVideoUploadLinkSimpleVO getOrderVideoUploadLinkSimpleVO(Long videoId);

    /**
     * 剪辑管理-上传成功
     */
    void uploadLink(OrderUploadLinkDTO dto);

    /**
     * 通过视频订单id查询上传素材列表
     */
    List<OrderVideoUploadLinkVO> selectListByVideoIds(List<Long> videoIds);

    /**
     * 根据dto查询列表
     *
     * @param listDTO
     * @return
     */
    List<OrderVideoUploadLink> selectListByDto(UploadLinkListDTO listDTO);

    /**
     * 剪辑管理-待上传、已完成 列表
     */
    List<UploadLinkListVO> selectUploadLinkListByCondition(UploadLinkListDTO dto);

    /**
     * 剪辑管理-待上传、已完成 拍摄模特下拉框
     */
    List<ModelInfoVO> uploadLinkListSelectShootModel(List<Integer> status);

    /**
     * 剪辑管理-历史上传记录
     */
    List<HistoryUploadRecordVO> getHistoryUploadRecord(Long uploadLinkId);

    /**
     * 剪辑管理-标记上传账号
     */
    void markUploadAccount(MarkUploadAccountDTO dto);

    /**
     * 剪辑管理-标记上传账号-下拉框
     */
    List<MarkUploadAccountSelectVO> getMarkUploadAccountSelect(Long uploadLinkId);

    /**
     * 剪辑管理-标记为待确认上传
     */
    void markUploadConfirm(Long uploadLinkId);

    /**
     * 剪辑管理-上传失败
     */
    void uploadLinkFail(OrderUploadLinkDTO dto);

    /**
     * 剪辑管理-取消上传
     */
    void cancelUploadLink(OrderUploadLinkDTO dto);

    /**
     * 插入一条已完成的无需上传的记录
     */
    void saveFinished(Long videoId);

    /**
     * 插入一条已完成的无需上传的记录
     */
    void saveFinisheds(List<Long> videoIds);

    /**
     * 编辑素材
     */
    void editUploadMaterial(OrderVideoUploadLinkDTO dto);

    /**
     * 剪辑管理-上传账号-下拉框
     */
    Set<String> uploadAccountSelect();
}
