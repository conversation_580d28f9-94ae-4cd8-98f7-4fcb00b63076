package com.wnkx.order.mapper;

import com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivityAmendmentRecord;
import com.ruoyi.system.api.domain.vo.order.promotion.PromotionActivityAmendmentRecordVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 
 * @Date 2025-05-21 14:34:03 
 */
@Mapper
public interface PromotionActivityAmendmentRecordMapper extends SuperMapper<PromotionActivityAmendmentRecord> {


    /**
     * 通过活动类型获取修改记录
     */
    List<PromotionActivityAmendmentRecordVO> selectPromotionActivityAmendmentRecordList(@Param("type") Integer type);
}
