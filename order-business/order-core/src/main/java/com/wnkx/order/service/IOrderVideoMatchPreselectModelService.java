package com.wnkx.order.service;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.PreselectModelAddTypeEnum;
import com.ruoyi.common.core.enums.PreselectStatusEnum;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoMatchPreselectModel;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.order.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 订单_视频_预选模特Service接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface IOrderVideoMatchPreselectModelService extends IService<OrderVideoMatchPreselectModel>
{
    /**
     * 订单列表-获取预选添加人下拉框
     */
    Set<String> orderPreselectUserSelect(String keyword);

    /**
     * 订单列表-获取预选模特下拉框
     */
    List<ModelInfoVO> orderPreselectModelSelect(String keyword, List<Long> backUserIds);

    /**
     * 更新预选模特列表为已淘汰
     */
    void outPreselectModel(List<OutPreselectModelDTO> dtoList);

    /**
     * 通过视频订单id和模特id查询未对接或已对接的模特
     *
     * @param matchId 匹配单ID
     * @param modelId 模特id
     * @return 未对接的模特
     */
    OrderVideoMatchPreselectModel getUnJointedOrJointedByVideoIdAndModelId(Long matchId, Long modelId);

    /**
     * 更改预选模特状态
     */
    void editPreselectModel(EditPreselectModelDTO editPreselectModelDTO);

    /**
     * 商家更换模特 对旧的选定的模特进行淘汰
     *
     * @param matchId 匹配单ID
     */
    void changeModel(Long matchId, String reason);

    /**
     * 查询选定模特列表通过匹配单ID
     *
     * @return
     */
    List<OrderVideoMatchPreselectModel> selectedListByMatchIds(List<Long> matchIds);

    /**
     * 添加预选模特
     */
    AddDistributionErrorVO addPreselectModel(Integer isGund, Long matchId, List<Long> modelIds, PreselectModelAddTypeEnum addType, PreselectStatusEnum status, DateTime matchStartTime);

    /**
     * 校验模特档期问题
     *
     * @param orderVideoMatchPreselectModels 预选模特
     * @return 是否存在无档期
     */
    boolean checkPreselectModelSchedule(List<OrderVideoMatchPreselectModel> orderVideoMatchPreselectModels);

    /**
     * 批量添加预选模特
     */
    void saveBatchOrderVideoMatchPreselectModel(List<SaveBatchOrderVideoMatchPreselectModelDTO> dtoList);

    /**
     * 查询选定模特列表通过匹配单ID
     */
    OrderVideoMatchPreselectModel getSelectedModelByMatchId(Long matchId);

    void modelCannotAcceptUpdateInfo(List<OrderVideoMatchPreselectModel> unCheckPreselectModel, Integer checkSelected);

    /**
     * 通过匹配单ID查询预选模特
     */
    List<OrderVideoMatchPreselectModelVO> selectListByMatchIds(List<Long> matchIds);

    /**
     * 通过匹配单ID查询非淘汰的预选模特（订单列表）
     */
    List<OrderVideoMatchPreselectModel> selectActiveListByMatchIdsOfOrderVideoList(List<Long> matchIds);

    /**
     * 查询当前匹配单活跃的预选模特
     */
    List<OrderVideoMatchPreselectModelVO> selectActivePreselectModelListByMatchId(Long matchId);

    /**
     * 查询当前匹配单活跃的预选模特（不填充数据）
     */
    List<OrderVideoMatchPreselectModel> selectActivePreselectModelListByMatchIdSimple(Long matchId);

    /**
     * 预选管理-模特匹配-我的预选-沟通中
     */
    List<OrderVideoMatchPreselectModelVO> selectMyPreselectDockingList(MyPreselectDockingListDTO dto);

    /**
     * 设置预选模特为已选定
     */
    void selectedModel(MarkOrderDTO markOrderDTO);

    /**
     * 提交预选模特 将其他非选定的预选模特置为已淘汰 同时设置模特快照数据
     */
    void submitPreselectModelUpdateToOut(Long matchId);

    /**
     * 通过匹配单ID查询预选模特记录
     */
    List<OrderVideoMatchPreselectModelVO> selectListByMatchId(Long matchId);

    /**
     * 查询匹配单已淘汰模特数量
     */
    Long countMatchOutModel(Long matchId);

    /**
     * 修改订单信息时将预选模特状态从非淘汰修改为未对接
     */
    void editOrderVideoPreselectModelFromJointedToUnJointed(Long matchId);

    /**
     * 设置模特选择记录的状态为卖方取消
     */
    void updateModelSelectStatusToCancelByVideoId(List<Long> machIds);

    /**
     * 暂停匹配单
     */
    void pauseMatch(Long matchId,String pauseReason);

    /**
     * 添加预选模特列表查询匹配单下非淘汰的模特
     */
    Set<Long> selectNormalPreselectModelByMatchId(Long matchId);

    /**
     * 校验模特是否匹符合单信息
     */
    void checkModelConformOrderVideoInfo(Map<Long, ModelInfoVO> modelMap, OrderVideo orderVideo);

    /**
     * 视频订单回退淘汰预选模特
     */
    void rollbackOrderOustPreselectModel(Long matchId,String cause);

    /**
     * 通过匹配单ID和模特ID查询未被淘汰的预选模特
     */
    OrderVideoMatchPreselectModel getActiveByMatchIdAndModelId(Long matchId, Long modelId);

    /**
     * 运营修改订单信息时 淘汰原有意向模特
     */
    void eliminateModelByMatchId(Long matchId);

    /**
     * 获取英文工作台统计
     * @param dto
     * @return
     */
    EnglishStatisticsVO selectEnglishWorkbenchStatistics(EnglishStatisticsDTO dto);

    /**
     * 通过视频订单ID和rollbackId检查是否有选定的模特
     */
    boolean checkExistSelectedModelByVideoIdAndRollbackId(Long videoId, Long rollbackId);

    /**
     * 我的预选-沟通中-预选模特下拉框
     */
    List<ModelInfoVO> myPreselectDockingModelSelect(String keyword);

    /**
     * 预选管理-查询预选模特拍摄注意事项
     */
    PreselectModelShootAttentionVO getPreselectModelShootAttention(Long preselectModelId);

    /**
     * 预选管理-填写模特拍摄注意事项
     */
    void preselectModelShootAttention(PreselectModelShootAttentionDTO dto);

    /**
     * 预选管理-添加分发
     */
    AddDistributionErrorVO addDistribution(AddDistributionDTO dto, OrderVideo orderVideo);

    /**
     * 添加分发模特列表查询匹配单下的模特
     */
    Set<Long> selectPreselectModelIdsByMatchId(Long matchId);

    /**
     * 预选管理-我的预选-分发中
     */
    List<MyPreselectDistributionListVO> selectMyPreselectDistributionListByCondition(MyPreselectDistributionListDTO dto);

    /**
     * 预选管理-我的预选-历史分发记录
     */
    List<DistributionHistoryListVO> selectDistributionHistoryListByModelId(Long modelId);

    /**
     * 预选管理-批量标记沟通
     */
    void batchMarkCommunication(List<Long> preselectModelIds);

    /**
     * 预选管理-设置模特分发结果
     */
    void setModelDistributionResult(SetModelDistributionResultDTO dto);

    /**
     * 我的预选-分发中-分发模特下拉框
     */
    List<ModelInfoVO> myPreselectDistributionModelSelect(String keyword);

    /**
     * 我的预选-分发中-英文部客服下拉框
     */
    List<UserVO> myPreselectDistributionEnglishSelect(String keyword);

    /**
     * 设置已提醒模特拍摄注意事项
     */
    void setModelShootAttentionRemind(Long preselectModelId);

    List<UserVO> distributionIssueList();
}
