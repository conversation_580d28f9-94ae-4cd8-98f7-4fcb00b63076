package com.wnkx.order.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/10
 */
@Component
@ConfigurationProperties(prefix = "order.invoice")
@Data
@RefreshScope
public class OrderInvoiceProperties {

    /**
     * 发票旧数据结束时间
     */
    private String oldDataEndTime;
}
