package com.wnkx.order.mapper;

import com.ruoyi.system.api.domain.dto.order.OrderModelListDTO;
import com.ruoyi.system.api.domain.vo.order.OrderModelListVO;
import com.ruoyi.system.api.domain.vo.order.RecommendModelListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ModelDiscoverMapper {

    /**
     * 模特端-首页-订单列表-优质模特
     * 筛选条件
     * a:视频订单满足模特基本要求（国家、模特类型、平台）
     * b:视频订单的预选模特列表不能有[已选定]的模特
     * c:视频订单的预选模特列表不能有当前模特且非模特主动取消申请
     * d:未淘汰预选模特数达到或超过设定的坑位数时 不展示
     * e:已暂停的匹配单不展示
     */
    List<OrderModelListVO> selectOrderQualityModelListByCondition(@Param("dto") OrderModelListDTO dto);

    /**
     * 模特端-首页-订单列表-非优质模特
     */
    List<OrderModelListVO> selectOrderOrdinaryModelListByCondition(@Param("dto") OrderModelListDTO dto);

    /**
     * 模特端-首页-订单列表数量统计-优质模特
     */
    Long getOrderQualityModelCountByCondition(@Param("dto") OrderModelListDTO dto);

    /**
     * 模特端-首页-订单列表数量统计-非优质模特
     */
    Long getOrderOrdinaryModelCountByCondition(@Param("dto") OrderModelListDTO dto);

    /**
     * RECOMMEND页面
     */
    List<RecommendModelListVO> selectRecommendList(@Param("modelId") Long modelId);

    /**
     * RECOMMEND页面-数量统计
     */
    Long getRecommendCount(@Param("modelId") Long modelId);
}
