package com.wnkx.order.mapper;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.OrderTaskStatusEnum;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.OrderTaskDTO;
import com.ruoyi.system.api.domain.dto.order.WorkOrderTaskListDTO;
import com.ruoyi.system.api.domain.dto.order.task.AfterSaleOrderTaskListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetail;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedCompleteCountInfo;
import com.ruoyi.system.api.domain.vo.order.OrderVideoTaskDetailVO;
import com.ruoyi.system.api.domain.vo.order.WorkOrderTaskDetailListVO;
import com.ruoyi.system.api.domain.vo.order.task.AfterSaleTaskDetailInfoVO;
import com.ruoyi.system.api.domain.vo.order.task.AfterSaleTaskDetailListVO;
import com.ruoyi.system.api.domain.vo.order.workbench.WorkbenchTaskDetailVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单_工单任务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Mapper
public interface OrderVideoTaskDetailMapper extends SuperMapper<OrderVideoTaskDetail>
{

    /**
     * 工单列表
     */
    List<WorkOrderTaskDetailListVO> selectWorkOrderTaskDetailListByCondition(@Param("dto") WorkOrderTaskListDTO dto);

    /**
     * 售后单列表
     * @param dto
     * @return
     */
    List<AfterSaleTaskDetailListVO> selectAfterSaleOrderTaskDetailListByCondition(@Param("dto") AfterSaleOrderTaskListDTO dto);


    /**
     * 工作台-剪辑部-任务列表
     * @param userId
     * @return
     */
    List<WorkbenchTaskDetailVO> selectWorkbenchTaskDetailList(@Param("userId") Long userId);

    /**
     * 工作台-剪辑部-被拒绝任务列表
     * @param userId
     * @return
     */
    List<WorkbenchTaskDetailVO> selectWorkbenchRefuseTaskDetailList(@Param("userId") Long userId);

    /**
     * 检查工单状态
     *
     * @param taskNum     工单编号
     * @param statusEnums 应该是什么状态
     * @return true 表示不符合条件 false 表示符合条件
     */
    default Boolean checkTaskStatus(String taskNum, OrderTaskStatusEnum... statusEnums) {
        return this.exists(new LambdaQueryWrapper<OrderVideoTaskDetail>()
                .eq(OrderVideoTaskDetail::getTaskNum, taskNum)
                .notIn(OrderVideoTaskDetail::getStatus, Arrays.stream(statusEnums).map(OrderTaskStatusEnum::getCode).collect(Collectors.toList()))
        );
    }

    /**
     * 检查当前工单的提交人是不是当前用户
     *
     * @param taskNum 工单编号
     * @param userId  用户id
     * @return true 表示不符合条件 false 表示符合条件
     */
    default Boolean checkTaskSubmit(String taskNum, Long userId) {
        return this.exists(new LambdaQueryWrapper<OrderVideoTaskDetail>()
                .eq(OrderVideoTaskDetail::getTaskNum, taskNum)
                .ne(OrderVideoTaskDetail::getSubmitBy, userId));
    }

    /**
     * 根据工单编号批量修改工单状态
     *
     * @param taskNum 工单编号
     * @param status  工单状态
     * @param time    任务处理时间
     */
    default void editBatchTaskStatusByTaskNum(List<String> taskNum, Integer status, DateTime time) {
        this.update(null, new LambdaUpdateWrapper<OrderVideoTaskDetail>()
                .in(OrderVideoTaskDetail::getTaskNum, taskNum)
                .set(OrderVideoTaskDetail::getStatus, status)
                .set(OrderVideoTaskDetail::getEndTime, time));
    }

    /**
     * 通过任务编号获取工单
     */
    default OrderVideoTaskDetail getTaskDetailByTaskNum(String taskNum) {
        return this.selectOne(new LambdaQueryWrapper<OrderVideoTaskDetail>()
                .eq(OrderVideoTaskDetail::getTaskNum, taskNum)
                .last("limit 1"));
    }

    /**
     * 获取售后详情
     * @param taskNum
     * @return
     */
    AfterSaleTaskDetailInfoVO getAfterSaleTaskDetailInfoVO(String taskNum);

    /**
     * 检查当前工单的处理人是不是当前用户
     *
     * @param taskNum 工单编号
     * @param userId  用户id
     * @return true 表示不符合条件 false 表示符合条件
     */
    default Boolean checkTaskAssignee(String taskNum, Long userId) {
        return this.exists(new LambdaQueryWrapper<OrderVideoTaskDetail>()
                .eq(OrderVideoTaskDetail::getTaskNum, taskNum)
                .ne(OrderVideoTaskDetail::getAssigneeId, userId));
    }

    /**
     * 获取所有模特的工单（去重、多个视频订单算一个）
     */
    List<OrderVideoTaskDetail> getModelAllDistinctTask();

    /**
     * 获取所有模特的售后单（重拍视频、补拍视频）（去重、多个视频订单算一个）
     */
    List<OrderVideo> getModelAfterSaleDistinctTask();

    /**
     * 校验任务单是否已存在
     */
    default Boolean checkTaskExist(OrderTaskDTO dto) {
        return exists(new LambdaQueryWrapper<OrderVideoTaskDetail>()
                .eq(OrderVideoTaskDetail::getTaskId, dto.getTaskId())
                .in(OrderVideoTaskDetail::getStatus, OrderTaskStatusEnum.UN_HANDLE.getCode(), OrderTaskStatusEnum.HANDLE_ING.getCode(), OrderTaskStatusEnum.APPLICATION_FOR_CANCELLATION.getCode())
                .eq(ObjectUtil.isNotNull(dto.getAfterSaleClass()), OrderVideoTaskDetail::getAfterSaleClass, dto.getAfterSaleClass())
                .eq(ObjectUtil.isNotNull(dto.getAfterSaleVideoType()), OrderVideoTaskDetail::getAfterSaleVideoType, dto.getAfterSaleVideoType())
                .eq(ObjectUtil.isNotNull(dto.getAfterSalePicType()), OrderVideoTaskDetail::getAfterSalePicType, dto.getAfterSalePicType())
                .eq(ObjectUtil.isNotNull(dto.getWorkOrderType()), OrderVideoTaskDetail::getWorkOrderType, dto.getWorkOrderType())
        );
    }

    /**
     * 申请补偿退款_获取视频订单相关联的任务单信息
     */
    List<OrderVideoTaskDetailVO> getRefundPendingTask(@Param("taskId") Long taskId, @Param("taskDetailIds") List<Long> taskDetailId, @Param("assigneeId") Long assigneeId);

    /**
     * 帮模特反馈素材 _获取视频订单相关联的任务单信息
     */
    List<OrderVideoTaskDetailVO> getBackHelpModelUploadMaterialPendingTask(@Param("taskId") Long taskId, @Param("taskDetailIds") List<Long> taskDetailIds, @Param("assigneeId") Long assigneeId);

    /**
     * 反馈素材给商家 _获取视频订单相关联的任务单信息
     */
    List<OrderVideoTaskDetailVO> getFeedbackMaterialPendingTask(@Param("taskIds") List<Long> taskIds,
                                                                @Param("taskDetailIds") List<Long> taskDetailId,
                                                                @Param("afterSaleClass") List<Integer> afterSaleClass,
                                                                @Param("existingTaskDetailIds") List<Long> existingTaskDetailIds,
                                                                @Param("isToBeEdited") Boolean isToBeEdited);

    /**
     * 校验工单处理人操作权限
     */
    default Boolean checkWorkOrderAssigneePermission(Long taskDetailId) {
        return exists(new LambdaQueryWrapper<OrderVideoTaskDetail>()
                .eq(OrderVideoTaskDetail::getId, taskDetailId)
                .eq(OrderVideoTaskDetail::getAssigneeId, SecurityUtils.getUserId())
        );
    }

    /**
     * 校验工单处理人操作权限
     */
    default Boolean checkWorkOrderAssigneePermission(List<Long> taskDetailIds) {
        Long count = selectCount(new LambdaQueryWrapper<OrderVideoTaskDetail>()
                .in(OrderVideoTaskDetail::getId, taskDetailIds)
                .eq(OrderVideoTaskDetail::getAssigneeId, SecurityUtils.getUserId())
        );
        return count == taskDetailIds.size();
    }

    /**
     * 校验工单提交人操作权限
     */
    default Boolean checkWorkOrderSubmitPermission(Long taskDetailId) {
        return exists(new LambdaQueryWrapper<OrderVideoTaskDetail>()
                .eq(OrderVideoTaskDetail::getId, taskDetailId)
                .eq(OrderVideoTaskDetail::getSubmitById, SecurityUtils.getUserId())
        );
    }

    /**
     * 校验工单 提交人 或 处理人 操作权限
     */
    default Boolean checkWorkOrderSubmitOrAssigneePermission(Long taskDetailId) {
        return exists(new LambdaQueryWrapper<OrderVideoTaskDetail>()
                .eq(OrderVideoTaskDetail::getId, taskDetailId)
                .and(wrapper -> wrapper
                        .eq(OrderVideoTaskDetail::getSubmitById, SecurityUtils.getUserId())
                        .or()
                        .eq(OrderVideoTaskDetail::getAssigneeId, SecurityUtils.getUserId())
                )
        );
    }

    /**
     * 通过任务单ID查询未完结的任务单详情
     */
    default List<OrderVideoTaskDetail> selectUncompletedTaskDetailListByTaskIds(List<Long> taskIds) {
        return selectList(new LambdaQueryWrapper<OrderVideoTaskDetail>()
                .in(OrderVideoTaskDetail::getTaskId, taskIds)
                .in(OrderVideoTaskDetail::getStatus, OrderTaskStatusEnum.UN_HANDLE.getCode(), OrderTaskStatusEnum.HANDLE_ING.getCode(), OrderTaskStatusEnum.APPLICATION_FOR_CANCELLATION.getCode(), OrderTaskStatusEnum.REJECT.getCode())
        );
    }

    /**
     * 通过视频订单ID查询任务单（部分字段）
     */
    List<OrderVideoTaskDetail> selectPartialFieldListByVideoIdsForSetAutoCompleteTimeStart(@Param("videoIds") List<Long> videoIds);

    /**
     * 通过日期 获取中文部客服新增/完成任务单数量
     */
    List<CustomerServiceAddedCompleteCountInfo> getChineseCustomerServiceTaskCountByDate(@Param("date") String date);
}
