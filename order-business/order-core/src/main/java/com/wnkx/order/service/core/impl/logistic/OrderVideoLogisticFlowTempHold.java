package com.wnkx.order.service.core.impl.logistic;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFlowDTO;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollowRecord;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.wnkx.order.service.OrderResourceService;
import com.wnkx.order.service.OrderVideoLogisticFollowRecordService;
import com.wnkx.order.service.OrderVideoLogisticFollowService;
import com.wnkx.order.service.core.OrderVideoLogisticFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :暂不处理
 * @create :2025-04-23 11:37
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoLogisticFlowTempHold implements OrderVideoLogisticFlowService {
    private final OrderResourceService orderResourceService;
    private final OrderVideoLogisticFollowService orderVideoLogisticFollowService;
    private final OrderVideoLogisticFollowRecordService orderVideoLogisticFollowRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void OrderVideoLogisticFlow(OrderVideoLogisticFlowDTO dto) {
        this.checkStatus(dto);
        List<Long> resourceIds = orderResourceService.saveBatchOrderResource(dto.getResourceIds());
        String resourceId = StrUtil.join(StrUtil.COMMA, resourceIds);

        LoginUserInfoVO loginUserInfoVo = dto.getLoginUserInfoVO();
        List<OrderVideoLogisticFollow> orderVideoLogisticFollowList = dto.getOrderVideoLogisticFollowList();
        List<OrderVideoLogisticFollow> updateList = new ArrayList<>();
        Date date = new Date();
        for (OrderVideoLogisticFollow orderVideoLogisticFollow : orderVideoLogisticFollowList) {
            if (HandleStatusEnum.DELAY_SHIPPING == dto.getHandleStatus()) {
                OrderVideoLogisticFollow updateEntity = new OrderVideoLogisticFollow();
                updateEntity.setId(orderVideoLogisticFollow.getId());
                updateEntity.setHandleStatus(dto.getHandleStatus().getCode());
                updateEntity.setLogisticStatus(this.getFollowStatusType().getLogisticStatus());
                updateEntity.setFollowStatus(this.getFollowStatusType().getCode());
                updateEntity.setLogisticStartTime(dto.getLogisticStartTime());
                updateEntity.setLatestRemark(StrUtil.isBlank(dto.getRemark()) ? "" : dto.getRemark());
                updateEntity.setLatestResourceId(StrUtil.isBlank(resourceId) ? "" : resourceId);
                updateEntity.setIsDefaultLogisticStartTime(dto.getIsDefaultLogisticStartTime());
                if (EventExecuteObjectEnum.BACK.getCode().equals(loginUserInfoVo.getUserType())) {
                    updateEntity.setUpdateById(loginUserInfoVo.getUserId());
                    updateEntity.setUpdateBy(loginUserInfoVo.getName());
                }
                updateEntity.setUpdateTime(new Date());
                updateList.add(updateEntity);
            } else if (HandleStatusEnum.REMARK_REPLENISH == dto.getHandleStatus()) {
                OrderVideoLogisticFollow updateEntity = new OrderVideoLogisticFollow();
                updateEntity.setId(orderVideoLogisticFollow.getId());
                updateEntity.setLatestRemark(StrUtil.isBlank(dto.getRemark()) ? "" : dto.getRemark());
                updateEntity.setLatestResourceId(StrUtil.isBlank(resourceId) ? "" : resourceId);
                if (EventExecuteObjectEnum.BACK.getCode().equals(loginUserInfoVo.getUserType())) {
                    updateEntity.setUpdateById(loginUserInfoVo.getUserId());
                    updateEntity.setUpdateBy(loginUserInfoVo.getName());
                }
                if (List.of(HandleStatusEnum.NOTIFIED_CONFIRM_MODEL.getCode(),
                        HandleStatusEnum.URGE_CONFIRM_MODEL_NOTIFIED.getCode()).contains(orderVideoLogisticFollow.getHandleStatus())) {
                    updateEntity.setNotifyTime(date);
                }
                updateEntity.setUpdateTime(new Date());
                updateList.add(updateEntity);
            } else {
                //无特殊流转 直接走这里：已通知、延迟发货已提醒、催发货已提醒、变更已通知、标记发货已提醒
                OrderVideoLogisticFollow updateEntity = new OrderVideoLogisticFollow();
                updateEntity.setId(orderVideoLogisticFollow.getId());
                updateEntity.setHandleStatus(dto.getHandleStatus().getCode());
                updateEntity.setFollowStatus(dto.getHandleStatus().getFollowStatusEnum().getCode());
                updateEntity.setNotifyTime(new Date());
                updateEntity.setLatestRemark(StrUtil.isBlank(dto.getRemark()) ? "" : dto.getRemark());
                updateEntity.setLatestResourceId(StrUtil.isBlank(resourceId) ? "" : resourceId);
                if (EventExecuteObjectEnum.BACK.getCode().equals(loginUserInfoVo.getUserType())) {
                    updateEntity.setUpdateById(loginUserInfoVo.getUserId());
                    updateEntity.setUpdateBy(loginUserInfoVo.getName());
                }
                updateEntity.setUpdateTime(new Date());
                updateList.add(updateEntity);
            }
        }
        orderVideoLogisticFollowService.updateBatchById(updateList);
        //添加跟进记录数据

        List<OrderVideoLogisticFollowRecord> orderVideoLogisticFollowRecords = new ArrayList<>();
        for (OrderVideoLogisticFollow orderVideoLogisticFollow : updateList){
            OrderVideoLogisticFollowRecord orderVideoLogisticFollowRecord = new OrderVideoLogisticFollowRecord();
            orderVideoLogisticFollowRecord.setFollowId(orderVideoLogisticFollow.getId());
            orderVideoLogisticFollowRecord.setResourceId(resourceId);
            orderVideoLogisticFollowRecord.setEventName(dto.getHandleStatus().getLabel());
            String replace = StrUtil.replace(dto.getHandleStatus().getDescription(), OrderConstant.ORDER_VIDEO_OPERATE_PREFIX_EVENT_CONTENT, loginUserInfoVo.getName());
            String remark = StrUtil.isNotBlank(dto.getRemark()) ? "，说明：\n" + dto.getRemark() : "";
            if (HandleStatusEnum.DELAY_SHIPPING == dto.getHandleStatus()) {
                String logisticStartTime = StatusTypeEnum.YES.getCode().equals(dto.getIsDefaultLogisticStartTime()) ? "未知" : DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, dto.getLogisticStartTime());
                orderVideoLogisticFollowRecord.setEventContent(StrUtil.format(replace, logisticStartTime)
                        + remark);
            } else if (HandleStatusEnum.REMARK_REPLENISH == dto.getHandleStatus()) {
                orderVideoLogisticFollowRecord.setEventContent(StrUtil.format(replace, dto.getRemark()));
            } else {
                orderVideoLogisticFollowRecord.setEventContent(replace + remark);
            }
            orderVideoLogisticFollowRecord.setEventExecuteObject(loginUserInfoVo.getUserType());
            orderVideoLogisticFollowRecord.setEventExecuteUserId(loginUserInfoVo.getUserId());
            orderVideoLogisticFollowRecord.setEventExecuteUserName(loginUserInfoVo.getName());
            orderVideoLogisticFollowRecord.setEventExecuteNickName(loginUserInfoVo.getNickName());
            orderVideoLogisticFollowRecord.setEventExecuteTime(new Date());
            orderVideoLogisticFollowRecord.setRemark(dto.getRemark());

            orderVideoLogisticFollowRecords.add(orderVideoLogisticFollowRecord);
        }

        orderVideoLogisticFollowRecordService.saveBatch(orderVideoLogisticFollowRecords);
    }

    @Override
    public void checkStatus(OrderVideoLogisticFlowDTO dto) {
        Assert.notNull(dto, "暂不处理：参数不能为空~");
        Assert.notNull(dto.getHandleStatus(), "暂不处理：处理状态不能为空~");
        Assert.isTrue(CollUtil.isNotEmpty(dto.getOrderVideoLogisticFollowList()), "暂不处理：物流跟进数据不能为空~");
        Assert.isTrue(dto.getHandleStatus().getFollowStatusEnum().getLogisticStatus().equals(this.getFollowStatusType().getLogisticStatus()),
                "暂不处理-{}：物流跟进状态不正确~", dto.getHandleStatus().getLabel());
        if (HandleStatusEnum.NOTIFIED == dto.getHandleStatus()) {
            orderVideoLogisticFollowService.checkHandleStatus(dto.getOrderVideoLogisticFollowList(), HandleStatusEnum.UN_NOTIFIED);
        } else if (HandleStatusEnum.DELAY_SHIPPING == dto.getHandleStatus()) {
            Assert.notNull(dto.getIsDefaultLogisticStartTime(), "暂不处理-延迟发货：是否默认发货时间不能为空~");
            if (StatusTypeEnum.YES.getCode().equals(dto.getIsDefaultLogisticStartTime())) {
                dto.setLogisticStartTime(DateUtils.addDays(new Date(), 5));
            }
            Assert.notNull(dto.getLogisticStartTime(), "暂不处理-延迟发货：预计发货时间不能为空~");
        } else if (HandleStatusEnum.REMARK_REPLENISH == dto.getHandleStatus()) {
            Assert.isTrue(StrUtil.isNotBlank(dto.getRemark()), "暂不处理-补充说明：说明不能为空~");
            orderVideoLogisticFollowService.checkHandleStatus(dto.getOrderVideoLogisticFollowList(), HandleStatusEnum.NOTIFIED,
                    HandleStatusEnum.DELAY_SHIPPING,
                    HandleStatusEnum.DELAY_NOTIFIED,
                    HandleStatusEnum.URGE_SHIPPING_NOTIFIED,
                    HandleStatusEnum.NOTIFIED_CONFIRM_MODEL,
                    HandleStatusEnum.URGE_CONFIRM_MODEL_NOTIFIED,
                    HandleStatusEnum.CHANGE_NOTIFIED,
                    HandleStatusEnum.REMARK_REPLENISH);
        } else if (HandleStatusEnum.DELAY_NOTIFIED == dto.getHandleStatus()) {
            orderVideoLogisticFollowService.checkHandleStatus(dto.getOrderVideoLogisticFollowList(), HandleStatusEnum.DELAY_REMINDER_OTHER);
        } else if (HandleStatusEnum.URGE_SHIPPING_NOTIFIED == dto.getHandleStatus()) {
            orderVideoLogisticFollowService.checkHandleStatus(dto.getOrderVideoLogisticFollowList(), HandleStatusEnum.URGE_SHIPPING_REMINDER);
        } else if (HandleStatusEnum.CHANGE_NOTIFIED == dto.getHandleStatus()) {
            orderVideoLogisticFollowService.checkHandleStatus(dto.getOrderVideoLogisticFollowList(), HandleStatusEnum.ADDRESS_CHANGE_NOTICE);
        }else if (HandleStatusEnum.NOTIFIED_SHIPPING_NOTIFIED == dto.getHandleStatus()) {
            orderVideoLogisticFollowService.checkHandleStatus(dto.getOrderVideoLogisticFollowList(), HandleStatusEnum.NOTIFIED_SHIPPING_REMINDER);
        } else if (HandleStatusEnum.NOTIFIED_SHIPPING == dto.getHandleStatus()) {
            for (OrderVideoLogisticFollow orderVideo : dto.getOrderVideoLogisticFollowList()) {
                Assert.isTrue(StatusTypeEnum.NO.getCode().equals(orderVideo.getLogisticStatus()), "订单状态发生变化，请刷新页面重试~");
            }
        } else if (HandleStatusEnum.NOTIFIED_CONFIRM_MODEL == dto.getHandleStatus()) {
            orderVideoLogisticFollowService.checkHandleStatus(dto.getOrderVideoLogisticFollowList(), HandleStatusEnum.UN_NOTIFIED_CONFIRM_MODEL);
        } else if (HandleStatusEnum.URGE_CONFIRM_MODEL_NOTIFIED == dto.getHandleStatus()) {
            orderVideoLogisticFollowService.checkHandleStatus(dto.getOrderVideoLogisticFollowList(),  HandleStatusEnum.URGE_CONFIRM_MODEL_UN_NOTIFIED);
        } else {
            throw new RuntimeException("暂不处理：数据已更新，请刷新页面~");
        }
    }

    @Override
    public FollowStatusEnum getFollowStatusType() {
        return FollowStatusEnum.TEMP_HOLD;
    }
}
