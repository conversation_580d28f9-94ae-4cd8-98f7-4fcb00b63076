package com.wnkx.order.service.core.impl.logistic;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFlowDTO;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollowRecord;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.wnkx.order.service.OrderResourceService;
import com.wnkx.order.service.OrderVideoLogisticFollowRecordService;
import com.wnkx.order.service.OrderVideoLogisticFollowService;
import com.wnkx.order.service.core.OrderVideoLogisticFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :模特待确认
 * @create :2025-04-23 11:40
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoLogisticFlowModelConfirmPend implements OrderVideoLogisticFlowService {
    private final OrderResourceService orderResourceService;
    private final OrderVideoLogisticFollowService orderVideoLogisticFollowService;
    private final OrderVideoLogisticFollowRecordService orderVideoLogisticFollowRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void OrderVideoLogisticFlow(OrderVideoLogisticFlowDTO dto) {
        if (StatusTypeEnum.YES.getCode().equals(dto.getIsCallback()) &&
                StatusTypeEnum.NO.getCode().equals(dto.getOrderVideoLogisticFollow().getIsCallBack())
        ){
            //如果本次调用是回调、系统物流回调记录非回调 则物流流转失效
            return;
        }
        this.checkStatus(dto);
        List<Long> resourceIds = orderResourceService.saveBatchOrderResource(dto.getResourceIds());
        String resourceId = StrUtil.join(StrUtil.COMMA, resourceIds);

        LoginUserInfoVO loginUserInfoVo = dto.getLoginUserInfoVO();

        log.debug("当前登录数据：{}", JSON.toJSONString(dto.getLoginUserInfoVO()));
        OrderVideoLogisticFollow updateEntity = new OrderVideoLogisticFollow();
        if (List.of(FollowStatusEnum.SHIP.getCode(), FollowStatusEnum.NO_FOLLOW_NEED.getCode(), FollowStatusEnum.NEED_FOLLOW_UP.getCode()).contains(dto.getOrderVideoLogisticFollow().getFollowStatus())) {
            updateEntity.setLatestMainStatus(dto.getLatestMainStatus());
            updateEntity.setLogisticUpdateTime(dto.getLogisticUpdateTime());
        }
        updateEntity.setId(dto.getOrderVideoLogisticFollow().getId());
        updateEntity.setLogisticStatus(this.getFollowStatusType().getLogisticStatus());
        updateEntity.setIsCallBack(dto.getIsCallback());
        updateEntity.setFollowStatus(this.getFollowStatusType().getCode());
        updateEntity.setModelResult(dto.getModelResult());
        updateEntity.setLatestRemark(StrUtil.isBlank(dto.getRemark()) ? "" : dto.getRemark());
        updateEntity.setLatestResourceId(StrUtil.isBlank(resourceId) ? "" : resourceId);
        if (EventExecuteObjectEnum.BACK.getCode().equals(loginUserInfoVo.getUserType())) {
            updateEntity.setUpdateById(loginUserInfoVo.getUserId());
            updateEntity.setUpdateBy(loginUserInfoVo.getName());
        }
        updateEntity.setUpdateTime(new Date());
        orderVideoLogisticFollowService.updateById(updateEntity);


        OrderVideoLogisticFollow orderVideoLogisticFollow = dto.getOrderVideoLogisticFollow();

        OrderVideoLogisticFollowRecord orderVideoLogisticFollowRecord = new OrderVideoLogisticFollowRecord();
        orderVideoLogisticFollowRecord.setFollowId(orderVideoLogisticFollow.getId());
        orderVideoLogisticFollowRecord.setResourceId(resourceId);
        String remark = StrUtil.isNotBlank(dto.getRemark()) ? "，备注：" + dto.getRemark() : "";
        if (List.of(FollowStatusEnum.NO_FOLLOW_NEED.getCode(), FollowStatusEnum.NEED_FOLLOW_UP.getCode()).contains(dto.getOrderVideoLogisticFollow().getFollowStatus())) {
            if (StatusTypeEnum.YES.getCode().equals(dto.getIsCallback())) {
                orderVideoLogisticFollowRecord.setEventName("新物流跟进：" + LogisticMainStatus.getSketchByLabel(dto.getLatestMainStatus()));
                orderVideoLogisticFollowRecord.setEventContent("生成新物流跟进记录");
            } else {
                String logisticUpdateTime = "，物流时间" + DateUtils.parseDateToStr("yyyy年MM月dd日 HH:mm", dto.getLogisticUpdateTime());
                orderVideoLogisticFollowRecord.setEventName("更新物流状态：" + LogisticMainStatus.getSketchByLabel(dto.getLatestMainStatus()));
                orderVideoLogisticFollowRecord.setEventContent(loginUserInfoVo.getName() + "更新物流" + logisticUpdateTime + remark);
            }

        } else if (FollowStatusEnum.MODEL_CONFIRM_PEND.getCode().equals(dto.getOrderVideoLogisticFollow().getFollowStatus())) {
            if (StatusTypeEnum.YES.getCode().equals(dto.getIsCallback())) {
                orderVideoLogisticFollowRecord.setEventName("新物流跟进：" + LogisticMainStatus.getSketchByLabel(dto.getLatestMainStatus()));
                orderVideoLogisticFollowRecord.setEventContent("生成新物流跟进记录");
            }else {
                orderVideoLogisticFollowRecord.setEventName("更新模特反馈结果：" + ModelResultEnum.getLabelByCode(dto.getModelResult()));
                orderVideoLogisticFollowRecord.setEventContent(loginUserInfoVo.getName() + "更新模特反馈结果" + remark);
            }
        } else {
            orderVideoLogisticFollowRecord.setEventName("新物流跟进：" + LogisticMainStatus.getSketchByLabel(dto.getLatestMainStatus()));
            orderVideoLogisticFollowRecord.setEventContent("生成新物流跟进记录");
        }

        orderVideoLogisticFollowRecord.setEventExecuteObject(loginUserInfoVo.getUserType());
        orderVideoLogisticFollowRecord.setEventExecuteUserId(loginUserInfoVo.getUserId());
        orderVideoLogisticFollowRecord.setEventExecuteUserName(loginUserInfoVo.getName());
        orderVideoLogisticFollowRecord.setEventExecuteNickName(loginUserInfoVo.getNickName());
        orderVideoLogisticFollowRecord.setEventExecuteTime(new Date());
        orderVideoLogisticFollowRecord.setRemark(dto.getRemark());
        orderVideoLogisticFollowRecordService.save(orderVideoLogisticFollowRecord);
    }

    @Override
    public void checkStatus(OrderVideoLogisticFlowDTO dto) {
        Assert.notNull(dto, "模特待确认：参数不能为空~");
        Assert.notNull(dto.getOrderVideoLogisticFollow(), "模特待确认：物流跟进数据不能为空~");
        Assert.notNull(dto.getModelResult(), "模特待确认：模特处理结果不能为空~");
        if (List.of(FollowStatusEnum.SHIP.getCode(), FollowStatusEnum.NO_FOLLOW_NEED.getCode(), FollowStatusEnum.NEED_FOLLOW_UP.getCode()).contains(dto.getOrderVideoLogisticFollow().getFollowStatus())
        || (FollowStatusEnum.MODEL_CONFIRM_PEND.getCode().equals(dto.getOrderVideoLogisticFollow().getFollowStatus()) && StatusTypeEnum.YES.getCode().equals(dto.getOrderVideoLogisticFollow().getIsCallBack())&& StatusTypeEnum.YES.getCode().equals(dto.getIsCallback()))
        ) {
            //当前状态为 无需跟进、需跟进、已发货、模特待确认（当前为系统修改、当前为回调）
            //模特待确认状态 需要投递失败、成功签收、到达待取
            Assert.notNull(dto.getLatestMainStatus(), "物流状态不能为空~");
            Assert.notNull(dto.getLogisticUpdateTime(), "物流时间不能为空~");
            Assert.isTrue(List.of(LogisticMainStatus.AVAILABLE_FOR_PICKUP.getLabel(), LogisticMainStatus.DELIVERY_FAILURE.getLabel(), LogisticMainStatus.DELIVERED.getLabel()).contains(dto.getLatestMainStatus()), "模特待确认：物流状态不在投递失败、成功签收、到达待取范围内~");
            Assert.isTrue(ModelResultEnum.PENDING.getCode().equals(dto.getModelResult()), "模特待确认：模特处理结果不在待确认范围内~");
        } else if (FollowStatusEnum.MODEL_CONFIRM_PEND.getCode().equals(dto.getOrderVideoLogisticFollow().getFollowStatus())) {
            //当前状态为 模特待确认
            Assert.isTrue(ModelResultEnum.INQUIRED.getCode().equals(dto.getModelResult()), "模特待确认：模特处理结果不在已询问范围内~");
        } else {
            throw new ServiceException("数据已更新，请刷新页面~！");
        }
    }

    @Override
    public FollowStatusEnum getFollowStatusType() {
        return FollowStatusEnum.MODEL_CONFIRM_PEND;
    }
}
