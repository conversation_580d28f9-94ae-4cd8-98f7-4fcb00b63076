package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollowRecord;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_video_logistic_follow_record(跟进记录表)】的数据库操作Mapper
 * @createDate 2025-04-22 16:26:39
 * @Entity com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollowRecord
 */
@Mapper
public interface OrderVideoLogisticFollowRecordMapper extends SuperMapper<OrderVideoLogisticFollowRecord> {

    /**
     * 根据物流跟进数据Id获取跟进记录列表
     * @param followId
     * @return
     */
    default List<OrderVideoLogisticFollowRecord> getListByFollowId(Long followId) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoLogisticFollowRecord>()
                .eq(OrderVideoLogisticFollowRecord::getFollowId, followId)
                .orderByDesc(OrderVideoLogisticFollowRecord::getEventExecuteTime)
        );
    }

    /**
     * 根据跟进表Id列表删除更新记录
     * @param ids
     */
    default void deleteByFollowIds(List<Long> ids){
        this.delete(new LambdaQueryWrapper<OrderVideoLogisticFollowRecord>()
                .in(OrderVideoLogisticFollowRecord::getFollowId, ids));
    }
}




