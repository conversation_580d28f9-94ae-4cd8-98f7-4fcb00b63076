package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceOperate;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:27
 */
@Mapper
public interface OrderInvoiceOperateMapper extends SuperMapper<OrderInvoiceOperate> {


    /**
     * 运营端-发票管理-流转记录
     */
    default List<OrderInvoiceOperate> getInvoiceOperateRecord(Long invoiceId) {
        return selectList(new LambdaQueryWrapper<OrderInvoiceOperate>()
                .eq(OrderInvoiceOperate::getInvoiceId, invoiceId)
                .orderByDesc(OrderInvoiceOperate::getCreateTime)
        );
    }
}
