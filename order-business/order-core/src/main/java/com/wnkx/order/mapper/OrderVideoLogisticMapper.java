package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.system.api.domain.entity.order.OrderVideoLogistic;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 订单_视频_物流关联Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Mapper
public interface OrderVideoLogisticMapper extends SuperMapper<OrderVideoLogistic> {
    /**
     * 通过视频订单id获取订单关联物流
     *
     * @param videoId 视频订单id
     * @return 订单关联物流
     */
    default List<OrderVideoLogistic> selectListByVideoId(Collection<Long> videoId) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoLogistic>()
                .in(OrderVideoLogistic::getVideoId, videoId)
                .eq(OrderVideoLogistic::getIsCancel, StatusTypeEnum.NO.getCode())
                .orderByDesc(OrderVideoLogistic::getShippingTime));
    }

    /**
     * 根据视频订单id和回滚id获取订单关联物流
     * @param videoId
     * @param rollbackId
     * @return
     */
    List<OrderVideoLogistic> selectListByVideoIdAndRollbackId(@Param("videoId")Long videoId,@Param("rollbackId") Long rollbackId);

    /**
     * 根据物流单号获取订单物流数据
     * @param number
     * @return
     */
    default List<OrderVideoLogistic> selectListByNumber(String number) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoLogistic>()
                .in(OrderVideoLogistic::getNumber, number)
                .eq(OrderVideoLogistic::getIsCancel, StatusTypeEnum.NO.getCode())
                .orderByDesc(OrderVideoLogistic::getShippingTime));
    }

    /**
     * 通过物流单号查询订单关联物流的视频id
     */
    List<Long> selectVideoIdsByLogisticNumber(@Param("numbers") Collection<String> numbers);

    /**
     * 通过视频订单id获取首次发货物流单号
     */
    List<OrderVideoLogistic> selectFirstListByVideoId(@Param("videoIds") List<Long> videoIds);

    /**
     * 根据确认收货状态及  视频订单id获取最后一次发货物流单号
     *
     * @param videoIds
     * @param receipt
     * @return
     */
    List<OrderVideoLogistic> selectLastListByDto(@Param("videoIds") Collection<Long> videoIds, @Param("receipt") Integer receipt);

    /**
     * 查询视频订单有几个发货物流
     */
    default Long selectCountByShippingAddressId(Long shippingAddressId) {
        return selectCount(new LambdaQueryWrapper<OrderVideoLogistic>()
                .eq(OrderVideoLogistic::getShippingAddressId, shippingAddressId)
                .eq(OrderVideoLogistic::getIsCancel, StatusTypeEnum.NO.getCode())
        );
    }

    /**
     * 确认收货
     *
     * @param logisticId
     */
    default void confirmReceipt(Long logisticId, Date signTime) {
        this.updateById(OrderVideoLogistic.builder()
                .id(logisticId)
                .receipt(StatusTypeEnum.YES.getCode())
                .receiptTime(signTime)
                .build());
    }
    /**
     * 取消确认收货
     *
     * @param logisticId
     */
    default void cancelConfirmReceipt(Long logisticId) {
        this.update(null, new LambdaUpdateWrapper<OrderVideoLogistic>()
                .set(OrderVideoLogistic::getReceipt, StatusTypeEnum.NO.getCode())
                .set(OrderVideoLogistic::getReceiptTime, null)
                .eq(OrderVideoLogistic::getId, logisticId));
    }


    /**
     * 通过模特收件地址ID获取发货物流
     */
    default List<OrderVideoLogistic> selectListByShippingAddressId(Long shippingAddressId) {
        return selectList(new LambdaQueryWrapper<OrderVideoLogistic>()
                .eq(OrderVideoLogistic::getShippingAddressId, shippingAddressId)
                .eq(OrderVideoLogistic::getIsCancel, StatusTypeEnum.NO.getCode())
                .orderByDesc(OrderVideoLogistic::getShippingTime)
        );
    }
}
