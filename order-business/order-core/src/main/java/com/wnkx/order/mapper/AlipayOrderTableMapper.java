package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.system.api.domain.entity.order.AlipayOrderTable;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/24 18:16
 */
@Mapper
public interface AlipayOrderTableMapper extends SuperMapper<AlipayOrderTable> {

    /**
     * 获取有效订单*
     */
    default AlipayOrderTable getValidAlipayOrderTableByOrderNum(String orderNum) {
        return selectOne(new LambdaQueryWrapper<AlipayOrderTable>()
                .eq(AlipayOrderTable::getOrderNum, orderNum)
                .eq(AlipayOrderTable::getStatus, StatusTypeEnum.YES.getCode())
        );
    }

    /**
     * 设置订单为无效*
     */
    default void banFyOrderTable(Long id) {
        update(null, new LambdaUpdateWrapper<AlipayOrderTable>()
                .eq(AlipayOrderTable::getId, id)
                .set(AlipayOrderTable::getStatus, StatusTypeEnum.NO.getCode())
        );
    }

    /**
     * 获取有效订单*
     */
    default AlipayOrderTable getValidAlipayOrderTableByOutTradeNo(String out_trade_no) {
        return selectOne(new LambdaQueryWrapper<AlipayOrderTable>()
                .eq(AlipayOrderTable::getMchntOrderNo, out_trade_no)
                .eq(AlipayOrderTable::getStatus, StatusTypeEnum.YES.getCode())
        );
    }


    /**
     * 根据orderNUm获取订单列表*
     *
     * @param order_num
     */
    default List<AlipayOrderTable> getValidAlipayOrderTableList(String order_num) {
        return this.selectList(new LambdaQueryWrapper<AlipayOrderTable>()
                .eq(AlipayOrderTable::getOrderNum, order_num)
                .eq(AlipayOrderTable::getStatus, StatusTypeEnum.YES.getCode())
        );
    }

    /**
     * 根据订单号无效支付宝订单数据*
     *
     * @param order_num
     */
    default void banAlipayOrderTableByOrderNum(String order_num) {
        update(null, new LambdaUpdateWrapper<AlipayOrderTable>()
                .set(AlipayOrderTable::getStatus, StatusTypeEnum.NO.getCode())
                .eq(AlipayOrderTable::getOrderNum, order_num)
        );
    }

    /**
     * 根据内部订单号获取订单*
     */
    default AlipayOrderTable getAlipayOrderTableByOutTradeNo(String out_trade_no) {
        return selectOne(new LambdaQueryWrapper<AlipayOrderTable>()
                .eq(AlipayOrderTable::getMchntOrderNo, out_trade_no)
        );
    }
    List<AlipayOrderTable> unPayAlipayOrderList();
}
