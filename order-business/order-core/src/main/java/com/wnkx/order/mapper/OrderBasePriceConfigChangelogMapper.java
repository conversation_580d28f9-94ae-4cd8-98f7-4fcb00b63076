package com.wnkx.order.mapper;

import com.ruoyi.system.api.domain.entity.order.OrderBasePriceConfigChangelog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.vo.order.OrderBasePriceConfigChangelogVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_base_price_config_changelog(订单基础价格配置表_更改记录)】的数据库操作Mapper
* @createDate 2025-05-16 16:58:03
* @Entity com.ruoyi.system.api.domain.entity.order.OrderBasePriceConfigChangelog
*/
public interface OrderBasePriceConfigChangelogMapper extends BaseMapper<OrderBasePriceConfigChangelog> {

    List<OrderBasePriceConfigChangelogVO> getConfigChangeLogList(Integer type);
}




