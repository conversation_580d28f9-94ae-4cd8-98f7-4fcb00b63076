package com.wnkx.order.service.core.impl.logistic;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.enums.EventExecuteObjectEnum;
import com.ruoyi.common.core.enums.FollowStatusEnum;
import com.ruoyi.common.core.enums.LogisticMainStatus;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFlowDTO;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollowRecord;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.wnkx.order.service.OrderVideoLogisticFollowRecordService;
import com.wnkx.order.service.OrderVideoLogisticFollowService;
import com.wnkx.order.service.core.OrderVideoLogisticFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :无需跟进
 * @create :2025-04-23 11:41
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoLogisticFlowNoFollowNeed implements OrderVideoLogisticFlowService {
    private final OrderVideoLogisticFollowService orderVideoLogisticFollowService;
    private final OrderVideoLogisticFollowRecordService orderVideoLogisticFollowRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void OrderVideoLogisticFlow(OrderVideoLogisticFlowDTO dto) {
        this.checkStatus(dto);
        LoginUserInfoVO loginUserInfoVo = dto.getLoginUserInfoVO();

        OrderVideoLogisticFollow updateEntity = new OrderVideoLogisticFollow();
        updateEntity.setId(dto.getOrderVideoLogisticFollow().getId());

        updateEntity.setLogisticStatus(this.getFollowStatusType().getLogisticStatus());
        updateEntity.setFollowStatus(this.getFollowStatusType().getCode());
        updateEntity.setIsCallBack(dto.getIsCallback());

        updateEntity.setLatestMainStatus(dto.getLatestMainStatus());
        updateEntity.setLogisticUpdateTime(dto.getLogisticUpdateTime());
        if (EventExecuteObjectEnum.BACK.getCode().equals(loginUserInfoVo.getUserType())) {
            updateEntity.setUpdateById(loginUserInfoVo.getUserId());
            updateEntity.setUpdateBy(loginUserInfoVo.getName());
        }
        updateEntity.setUpdateTime(new Date());
        orderVideoLogisticFollowService.updateById(updateEntity);


        OrderVideoLogisticFollow orderVideoLogisticFollow = dto.getOrderVideoLogisticFollow();

        OrderVideoLogisticFollowRecord orderVideoLogisticFollowRecord = new OrderVideoLogisticFollowRecord();
        orderVideoLogisticFollowRecord.setFollowId(orderVideoLogisticFollow.getId());
        orderVideoLogisticFollowRecord.setEventName("新物流跟进：" + LogisticMainStatus.getSketchByLabel(dto.getLatestMainStatus()));
        orderVideoLogisticFollowRecord.setEventContent("生成新物流跟进记录");

        orderVideoLogisticFollowRecord.setEventExecuteObject(loginUserInfoVo.getUserType());
        orderVideoLogisticFollowRecord.setEventExecuteUserId(loginUserInfoVo.getUserId());
        orderVideoLogisticFollowRecord.setEventExecuteUserName(loginUserInfoVo.getName());
        orderVideoLogisticFollowRecord.setEventExecuteNickName(loginUserInfoVo.getNickName());
        orderVideoLogisticFollowRecord.setEventExecuteTime(new Date());
        orderVideoLogisticFollowRecord.setRemark(dto.getRemark());
        orderVideoLogisticFollowRecordService.save(orderVideoLogisticFollowRecord);
    }

    @Override
    public void checkStatus(OrderVideoLogisticFlowDTO dto) {
        Assert.notNull(dto, "无需跟进：参数不能为空~");
        Assert.notNull(StatusTypeEnum.YES.getCode().equals(dto.getIsCallback()), "无需跟进：只有回调数据才无需跟进~");
        Assert.notNull(dto.getOrderVideoLogisticFollow(), "无需跟进：物流跟进数据不能为空~");
//        Assert.isTrue(List.of(FollowStatusEnum.SHIP.getCode(), FollowStatusEnum.NO_FOLLOW_NEED.getCode()).contains(dto.getOrderVideoLogisticFollow().getFollowStatus()),
//                "无需跟进：只有已发货和无需跟进状态才能进入无需跟进~");
        Assert.isTrue(StatusTypeEnum.YES.getCode().equals(dto.getOrderVideoLogisticFollow().getLogisticStatus()), "无需跟进：只有已发货数据才能无需跟进~");
        Assert.isFalse(List.of(FollowStatusEnum.CLOSE.getCode(), FollowStatusEnum.DELETE.getCode()).contains(dto.getOrderVideoLogisticFollow().getFollowStatus()), "无需跟进：已结束、已删除数据无法流转为无需跟进~");
        Assert.notNull(dto.getLatestMainStatus(), "无需跟进：最新物流状态不能为空~");
        Assert.notNull(dto.getLogisticUpdateTime(), "无需跟进：物流系统同步时间不能为空~");


    }

    @Override
    public FollowStatusEnum getFollowStatusType() {
        return FollowStatusEnum.NO_FOLLOW_NEED;
    }
}
