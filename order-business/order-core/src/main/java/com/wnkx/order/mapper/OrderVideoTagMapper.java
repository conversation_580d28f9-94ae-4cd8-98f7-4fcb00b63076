package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTag;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * 订单_视频_关联标签Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-14
 */
@Mapper
public interface OrderVideoTagMapper extends SuperMapper<OrderVideoTag>
{
    /**
     * 查询订单_视频_关联标签
     *
     * @param id 订单_视频_关联标签主键
     * @return 订单_视频_关联标签
     */
    public OrderVideoTag selectOrderVideoTagById(Long id);

    /**
     * 查询订单_视频_关联标签列表
     *
     * @param orderVideoTag 订单_视频_关联标签
     * @return 订单_视频_关联标签集合
     */
    public List<OrderVideoTag> selectOrderVideoTagList(OrderVideoTag orderVideoTag);

    /**
     * 新增订单_视频_关联标签
     *
     * @param orderVideoTag 订单_视频_关联标签
     * @return 结果
     */
    public int insertOrderVideoTag(OrderVideoTag orderVideoTag);

    /**
     * 修改订单_视频_关联标签
     *
     * @param orderVideoTag 订单_视频_关联标签
     * @return 结果
     */
    public int updateOrderVideoTag(OrderVideoTag orderVideoTag);

    /**
     * 删除订单_视频_关联标签
     *
     * @param id 订单_视频_关联标签主键
     * @return 结果
     */
    public int deleteOrderVideoTagById(Long id);

    /**
     * 批量删除订单_视频_关联标签
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrderVideoTagByIds(Long[] ids);

    /**
     * 通过视频订单id和分类id查询视频订单标签
     *
     * @param videoId 视频订单id
     * @param code    分类id
     * @return
     */
    default List<OrderVideoTag> selectListByVideoIdAndCategory(Long videoId, Long code) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoTag>()
                .eq(OrderVideoTag::getVideoId, videoId)
                .eq(OrderVideoTag::getCategoryId, code)
        );
    }

    /**
     * 通过视频订单id获取单个
     */
    default OrderVideoTag getOneByVideoId(Long videoId) {
        return selectOne(new LambdaQueryWrapper<OrderVideoTag>()
                .eq(OrderVideoTag::getVideoId, videoId)
                .last("limit 1")
        );
    }

    /**
     * 通过视频订单ID删除数据
     *
     * @param videoIds
     */
    default void removeBatchByVideoIds(Set<Long> videoIds) {
        delete(new LambdaQueryWrapper<OrderVideoTag>()
                .in(OrderVideoTag::getVideoId, videoIds)
        );
    }
}
