package com.wnkx.order.service.core;

import cn.hutool.core.lang.Assert;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.dto.order.OrderVideoTaskFlowDTO;
import com.wnkx.order.factory.OrderVideoTaskServiceFactory;
import com.wnkx.order.service.IOrderVideoTaskDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-12-11 17:35
 **/
@Component
@RequiredArgsConstructor
@Slf4j
@Validated
public class OrderVideoTaskFlowCore {

    private final OrderVideoTaskServiceFactory orderVideoTaskServiceFactory;

    private final RedisService redisService;

    public void flowVideoTask(@Valid OrderVideoTaskFlowDTO dto) {
        Assert.notNull(dto.getOrderVideoTaskDetail().getId(), "任务单数据不能为空");
        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_TASK_FLOW_KEY + dto.getOrderVideoTaskDetail().getId(), 60L), "订单任务单流转中，请稍后重试！");
            OrderVideoTaskFlowService orderVideoFlowService = orderVideoTaskServiceFactory.getOrderVideoFlowService(dto.getOrderTaskStatus());
            orderVideoFlowService.flowVideoTask(dto);

            SpringUtils.getBean(IOrderVideoTaskDetailService.class).updateById(dto.getOrderVideoTaskDetail());
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_TASK_FLOW_KEY + dto.getOrderVideoTaskDetail().getId());
        }
    }
}
