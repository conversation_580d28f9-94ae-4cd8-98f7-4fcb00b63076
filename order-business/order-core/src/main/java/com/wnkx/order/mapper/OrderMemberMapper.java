package com.wnkx.order.mapper;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.OrderMemberStatusEnum;
import com.ruoyi.system.api.domain.dto.order.OrderMemberListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderMember;
import com.ruoyi.system.api.domain.vo.order.OrderMemberVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_member(订单_会员表)】的数据库操作Mapper
* @createDate 2024-06-24 16:00:58
* @Entity com.ruoyi.system.api.domain.entity.biz.business.OrderMember
*/
@Mapper
public interface OrderMemberMapper extends SuperMapper<OrderMember> {

    /**
     * 获取会员订单列表
     * @param dto
     * @return
     */
    List<OrderMemberVO> getOrderMemberList(@Param("dto") OrderMemberListDTO dto);

    /**
     * 获取工作台-财务部-会员待审批列表
     * @return
     */
    List<OrderMemberVO> workbenchFinanceMemberList();

    /**
     * 会员订单列表
     * @param dto
     * @return
     */
    List<OrderMemberVO> getBackendOrderMemberList(@Param("dto") OrderMemberListDTO dto);

    /**
     * 填充商家数据
     * @param bizUserId
     * @param businessAccountId
     * @param businessAccountName
     */
    default void setBusinessInfo(Long bizUserId, Long businessAccountId, String businessAccountName){
        this.update(null, new LambdaUpdateWrapper<OrderMember>()
                        .set(OrderMember::getCreateUserId, businessAccountId)
                        .set(OrderMember::getUpdateUserId, businessAccountId)
                        .set(OrderMember::getCreateUserName, businessAccountName)
                        .set(OrderMember::getUpdateUserName, businessAccountName)
                .eq(OrderMember::getBizUserId, bizUserId));

    }

    /**
     * 根据订单号列表获取会员订单数据
     *
     * @param orderNums
     * @return
     */
    default List<OrderMember> getByOrderNums(List<String> orderNums) {
        return this.selectList(new LambdaQueryWrapper<OrderMember>()
                .in(OrderMember::getOrderNum, orderNums));
    }

    /**
     * 检查是否是首次购买
     *
     * @return true:首次购买 false:非首次购买
     */
    default Boolean checkFirstBuy(Long bizUserId, String orderNum) {
        return !exists(new LambdaQueryWrapper<OrderMember>()
                .eq(OrderMember::getBizUserId, bizUserId)
                .ne(StrUtil.isNotBlank(orderNum), OrderMember::getOrderNum, orderNum)
                .ne(OrderMember::getStatus, OrderMemberStatusEnum.UN_MATCH.getCode())
        );
    }

    /**
     * 获取登录账号数量
     * @param bizUserId
     * @param orderNum
     * @return
     */
    Long getBizUserOrderCount(@Param("bizUserId")Long bizUserId, @Param("orderNum")String orderNum);

    /**
     * 获取未支付 订单ID列表
     * @param businessId
     * @param bizUserId
     * @return
     */
    List<OrderMemberVO> getMemberUnPayOrderNum(@Param("businessId") Long businessId, @Param("bizUserId") Long bizUserId);


    /**
     * 通过订单号获取会员订单
     */
    default OrderMember getByOrderNum(String orderNum) {
        return selectOne(new LambdaQueryWrapper<OrderMember>()
                .eq(OrderMember::getOrderNum, orderNum)
        );
    }

    void updateOrderMemberBuyFlag();

    /**
     * 获取会员有效订单
     */
    default List<OrderMember> getValidOrderMemberList(Long bizUserId) {
        return this.selectList(new LambdaQueryWrapper<OrderMember>()
                .eq(OrderMember::getBizUserId, bizUserId)
                .notIn(OrderMember::getStatus, OrderMemberStatusEnum.UN_CONFIRM.getCode(), OrderMemberStatusEnum.UN_MATCH.getCode())
        );
    }
}
