package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTag;

import java.util.List;

/**
 * 订单_视频_关联标签Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-14
 */
public interface IOrderVideoTagService extends IService<OrderVideoTag>
{
    /**
     * 通过视频订单id和分类id查询视频订单标签
     * @param videoId    视频订单id
     * @param code  分类id
     * @return
     */
    List<OrderVideoTag> selectListByVideoIdAndCategory(Long videoId, Long code);

    /**
     * 通过视频订单id获取单个
     */
    OrderVideoTag getOneByVideoId(Long videoId);

    /**
     * 批量添加订单视频标签
     */
    void saveBatchOrderVideoTag(List<OrderVideoTag> orderVideoTags);
}
