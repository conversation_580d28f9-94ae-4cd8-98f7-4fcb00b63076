package com.wnkx.order.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.VideoScoreListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBack;
import com.ruoyi.system.api.domain.vo.order.VideoScoreListVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【order_video_feed_back(订单反馈表(商家))】的数据库操作Mapper
 * @createDate 2024-06-18 17:43:06
 * @Entity
 */
public interface OrderVideoFeedBackMapper extends SuperMapper<OrderVideoFeedBack> {

    default void addFeedBack(OrderVideoFeedBack orderVideoFeedBack) {
        final LoginBaseEntity loginUser = SecurityUtils.getLoginUser();
        if (loginUser != null) {
            orderVideoFeedBack.setCreateUserId(loginUser.getUserid());
            orderVideoFeedBack.setCreateBy(loginUser.getUsername());
        }
        insert(orderVideoFeedBack);
    }

    default List<OrderVideoFeedBack> getFeedBackList(Long videoId, Long rollbackId) {
        return selectList(new LambdaQueryWrapper<OrderVideoFeedBack>()
                .eq(OrderVideoFeedBack::getVideoId, videoId)
                .eq(ObjectUtil.isNotNull(rollbackId), OrderVideoFeedBack::getRollbackId, rollbackId)
        );
    }


    /**
     * 通过视频订单ID查询反馈给商家的素材
     */
    default List<OrderVideoFeedBack> selectFeedBackListByVideoIds(List<Long> videoIds) {
        List<OrderVideoFeedBack> orderVideoFeedBacks = selectList(new LambdaQueryWrapper<OrderVideoFeedBack>()
                .in(OrderVideoFeedBack::getVideoId, videoIds)
                .eq(Integer.valueOf(UserTypeConstants.USER_TYPE).equals(SecurityUtils.getLoginUserType()), OrderVideoFeedBack::getIsNew, StatusTypeEnum.YES.getCode())
                .orderByDesc(OrderVideoFeedBack::getCreateTime)
        );
        if (!Integer.valueOf(UserTypeConstants.USER_TYPE).equals(SecurityUtils.getLoginUserType())) {
            return orderVideoFeedBacks;
        }

        for (OrderVideoFeedBack orderVideoFeedBack : orderVideoFeedBacks) {
            orderVideoFeedBack.setModifyReason(null);
        }

        return orderVideoFeedBacks;
    }

    /**
     * 视频评价记录 列表
     */
    List<VideoScoreListVO> selectVideoScoreListByCondition(@Param("dto") VideoScoreListDTO dto);

    /**
     * 视频评价记录 拍摄模特下拉框
     */
    List<Long> getShootModelId();

    /**
     * 视频评价记录 评价人下拉框
     */
    default List<Long> getVideoScoreByIds() {
        return selectList(new LambdaQueryWrapper<OrderVideoFeedBack>()
                .isNotNull(OrderVideoFeedBack::getVideoScoreById)
        ).stream().map(OrderVideoFeedBack::getVideoScoreById).collect(Collectors.toList());
    }

    /**
     * 通过模特反馈素材详情ID查询数据
     */
    default List<OrderVideoFeedBack> selectListByMaterialInfoIds(List<Long> materialInfoIds) {
        return selectList(new LambdaQueryWrapper<OrderVideoFeedBack>()
                .in(OrderVideoFeedBack::getMaterialInfoId, materialInfoIds)
                .orderByDesc(OrderVideoFeedBack::getFeedbackTime)
        );
    }

    /**
     * 通过视频订单ID和回退ID查询反馈给商家的素材
     */
    default List<OrderVideoFeedBack> selectListByVideoIdAndRollbackId(Long videoId, Long rollbackId) {
        return selectList(new LambdaQueryWrapper<OrderVideoFeedBack>()
                .eq(OrderVideoFeedBack::getVideoId, videoId)
                .eq(ObjectUtil.isNotNull(rollbackId), OrderVideoFeedBack::getRollbackId, rollbackId)
        );
    }

    /**
     * 通过视频订单ID查询反馈给商家的素材（部分字段）
     */
    default List<OrderVideoFeedBack> selectPartialFieldListByVideoIdsForSetAutoCompleteTimeStart(List<Long> videoIds) {
        return selectList(new LambdaQueryWrapper<OrderVideoFeedBack>()
                .in(OrderVideoFeedBack::getVideoId, videoIds)
                .select(OrderVideoFeedBack::getVideoId, OrderVideoFeedBack::getRollbackId, OrderVideoFeedBack::getVideoUrl, OrderVideoFeedBack::getPicUrl, OrderVideoFeedBack::getCreateTime)
        );
    }

    /**
     * 通过视频订单ID获取每个视频订单最新一条的反馈给商家素材
     */
    List<OrderVideoFeedBack> selectLatestFeedBackListByVideoIds(@Param("videoIds") List<Long> videoIds);
}




