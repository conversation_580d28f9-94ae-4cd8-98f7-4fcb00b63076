package com.wnkx.order.service.core.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.FlagEnum;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.common.redis.service.DelayQueueService;
import com.ruoyi.system.api.domain.dto.order.OrderVideoFlowDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.wnkx.order.config.OrderVideoProperties;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.IOrderVideoModelService;
import com.wnkx.order.service.OrderVideoFlowRecordService;
import com.wnkx.order.service.OrderVideoMatchService;
import com.wnkx.order.service.core.OrderVideoFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 视频订单流转至待完成
 *
 * <AUTHOR>
 * @date 2024/6/29 10:40
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoFlowUnFinished implements OrderVideoFlowService {
    private final RemoteService remoteService;
    private final IOrderVideoModelService orderVideoModelService;
    private final OrderVideoMatchService orderVideoMatchService;
    private final DelayQueueService delayQueueService;
    private final OrderVideoProperties orderVideoProperties;
    private final OrderVideoFlowRecordService orderVideoFlowRecordService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderVideoFlow(List<OrderVideo> orderVideos, String eventName) {
        //  ->待完成
        //  a:订单状态修改

        List<Long> shootModelIds = orderVideos.stream().map(OrderVideo::getShootModelId).collect(Collectors.toList());
        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(shootModelIds);
        List<OrderVideoFlowDTO> orderVideoFlowDTOS = new ArrayList<>();
        for (OrderVideo orderVideo : orderVideos) {
            orderVideoFlowDTOS.add(OrderVideoFlowDTO.builder().videoId(orderVideo.getId()).originStatus(orderVideo.getStatus()).targetStatus(OrderStatusEnum.UN_FINISHED.getCode()).build());

            orderVideo.setStatus(OrderStatusEnum.UN_FINISHED.getCode());
            orderVideo.setStatusTime(DateUtil.date());
            if (ObjectUtil.isNull(orderVideo.getIssueId())) {
                List<UserVO> persons = modelMap.getOrDefault(orderVideo.getShootModelId(), new ModelInfoVO()).getPersons();
                orderVideo.setIssueId(CollUtil.isNotEmpty(persons) ? persons.get(0).getId() : null);
            }
            orderVideo.setLogisticFlag(FlagEnum.NO_FLAG.getCode());
            orderVideo.setUnFinishedTime(DateUtil.date());
        }

        List<Long> videoIds = orderVideos.stream().map(OrderVideo::getId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        //  确定模特接单时间
        orderVideoModelService.acceptModelOrder(videoIds);

        //  更新模特选择记录状态
        orderVideoMatchService.updateModelSelectStatusToConfirmByVideoId(videoIds);

        // videoIds.forEach(item ->
                //  添加模特超时未上传素材队列
                // delayQueueService.addTaskIfAbsent(DelayQueueConstant.ORDER_MODEL_TIMEOUT_NOT_UPLOAD_QUEUE_NAME, item.toString(), orderVideoProperties.getOrderFeedbackOverdueTime(), TimeUnit.HOURS)
        // );
        orderVideoFlowRecordService.createOrderVideoFlow(eventName, orderVideoFlowDTOS);
    }

    @Override
    public OrderStatusEnum getOrderStatusType() {
        return OrderStatusEnum.UN_FINISHED;
    }
}
