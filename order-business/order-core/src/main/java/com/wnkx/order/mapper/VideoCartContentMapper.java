package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.VideoCartContent;
import com.wnkx.db.mapper.SuperMapper;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/1 14:43
 */
public interface VideoCartContentMapper extends SuperMapper<VideoCartContent> {

    /**
     * 通过购物车id查询关联内容
     */
    default List<VideoCartContent> selectListByCartId(Collection<Long> cartIds) {
        return this.selectList(new LambdaQueryWrapper<VideoCartContent>()
                .in(VideoCartContent::getVideoCartId, cartIds)
        );
    }

    /**
     * 通过购物车id删除关联内容
     */
    default void deleteByCartId(Collection<Long> cartIds) {
        this.delete(new LambdaQueryWrapper<VideoCartContent>()
                .in(VideoCartContent::getVideoCartId, cartIds)
        );
    }
}
