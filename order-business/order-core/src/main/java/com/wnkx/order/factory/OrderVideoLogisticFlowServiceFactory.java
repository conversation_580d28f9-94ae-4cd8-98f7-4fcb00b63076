package com.wnkx.order.factory;

import com.ruoyi.common.core.enums.FollowStatusEnum;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.wnkx.order.service.core.OrderVideoLogisticFlowService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.EnumMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :已发货
 * @create :2025-04-23 11:38
 **/
@Component
@RequiredArgsConstructor
public class OrderVideoLogisticFlowServiceFactory {

    private final EnumMap<FollowStatusEnum, OrderVideoLogisticFlowService> orderVideoLogisticFlowServiceMap = new EnumMap<>(FollowStatusEnum.class);

    private final ApplicationContext applicationContext;


    @PostConstruct
    public void init() {
        Map<String, OrderVideoLogisticFlowService> services = applicationContext.getBeansOfType(OrderVideoLogisticFlowService.class);
        for (OrderVideoLogisticFlowService service : services.values()) {
            orderVideoLogisticFlowServiceMap.put(service.getFollowStatusType(), service);
        }
    }

    public OrderVideoLogisticFlowService getOrderVideoLogisticFlowService(FollowStatusEnum orderStatus) {
        OrderVideoLogisticFlowService service = orderVideoLogisticFlowServiceMap.get(orderStatus);
        if (service == null) {
            throw new IllegalArgumentException("无效的跟进状态： " + orderStatus);
        }
        return service;
    }
}
