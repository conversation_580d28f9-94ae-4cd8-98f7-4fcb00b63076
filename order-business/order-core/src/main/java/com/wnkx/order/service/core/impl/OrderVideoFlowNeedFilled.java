package com.wnkx.order.service.core.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.system.api.domain.dto.order.OrderVideoFlowDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.IOrderVideoService;
import com.wnkx.order.service.OrderVideoFlowRecordService;
import com.wnkx.order.service.core.OrderVideoFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 视频订单流转至待发货
 *
 * <AUTHOR>
 * @date 2024/6/29 10:40
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoFlowNeedFilled implements OrderVideoFlowService {

    /**
     * 订单视频服务
     */
    private final IOrderVideoService orderVideoService;
    private final OrderVideoFlowRecordService orderVideoFlowRecordService;
    private final RemoteService remoteService;

    @Override
    public void orderVideoFlow(List<OrderVideo> orderVideos, String eventName) {
        //  ->待发货
        //  a:设置拍摄模特id（调用之前设置）
        //  b:订单状态修改
        //  c:设置出单人

        orderVideoService.checkVideoStatus(orderVideos, OrderStatusEnum.UN_MATCH);

        //  获取拍摄模特的信息
        List<Long> shootModelId = orderVideos.stream().map(OrderVideo::getShootModelId).collect(Collectors.toList());
        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(shootModelId);

        List<OrderVideoFlowDTO> orderVideoFlowDTOS = new ArrayList<>();
        for (OrderVideo orderVideo : orderVideos) {
            orderVideoFlowDTOS.add(OrderVideoFlowDTO.builder().videoId(orderVideo.getId()).originStatus(orderVideo.getStatus()).targetStatus(OrderStatusEnum.NEED_FILLED.getCode()).build());

            orderVideo.setStatus(OrderStatusEnum.NEED_FILLED.getCode());
            orderVideo.setStatusTime(DateUtil.date());

            List<UserVO> persons = modelMap.getOrDefault(orderVideo.getShootModelId(), new ModelInfoVO()).getPersons();
            if (ObjectUtil.isNull(orderVideo.getIssueId())){
                orderVideo.setIssueId(CollUtil.isNotEmpty(persons) ? persons.get(0).getId() : null);
            }
        }
        orderVideoFlowRecordService.createOrderVideoFlow(eventName, orderVideoFlowDTOS);
    }

    @Override
    public OrderStatusEnum getOrderStatusType() {
        return OrderStatusEnum.NEED_FILLED;
    }
}
