package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderBasePriceConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <AUTHOR>
 * @description 针对表【order_base_price_config(订单基础价格配置表)】的数据库操作Mapper
 * @createDate 2025-05-16 16:58:03
 * @Entity com.ruoyi.system.api.domain.entity.order.OrderBasePriceConfig
 */
public interface OrderBasePriceConfigMapper extends BaseMapper<OrderBasePriceConfig> {

    default void updateByType(OrderBasePriceConfig orderBasePriceConfig) {
        update(null, new LambdaUpdateWrapper<OrderBasePriceConfig>()
                .set(OrderBasePriceConfig::getOriginPrice, orderBasePriceConfig.getOriginPrice())
                .set(OrderBasePriceConfig::getOriginPriceProxy, orderBasePriceConfig.getOriginPriceProxy())
                .set(OrderBasePriceConfig::getCurrentPrice, orderBasePriceConfig.getCurrentPrice())
                .set(OrderBasePriceConfig::getCurrentPriceProxy, orderBasePriceConfig.getCurrentPriceProxy())
                .set(OrderBasePriceConfig::getSinceTime, orderBasePriceConfig.getSinceTime())
                .set(OrderBasePriceConfig::getCreateBy, orderBasePriceConfig.getCreateBy())
                .set(OrderBasePriceConfig::getCreateById, orderBasePriceConfig.getCreateById())
                .set(OrderBasePriceConfig::getUpdateBy, orderBasePriceConfig.getUpdateBy())
                .set(OrderBasePriceConfig::getUpdateById, orderBasePriceConfig.getUpdateById())
                .eq(OrderBasePriceConfig::getPriceType, orderBasePriceConfig.getPriceType())
        );
    }

    default OrderBasePriceConfig getCurrentConfigByte(Integer type) {
        return selectOne(new LambdaUpdateWrapper<OrderBasePriceConfig>().eq(OrderBasePriceConfig::getPriceType, type).last("limit 1"));
    }
}




