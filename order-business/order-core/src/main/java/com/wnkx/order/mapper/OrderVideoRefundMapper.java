package com.wnkx.order.mapper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.enums.InitiatorSourceEnum;
import com.ruoyi.common.core.enums.RefundStatusEnum;
import com.ruoyi.system.api.domain.dto.order.OrderVideoRefundListDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoRefundSuccessListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRefund;
import com.ruoyi.system.api.domain.vo.order.OrderVideoRefundSuccessVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoRefundStatisticsVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoRefundVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单_视频_退款Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@Mapper
public interface OrderVideoRefundMapper extends SuperMapper<OrderVideoRefund>
{
    /**
     * 退款订单列表
     *
     * @param orderVideoRefundListDTO 列表条件入参
     * @return 退款订单列表
     */
    public List<OrderVideoRefundVO> selectOrderVideoRefundListByCondition(OrderVideoRefundListDTO orderVideoRefundListDTO);

    /**
     * 判断能否申请退款
     *
     * @param videoId 视频订单id
     * @return true 不可以 false 可以
     */
    default Boolean checkCanApply(Long videoId) {
        return this.exists(new LambdaQueryWrapper<OrderVideoRefund>().eq(OrderVideoRefund::getVideoId, videoId)
                .in(OrderVideoRefund::getRefundStatus, RefundStatusEnum.AFTER_SALE_UN_CHECK.getCode(), RefundStatusEnum.AFTER_SALE.getCode(), RefundStatusEnum.AFTER_SALE_FINISHED.getCode()));
    }

    /**
     * 查询列表通过退款审批号
     *
     * @param refundNum 退款审批号
     */
    default List<OrderVideoRefund> getRefundByRefundNum(List<String> refundNum) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoRefund>().in(OrderVideoRefund::getRefundNum, refundNum));
    }

    /**
     * 判断是否有退款成功订单
     *
     * @param videoId 视频订单id
     * @return true 存在 false 不存在
     */
    default Boolean checkRefundSuccess(List<Long> videoId) {
        return this.exists(new LambdaQueryWrapper<OrderVideoRefund>().in(OrderVideoRefund::getVideoId, videoId)
                .eq(OrderVideoRefund::getRefundStatus, RefundStatusEnum.AFTER_SALE_FINISHED.getCode()));
    }

    /**
     * 判断订单类型是否有退款成功订单
     * @param videoId   视频订单id
     * @param refundTypes   订单类型
     * @return
     */
    default Boolean checkRefundTypeSuccess(List<Long> videoId, List<Integer> refundTypes) {
        return this.exists(new LambdaQueryWrapper<OrderVideoRefund>().in(OrderVideoRefund::getVideoId, videoId)
                .eq(OrderVideoRefund::getRefundStatus, RefundStatusEnum.AFTER_SALE_FINISHED.getCode())
                .in(CollUtil.isNotEmpty(refundTypes), OrderVideoRefund::getRefundType, refundTypes)
        );
    }

    /**
     * 判断当前退款订单状态可否退款
     *
     * @param refundId 退款订单id
     * @return true 不可退款 false 可退款
     */
    default Boolean checkCanRefund(List<Long> refundId) {
        return this.exists(new LambdaQueryWrapper<OrderVideoRefund>().in(OrderVideoRefund::getId, refundId)
                .ne(OrderVideoRefund::getRefundStatus, RefundStatusEnum.AFTER_SALE_UN_CHECK.getCode()));
    }

    /**
     * 校验订单是否符合状态
     *
     * @param id               退款订单id
     * @param afterSaleUnCheck 应该是什么状态
     * @return true 存在不符合 false 符合
     */
    default Boolean checkRefundStatus(List<Long> id, RefundStatusEnum... afterSaleUnCheck) {
        return this.exists(new LambdaQueryWrapper<OrderVideoRefund>().in(OrderVideoRefund::getId, id)
                .notIn(OrderVideoRefund::getRefundStatus, Arrays.stream(afterSaleUnCheck).map(RefundStatusEnum::getCode).collect(Collectors.toList())));
    }

    /**
     * 检查是否可以取消退款
     *
     * @param id         退款订单id
     * @param merchantId 商家id
     * @return true 不可以 false 可以
     */
    default Boolean checkCanCancel(List<Long> id, Long merchantId) {
        return this.exists(new LambdaQueryWrapper<OrderVideoRefund>().in(OrderVideoRefund::getId, id)
                .and(wrapper -> wrapper.ne(OrderVideoRefund::getInitiatorSource, InitiatorSourceEnum.MERCHANT.getCode())
                        .or().ne(OrderVideoRefund::getBusinessId, merchantId)
                )
        );
    }

    /**
     * 根据视频订单id查询退款列表
     */
    default List<OrderVideoRefund> selectOrderVideoRefundListByVideoId(Collection<Long> videoId) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoRefund>()
                .in(OrderVideoRefund::getVideoId, videoId)
                .orderByDesc(OrderVideoRefund::getApplyTime)
        );
    }

    /**
     * 查询有退款或者正在退款的视频订单id
     */
    List<Long> selectVideoIdByRefund(@Param("refundStatus") Map<String, Integer> getCodeMap,
                                     @Param("videoIds") List<Long> videoIds);

    /**
     * 计算退款金额总计
     * @param videoId
     * @return
     */
    BigDecimal getRefundTotalAmount(Long videoId);

    /**
     * 退款金额统计
     * @param videoIds
     * @return
     */
    List<OrderVideoRefundStatisticsVO> refundStatistics(@Param("videoIds")List<Long> videoIds);

    /**
     * 获取有效退款数据
     * @param videoIds
     * @return
     */
    default List<OrderVideoRefund> selectValidOrderVideoRefundListByVideoId(Collection<Long> videoIds){
        return this.selectList(new LambdaQueryWrapper<OrderVideoRefund>()
                .in(OrderVideoRefund::getVideoId, videoIds)
                .eq(OrderVideoRefund::getRefundStatus, RefundStatusEnum.AFTER_SALE_FINISHED.getCode())
        );
    }

    /**
     * 财务对账-退款记录列表
     */
    List<OrderVideoRefundSuccessVO> refundSuccessList(@Param("dto") OrderVideoRefundSuccessListDTO dto);

    default List<OrderVideoRefund> getOrderVideoRefundList(List<String> numbers) {
        return selectList(Wrappers.lambdaQuery(OrderVideoRefund.class).in(OrderVideoRefund::getRefundNum, numbers));
    }
}
