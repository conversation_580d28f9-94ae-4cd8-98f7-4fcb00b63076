package com.wnkx.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTaskWorkAssigneeHistory;
import com.wnkx.order.mapper.OrderVideoTaskWorkAssigneeHistoryMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.OrderVideoTaskWorkAssigneeHistoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/12/10 14:04
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoTaskWorkAssigneeHistoryServiceImpl extends ServiceImpl<OrderVideoTaskWorkAssigneeHistoryMapper, OrderVideoTaskWorkAssigneeHistory> implements OrderVideoTaskWorkAssigneeHistoryService {


    private final RemoteService remoteService;

    /**
     * 工单-历史处理人下拉框
     */
    @Override
    public List<UserVO> getHistoryAssigneeUserList() {
        Set<Long> historyAssigneeIds = baseMapper.selectHistoryAssigneeList();
        if (CollUtil.isEmpty(historyAssigneeIds)) {
            return Collections.emptyList();
        }

        return remoteService.getUserList(historyAssigneeIds);
    }
}
