package com.wnkx.order.mapper;

import com.ruoyi.system.api.domain.vo.order.OrderModelWorkbenchForMeListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ModelWorkbenchMapper {

    /**
     * 给我的
     */
    List<OrderModelWorkbenchForMeListVO> forMe(@Param("modelId") Long modelId,
                                               @Param("overTime") Integer overTime,
                                               @Param("videoId") Long videoId);

    /**
     * 模特端-首页-forMe列表数量统计
     */
    Long getForMeCount(@Param("modelId") Long modelId,
                       @Param("overTime") Integer overTime);
}
