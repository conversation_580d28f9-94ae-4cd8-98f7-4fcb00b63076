package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderVideoDataStatisticsDay;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR> 
 * @Date 2025-06-04 10:37:55 
 */
@Mapper
public interface OrderVideoDataStatisticsDayMapper extends SuperMapper<OrderVideoDataStatisticsDay> {

    /**
     * 通过日期查询客服数据
     */
    default OrderVideoDataStatisticsDay getByWriteTime(String date) {
        return selectOne(new LambdaQueryWrapper<OrderVideoDataStatisticsDay>()
                .last("WHERE DATE_FORMAT(write_time_begin,'%Y-%m-%d') = '" + date + "'")
        );
    }
}
