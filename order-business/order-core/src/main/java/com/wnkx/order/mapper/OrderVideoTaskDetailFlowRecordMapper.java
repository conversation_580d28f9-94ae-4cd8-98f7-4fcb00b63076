package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetailFlowRecord;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订单_工单任务_流转记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-24
 */
@Mapper
public interface OrderVideoTaskDetailFlowRecordMapper extends SuperMapper<OrderVideoTaskDetailFlowRecord>
{
    /**
     * 新增订单_工单任务_流转记录
     * 
     * @param orderVideoTaskDetailFlowRecord 订单_工单任务_流转记录
     * @return 结果
     */
    int insertOrderTaskFlowRecord(OrderVideoTaskDetailFlowRecord orderVideoTaskDetailFlowRecord);

    /**
     * 通过工单编号查询流转记录
     */
    default List<OrderVideoTaskDetailFlowRecord> selectOrderTaskFlowRecordListByTaskNum(String taskNum) {
        return this.selectList(new LambdaQueryWrapper<OrderVideoTaskDetailFlowRecord>()
                .eq(OrderVideoTaskDetailFlowRecord::getTaskNum, taskNum)
                .orderByAsc(OrderVideoTaskDetailFlowRecord::getTime));
    }
}
