package com.wnkx.order.mapper;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigInfoDTO;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfigInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【order_payee_account_config_info(收款人账号配置表)】的数据库操作Mapper
* @createDate 2024-12-17 16:04:06
* @Entity com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfigInfo
*/
public interface OrderPayeeAccountConfigInfoMapper extends BaseMapper<OrderPayeeAccountConfigInfo> {

    default OrderPayeeAccountConfigInfoDTO getByBankAccount(String appId) {
        OrderPayeeAccountConfigInfo orderPayeeAccountConfigInfo = selectOne(
                new LambdaQueryWrapper<OrderPayeeAccountConfigInfo>()
                        .eq(OrderPayeeAccountConfigInfo::getBankAccount, appId)
                        .last("limit 1")
        );
        return BeanUtil.copyProperties(orderPayeeAccountConfigInfo, OrderPayeeAccountConfigInfoDTO.class);
    }
}




