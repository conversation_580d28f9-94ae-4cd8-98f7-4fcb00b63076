package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivity;
import com.ruoyi.system.api.domain.vo.order.promotion.PromotionActivityVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【promotion_activity(活动信息表)】的数据库操作Mapper
 * @createDate 2025-03-19 15:50:19
 * @Entity com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivity
 */
public interface PromotionActivityMapper extends BaseMapper<PromotionActivity> {

    /**
     * 根据活动类型获取活动信息
     * @param type
     * @return
     */
    PromotionActivityVO getValidPromotionActivityByType(@Param("type") Integer type);

    /**
     * 获取进行中的活动列表
     */
    List<PromotionActivityVO> selectValidPromotionActivityList();

    /**
     * 检查当前商家是否满足每月首单立减
     */
    boolean checkCurrentBusinessSatisfyMonthFirstOrderDiscounted(@Param("businessId") Long businessId, @Param("bizUserId") Long bizUserId);

    /**
     * 根据活动类型获取活动
     */
    PromotionActivityVO getPromotionActivityByType(@Param("type") Integer type);
}




