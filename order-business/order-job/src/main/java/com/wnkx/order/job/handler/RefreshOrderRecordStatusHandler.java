package com.wnkx.order.job.handler;

import com.ruoyi.common.core.enums.AuditStatusEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.wnkx.order.service.IOrderService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/30
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RefreshOrderRecordStatusHandler {
    private final IOrderService orderService;

    /**
     * 更新订单入账状态
     */
    @XxlJob("refreshOrderRecordStatusHandler")
    @Transactional(rollbackFor = Exception.class)
    public void refreshOrderRecordStatusHandler() {
        XxlJobHelper.log("任务updateRecentOrderHandler执行中.....");
        //获取所有未入账、财务审核状态为审核通过的数据
        List<Order> orderList = orderService.lambdaQuery()
                .eq(Order::getIsRecord, StatusTypeEnum.NO.getCode())
                .eq(Order::getAuditStatus, AuditStatusEnum.APPROVE.getCode())
                .list();
        if (StringUtils.isEmpty(orderList)) {
            XxlJobHelper.log("不存在未入账数据.....");
            return;
        }
        List<Order> updatedOrderList = new ArrayList<>();

        for (Order order : orderList) {
            if (StringUtils.isNull(order.getRecordTime()) ||
                    // order.getRecordTime() = 2024-07-04 00:00:00
                    // date = 2024-07-04 06:00:00 NO before 2024-07-04 00:00:00  执行更新
                    // date = 2024-07-03 06:00:00 before 2024-07-04 00:00:00  return
                    // 时间没到 不需要更新
                    DateUtils.getDatePoorDay(DateUtils.getEndOfDay(order.getRecordTime()), new Date()) > 0) {
                continue;
            }
            Order updatedOrder = new Order();
            updatedOrder.setId(order.getId());
            updatedOrder.setIsRecord(StatusTypeEnum.YES.getCode());
            updatedOrderList.add(updatedOrder);
        }
        if (StringUtils.isEmpty(updatedOrderList)){
            XxlJobHelper.log("不存在到期未入账数据.....");
            return;
        }
        orderService.updateBatchById(updatedOrderList);
    }
}
