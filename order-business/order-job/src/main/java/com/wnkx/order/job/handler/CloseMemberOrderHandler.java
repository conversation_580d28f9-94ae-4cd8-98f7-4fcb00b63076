package com.wnkx.order.job.handler;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.OrderMemberStatusEnum;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.entity.order.OrderMember;
import com.wnkx.order.job.config.OrderMemberProperties;
import com.wnkx.order.service.IOrderMemberService;
import com.wnkx.order.service.IOrderService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 取消会员订单
 * @create :2024-10-12 17:36
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class CloseMemberOrderHandler {

    private final IOrderMemberService orderMemberService;

    private final OrderMemberProperties orderMemberProperties;

    private final IOrderService orderService;

    @XxlJob("closeMemberOrderHandler")
    public void closeMemberOrderHandler() {
        XxlJobHelper.log("任务closeMemberOrderHandler执行中.....");
        //查询30天前、未支付的订单
        List<OrderMember> orderMemberList = orderMemberService.list(new LambdaQueryWrapper<OrderMember>()
                .le(OrderMember::getCreateTime, DateUtils.addHours(DateUtils.getNowDate(), -orderMemberProperties.getCloseOrderHours()))
                .eq(OrderMember::getStatus, OrderMemberStatusEnum.UN_PAY.getCode())
        );
        if (CollUtil.isEmpty(orderMemberList)) {
            return;
        }
        for (OrderMember item : orderMemberList) {
            try {
                XxlJobHelper.log("订单：" + item.getOrderNum() + ", 创建时间：" + DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, item.getCreateTime()) + ", 状态：" + item.getStatus() + "，开始取消");
                orderService.cancelMemberOrder(item.getOrderNum());
            } catch (Exception e) {
                log.error("取消会员订单失败：{}", e);
            }
        }
    }
}
