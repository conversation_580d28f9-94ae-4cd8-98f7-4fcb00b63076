package com.wnkx.order.job.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.wnkx.order.service.IOrderService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 分配视频订单使用余额
 * @create :2024-10-12 17:36
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class AssignVideoBalanceHandler {
    private final IOrderService orderService;

    @XxlJob("assignVideoBalanceHandler")
    public void assignVideoBalanceHandler() {
        //获取所有未审核、使用余额大于0的数据
        List<Order> list = orderService.list(new LambdaQueryWrapper<Order>()
                .gt(Order::getUseBalance, 0)
                .in(Order::getAuditStatus, Arrays.asList(0, 2))
                .eq(Order::getOrderType, 0)
        );

        for (Order item : list) {
            orderService.assignVideoBalance(list, item.getUseBalance());
        }
    }
}
