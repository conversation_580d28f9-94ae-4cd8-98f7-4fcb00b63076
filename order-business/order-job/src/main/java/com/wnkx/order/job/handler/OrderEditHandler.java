package com.wnkx.order.job.handler;

import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum;
import com.ruoyi.common.core.enums.TaskDetailFlowRecordOperateByTypeEnum;
import com.ruoyi.common.core.enums.UploadLinkStatusEnum;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.domain.entity.order.*;
import com.wnkx.order.service.*;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 剪辑管理相关 XXL-JOB
 * <AUTHOR>
 * @date 2025/3/26 11:12
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class OrderEditHandler {

    private final OrderVideoUploadLinkService orderVideoUploadLinkService;
    private final OrderVideoUploadLinkRecordService orderVideoUploadLinkRecordService;
    private final OrderVideoFeedBackService orderVideoFeedBackService;
    private final IOrderVideoTaskDetailFlowRecordService orderVideoTaskDetailFlowRecordService;
    private final OrderVideoTaskDetailProcessRecordService orderVideoTaskDetailProcessRecordService;


    /**
     * (一次性)初始化order_video_upload_link.asin
     */
    @XxlJob("initOrderVideoUploadLinkAsin")
    public void initOrderVideoUploadLinkAsin() {
        XxlJobHelper.log("初始化order_video_upload_link.asin.....");

        List<OrderVideoUploadLink> list = orderVideoUploadLinkService.lambdaQuery()
                .isNull(OrderVideoUploadLink::getAsin)
                .list();

        if (CollUtil.isEmpty(list)) {
            return;
        }

        for (OrderVideoUploadLink orderVideoUploadLink : list) {
            orderVideoUploadLink.setAsin(StringUtils.extractAmazonGoodsId(orderVideoUploadLink.getNeedUploadLink()));
        }

        orderVideoUploadLinkService.updateBatchById(list);
    }

    /**
     * （一次性）重新设置评分
     */
    @XxlJob("resetOrderVideoUploadLinkScore")
    public void resetOrderVideoUploadLinkScore() {
        XxlJobHelper.log("重新设置评分.....");

        List<OrderVideoFeedBack> list = orderVideoFeedBackService.lambdaQuery()
                .isNotNull(OrderVideoFeedBack::getVideoScore)
                .list();

        if (CollUtil.isEmpty(list)) {
            return;
        }

        for (OrderVideoFeedBack orderVideoFeedBack : list) {
            orderVideoFeedBack.setVideoScore(orderVideoFeedBack.getVideoScore() * 2);
        }
        orderVideoFeedBackService.updateBatchById(list);
    }

    /**
     * （一次性）设置order_video_task_detail_flow_record和order_video_task_detail_process_record 操作类型为订单回退的 operate_by_type设为3
     */
    @XxlJob("resetOrderVideoTaskDetailFlowRecordAndOrderVideoTaskDetailProcessRecord")
    public void resetOrderVideoTaskDetailFlowRecordAndOrderVideoTaskDetailProcessRecord() {
        List<OrderVideoTaskDetailFlowRecord> list = orderVideoTaskDetailFlowRecordService.lambdaQuery()
                .eq(OrderVideoTaskDetailFlowRecord::getOperateType, OrderTaskDetailFlowOperateTypeEnum.ROLLBACK_ORDER.getCode())
                .list();

        for (OrderVideoTaskDetailFlowRecord orderVideoTaskDetailFlowRecord : list) {
            orderVideoTaskDetailFlowRecord.setOperateByType(TaskDetailFlowRecordOperateByTypeEnum.SYSTEM.getCode());
        }
        orderVideoTaskDetailFlowRecordService.updateBatchById(list);

        List<OrderVideoTaskDetailProcessRecord> processRecords = orderVideoTaskDetailProcessRecordService.lambdaQuery()
                .eq(OrderVideoTaskDetailProcessRecord::getOperateType, OrderTaskDetailFlowOperateTypeEnum.ROLLBACK_ORDER.getCode())
                .list();

        for (OrderVideoTaskDetailProcessRecord processRecord : processRecords) {
            processRecord.setOperateByType(TaskDetailFlowRecordOperateByTypeEnum.SYSTEM.getCode());
        }
        orderVideoTaskDetailProcessRecordService.updateBatchById(processRecords);
    }

    /**
     * （一次性）补全order_video_upload_link的上传记录
     */
    @XxlJob("orderVideoUploadLinkHistoryUploadRecord")
    public void orderVideoUploadLinkHistoryUploadRecord() {
        XxlJobHelper.log("补全order_video_upload_link的上传记录.....");
        List<OrderVideoUploadLink> list = orderVideoUploadLinkService.list();

        List<OrderVideoUploadLinkRecord> uploadLinkRecords = orderVideoUploadLinkRecordService.lambdaQuery()
                .list();

        Map<Long, List<OrderVideoUploadLinkRecord>> collect = uploadLinkRecords.stream().collect(Collectors.groupingBy(OrderVideoUploadLinkRecord::getUploadLinkId));

        List<OrderVideoUploadLinkRecord> saveRecords = new ArrayList<>();
        for (OrderVideoUploadLink orderVideoUploadLink : list) {
            List<OrderVideoUploadLinkRecord> records = collect.get(orderVideoUploadLink.getId());
            if (CollUtil.isNotEmpty(records)) {
                continue;
            }
            if (!UploadLinkStatusEnum.HAVE_ALREADY_UPLOADED.getCode().equals(orderVideoUploadLink.getStatus()) && !UploadLinkStatusEnum.HAVEN_T_UPLOADED.getCode().equals(orderVideoUploadLink.getStatus())) {
                continue;
            }

            OrderVideoUploadLinkRecord orderVideoUploadLinkRecord = new OrderVideoUploadLinkRecord();
            orderVideoUploadLinkRecord.setUploadLinkId(orderVideoUploadLink.getId());
            orderVideoUploadLinkRecord.setNeedUploadLink(orderVideoUploadLink.getNeedUploadLink());
            orderVideoUploadLinkRecord.setVideoTitle(orderVideoUploadLink.getVideoTitle());
            orderVideoUploadLinkRecord.setVideoCover(orderVideoUploadLink.getVideoCover());
            orderVideoUploadLinkRecord.setRemark(orderVideoUploadLink.getRemark());
            orderVideoUploadLinkRecord.setUploadLink(orderVideoUploadLink.getUploadLink());
            orderVideoUploadLinkRecord.setStatus(orderVideoUploadLink.getStatus());
            orderVideoUploadLinkRecord.setUploadUserId(orderVideoUploadLink.getUploadUserId());
            orderVideoUploadLinkRecord.setUploadUserName(orderVideoUploadLink.getUploadUserName());
            orderVideoUploadLinkRecord.setUploadTime(orderVideoUploadLink.getUploadTime());
            saveRecords.add(orderVideoUploadLinkRecord);
        }

        orderVideoUploadLinkRecordService.saveBatch(saveRecords);
    }

    /**
     * （一次性）初始化order_video_upload_link.video_title_first
     */
    @XxlJob("initOrderVideoFirstInfoVideoTitle")
    public void initOrderVideoFirstInfoVideoTitle() {
        List<OrderVideoUploadLinkRecord> list = orderVideoUploadLinkRecordService.lambdaQuery()
                .eq(OrderVideoUploadLinkRecord::getCount, 1)
                .list();
        if (CollUtil.isEmpty(list)) {
            return;
        }

        List<OrderVideoUploadLink> collect = list.stream().map(item -> {
            OrderVideoUploadLink orderVideoUploadLink = new OrderVideoUploadLink();
            orderVideoUploadLink.setId(item.getUploadLinkId());
            orderVideoUploadLink.setVideoTitleFirst(item.getVideoTitle());
            orderVideoUploadLink.setVideoCoverFirst(item.getVideoCover());
            return orderVideoUploadLink;
        }).collect(Collectors.toList());

        orderVideoUploadLinkService.updateBatchById(collect);
    }
}
