package com.wnkx.order.job.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoMatch;
import com.ruoyi.system.api.domain.entity.order.OrderVideoMatchPreselectModel;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.wnkx.order.config.OrderVideoProperties;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.IOrderVideoMatchPreselectModelService;
import com.wnkx.order.service.IOrderVideoService;
import com.wnkx.order.service.OrderVideoMatchService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单匹配相关XXL-JOB
 * <AUTHOR>
 * @date 2024/11/22
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class OrderVideoMatchHandler {

    private final IOrderVideoService orderVideoService;
    private final OrderVideoMatchService orderVideoMatchService;
    private final IOrderVideoMatchPreselectModelService orderVideoMatchPreselectModelService;
    private final RemoteService remoteService;
    private final OrderVideoProperties orderVideoProperties;

    /**
     * 淘汰已过期的预选模特
     */
    @XxlJob("weedingOutOverduePreselectModel")
    public void weedingOutOverduePreselectModel() {
        XxlJobHelper.log("任务[淘汰已过期的预选模特]执行中.....");
        DateTime date = DateUtil.date();
        long needOutCount = orderVideoMatchPreselectModelService.count(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .eq(OrderVideoMatchPreselectModel::getModelIntention, ModelIntentionEnum.MT_UN_CONFIRM.getCode())
                .eq(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.UN_JOINTED.getCode())
                .in(OrderVideoMatchPreselectModel::getAddType, PreselectModelAddTypeEnum.INTENTION_MODEL.getCode(), PreselectModelAddTypeEnum.OPERATION.getCode())
                .le(OrderVideoMatchPreselectModel::getSelectTimeout, date));
        if (needOutCount <= 0) {
            return;
        }
        List<OrderVideoMatchPreselectModel> needOutOrderVideoMatchPreselectModels = orderVideoMatchPreselectModelService.list(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .eq(OrderVideoMatchPreselectModel::getModelIntention, ModelIntentionEnum.MT_UN_CONFIRM.getCode())
                .eq(OrderVideoMatchPreselectModel::getStatus, PreselectStatusEnum.UN_JOINTED.getCode())
                .in(OrderVideoMatchPreselectModel::getAddType, PreselectModelAddTypeEnum.INTENTION_MODEL.getCode(), PreselectModelAddTypeEnum.OPERATION.getCode())
                .le(OrderVideoMatchPreselectModel::getSelectTimeout, date));

        for (OrderVideoMatchPreselectModel needOutOrderVideoMatchPreselectModel : needOutOrderVideoMatchPreselectModels) {
            needOutOrderVideoMatchPreselectModel.setStatus(PreselectStatusEnum.OUT.getCode());
            needOutOrderVideoMatchPreselectModel.setRemark(OrderConstant.ORDER_PRESELECT_MODEL_OVERTIME_REMARK);
            needOutOrderVideoMatchPreselectModel.setOustType(PreselectModelOustTypeEnum.TIMEOUT_INTENTION_NOT_SELECTED.getCode());
            needOutOrderVideoMatchPreselectModel.setOustTime(date);
            if (ModelIntentionEnum.MT_UN_CONFIRM.getCode().equals(needOutOrderVideoMatchPreselectModel.getModelIntention())) {
                needOutOrderVideoMatchPreselectModel.setModelIntention(ModelIntentionEnum.TIMEOUT_NOT_SELECTED.getCode());
            }
            needOutOrderVideoMatchPreselectModel.setProcessTime(date);
            needOutOrderVideoMatchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.EXPIRE.getCode());
            needOutOrderVideoMatchPreselectModel.setSelectTime(date);
        }
        orderVideoMatchPreselectModelService.updateBatchById(needOutOrderVideoMatchPreselectModels);
    }

    /**
     * （一次性）初始化order_video_match.carry_ignore的值
     */
    @XxlJob("initOrderVideoMatchCarryIgnore")
    public void initOrderVideoMatchCarryIgnore() {
        XxlJobHelper.log("任务[初始化order_video_match.carry_ignore的值]执行中.....");
        List<OrderVideoMatch> notCarryIgnoreMatch = orderVideoMatchService.getNotCarryIgnoreMatch();
        if (CollUtil.isEmpty(notCarryIgnoreMatch)) {
            return;
        }

        List<Long> videoIds = notCarryIgnoreMatch.stream().map(OrderVideoMatch::getVideoId).collect(Collectors.toList());
        List<OrderVideo> orderVideos = orderVideoService.listByIds(videoIds);
        Map<Long, OrderVideo> orderVideoMap = orderVideos.stream().collect(Collectors.toMap(OrderVideo::getId, Function.identity()));

        for (OrderVideoMatch carryIgnoreMatch : notCarryIgnoreMatch) {
            OrderVideo orderVideo = orderVideoMap.get(carryIgnoreMatch.getVideoId());
            if (ObjectUtil.isNull(orderVideo)) {
                continue;
            }

            carryIgnoreMatch.setCarryIgnore(orderVideo.getCarryIgnore());
        }

        orderVideoMatchService.updateBatchById(notCarryIgnoreMatch);

        XxlJobHelper.log("任务[初始化order_video_match.carry_ignore的值]执行结束.....");
        XxlJobHelper.log("本次更新的匹配单ID：{}", notCarryIgnoreMatch.stream().map(OrderVideoMatch::getId).collect(Collectors.toList()));
    }

    /**
     * （一次性）补充order_video_match_preselect_model模特快照数据
     */
    @XxlJob("fillOrderVideoMatchPreselectModelSnapshot")
    public void fillOrderVideoMatchPreselectModelSnapshot() {
        XxlJobHelper.log("任务[补充order_video_match_preselect_model模特快照数据]执行中.....");
        List<OrderVideoMatchPreselectModel> list = orderVideoMatchPreselectModelService.lambdaQuery()
                .ge(OrderVideoMatchPreselectModel::getAddTime, "2025-03-19 18:00:00")
                .isNull(OrderVideoMatchPreselectModel::getModelType)
                .list();

        if (CollUtil.isEmpty(list)) {
            return;
        }

        Set<Long> collect = list.stream().map(OrderVideoMatchPreselectModel::getModelId).collect(Collectors.toSet());
        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(collect);

        for (OrderVideoMatchPreselectModel matchPreselectModel : list) {
            ModelInfoVO modelInfoVO = modelMap.get(matchPreselectModel.getModelId());
            if (ObjectUtil.isNull(modelInfoVO)) {
                continue;
            }
            matchPreselectModel.setModelType(modelInfoVO.getType());
            matchPreselectModel.setModelPlatform(modelInfoVO.getPlatform());
            matchPreselectModel.setModelCooperation(modelInfoVO.getCooperation());
            matchPreselectModel.setModelCooperationScore(modelInfoVO.getCooperationScore());
            List<UserVO> persons = modelInfoVO.getPersons();
            if (CollUtil.isNotEmpty(persons)) {
                matchPreselectModel.setModelPersonId(persons.get(0).getId());
                matchPreselectModel.setModelPersonName(persons.get(0).getName());
            }
        }

        orderVideoMatchPreselectModelService.updateBatchById(list);
    }
}
