package com.wnkx.order.job.handler;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.system.api.domain.entity.order.AlipayOrderTable;
import com.ruoyi.system.api.domain.entity.order.WechatOrderTable;
import com.wnkx.order.mapper.AlipayOrderTableMapper;
import com.wnkx.order.mapper.WechatOrderTableMapper;
import com.wnkx.order.service.WeChatService;
import com.wnkx.order.service.core.AsyncTaskService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 关闭现有的订单
 *
 * <AUTHOR>
 * @date 2025/2/12
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CloseOpenOrderLinkHandler {
    private final AlipayOrderTableMapper alipayOrderTableMapper;
    private final WechatOrderTableMapper wechatOrderTableMapper;
    private final AsyncTaskService asyncTaskService;
    private final WeChatService weChatService;

    @XxlJob("closeOpenOrderLinkHandler")
    public void closeOpenOrderLink() {
        XxlJobHelper.log("开始关闭现有待支付订单.....");
        List<AlipayOrderTable> alipayOrderTables = alipayOrderTableMapper.unPayAlipayOrderList();
        List<WechatOrderTable> wechatOrderTables = wechatOrderTableMapper.unPayAlipayOrderList();
        XxlJobHelper.log("支付宝订单号列表：" + alipayOrderTables.stream().map(AlipayOrderTable::getOrderNum).collect(Collectors.joining(",")));
        XxlJobHelper.log("微信订单号列表：" + wechatOrderTables.stream().map(WechatOrderTable::getOrderNum).collect(Collectors.joining(",")));
//        关单
        if (CollUtil.isNotEmpty(alipayOrderTables)) {
            alipayOrderTables.forEach(alipayOrderTable -> {
                try {
                    asyncTaskService.closeAlipayOrder(alipayOrderTable.getMchntOrderNo(), alipayOrderTable.getAppId());
                } catch (Exception e) {
                    XxlJobHelper.log(String.format("关单失败，单号%s,对外单号:%s", alipayOrderTable.getOrderNum(), alipayOrderTable.getMchntOrderNo()));
                }
            });
            alipayOrderTableMapper.update(null, new LambdaUpdateWrapper<AlipayOrderTable>()
                    .set(AlipayOrderTable::getOrderNum, 0)
                    .in(AlipayOrderTable::getId, alipayOrderTables.stream().map(AlipayOrderTable::getId).collect(Collectors.toList()))
            );
        }
        if (CollUtil.isNotEmpty(wechatOrderTables)) {
            wechatOrderTables.forEach(wechatOrderTable -> {
                try {
                    weChatService.closeOrder(wechatOrderTable.getMchntOrderNo(), wechatOrderTable.getAppId());
                } catch (Exception e) {
                    XxlJobHelper.log(String.format("关单失败，单号%s,对外单号:%s", wechatOrderTable.getOrderNum(), wechatOrderTable.getMchntOrderNo()));
                }
            });
            wechatOrderTableMapper.update(null, new LambdaUpdateWrapper<WechatOrderTable>()
                    .set(WechatOrderTable::getOrderNum, 0)
                    .in(WechatOrderTable::getId, wechatOrderTables.stream().map(WechatOrderTable::getId).collect(Collectors.toList()))
            );
        }

    }
}
