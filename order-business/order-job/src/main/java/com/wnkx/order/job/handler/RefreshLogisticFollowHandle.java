package com.wnkx.order.job.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFlowDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFollowDTO;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollowRecord;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.ruoyi.system.api.domain.vo.LogisticInfoVO;
import com.ruoyi.system.api.domain.vo.LogisticVO;
import com.wnkx.order.job.config.OrderLogisticProperties;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.OrderVideoLogisticFollowRecordService;
import com.wnkx.order.service.OrderVideoLogisticFollowService;
import com.wnkx.order.service.core.OrderVideoLogisticCore;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :刷新当前汇率
 * @create :2025-04-25 13:59
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class RefreshLogisticFollowHandle {
    private final OrderVideoLogisticFollowService orderVideoLogisticFollowService;
    private final OrderVideoLogisticFollowRecordService orderVideoLogisticFollowRecordService;
    private final OrderVideoLogisticCore orderVideoLogisticCore;
    private final RedisService redisService;
    private final RemoteService remoteService;
    private final OrderLogisticProperties orderLogisticProperties;

    @XxlJob("refreshLogisticFollowHandle")
    public void refreshLogisticFollowHandle() {
        XxlJobHelper.log("任务refreshLogisticFollowHandle执行中.....");
        List<OrderVideoLogisticFollow> orderVideoLogisticFollows = orderVideoLogisticFollowService.queryListBase(OrderVideoLogisticFollowDTO.builder()
                .followStatusList(Arrays.asList(FollowStatusEnum.TEMP_HOLD.getCode(), FollowStatusEnum.NO_FOLLOW_NEED.getCode()))
                .build());
        if (CollUtil.isEmpty(orderVideoLogisticFollows)) {
            XxlJobHelper.log("任务refreshLogisticFollowHandle无无需处理跟进数据..................................................");
            return;
        }

        List<OrderVideoLogisticFollow> updateList = new ArrayList<>();
        //查询所有暂不处理数据
        for (OrderVideoLogisticFollow item : orderVideoLogisticFollows) {

            if (FollowStatusEnum.NO_FOLLOW_NEED.getCode().equals(item.getFollowStatus())) {
                if (ObjectUtil.isNotNull(item.getLogisticUpdateTime())
                        && DateUtils.getDayDifference(item.getLogisticUpdateTime(), DateUtils.getNowDate()) >= orderLogisticProperties.getNoFollowNeedRefreshDays()){
                    OrderVideoLogisticFollow orderVideoLogisticFollow = new OrderVideoLogisticFollow();
                    orderVideoLogisticFollow.setId(item.getId());
                    orderVideoLogisticFollow.setBusinessId(item.getBusinessId());
                    orderVideoLogisticFollow.setFollowStatus(FollowStatusEnum.NEED_FOLLOW_UP.getCode());
                    updateList.add(orderVideoLogisticFollow);
                }
                continue;
            }

            if (HandleStatusEnum.NOTIFIED.getCode().equals(item.getHandleStatus()) && DateUtils.getDayDifference(item.getNotifyTime(), DateUtils.getNowDate()) >= 5
                    || (HandleStatusEnum.URGE_SHIPPING_NOTIFIED.getCode().equals(item.getHandleStatus()) && DateUtils.getDayDifference(item.getNotifyTime(), DateUtils.getNowDate()) >= 3)
            ) {
                //进入【已通知】处理状态5天后 流转为 催发货提醒
                OrderVideoLogisticFlowDTO orderVideoLogisticFlowDTO = new OrderVideoLogisticFlowDTO();
                orderVideoLogisticFlowDTO.setBusinessId(item.getBusinessId());
                orderVideoLogisticFlowDTO.setMemberCode(item.getMemberCode());
                orderVideoLogisticFlowDTO.setVideoCode(item.getVideoCode());
                orderVideoLogisticFlowDTO.setVideoId(item.getId());
                orderVideoLogisticFlowDTO.setHandleStatus(HandleStatusEnum.URGE_SHIPPING_REMINDER);
                orderVideoLogisticFlowDTO.setOrderVideoLogisticFollow(item);
                try {
                    orderVideoLogisticCore.logisticFlow(orderVideoLogisticFlowDTO, FollowStatusEnum.NEED_HANDLE);
                    continue;
                } catch (Exception e) {
                    log.error("物流流转失败：{}", e.getMessage());
                }
            }
            if (HandleStatusEnum.NOTIFIED_CONFIRM_MODEL.getCode().equals(item.getHandleStatus()) && DateUtils.getDayDifference(item.getNotifyTime(), DateUtils.getNowDate()) >= 5
                    || (HandleStatusEnum.URGE_CONFIRM_MODEL_NOTIFIED.getCode().equals(item.getHandleStatus()) && DateUtils.getDayDifference(item.getNotifyTime(), DateUtils.getNowDate()) >= 3)
            ) {
                //进入【已通知】处理状态5天后 流转为 催发货提醒
                OrderVideoLogisticFlowDTO orderVideoLogisticFlowDTO = new OrderVideoLogisticFlowDTO();
                orderVideoLogisticFlowDTO.setBusinessId(item.getBusinessId());
                orderVideoLogisticFlowDTO.setMemberCode(item.getMemberCode());
                orderVideoLogisticFlowDTO.setVideoCode(item.getVideoCode());
                orderVideoLogisticFlowDTO.setVideoId(item.getId());
                orderVideoLogisticFlowDTO.setHandleStatus(HandleStatusEnum.URGE_CONFIRM_MODEL_UN_NOTIFIED);
                orderVideoLogisticFlowDTO.setOrderVideoLogisticFollow(item);
                try {
                    orderVideoLogisticCore.logisticFlow(orderVideoLogisticFlowDTO, FollowStatusEnum.NEED_HANDLE);
                    continue;
                } catch (Exception e) {
                    log.error("物流流转失败：{}", e.getMessage());
                }
            }
            if (HandleStatusEnum.NOTIFIED_SHIPPING.getCode().equals(item.getHandleStatus()) && DateUtils.getDayDifference(item.getNotifyTime(), DateUtils.getNowDate()) >= 5
                    || (HandleStatusEnum.NOTIFIED_SHIPPING_NOTIFIED.getCode().equals(item.getHandleStatus()) && DateUtils.getDayDifference(item.getNotifyTime(), DateUtils.getNowDate()) >= 3)
            ) {
                //进入【已通知】处理状态5天后 流转为 催发货提醒
                OrderVideoLogisticFlowDTO orderVideoLogisticFlowDTO = new OrderVideoLogisticFlowDTO();
                orderVideoLogisticFlowDTO.setBusinessId(item.getBusinessId());
                orderVideoLogisticFlowDTO.setMemberCode(item.getMemberCode());
                orderVideoLogisticFlowDTO.setVideoCode(item.getVideoCode());
                orderVideoLogisticFlowDTO.setVideoId(item.getId());
                orderVideoLogisticFlowDTO.setHandleStatus(HandleStatusEnum.NOTIFIED_SHIPPING_REMINDER);
                orderVideoLogisticFlowDTO.setOrderVideoLogisticFollow(item);
                try {
                    orderVideoLogisticCore.logisticFlow(orderVideoLogisticFlowDTO, FollowStatusEnum.NEED_HANDLE);
                    continue;
                } catch (Exception e) {
                    log.error("物流流转失败：{}", e.getMessage());
                }
            }

            if (HandleStatusEnum.DELAY_SHIPPING.getCode().equals(item.getHandleStatus()) && DateUtils.getDayDifference(item.getLogisticStartTime(), DateUtils.getNowDate()) >= 0) {
                //进入【已通知】处理状态5天后 流转为 催发货提醒
                OrderVideoLogisticFlowDTO orderVideoLogisticFlowDTO = new OrderVideoLogisticFlowDTO();
                orderVideoLogisticFlowDTO.setBusinessId(item.getBusinessId());
                orderVideoLogisticFlowDTO.setMemberCode(item.getMemberCode());
                orderVideoLogisticFlowDTO.setVideoCode(item.getVideoCode());
                orderVideoLogisticFlowDTO.setVideoId(item.getId());
                orderVideoLogisticFlowDTO.setIsDefaultLogisticStartTime(item.getIsDefaultLogisticStartTime());
                orderVideoLogisticFlowDTO.setHandleStatus(StatusTypeEnum.YES.getCode().equals(item.getIsDefaultLogisticStartTime()) ? HandleStatusEnum.DELAY_REMINDER_OTHER : HandleStatusEnum.DELAY_REMINDER);
                orderVideoLogisticFlowDTO.setOrderVideoLogisticFollow(item);
                try {
                    orderVideoLogisticCore.logisticFlow(orderVideoLogisticFlowDTO, FollowStatusEnum.NEED_HANDLE);
                    continue;
                } catch (Exception e) {
                    log.error("物流流转失败：{}", e.getMessage());
                }
            }
            if (HandleStatusEnum.DELAY_NOTIFIED.getCode().equals(item.getHandleStatus()) && DateUtils.getDayDifference(item.getNotifyTime(), DateUtils.getNowDate()) >= 3) {
                OrderVideoLogisticFlowDTO orderVideoLogisticFlowDTO = new OrderVideoLogisticFlowDTO();
                orderVideoLogisticFlowDTO.setBusinessId(item.getBusinessId());
                orderVideoLogisticFlowDTO.setMemberCode(item.getMemberCode());
                orderVideoLogisticFlowDTO.setVideoCode(item.getVideoCode());
                orderVideoLogisticFlowDTO.setVideoId(item.getId());
                orderVideoLogisticFlowDTO.setIsDefaultLogisticStartTime(item.getIsDefaultLogisticStartTime());
                orderVideoLogisticFlowDTO.setHandleStatus(StatusTypeEnum.YES.getCode().equals(item.getIsDefaultLogisticStartTime()) ? HandleStatusEnum.DELAY_REMINDER_OTHER : HandleStatusEnum.DELAY_REMINDER);
                orderVideoLogisticFlowDTO.setOrderVideoLogisticFollow(item);
                try {
                    orderVideoLogisticCore.logisticFlow(orderVideoLogisticFlowDTO, FollowStatusEnum.NEED_HANDLE);
                    continue;
                } catch (Exception e) {
                    log.error("物流流转失败：{}", e.getMessage());
                }

            }
            if (HandleStatusEnum.CHANGE_NOTIFIED.getCode().equals(item.getHandleStatus()) && DateUtils.getDayDifference(item.getNotifyTime(), DateUtils.getNowDate()) >= 3) {
                OrderVideoLogisticFlowDTO orderVideoLogisticFlowDTO = new OrderVideoLogisticFlowDTO();
                orderVideoLogisticFlowDTO.setBusinessId(item.getBusinessId());
                orderVideoLogisticFlowDTO.setMemberCode(item.getMemberCode());
                orderVideoLogisticFlowDTO.setVideoCode(item.getVideoCode());
                orderVideoLogisticFlowDTO.setVideoId(item.getId());
                orderVideoLogisticFlowDTO.setIsDefaultLogisticStartTime(item.getIsDefaultLogisticStartTime());
                orderVideoLogisticFlowDTO.setHandleStatus(HandleStatusEnum.URGE_SHIPPING_REMINDER);
                orderVideoLogisticFlowDTO.setOrderVideoLogisticFollow(item);
                try {
                    orderVideoLogisticCore.logisticFlow(orderVideoLogisticFlowDTO, FollowStatusEnum.NEED_HANDLE);
                    continue;
                } catch (Exception e) {
                    log.error("物流流转失败：{}", e.getMessage());
                }

            }

        }
        List<String> keys = new ArrayList<>();
        try {
            if (CollUtil.isNotEmpty(updateList)) {
                for (OrderVideoLogisticFollow item : updateList) {
                    String key = CacheConstants.ORDER_LOGISTIC_FOLLOW_FLOW_KEY + item.getBusinessId() + "_" + item.getId();
                    Assert.isTrue(redisService.getLock(key, 60L), "物流跟进流转中，请稍后重试！");
                    keys.add(key);
                }
            }
        } catch (Exception e) {
            //  异常 把加的锁释放掉
            for (String key : keys) {
                redisService.releaseLock(key);
            }
            throw new ServiceException("物流跟进流转中，请稍后重试！");
        }
        if (CollUtil.isNotEmpty(updateList)) {
            try {
                orderVideoLogisticFollowService.updateBatchById(updateList);
            } finally {
                for (String key : keys) {
                    redisService.releaseLock(key);
                }
            }
        }

        XxlJobHelper.log("任务refreshLogisticFollowHandle执行结束..................................................");
    }


    @XxlJob("refreshShipLogisticFollowHandle")
    public void refreshShipLogisticFollowHandle() {
        XxlJobHelper.log("任务refreshShipLogisticFollowHandle执行中.....");
        List<OrderVideoLogisticFollow> orderVideoLogisticFollows = orderVideoLogisticFollowService.queryListBase(OrderVideoLogisticFollowDTO.builder()
                .followStatusList(Arrays.asList(FollowStatusEnum.SHIP.getCode()))
                .build());
        if (CollUtil.isEmpty(orderVideoLogisticFollows)) {
            XxlJobHelper.log("任务refreshShipLogisticFollowHandle无无需处理跟进数据..................................................");
            return;
        }

        List<OrderVideoLogisticFollow> updateList = new ArrayList<>();
        List<String> numberList = new ArrayList<>();
        //查询所有暂不处理数据
        for (OrderVideoLogisticFollow item : orderVideoLogisticFollows) {
            if (DateUtils.getDatePoorSec(DateUtils.getNowDate(), item.getUpdateTime()) >= orderLogisticProperties.getShipRefreshSeconds()) {
                updateList.add(item);
                numberList.add(item.getNumber());
            }
        }
        if (CollUtil.isEmpty(numberList)){
            return;
        }
        LoginUserInfoVO loginUserInfoVo = new LoginUserInfoVO();
        loginUserInfoVo.setUserType(EventExecuteObjectEnum.SYSTEM.getCode());
        loginUserInfoVo.setName("system");

        Map<String, LogisticVO> logisticMap = remoteService.getLogisticMap(numberList);

        List<OrderVideoLogisticFollow> resultUpdateList = new ArrayList<>();
        List<OrderVideoLogisticFollowRecord> recordList = new ArrayList<>();
        for (OrderVideoLogisticFollow item : updateList) {
            OrderVideoLogisticFollow orderVideoLogisticFollow = new OrderVideoLogisticFollow();
            orderVideoLogisticFollow.setId(item.getId());
            orderVideoLogisticFollow.setBusinessId(item.getBusinessId());

            orderVideoLogisticFollow.setIsCallBack(StatusTypeEnum.YES.getCode());

            List<LogisticInfoVO> logisticInfo = logisticMap.getOrDefault(item.getNumber(), new LogisticVO()).getLogisticInfo();
            if (CollUtil.isEmpty(logisticInfo)) {
                orderVideoLogisticFollow.setLatestMainStatus(LogisticMainStatus.NOT_FOUND.getLabel());
                orderVideoLogisticFollow.setFollowStatus(FollowStatusEnum.NEED_FOLLOW_UP.getCode());
                orderVideoLogisticFollow.setLogisticUpdateTime(new Date());
            } else if (List.of(LogisticMainStatus.NOT_FOUND.getLabel(), LogisticMainStatus.EXPIRED.getLabel(),
                    LogisticMainStatus.EXCEPTION.getLabel()).contains(logisticInfo.get(0).getMainStatus())) {
                //物流状态为：查询不到、运输过久、可能异常 流转为需跟进
                orderVideoLogisticFollow.setLatestMainStatus(logisticInfo.get(0).getMainStatus());
                orderVideoLogisticFollow.setFollowStatus(FollowStatusEnum.NEED_FOLLOW_UP.getCode());
                orderVideoLogisticFollow.setLogisticUpdateTime(logisticInfo.get(0).getCurTime());
            } else if (List.of(LogisticMainStatus.AVAILABLE_FOR_PICKUP.getLabel(), LogisticMainStatus.DELIVERY_FAILURE.getLabel(),
                    LogisticMainStatus.DELIVERED.getLabel()).contains(logisticInfo.get(0).getMainStatus())) {
                //物流状态为：到达待取、投递失败、成功签收 流转为模特待确认
                orderVideoLogisticFollow.setLatestMainStatus(logisticInfo.get(0).getMainStatus());
                orderVideoLogisticFollow.setFollowStatus(FollowStatusEnum.MODEL_CONFIRM_PEND.getCode());
                orderVideoLogisticFollow.setModelResult(ModelResultEnum.PENDING.getCode());
                orderVideoLogisticFollow.setLogisticUpdateTime(logisticInfo.get(0).getCurTime());
//                if (LogisticMainStatus.DELIVERED.getLabel().equals(logisticInfo.get(0).getMainStatus())) {
//                    orderVideoLogisticFollow.setSignTime(new Date());
//                }
            } else{
                //物流状态为：运输中 流转为无需跟进
                orderVideoLogisticFollow.setLatestMainStatus(logisticInfo.get(0).getMainStatus());
                orderVideoLogisticFollow.setLogisticUpdateTime(logisticInfo.get(0).getCurTime());
                orderVideoLogisticFollow.setFollowStatus(FollowStatusEnum.NO_FOLLOW_NEED.getCode());
            }
            if (ObjectUtil.isNull(orderVideoLogisticFollow.getLogisticUpdateTime())){
                orderVideoLogisticFollow.setLogisticUpdateTime(new Date());
            }
            OrderVideoLogisticFollowRecord orderVideoLogisticFollowRecord = new OrderVideoLogisticFollowRecord();
            orderVideoLogisticFollowRecord.setFollowId(orderVideoLogisticFollow.getId());
            orderVideoLogisticFollowRecord.setEventName("新物流跟进：" + LogisticMainStatus.getSketchByLabel(orderVideoLogisticFollow.getLatestMainStatus()));
            orderVideoLogisticFollowRecord.setEventContent("生成新物流跟进记录");
            orderVideoLogisticFollowRecord.setEventExecuteObject(loginUserInfoVo.getUserType());
            orderVideoLogisticFollowRecord.setEventExecuteUserId(loginUserInfoVo.getUserId());
            orderVideoLogisticFollowRecord.setEventExecuteUserName(loginUserInfoVo.getName());
            orderVideoLogisticFollowRecord.setEventExecuteNickName(loginUserInfoVo.getNickName());
            orderVideoLogisticFollowRecord.setEventExecuteTime(new Date());
            //判断变成那种状态
            resultUpdateList.add(orderVideoLogisticFollow);
            recordList.add(orderVideoLogisticFollowRecord);
        }

        //远程获取所有物流状态
        List<String> keys = new ArrayList<>();
        try {
            for (OrderVideoLogisticFollow item : resultUpdateList) {
                String key = CacheConstants.ORDER_LOGISTIC_FOLLOW_FLOW_KEY + item.getBusinessId() + "_" + item.getId();
                Assert.isTrue(redisService.getLock(key, 60L), "物流跟进流转中，请稍后重试！");
                keys.add(key);
            }
        } catch (Exception e) {
            //  异常 把加的锁释放掉
            for (String key : keys) {
                redisService.releaseLock(key);
            }
            throw new ServiceException("物流跟进流转中，请稍后重试！");
        }
        try {
            orderVideoLogisticFollowService.updateBatchById(resultUpdateList);
            orderVideoLogisticFollowRecordService.saveBatch(recordList);
        } finally {
            for (String key : keys) {
                redisService.releaseLock(key);
            }
        }

        XxlJobHelper.log("任务refreshShipLogisticFollowHandle执行结束..................................................");
    }

    @XxlJob("refreshLogisticUpdateTimeHandle")
    public void refreshLogisticUpdateTimeHandle() {
        XxlJobHelper.log("任务refreshShipLogisticFollowHandle执行中.....");
        List<OrderVideoLogisticFollow> orderVideoLogisticFollows = orderVideoLogisticFollowService.queryListBase(OrderVideoLogisticFollowDTO.builder()
                .followStatusList(Arrays.asList(FollowStatusEnum.SHIP.getCode(),
                        FollowStatusEnum.NEED_FOLLOW_UP.getCode(),
                        FollowStatusEnum.MODEL_CONFIRM_PEND.getCode(),
                        FollowStatusEnum.NO_FOLLOW_NEED.getCode(),
                        FollowStatusEnum.CLOSE.getCode()))
                .build());
        if (CollUtil.isEmpty(orderVideoLogisticFollows)) {
            XxlJobHelper.log("任务refreshShipLogisticFollowHandle无无需处理跟进数据..................................................");
            return;
        }

        List<OrderVideoLogisticFollow> updateList = new ArrayList<>();
        List<String> numberList = new ArrayList<>();
        //查询所有暂不处理数据
        for (OrderVideoLogisticFollow item : orderVideoLogisticFollows) {
            //旧数据不处理手动修改数据
            if (StatusTypeEnum.NO.getCode().equals(item.getIsCallBack())){
                continue;
            }
            updateList.add(item);
            numberList.add(item.getNumber());
        }
        if (CollUtil.isEmpty(numberList)){
            return;
        }
        Map<String, LogisticVO> logisticMap = remoteService.getLogisticMap(numberList);

        List<OrderVideoLogisticFollow> resultUpdateList = new ArrayList<>();
        for (OrderVideoLogisticFollow item : updateList) {
            OrderVideoLogisticFollow orderVideoLogisticFollow = new OrderVideoLogisticFollow();
            orderVideoLogisticFollow.setId(item.getId());
            orderVideoLogisticFollow.setBusinessId(item.getBusinessId());

            List<LogisticInfoVO> logisticInfo = logisticMap.getOrDefault(item.getNumber(), new LogisticVO()).getLogisticInfo();
            if (CollUtil.isNotEmpty(logisticInfo)) {
                orderVideoLogisticFollow.setLogisticUpdateTime(logisticInfo.get(0).getCurTime());
                resultUpdateList.add(orderVideoLogisticFollow);
            }
        }

        //远程获取所有物流状态
        List<String> keys = new ArrayList<>();
        try {
            for (OrderVideoLogisticFollow item : resultUpdateList) {
                String key = CacheConstants.ORDER_LOGISTIC_FOLLOW_FLOW_KEY + item.getBusinessId() + "_" + item.getId();
                Assert.isTrue(redisService.getLock(key, 60L), "物流跟进流转中，请稍后重试！");
                keys.add(key);
            }
        } catch (Exception e) {
            //  异常 把加的锁释放掉
            for (String key : keys) {
                redisService.releaseLock(key);
            }
            throw new ServiceException("物流跟进流转中，请稍后重试！");
        }
        try {
            orderVideoLogisticFollowService.updateBatchById(resultUpdateList);
        } finally {
            for (String key : keys) {
                redisService.releaseLock(key);
            }
        }

        XxlJobHelper.log("任务refreshShipLogisticFollowHandle执行结束..................................................");
    }

}
