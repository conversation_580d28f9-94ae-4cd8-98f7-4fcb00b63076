package com.wnkx.order.job.handler;

import com.wnkx.order.service.OrderVideoMatchService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create :2025-04-25 13:59
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class RefreshModelAllSortHandle {
    private final OrderVideoMatchService orderVideoMatchService;

    /**
     * 刷新所有模特的排序
     */
    @XxlJob("refreshModelAllSortHandle")
    public void refreshModelAllSortHandle() {
        XxlJobHelper.log("任务refreshModelAllSortHandle执行中.....");
        orderVideoMatchService.refreshModelAllSort();
    }

}
