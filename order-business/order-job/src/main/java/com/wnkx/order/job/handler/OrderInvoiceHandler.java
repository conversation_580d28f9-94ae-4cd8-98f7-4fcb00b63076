package com.wnkx.order.job.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderInvoice;
import com.wnkx.order.service.IOrderInvoiceService;
import com.wnkx.order.service.IOrderService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class OrderInvoiceHandler {
    private final IOrderInvoiceService orderInvoiceService;
    private final IOrderService orderService;

    @XxlJob("initAuditUser")
    public void initAuditUser() {
        List<OrderInvoice> list = orderInvoiceService.list(new LambdaQueryWrapper<OrderInvoice>()
                .select(OrderInvoice::getOrderNum)
                .eq(OrderInvoice::getAuditById, 0)
        );
        List<String> orderNumberList = list.stream().map(OrderInvoice::getOrderNum).collect(Collectors.toList());
        Map<String, Long> collect = orderService.list(new LambdaQueryWrapper<Order>()
                .select(Order::getOrderNum, Order::getAuditUserId)
                .in(Order::getOrderNum, orderNumberList)
                .isNotNull(Order::getAuditUserId)
        ).stream().collect(Collectors.toMap(Order::getOrderNum, Order::getAuditUserId));
        XxlJobHelper.log("订单id" + Arrays.toString(collect.keySet().toArray()));
        list.forEach(orderInvoice -> {
            orderInvoiceService.update(null, new LambdaUpdateWrapper<OrderInvoice>()
                    .eq(OrderInvoice::getOrderNum, orderInvoice.getOrderNum())
                    .set(collect.get(orderInvoice.getOrderNum()) != 0, OrderInvoice::getAuditById, collect.get(orderInvoice.getOrderNum())));
            XxlJobHelper.log(String.format("更新订单%s,的审核人为%s", orderInvoice.getOrderNum(), collect.get(orderInvoice.getOrderNum())));
        });


    }
}
