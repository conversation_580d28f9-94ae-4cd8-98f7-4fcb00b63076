package com.wnkx.order.job.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionChannelOrderBatchDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionChannelOrderDTO;
import com.ruoyi.system.api.domain.dto.order.OrderListDTO;
import com.ruoyi.system.api.domain.vo.order.OrderListVO;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.IOrderService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 保存渠道会员订单
 * @create :2024-10-12 17:36
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class SaveDistributionChannelOrderHandler {

    private final IOrderService orderService;

    private final RemoteService remoteService;

    @XxlJob("saveDistributionChannelOrderHandler")
    public void saveMemberValidityFlowHandler() {
        XxlJobHelper.log("任务saveDistributionChannelOrderHandler执行中.....");
        OrderListDTO orderListDTO = new OrderListDTO();
        orderListDTO.setAuditStatus(AuditStatusEnum.APPROVE.getCode());
        DistributionChannelOrderBatchDTO dto = new DistributionChannelOrderBatchDTO();
        String param = XxlJobHelper.getJobParam();
        Integer isAll = 0;
        if (ObjectUtil.isNotNull(param)){
            isAll = Convert.toInt(param);
        }
        XxlJobHelper.log("接收調度中心参数...[{}]",param);
        if (StatusTypeEnum.YES.getCode().equals(isAll)) {
            orderListDTO.setPayTimeBegin(null);
            orderListDTO.setPayTimeEnd(null);
            dto.setIsAll(isAll);
        } else {
            orderListDTO.setPayTimeBegin(DateUtils.getStartOfToday());
            orderListDTO.setPayTimeEnd(DateUtils.getEndOfToday());
        }
        List<OrderListVO> orderList = orderService.getOrderList(orderListDTO);
        if (CollUtil.isNotEmpty(orderList)) {
            List<DistributionChannelOrderDTO> distributionChannelOrderDTOS = new ArrayList<>();
            for (OrderListVO item : orderList) {
                XxlJobHelper.log("订单：{}，加入数据", item.toString());
                DistributionChannelOrderDTO distributionChannelOrderDTO = new DistributionChannelOrderDTO();
                distributionChannelOrderDTO.setBusinessId(item.getMerchantId());
                distributionChannelOrderDTO.setOrderNum(item.getOrderNum());
                distributionChannelOrderDTO.setOrderAmount(item.getPayAmount());
                distributionChannelOrderDTO.setRealPayAmount(item.getRealPayAmount());
                distributionChannelOrderDTO.setPayTime(item.getPayTime());
                distributionChannelOrderDTOS.add(distributionChannelOrderDTO);
            }
            dto.setList(distributionChannelOrderDTOS);
            remoteService.saveBatchDistributionChannelOrder(dto);
        }

    }
}
