package com.wnkx.order.job.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-05-12 10:35
 **/
@Component
@ConfigurationProperties(prefix = "order.logistic")
@Data
@RefreshScope
public class OrderLogisticProperties {

    /**
     * 无需跟进未处理自动更新时间 单位（天）
     */
    private Integer noFollowNeedRefreshDays;

    /**
     * 已发货未处理自动更新时间 单位（秒）
     */
    private Integer shipRefreshSeconds;
}
