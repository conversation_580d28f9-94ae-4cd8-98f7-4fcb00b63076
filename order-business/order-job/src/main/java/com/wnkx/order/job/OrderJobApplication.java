package com.wnkx.order.job;

import com.ruoyi.common.security.annotation.EnableCustomConfig;
import com.ruoyi.common.security.annotation.EnableRyFeignClients;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/6/30
 */
@EnableDiscoveryClient
@EnableTransactionManagement
@MapperScan(basePackages = {"com.wnkx.order.**.mapper"})
@SpringBootApplication(scanBasePackages = {"com.wnkx.order"})
@EnableRyFeignClients
@RestController
@EnableCustomConfig
public class OrderJobApplication {
    public static void main(String[] args) {
        SpringApplication.run(OrderJobApplication.class, args);
    }
    @GetMapping("/hello")
    public String hello()
    {
        return "hello";
    }
}
