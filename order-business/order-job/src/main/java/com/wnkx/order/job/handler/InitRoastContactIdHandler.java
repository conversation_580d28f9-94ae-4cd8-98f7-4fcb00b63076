package com.wnkx.order.job.handler;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRoast;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.wnkx.order.mapper.OrderVideoRoastMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.OrderVideoRoastService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/11/20 17:09
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class InitRoastContactIdHandler {
    private final OrderVideoRoastService orderVideoRoastService;

    private final RemoteService remoteService;

    /**
     * 补全order_video_roast系统订单 contact_id
     */
    @XxlJob("initRoastContactIdHandler")
    public void initRoastContactIdHandler() {
        List<OrderVideoRoast> orderVideoRoasts = orderVideoRoastService.list(new LambdaQueryWrapper<OrderVideoRoast>()
                .eq(OrderVideoRoast::getRoastType, StatusTypeEnum.YES.getCode())
                .isNull(OrderVideoRoast::getContactId)
        );
        if (CollUtil.isEmpty(orderVideoRoasts)){
            return;
        }
        List<Long> businessIds = orderVideoRoasts.stream().map(OrderVideoRoast::getBusinessId).collect(Collectors.toList());
        final List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchant(BusinessAccountDetailDTO.builder().isOwnerAccount(StatusTypeEnum.YES.getCode())
                .businessIds(businessIds).build());
        if (CollUtil.isEmpty(businessAccountDetailVOS)){
            return;
        }
        Map<Long, BusinessAccountDetailVO> businessMap = businessAccountDetailVOS.stream().collect(Collectors.toMap(BusinessAccountDetailVO::getBusinessId, Function.identity()));

        List<OrderVideoRoast> updateList = new ArrayList<>();
        for (OrderVideoRoast item :orderVideoRoasts){
            OrderVideoRoast update = new OrderVideoRoast();
            update.setId(item.getId());
            update.setContactId(businessMap.get(item.getBusinessId()).getWaiterId());
            updateList.add(update);
        }
        orderVideoRoastService.updateBatchById(updateList);

    }

}
