package com.wnkx.order.job.handler;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.system.api.domain.vo.order.RealTimeExchangeRateVO;
import com.wnkx.order.service.ExchangeRateService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :刷新当前汇率
 * @create :2025-02-10 13:59
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class RefreshCurrentExchangeHandle {
    private final ExchangeRateService exchangeRateService;

    @XxlJob("refreshCurrentExchangeHandle")
    public void refreshCurrentExchangeHandle() {
        XxlJobHelper.log("任务refreshCurrentExchangeHandle执行中.....");
        log.info("刷新当前汇率开始");
        RealTimeExchangeRateVO currentExchange = exchangeRateService.getRealTimeExchangeRate();
        if (ObjectUtil.isNotNull(currentExchange.isDefault()) && currentExchange.isDefault()) {
            XxlJobHelper.log("任务refreshCurrentExchangeHandle失败再次执行执行中.....");
            log.warn("定时任务刷新汇率失败！");
            exchangeRateService.getRealTimeExchangeRate();
        }
        XxlJobHelper.log("任务refreshCurrentExchangeHandle执行结束..................................................");
    }

}
