package com.wnkx.order.job.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.enums.MemberValidTypeEnum;
import com.ruoyi.common.core.enums.OrderMemberStatusEnum;
import com.ruoyi.common.core.enums.PackageTypeEnum;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.RemoteBusinessAccountService;
import com.ruoyi.system.api.domain.dto.biz.business.BatchBusinessMemberValidityFlowDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberValidityFlow;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderMember;
import com.wnkx.order.job.config.OrderMemberProperties;
import com.wnkx.order.service.IOrderMemberService;
import com.wnkx.order.service.IOrderService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 取消会员订单
 * @create :2024-10-12 17:36
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class SaveMemberValidityFlowHandler {

    private final IOrderMemberService orderMemberService;

    private final OrderMemberProperties orderMemberProperties;

    private final RemoteBusinessAccountService remoteBusinessAccountService;

    private final IOrderService orderService;

    @XxlJob("saveMemberValidityFlowHandler")
    public void saveMemberValidityFlowHandler() {
        XxlJobHelper.log("任务saveMemberValidityFlowHandler执行中.....");
        //查询30天前、未支付的订单
        List<OrderMember> orderMemberList = orderMemberService.list(new LambdaQueryWrapper<OrderMember>()
                .eq(OrderMember::getStatus, OrderMemberStatusEnum.UN_CONFIRM.getCode())
        );
        if (CollUtil.isEmpty(orderMemberList)) {
            return;
        }
        List<String> orderNums = orderMemberList.stream().map(OrderMember::getOrderNum).collect(Collectors.toList());
        List<Order> orders = orderService.list(new LambdaQueryWrapper<Order>()
                .in(Order::getOrderNum, orderNums));

        Map<String, Order> orderMap = orders.stream().collect(Collectors.toMap(Order::getOrderNum, p -> p));


        List<BusinessMemberValidityFlow> list = new ArrayList<>();

        for (OrderMember item : orderMemberList) {
            Order order = orderMap.get(item.getOrderNum());
            if (ObjectUtil.isNull(order)) {
                return;
            }

            int daysOfMonth = DateUtils.getDaysOfMonth(item.getMemberStartTime());
            Integer presentedTime = 0;
            if (daysOfMonth <= 20 && item.getPackageType().equals(PackageTypeEnum.YEAR.getCode())) {
                presentedTime = 1;
            }

            Date endTime = DateUtils.addMonths(item.getMemberStartTime(), presentedTime + PackageTypeEnum.getMonthByCode(item.getPackageType()));
            list.add(BusinessMemberValidityFlow.builder()
                    .businessId(order.getMerchantId())
                    .originMemberValidity(item.getMemberStartTime())
                    .orderNum(item.getOrderNum())
                    .resultMemberValidity(endTime)
                    .presentedTime(presentedTime)
                    .type(MemberValidTypeEnum.USER.getCode())
                    .memberPackageType(item.getPackageType())
                    .remark("")
                    .currency(order.getCurrency())
                    .realPayAmount(order.getRealPayAmount())
                    .realPayAmountCurrency(order.getRealPayAmountCurrency())
                    .currency(order.getCurrency())
                    .createBy(StrUtil.isNotBlank(order.getAuditUserName()) ? order.getAuditUserName() : "商家购买")
                    .createById(Optional.ofNullable(order.getAuditUserId()).orElse(0L))
                    .createTime(Optional.ofNullable(order.getAuditTime()).orElse(order.getRecordTime()))
                    .build());
            item.setMemberEndTime(endTime);
            item.setPresentedTime(presentedTime);
        }
        orderMemberService.updateBatchById(orderMemberList);
        remoteBusinessAccountService.saveBusinessMemberValidityFlow(BatchBusinessMemberValidityFlowDTO.builder().list(list).build(), SecurityConstants.INNER);
    }
}
