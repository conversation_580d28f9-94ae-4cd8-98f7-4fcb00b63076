package com.wnkx.order.job.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.OrderTypeEnum;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.IOrderService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description : 初始化订单种草官会员状态
 * @create :2025-07-11
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class InitOrderSeedMemberStatusHandler {

    private final IOrderService orderService;
    private final RemoteService remoteService;

    @XxlJob("initOrderSeedMemberStatusHandler")
    public void initOrderSeedMemberStatusHandler() {
        XxlJobHelper.log("任务initOrderSeedMemberStatusHandler执行中.....");
        List<Order> list = orderService.list(new LambdaQueryWrapper<Order>()
                .isNotNull(Order::getSeedId)
                .isNull(Order::getSeedMemberStatus)
                .eq(Order::getOrderType, OrderTypeEnum.VIP_ORDER.getCode())
        );

        if (CollUtil.isEmpty(list)) {
            XxlJobHelper.log("没有需要初始化种草官会员状态的订单。");
            return;
        }

        List<String> seedIds = list.stream()
                .map(Order::getSeedId)
                .distinct()
                .collect(Collectors.toList());

        Map<String, Integer> seedMemberStatusMap = remoteService.getUserMemberStatusBySeedId(seedIds);

        List<Order> ordersToUpdate = new ArrayList<>();
        for (Order item : list) {
            try {
                Integer memberStatus = seedMemberStatusMap.get(item.getSeedId());
                if (memberStatus != null) {
                    XxlJobHelper.log("订单：" + item.getOrderNum() + ", seedId：" + item.getSeedId() + ", 种草官会员状态：" + memberStatus + "，开始修改。");
                    Order order = new Order();
                    order.setId(item.getId());
                    order.setSeedMemberStatus(memberStatus);
                    ordersToUpdate.add(order);
                }
            } catch (Exception e) {
                log.error("修改订单[{}]种草官会员状态失败：", item.getOrderNum(), e);
            }
        }

        if (CollUtil.isNotEmpty(ordersToUpdate)) {
            orderService.updateBatchById(ordersToUpdate);
            XxlJobHelper.log("成功更新 " + ordersToUpdate.size() + " 条订单的种草官会员状态。");
        } else {
            XxlJobHelper.log("没有订单的种草官会员状态需要更新。 ");
        }
    }
}
