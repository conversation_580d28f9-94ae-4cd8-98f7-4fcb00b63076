package com.wnkx.order.job.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.common.core.enums.OrderTypeEnum;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.dto.order.OrderListDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.vo.order.OrderListVO;
import com.wnkx.order.config.OrderVideoProperties;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.IOrderService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 取消会员订单
 * @create :2024-10-12 17:36
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class InitOrderSeedCodeHandler {

    private final IOrderService orderService;

    private final RemoteService remoteService;

    @XxlJob("initOrderSeedCodeHandler")
    public void initOrderSeedCodeHandler() {
        XxlJobHelper.log("任务initOrderSeedCodeHandler执行中.....");
        List<Order> list = orderService.list(new LambdaQueryWrapper<Order>()
                .isNotNull(Order::getSeedCode)
                .eq(Order::getOrderType, OrderTypeEnum.VIP_ORDER.getCode())
                .ne(Order::getSeedCode, "")
                .eq(Order::getChannelName, "")
        );
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<String> seedCodes = list.stream().map(Order::getSeedCode).distinct().collect(Collectors.toList());
        List<DistributionChannel> distributionChannels = remoteService.queryDistributionChannelsBySeedCodes(seedCodes);
        if (CollUtil.isEmpty(distributionChannels)){
            return;
        }
        Map<String, String> distributionChannelMap = distributionChannels.stream().collect(Collectors.toMap(DistributionChannel::getSeedCode, DistributionChannel::getChannelName));
        List<Order> listOrder = new ArrayList<>();
        for (Order item : list) {
            try {
                String channelName = distributionChannelMap.getOrDefault(item.getSeedCode(), "");
                if (StrUtil.isBlank(channelName)){
                    continue;
                }
                XxlJobHelper.log("订单：" + item.getOrderNum() + ", seedCode：" + item.getSeedCode() + ", channelName：" + item.getChannelName() + "，开始修改渠道名称");
                Order order = new Order();
                order.setId(item.getId());
                order.setSettleRage(item.getPayAmount().subtract(item.getTaxPointCost()).multiply(new BigDecimal(100)).divide(item.getOrderAmount(), 2, RoundingMode.DOWN));
                order.setChannelName(channelName);
                listOrder.add(order);
            } catch (Exception e) {
                log.error("修改订单渠道名称失败：", e);
            }
        }
        orderService.updateBatchById(listOrder);
    }
}
