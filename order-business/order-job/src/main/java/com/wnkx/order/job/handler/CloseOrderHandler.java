package com.wnkx.order.job.handler;

import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.dto.order.OrderListDTO;
import com.ruoyi.system.api.domain.vo.order.OrderListVO;
import com.wnkx.order.config.OrderVideoProperties;
import com.wnkx.order.service.IOrderService;
import com.wnkx.order.service.OrderMergeService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 取消会员订单
 * @create :2024-10-12 17:36
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class CloseOrderHandler {

    private final OrderVideoProperties orderVideoProperties;

    private final IOrderService orderService;
    private final OrderMergeService orderMergeService;

    @XxlJob("closeOrderHandler")
    public void closeOrderHandler() {
        XxlJobHelper.log("任务closeOrderHandler执行中.....");
        //查询n天前、未支付的订单
        List<OrderListVO> orderList = orderService.getOrderList(OrderListDTO.builder()
                .status(List.of(OrderStatusEnum.UN_PAY.getCode()))
                .orderTimeSignBegin(DateUtils.addYears(DateUtils.getNowDate(), -1))
                .orderTimeSignEnd(DateUtils.addHours(DateUtils.getNowDate(), -orderVideoProperties.getCloseOrderHours()))
                .build());

        if (CollUtil.isEmpty(orderList)) {
            return;
        }
        for (OrderListVO item : orderList) {
            try {
                try {
                    orderMergeService.checkOrderMerge(List.of(item.getOrderNum()));
                } catch (IllegalArgumentException e) {
                    XxlJobHelper.log("订单：" + item.getOrderNum() + ", 创建时间：" + DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, item.getOrderTime()) + ", 状态：" + item.getAuditStatus() + "，合并订单，跳过");
                    continue;
                }

                XxlJobHelper.log("订单：" + item.getOrderNum() + ", 创建时间：" + DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, item.getOrderTime()) + ", 状态：" + item.getAuditStatus() + "，开始取消");
                orderService.cancelOrder(item.getOrderNum(), false);
            } catch (Exception e) {
                log.error("取消订单失败：{}", e);
            }
        }
    }
}
