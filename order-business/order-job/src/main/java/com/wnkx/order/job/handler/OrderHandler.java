package com.wnkx.order.job.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.dto.order.AsyncCrawlProductPicDTO;
import com.ruoyi.system.api.domain.dto.order.AsyncCrawlTask;
import com.ruoyi.system.api.domain.dto.order.OrderVideoOperateDTO;
import com.ruoyi.system.api.domain.entity.order.*;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedCompleteCountInfo;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedOustModelCountInfo;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceDataStatisticsDay;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceDataStatisticsMonth;
import com.ruoyi.system.api.domain.entity.order.promotion.OrderPromotionDetail;
import com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivity;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.wnkx.order.config.AnotherPayProperties;
import com.wnkx.order.config.OrderVideoProperties;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单相关XXL-JOB
 * <AUTHOR>
 * @date 2024/8/14 17:06
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class OrderHandler {
    private final RedisService redisService;
    private final IOrderVideoService orderVideoService;
    private final VideoCartService videoCartService;
    private final RemoteService remoteService;
    private final OrderAnotherPayService orderAnotherPayService;
    private final AnotherPayProperties anotherPayProperties;
    private final IOrderMemberService orderMemberService;
    private final OrderMergeService orderMergeService;
    private final OrderVideoProperties orderVideoProperties;
    private final OrderVideoMatchService orderVideoMatchService;
    private final OrderVideoModelChangeService orderVideoModelChangeService;
    private final OrderVideoOperateService orderVideoOperateService;
    private final OrderVideoFeedBackService orderVideoFeedBackService;
    private final OrderVideoTaskService orderVideoTaskService;
    private final IOrderVideoTaskDetailService orderVideoTaskDetailService;
    private final IOrderService orderService;
    private final OrderVideoUploadLinkService orderVideoUploadLinkService;
    private final OrderVideoFlowNodeDiagramService orderVideoFlowNodeDiagramService;
    private final OrderVideoFeedBackMaterialInfoTaskDetailService orderVideoFeedBackMaterialInfoTaskDetailService;
    private final OrderVideoFeedBackMaterialService orderVideoFeedBackMaterialService;
    private final PromotionActivityService promotionActivityService;
    private final OrderPromotionDetailService orderPromotionDetailService;
    private final CustomerServiceDataStatisticsDayService customerServiceDataStatisticsDayService;
    private final CustomerServiceDataStatisticsMonthService customerServiceDataStatisticsMonthService;
    private final OrderVideoDataStatisticsDayService orderVideoDataStatisticsDayService;


    /**
     * 清除催一催次数
     */
    @XxlJob("orderClearReminderHandler")
    public void orderClearReminderHandler() {
        XxlJobHelper.log("任务orderClearReminderHandler执行中.....");
        redisService.deleteKeysByPrefix(OrderConstant.ORDER_VIDEO_REMINDER_KEY);
    }

    /**
     * 定时抓取视频订单产品图
     */
    @XxlJob("orderCrawlOrderVideoProductPicHandler")
    public void orderCrawlOrderVideoProductPicHandler() {
        XxlJobHelper.log("任务[定时抓取视频订单产品图]执行中.....");
        List<OrderVideo> orderVideos = orderVideoService.lambdaQuery()
                .isNull(OrderVideo::getProductPic)
                .likeRight(OrderVideo::getProductLink, "https://www.amazon.com")
                .list();

        if (CollUtil.isEmpty(orderVideos)) {
            return;
        }
        XxlJobHelper.log("任务[定时抓取视频订单产品图]任务数:{}.....", orderVideos.size());
        List<AsyncCrawlProductPicDTO> asyncCrawlProductPicDTOS = new ArrayList<>();
        for (OrderVideo orderVideo : orderVideos) {
            AsyncCrawlProductPicDTO dto = new AsyncCrawlProductPicDTO();
            dto.setId(orderVideo.getId());
            dto.setProductLink(orderVideo.getProductLink());
            asyncCrawlProductPicDTOS.add(dto);
        }

        AsyncCrawlTask asyncCrawlTask = new AsyncCrawlTask();
        asyncCrawlTask.setType(OrderTypeEnum.VIDEO_ORDER.getCode());
        asyncCrawlTask.setAsyncCrawlProductPic(asyncCrawlProductPicDTOS);
        remoteService.asyncUpdateOrderVideoImage(asyncCrawlTask);
    }
    /**
     * 定时抓取购物车产品图
     */
    @XxlJob("orderCrawlVideoCartProductPicHandler")
    public void orderCrawlVideoCartProductPicHandler() {
        XxlJobHelper.log("任务[定时抓取购物车产品图]执行中.....");
        List<VideoCart> videoCarts = videoCartService.lambdaQuery()
                .isNull(VideoCart::getProductPic)
                .likeRight(VideoCart::getProductLink, "https://www.amazon.com")
                .list();

        if (CollUtil.isEmpty(videoCarts)) {
            return;
        }
        XxlJobHelper.log("任务[定时抓取购物车产品图]任务数:{}.....", videoCarts.size());
        List<AsyncCrawlProductPicDTO> asyncCrawlProductPicDTOS = new ArrayList<>();
        for (VideoCart videoCart : videoCarts) {
            AsyncCrawlProductPicDTO dto = new AsyncCrawlProductPicDTO();
            dto.setId(videoCart.getId());
            dto.setProductLink(videoCart.getProductLink());
            asyncCrawlProductPicDTOS.add(dto);
        }

        AsyncCrawlTask asyncCrawlTask = new AsyncCrawlTask();
        asyncCrawlTask.setType(OrderTypeEnum.VIDEO_CART.getCode());
        asyncCrawlTask.setAsyncCrawlProductPic(asyncCrawlProductPicDTOS);
        remoteService.asyncUpdateOrderVideoImage(asyncCrawlTask);
    }

    /**
     * 关闭已失效的代付链接
     */
    @XxlJob("orderCloseAnotherPayHandler")
    public void orderCloseAnotherPayHandler() {
        XxlJobHelper.log("任务orderCloseAnotherPayHandler执行中.....");
        List<OrderAnotherPay> orderAnotherPays = orderAnotherPayService.lambdaQuery()
                .eq(OrderAnotherPay::getStatus, StatusTypeEnum.YES.getCode())
                .le(OrderAnotherPay::getCreateTime, DateUtil.offsetHour(DateUtil.date(), -anotherPayProperties.getTimeLimit()))
                .list();
        if (CollUtil.isEmpty(orderAnotherPays)) {
            return;
        }

        for (OrderAnotherPay orderAnotherPay : orderAnotherPays) {
            XxlJobHelper.log("代付链接uuid：" + orderAnotherPay.getUuid() + ", 创建时间：" + DateUtil.format(orderAnotherPay.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN) + ", 状态：" + orderAnotherPay.getStatus() + "，开始取消");
            orderAnotherPay.setStatus(StatusTypeEnum.NO.getCode());
        }

        orderAnotherPayService.updateBatchById(orderAnotherPays);
    }

    @XxlJob("initOrderMemberFirstBuy")
    @Transactional(rollbackFor = Exception.class)
    public void initOrderMemberFirstBuyHandler() {
        XxlJobHelper.log("初始化会员订单标志开始/****/");
        orderMemberService.initUserOrderMemberFlag();
        XxlJobHelper.log("初始化会员订单标志结束/****/");
    }

    /**
     * 到期自动关闭合并单
     */
    @XxlJob("expireCloseMergeHandler")
    public void expireCloseMergeHandler() {
        XxlJobHelper.log("任务expireCloseMergeHandler执行中.....");
        // 查询未关闭的合并单
        List<OrderMerge> orderMerges = orderMergeService.lambdaQuery()
                .eq(OrderMerge::getStatus, OrderMergeStatusEnum.NORMAL.getCode())
                .between(OrderMerge::getMergeTime, DateUtils.addYears(DateUtils.getNowDate(), -1), DateUtils.addHours(DateUtils.getNowDate(), -orderVideoProperties.getCloseOrderHours()))
                .list();

        if (CollUtil.isEmpty(orderMerges)) {
            return;
        }
        for (OrderMerge item : orderMerges) {
            try {
                XxlJobHelper.log("合并支付单号：" + item.getPayNum() + ", 合并时间：" + DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, item.getMergeTime()) + ", 状态：" + item.getStatus() + "，开始取消");
                orderMergeService.cancelMerge(item.getId(), true);
            } catch (Exception e) {
                log.error("关闭合并单失败", e);
            }
        }
    }

    /**
     * （一次性）补全order_video_model_change的选定信息
     */
    @XxlJob("orderVideoModelChangeHandler")
    public void orderVideoModelChangeHandler() {
        XxlJobHelper.log("任务orderVideoModelChangeHandler执行中.....");
        List<OrderVideoModelChange> test = orderVideoService.test();
        if (CollUtil.isEmpty(test)) {
            return;
        }

        List<Long> videoIds = test.stream().map(OrderVideoModelChange::getVideoId).collect(Collectors.toList());

        List<OrderVideoMatch> matchs = orderVideoMatchService.lambdaQuery().in(OrderVideoMatch::getVideoId, videoIds).eq(OrderVideoMatch::getStatus, StatusTypeEnum.YES.getCode()).list();
        List<OrderVideo> orderVideos = orderVideoService.listByIds(videoIds);

        Map<Long, List<OrderVideoMatch>> matchMap = matchs.stream().collect(Collectors.groupingBy(OrderVideoMatch::getVideoId));
        Map<Long, OrderVideo> orderVideoMap = orderVideos.stream().collect(Collectors.toMap(OrderVideo::getId, Function.identity()));


        for (OrderVideoModelChange orderVideoModelChange : test) {
            List<OrderVideoMatch> orderVideoMatches = matchMap.get(orderVideoModelChange.getVideoId());
            OrderVideo orderVideo = orderVideoMap.get(orderVideoModelChange.getVideoId());

            if (ObjectUtil.isNotNull(orderVideoModelChange.getRollbackId())) {
                if (CollUtil.isNotEmpty(orderVideoMatches)) {
                    Optional<OrderVideoMatch> first = orderVideoMatches.stream().filter(item -> orderVideoModelChange.getRollbackId().equals(item.getRollbackId())).findFirst();
                    if (first.isPresent()) {
                        OrderVideoMatch orderVideoMatch = first.get();
                        orderVideoModelChange.setScheduleType(orderVideoMatch.getScheduleType());
                        orderVideoModelChange.setCarryType(orderVideoMatch.getCarryType());
                        orderVideoModelChange.setCommissionUnit(orderVideoMatch.getCommissionUnit());
                        orderVideoModelChange.setCommission(orderVideoMatch.getCommission());
                        orderVideoModelChange.setOverstatement(orderVideoMatch.getOverstatement());
                    } else if (orderVideoModelChange.getRollbackId().equals(orderVideo.getRollbackId())) {
                        orderVideoModelChange.setScheduleType(orderVideo.getScheduleType());
                        orderVideoModelChange.setCarryType(orderVideo.getCarryType());
                        orderVideoModelChange.setCommissionUnit(orderVideo.getCommissionUnit());
                        orderVideoModelChange.setCommission(orderVideo.getCommission());
                        orderVideoModelChange.setOverstatement(orderVideo.getOverstatement());
                    }
                } else if (orderVideoModelChange.getRollbackId().equals(orderVideo.getRollbackId())) {
                    orderVideoModelChange.setScheduleType(orderVideo.getScheduleType());
                    orderVideoModelChange.setCarryType(orderVideo.getCarryType());
                    orderVideoModelChange.setCommissionUnit(orderVideo.getCommissionUnit());
                    orderVideoModelChange.setCommission(orderVideo.getCommission());
                    orderVideoModelChange.setOverstatement(orderVideo.getOverstatement());
                }
            } else {
                if (CollUtil.isNotEmpty(orderVideoMatches)) {
                    Optional<OrderVideoMatch> first = orderVideoMatches.stream().filter(item -> ObjectUtil.isNull(item.getRollbackId())).findFirst();
                    if (first.isPresent()) {
                        OrderVideoMatch orderVideoMatch = first.get();
                        orderVideoModelChange.setScheduleType(orderVideoMatch.getScheduleType());
                        orderVideoModelChange.setCarryType(orderVideoMatch.getCarryType());
                        orderVideoModelChange.setCommissionUnit(orderVideoMatch.getCommissionUnit());
                        orderVideoModelChange.setCommission(orderVideoMatch.getCommission());
                        orderVideoModelChange.setOverstatement(orderVideoMatch.getOverstatement());
                    } else if (ObjectUtil.isNull(orderVideo.getRollbackId())) {
                        orderVideoModelChange.setScheduleType(orderVideo.getScheduleType());
                        orderVideoModelChange.setCarryType(orderVideo.getCarryType());
                        orderVideoModelChange.setCommissionUnit(orderVideo.getCommissionUnit());
                        orderVideoModelChange.setCommission(orderVideo.getCommission());
                        orderVideoModelChange.setOverstatement(orderVideo.getOverstatement());
                    }
                } else if (ObjectUtil.isNull(orderVideo.getRollbackId())) {
                    orderVideoModelChange.setScheduleType(orderVideo.getScheduleType());
                    orderVideoModelChange.setCarryType(orderVideo.getCarryType());
                    orderVideoModelChange.setCommissionUnit(orderVideo.getCommissionUnit());
                    orderVideoModelChange.setCommission(orderVideo.getCommission());
                    orderVideoModelChange.setOverstatement(orderVideo.getOverstatement());
                }
            }
        }
        orderVideoModelChangeService.updateBatchById(test);
    }

    /**
     * （一次性）补全order_video_match的选定信息
     */
    @XxlJob("orderVideoMatchHandler")
    public void orderVideoMatchHandler() {
        XxlJobHelper.log("任务orderVideoMatchHandler执行中.....");
        List<OrderVideoMatch> test = orderVideoService.test2();
        if (CollUtil.isEmpty(test)) {
            return;
        }

        List<Long> videoIds = test.stream().map(OrderVideoMatch::getVideoId).collect(Collectors.toList());

        List<OrderVideo> orderVideos = orderVideoService.listByIds(videoIds);

        Map<Long, OrderVideo> orderVideoMap = orderVideos.stream().collect(Collectors.toMap(OrderVideo::getId, Function.identity()));

        for (OrderVideoMatch orderVideoMatch : test) {
            OrderVideo orderVideo = orderVideoMap.get(orderVideoMatch.getVideoId());
            if (ObjectUtil.isNotNull(orderVideo)) {
                if (ObjectUtil.isNotNull(orderVideoMatch.getRollbackId())) {
                    if (orderVideoMatch.getRollbackId().equals(orderVideo.getRollbackId())) {
                        orderVideoMatch.setScheduleType(orderVideo.getScheduleType());
                        orderVideoMatch.setCommissionUnit(orderVideo.getCommissionUnit());
                        orderVideoMatch.setCommission(orderVideo.getCommission());
                        orderVideoMatch.setOverstatement(orderVideo.getOverstatement());
                        orderVideoMatch.setCarryType(orderVideo.getCarryType());
                        orderVideoMatch.setMainCarryCount(orderVideo.getMainCarryCount());
                        orderVideoMatch.setMainCarryVideoId(orderVideo.getMainCarryVideoId());
                    }
                } else {
                    if (ObjectUtil.isNull(orderVideo.getRollbackId())) {
                        orderVideoMatch.setScheduleType(orderVideo.getScheduleType());
                        orderVideoMatch.setCommissionUnit(orderVideo.getCommissionUnit());
                        orderVideoMatch.setCommission(orderVideo.getCommission());
                        orderVideoMatch.setOverstatement(orderVideo.getOverstatement());
                        orderVideoMatch.setCarryType(orderVideo.getCarryType());
                        orderVideoMatch.setMainCarryCount(orderVideo.getMainCarryCount());
                        orderVideoMatch.setMainCarryVideoId(orderVideo.getMainCarryVideoId());
                    }
                }
            }
        }
        orderVideoMatchService.updateBatchById(test);
    }

    /**
     * (一次性)更新order_video_model_change数据
     */
    @XxlJob("orderVideoModelChangeHandler2")
    public void orderVideoModelChangeHandler2() {
        XxlJobHelper.log("orderVideoModelChangeHandler2.....");
        List<OrderVideoModelChange> test = orderVideoService.test3();
        if (CollUtil.isEmpty(test)) {
            return;
        }

        List<Long> videoIds = test.stream().map(OrderVideoModelChange::getVideoId).collect(Collectors.toList());

        List<OrderVideoMatch> matchs = orderVideoMatchService.lambdaQuery().in(OrderVideoMatch::getVideoId, videoIds).eq(OrderVideoMatch::getStatus, StatusTypeEnum.YES.getCode()).list();

        for (OrderVideoModelChange orderVideoModelChange : test) {
            if (ObjectUtil.isNotNull(orderVideoModelChange.getRollbackId())) {
                Optional<OrderVideoMatch> first = matchs.stream().filter(item -> orderVideoModelChange.getVideoId().equals(item.getVideoId())
                                && orderVideoModelChange.getModelId().equals(item.getShootModelId())
                                && orderVideoModelChange.getRollbackId().equals(item.getRollbackId())
                                && DateUtil.compare(orderVideoModelChange.getSelectedTime(), item.getSubmitTime()) == 0)
                        .findFirst();
                if (first.isPresent()) {
                    OrderVideoMatch orderVideoMatch = first.get();
                    orderVideoModelChange.setScheduleType(orderVideoMatch.getScheduleType());
                    orderVideoModelChange.setCarryType(orderVideoMatch.getCarryType());
                    orderVideoModelChange.setCommissionUnit(orderVideoMatch.getCommissionUnit());
                    orderVideoModelChange.setCommission(orderVideoMatch.getCommission());
                    orderVideoModelChange.setOverstatement(orderVideoMatch.getOverstatement());
                }
            } else {
                Optional<OrderVideoMatch> first = matchs.stream().filter(item -> orderVideoModelChange.getVideoId().equals(item.getVideoId())
                                && orderVideoModelChange.getModelId().equals(item.getShootModelId())
                                && ObjectUtil.isNull(item.getRollbackId())
                                && DateUtil.compare(orderVideoModelChange.getSelectedTime(), item.getSubmitTime()) == 0)
                        .findFirst();
                if (first.isPresent()) {
                    OrderVideoMatch orderVideoMatch = first.get();
                    orderVideoModelChange.setScheduleType(orderVideoMatch.getScheduleType());
                    orderVideoModelChange.setCarryType(orderVideoMatch.getCarryType());
                    orderVideoModelChange.setCommissionUnit(orderVideoMatch.getCommissionUnit());
                    orderVideoModelChange.setCommission(orderVideoMatch.getCommission());
                    orderVideoModelChange.setOverstatement(orderVideoMatch.getOverstatement());
                }
            }
        }

        orderVideoModelChangeService.updateOrderVideoModelChangeBatchFieldNullToNull(test);
    }

    /**
     * 自动完成视频订单
     */
    @XxlJob("autoCompleteOrderVideoHandler")
    @Transactional(rollbackFor = Exception.class)
    public void autoCompleteOrderVideoHandler() {
        XxlJobHelper.log("autoCompleteOrderVideoHandler执行中.....");

        List<OrderVideo> orderVideos = orderVideoService.lambdaQuery()
                .eq(OrderVideo::getStatus, OrderStatusEnum.NEED_CONFIRM.getCode())
                .list();
        if (CollUtil.isEmpty(orderVideos)) {
            return;
        }

        List<Long> videoIds = orderVideos.stream().map(OrderVideo::getId).collect(Collectors.toList());

        //  获取视频订单反馈给商家的素材
        List<OrderVideoFeedBack> orderVideoFeedBacks = orderVideoFeedBackService.selectPartialFieldListByVideoIdsForSetAutoCompleteTimeStart(videoIds);
        Map<Long, List<OrderVideoFeedBack>> orderVideoFeedBackMap = orderVideoFeedBacks.stream().collect(Collectors.groupingBy(OrderVideoFeedBack::getVideoId));

        //  获取视频订单的任务单
        List<OrderVideoTaskDetail> orderVideoTaskDetails = orderVideoTaskDetailService.selectPartialFieldListByVideoIdsForSetAutoCompleteTimeStart(videoIds);
        Map<Long, List<OrderVideoTaskDetail>> orderVideoTaskDetailMap = orderVideoTaskDetails.stream().collect(Collectors.groupingBy(OrderVideoTaskDetail::getTaskId));

        for (OrderVideo orderVideo : orderVideos) {
            List<Date> autoCompleteStartTimes = new ArrayList<>();

            List<OrderVideoFeedBack> feedBacks;
            //  1、校验素材是否已全部反馈给商家
            if (CollUtil.isEmpty(orderVideoFeedBackMap.get(orderVideo.getId()))) {
                orderVideo.setAutoCompleteStartTime(null);
                continue;
            } else {
                feedBacks = orderVideoFeedBackMap.get(orderVideo.getId()).stream().filter(item -> ObjectUtil.equals(orderVideo.getRollbackId(), item.getRollbackId())).collect(Collectors.toList());
                if (CollUtil.isEmpty(feedBacks)) {
                    orderVideo.setAutoCompleteStartTime(null);
                    continue;
                }
            }
            //  1.1校验是否已反馈给商家视频素材
            if (feedBacks.stream().allMatch(item -> CharSequenceUtil.isBlank(item.getVideoUrl()))) {
                orderVideo.setAutoCompleteStartTime(null);
                continue;
            }
            //  1.2判断是否需要反馈给商家照片素材
            if (ObjectUtil.isNotNull(orderVideo.getPicCount()) && PicCountEnum.getValue(orderVideo.getPicCount()) > orderVideo.getRefundPicCount()) {
                //  1.2.1校验是否已反馈给商家照片素材
                if (feedBacks.stream().allMatch(item -> CharSequenceUtil.isBlank(item.getPicUrl()))) {
                    orderVideo.setAutoCompleteStartTime(null);
                    continue;
                }
            }
            autoCompleteStartTimes.addAll(feedBacks.stream().map(OrderVideoFeedBack::getCreateTime).collect(Collectors.toSet()));

            //  2、查询是否有未完结的任务单
            List<OrderVideoTaskDetail> taskDetails = orderVideoTaskDetailMap.get(orderVideo.getId());
            if (CollUtil.isNotEmpty(taskDetails)) {
                if (taskDetails.stream().anyMatch(taskDetail -> OrderTaskStatusEnum.UN_HANDLE.getCode().equals(taskDetail.getStatus())
                        || OrderTaskStatusEnum.HANDLE_ING.getCode().equals(taskDetail.getStatus())
                        || OrderTaskStatusEnum.APPLICATION_FOR_CANCELLATION.getCode().equals(taskDetail.getStatus())
                )) {
                    orderVideo.setAutoCompleteStartTime(null);
                    continue;
                }
                autoCompleteStartTimes.addAll(taskDetails.stream().map(OrderVideoTaskDetail::getEndTime).filter(ObjectUtil::isNotNull).collect(Collectors.toSet()));
                autoCompleteStartTimes.addAll(taskDetails.stream().filter(item -> OrderTaskStatusEnum.REJECT.getCode().equals(item.getStatus())).map(OrderVideoTaskDetail::getUpdateTime).filter(ObjectUtil::isNotNull).collect(Collectors.toSet()));
            }

            //  3、设置自动完成起始时间
            if (CollUtil.isNotEmpty(autoCompleteStartTimes)) {
                autoCompleteStartTimes.sort(Comparator.reverseOrder());
                orderVideo.setAutoCompleteStartTime(autoCompleteStartTimes.get(0));
            } else {
                orderVideo.setAutoCompleteStartTime(null);
            }
        }

        List<OrderVideo> autoCompleteVideos = orderVideos.stream()
                .filter(item -> ObjectUtil.isNotNull(item.getAutoCompleteStartTime()))
                .filter(item -> DateUtil.compare(item.getAutoCompleteStartTime(), DateUtil.offsetHour(DateUtil.date(), -orderVideoProperties.getOrderAutoFinishedTime())) <= 0)
                .collect(Collectors.toList());

        Collection<OrderVideo> disjunction = CollUtil.disjunction(orderVideos, autoCompleteVideos);
        if (CollUtil.isNotEmpty(disjunction)) {
            orderVideoService.updateBatchById(disjunction);
        }

        if (CollUtil.isEmpty(autoCompleteVideos)) {
            return;
        }

        //  执行流转
        SpringUtils.getBean(IOrderService.class).createOrderFlow(autoCompleteVideos, OrderStatusEnum.FINISHED, "系统执行订单自动完成");

        //  记录操作记录
        List<OrderVideoOperateDTO> orderVideoOperateDTOS = new ArrayList<>();
        for (OrderVideo orderVideo : autoCompleteVideos) {
            OrderVideoOperateDTO orderVideoOperateDTO = new OrderVideoOperateDTO();
            orderVideoOperateDTO.setVideoId(orderVideo.getId());
            orderVideoOperateDTO.setEventContent(OrderVideoOperateTypeEnum.ORDER_AUTO_COMPLETION.getEventContent());
            orderVideoOperateDTOS.add(orderVideoOperateDTO);
        }
        orderVideoOperateService.createOrderVideoOperate(
                OrderVideoOperateTypeEnum.ORDER_AUTO_COMPLETION.getEventName(),
                OrderVideoOperateTypeEnum.ORDER_AUTO_COMPLETION.getIsPublic(),
                orderVideoOperateDTOS
        );

        List<Long> autoCompleteVideoIds = autoCompleteVideos.stream().map(OrderVideo::getId).collect(Collectors.toList());

        orderVideoUploadLinkService.saveFinisheds(autoCompleteVideoIds);
        orderVideoFlowNodeDiagramService.setNodeCompleteTime(autoCompleteVideoIds, OrderVideoFlowNodeEnum.ORDER_COMPLETION);
        orderVideoFeedBackMaterialInfoTaskDetailService.finishTaskByVideoIds(autoCompleteVideoIds);
        orderVideoFeedBackMaterialService.closeMaterialInfoByVideoIds(autoCompleteVideoIds, EditCloseReasonEnum.LINKAGE_OFF);

        XxlJobHelper.log("autoCompleteOrderVideoHandler执行完成.....");
    }

    /**
     * 定时更新order_video.create_order_user_name、create_order_user_nick_name
     */
    @XxlJob("updateOrderVideoCreateOrderUserNameAndNickNameHandler")
    public void updateOrderVideoCreateOrderUserNameAndNickNameHandler() {
        List<OrderVideo> orderVideos = orderVideoService.lambdaQuery()
                .notIn(OrderVideo::getStatus, OrderStatusEnum.TRADE_CLOSE.getCode(), OrderStatusEnum.FINISHED.getCode())
                .select(OrderVideo::getId, OrderVideo::getCreateOrderUserId)
                .list();

        if (CollUtil.isEmpty(orderVideos)) {
            return;
        }

        Set<Long> createOrderUserIds = orderVideos.stream().map(OrderVideo::getCreateOrderUserId).collect(Collectors.toSet());
        List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchantByBusinessIdsAndAccountIds(null, createOrderUserIds);
        Map<Long, BusinessAccountDetailVO> accountMap = RemoteService.RemoteUtil.getAccountMap(businessAccountDetailVOS);

        for (OrderVideo orderVideo : orderVideos) {
            BusinessAccountDetailVO businessAccountDetailVO = accountMap.get(orderVideo.getCreateOrderUserId());
            if (ObjectUtil.isNotNull(businessAccountDetailVO)) {
                orderVideo.setCreateOrderUserName(businessAccountDetailVO.getName());
                orderVideo.setCreateOrderUserNickName(businessAccountDetailVO.getNickName());
            }
        }

        orderVideoService.updateBatchById(orderVideos);
    }

    /**
     * （一次性）初始化order_table.order_user_name、order_user_nick_name
     */
    @XxlJob("initOrderTableOrderUserNameAndNickNameHandler")
    public void initOrderTableOrderUserNameAndNickNameHandler() {
        List<Order> list = orderService.list();

        Set<Long> orderUserIds = list.stream().map(Order::getOrderUserId).collect(Collectors.toSet());
        Set<Long> payUserIds = list.stream().map(Order::getPayUserId).collect(Collectors.toSet());

        orderUserIds.addAll(payUserIds);
        List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchantByBusinessIdsAndAccountIds(null, orderUserIds);
        Map<Long, BusinessAccountDetailVO> accountMap = RemoteService.RemoteUtil.getAccountMap(businessAccountDetailVOS);

        for (Order order : list) {
            order.setOrderUserAccount(accountMap.getOrDefault(order.getOrderUserId(), new BusinessAccountDetailVO()).getAccount());
            order.setOrderUserName(accountMap.getOrDefault(order.getOrderUserId(), new BusinessAccountDetailVO()).getName());
            order.setOrderUserNickName(accountMap.getOrDefault(order.getOrderUserId(), new BusinessAccountDetailVO()).getNickName());
            order.setPayUserAccount(accountMap.getOrDefault(order.getPayUserId(), new BusinessAccountDetailVO()).getAccount());
            order.setPayUserName(accountMap.getOrDefault(order.getPayUserId(), new BusinessAccountDetailVO()).getName());
            order.setPayUserNickName(accountMap.getOrDefault(order.getPayUserId(), new BusinessAccountDetailVO()).getNickName());
        }

        orderService.updateBatchById(list);
    }

    /**
     * （一次性）初始化order_video.create_order_operation_user_name、create_order_operation_user_nick_name
     */
    @XxlJob("initOrderVideoCreateOrderUserNameAndNickNameHandler")
    public void initOrderVideoCreateOrderUserNameAndNickNameHandler() {
        List<OrderVideo> orderVideos = orderVideoService.lambdaQuery()
                .list();

        for (OrderVideo orderVideo : orderVideos) {
            orderVideo.setCreateOrderOperationUserName(orderVideo.getCreateOrderUserName());
            orderVideo.setCreateOrderOperationUserNickName(orderVideo.getCreateOrderUserNickName());
        }

        orderVideoService.updateBatchById(orderVideos);
    }

    /**
     * （一次性）初始化order_merge.merge_nick_by
     */
    @XxlJob("initOrderMergeMergeNickByHandler")
    public void initOrderMergeMergeNickByHandler() {
        List<OrderMerge> orderMerges = orderMergeService.lambdaQuery()
                .list();

        Set<Long> mergeByIds = orderMerges.stream().map(OrderMerge::getMergeById).collect(Collectors.toSet());
        List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchantByBusinessIdsAndAccountIds(null, mergeByIds);
        Map<Long, BusinessAccountDetailVO> accountMap = RemoteService.RemoteUtil.getAccountMap(businessAccountDetailVOS);

        for (OrderMerge orderMerge : orderMerges) {
            orderMerge.setMergeNickBy(accountMap.getOrDefault(orderMerge.getMergeById(), new BusinessAccountDetailVO()).getNickName());
        }

        orderMergeService.updateBatchById(orderMerges);
    }

    /**
     * （一次性）初始化order_another_pay.create_nick_by
     */
    @XxlJob("initOrderAnotherPayCreateNickByHandler")
    public void initOrderAnotherPayCreateNickByHandler() {
        List<OrderAnotherPay> orderAnotherPays = orderAnotherPayService.lambdaQuery()
                .list();

        Set<Long> createByIds = orderAnotherPays.stream().map(OrderAnotherPay::getCreateById).collect(Collectors.toSet());
        List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchantByBusinessIdsAndAccountIds(null, createByIds);
        Map<Long, BusinessAccountDetailVO> accountMap = RemoteService.RemoteUtil.getAccountMap(businessAccountDetailVOS);

        for (OrderAnotherPay orderAnotherPay : orderAnotherPays) {
            orderAnotherPay.setCreateNickBy(accountMap.getOrDefault(orderAnotherPay.getCreateById(), new BusinessAccountDetailVO()).getNickName());
        }

        orderAnotherPayService.updateBatchById(orderAnotherPays);
    }

    /**
     * （一次性）初始化种草码和满5减100的活动使用详情记录
     */
    @XxlJob("initOrderPromotionDetailHandler")
    public void initOrderPromotionDetailHandler() {
        List<Order> orders = orderService.lambdaQuery()
                .isNotNull(Order::getSeedCodeDiscount)
                .ne(Order::getSeedCodeDiscount, "0.00")
                .list();

        List<OrderVideo> orderVideos = orderVideoService.lambdaQuery()
                .isNotNull(OrderVideo::getVideoPromotionAmount)
                .ne(OrderVideo::getVideoPromotionAmount, "0.00")
                .list();

        if (CollUtil.isEmpty(orders) && CollUtil.isEmpty(orderVideos)) {
            return;
        }

        List<PromotionActivity> promotionActivities = promotionActivityService.list();
        if (CollUtil.isEmpty(promotionActivities)) {
            return;
        }

        PromotionActivity ORDER_COUNT_FULL_REDUCTION = promotionActivities.stream().filter(item -> PromotionActivityTypeEnum.ORDER_COUNT_FULL_REDUCTION.getCode().equals(item.getType())).findFirst().orElse(null);
        PromotionActivity SEED_CODE_DISCOUNT = promotionActivities.stream().filter(item -> PromotionActivityTypeEnum.SEED_CODE_DISTRIBUTION_DISCOUNT.getCode().equals(item.getType())).findFirst().orElse(null);

        List<OrderPromotionDetail> orderPromotionDetails = new ArrayList<>();

        if (ObjectUtil.isNotNull(SEED_CODE_DISCOUNT)) {
            for (Order order : orders) {
                OrderPromotionDetail orderPromotionDetail = new OrderPromotionDetail();
                orderPromotionDetail.setActivityId(SEED_CODE_DISCOUNT.getId());
                orderPromotionDetail.setOrderNum(order.getOrderNum());
                orderPromotionDetail.setDiscountAmount(order.getSeedCodeDiscount());
                orderPromotionDetail.setDiscountAmountDollar(order.getOrderAmountDollar().subtract(order.getPayAmountDollar()));
                orderPromotionDetails.add(orderPromotionDetail);
            }
        }

        if (ObjectUtil.isNotNull(ORDER_COUNT_FULL_REDUCTION)) {
            for (OrderVideo video : orderVideos) {
                OrderPromotionDetail orderPromotionDetail = new OrderPromotionDetail();
                orderPromotionDetail.setActivityId(ORDER_COUNT_FULL_REDUCTION.getId());
                orderPromotionDetail.setOrderNum(video.getOrderNum());
                orderPromotionDetail.setVideoId(video.getId());
                orderPromotionDetail.setDiscountAmount(video.getVideoPromotionAmount());
                orderPromotionDetail.setDiscountAmountDollar(video.getAmountDollar().subtract(video.getPayAmountDollar()));
                orderPromotionDetails.add(orderPromotionDetail);
            }
        }

        orderPromotionDetailService.saveBatchOrderPromotionDetail(orderPromotionDetails);
    }

    /**
     * （一次性）重新设置有种草码优惠的order_table.pay_amount_dollar
     */
    @XxlJob("resetOrderTablePayAmountDollar")
    public void resetOrderTablePayAmountDollar() {
        List<Order> orders = orderService.lambdaQuery()
                .isNotNull(Order::getSeedCodeDiscount)
                .ne(Order::getSeedCodeDiscount, "0.00")
                .last(" and order_amount_dollar=pay_amount_dollar")
                .list();


        if (CollUtil.isEmpty(orders)) {
            return;
        }

        for (Order order : orders) {
            order.setPayAmountDollar(order.getPayAmount().divide(order.getCurrentExchangeRate(), 2, RoundingMode.DOWN));
        }

        orderService.updateBatchById(orders);
    }

    /**
     * 客服数据统计-按天统计客服数据
     */
    @XxlJob("customerServiceDataStatisticsDayHandler")
    public void customerServiceDataStatisticsDayHandler() {
        XxlJobHelper.log("任务customerServiceDataStatisticsDayHandler执行中.....");

        DateTime yesterday = DateUtil.yesterday();
        CustomerServiceDataStatisticsDay customerServiceDataStatisticsDay = new CustomerServiceDataStatisticsDay();
        customerServiceDataStatisticsDay.setWriteTimeBegin(DateUtil.parse(DateUtil.format(DateUtil.beginOfDay(yesterday), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN));
        customerServiceDataStatisticsDay.setWriteTimeEnd(DateUtil.parse(DateUtil.format(DateUtil.endOfDay(yesterday), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN));

        List<CustomerServiceAddedCompleteCountInfo> chineseCustomerServiceAddedCompleteOrderCounts = orderVideoService.getChineseCustomerServiceOrderCountByDate(DateUtil.format(yesterday, DatePattern.NORM_DATE_PATTERN));
        List<CustomerServiceAddedCompleteCountInfo> chineseCustomerServiceAddedCompleteTaskCounts = orderVideoTaskDetailService.getChineseCustomerServiceTaskCountByDate(DateUtil.format(yesterday, DatePattern.NORM_DATE_PATTERN));
        List<CustomerServiceAddedCompleteCountInfo> englishCustomerServiceAddedCompleteTaskCounts = orderVideoService.getEnglishCustomerServiceOrderCountByDate(DateUtil.format(yesterday, DatePattern.NORM_DATE_PATTERN));

        customerServiceDataStatisticsDay.setChineseCustomerServiceAddedCompleteOrderCountJson(JSONUtil.toJsonStr(chineseCustomerServiceAddedCompleteOrderCounts));
        customerServiceDataStatisticsDay.setChineseCustomerServiceAddedCompleteTaskCountJson(JSONUtil.toJsonStr(chineseCustomerServiceAddedCompleteTaskCounts));
        customerServiceDataStatisticsDay.setEnglishCustomerServiceAddedCompleteOrderCountJson(JSONUtil.toJsonStr(englishCustomerServiceAddedCompleteTaskCounts));

        customerServiceDataStatisticsDayService.saveOrUpdate(customerServiceDataStatisticsDay, new LambdaUpdateWrapper<CustomerServiceDataStatisticsDay>().eq(CustomerServiceDataStatisticsDay::getWriteTimeBegin, customerServiceDataStatisticsDay.getWriteTimeBegin()).eq(CustomerServiceDataStatisticsDay::getWriteTimeEnd, customerServiceDataStatisticsDay.getWriteTimeEnd()));
        XxlJobHelper.log("任务customerServiceDataStatisticsDayHandler执行完成.....");
    }

    /**
     * 客服数据统计-按月统计客服数据
     */
    @XxlJob("customerServiceDataStatisticsMonthHandler")
    public void customerServiceDataStatisticsMonthHandler() {
        XxlJobHelper.log("任务customerServiceDataStatisticsMonthHandler执行中.....");

        DateTime lastMonth = DateUtil.lastMonth();
        CustomerServiceDataStatisticsMonth customerServiceDataStatisticsMonth = new CustomerServiceDataStatisticsMonth();
        customerServiceDataStatisticsMonth.setWriteTimeBegin(DateUtil.parse(DateUtil.format(DateUtil.beginOfMonth(lastMonth), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN));
        customerServiceDataStatisticsMonth.setWriteTimeEnd(DateUtil.parse(DateUtil.format(DateUtil.endOfMonth(lastMonth), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN));

        List<CustomerServiceAddedOustModelCountInfo> customerServiceAddedOustModelCountInfos = remoteService.getEnglishCustomerServiceAddedOustModelCounts(DateUtil.format(lastMonth, DatePattern.NORM_MONTH_PATTERN));

        customerServiceDataStatisticsMonth.setEnglishCustomerServiceAddedOustModelCountJson(JSONUtil.toJsonStr(customerServiceAddedOustModelCountInfos));

        customerServiceDataStatisticsMonthService.saveOrUpdate(customerServiceDataStatisticsMonth, new LambdaUpdateWrapper<CustomerServiceDataStatisticsMonth>().eq(CustomerServiceDataStatisticsMonth::getWriteTimeBegin, customerServiceDataStatisticsMonth.getWriteTimeBegin()).eq(CustomerServiceDataStatisticsMonth::getWriteTimeEnd, customerServiceDataStatisticsMonth.getWriteTimeEnd()));
        XxlJobHelper.log("任务customerServiceDataStatisticsMonthHandler执行完成.....");
    }

    /**
     * 视频订单数据统计-按天统计视频订单数据
     */
    @XxlJob("orderVideoDataStatisticsDayHandler")
    public void orderVideoDataStatisticsDayHandler() {
        XxlJobHelper.log("任务orderVideoDataStatisticsDayHandler执行中.....");

        DateTime yesterday = DateUtil.yesterday();
        OrderVideoDataStatisticsDay orderVideoDataStatisticsDay = Optional.ofNullable(orderVideoService.getOrderVideoDataStatisticsDay(DateUtil.format(yesterday, DatePattern.NORM_DATE_PATTERN))).orElse(new OrderVideoDataStatisticsDay());
        orderVideoDataStatisticsDay.setWriteTimeBegin(DateUtil.parse(DateUtil.format(DateUtil.beginOfDay(yesterday), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN));
        orderVideoDataStatisticsDay.setWriteTimeEnd(DateUtil.parse(DateUtil.format(DateUtil.endOfDay(yesterday), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN));

        orderVideoDataStatisticsDayService.saveOrUpdate(orderVideoDataStatisticsDay, new LambdaUpdateWrapper<OrderVideoDataStatisticsDay>().eq(OrderVideoDataStatisticsDay::getWriteTimeBegin, orderVideoDataStatisticsDay.getWriteTimeBegin()).eq(OrderVideoDataStatisticsDay::getWriteTimeEnd, orderVideoDataStatisticsDay.getWriteTimeEnd()));
        XxlJobHelper.log("任务customerServiceDataStatisticsDayHandler执行完成.....");
    }
}
