package com.wnkx.order.job.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/7/9 15:13
 */
@Component
@ConfigurationProperties(prefix = "order.member")
@Data
@RefreshScope
public class OrderMemberProperties {
    /**
     * 定时关闭订单时间 单位（小时）
     */
    private Integer closeOrderHours;

}
