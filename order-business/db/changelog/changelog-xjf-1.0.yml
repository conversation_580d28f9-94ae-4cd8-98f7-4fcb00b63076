databaseChangeLog:
  - logicalFilePath: 'changelog-xjf-1.0.yml'
  - changeSet:
      id: xjf-1
      author: xjf
      changes:
        - addColumn:
            tableName: order_video_content
            columns:
              - column:
                  name: first_content
                  type: varchar(5000)
                  constraints:
                    nullable: true
                  remarks: 记录第一次注意事项内容
              - column:
                  name: first_edit
                  type: tinyint(1)
                  defaultValue: 1
                  constraints:
                    nullable: true
                  remarks: 是否第一次编辑
  - changeSet:
      id: xjf-2
      author: xjf
      changes:
        - createTable:
            tableName: order_video_content_log
            remarks: 首次键入匹配注意事项
            columns:
              - column:
                  name: id
                  type: bigint
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
              - column:
                  name: video_id
                  type: bigint
                  constraints:
                    nullable: false
                  remarks: 视频订单id (FK:order_video.id)
              - column:
                  name: type
                  type: bigint
                  constraints:
                    nullable: false
                  remarks: 类型（1:拍摄要求,2:匹配模特注意事项（原限制条件）,3:剪辑要求）
              - column:
                  name: content
                  type: varchar(5000)
                  defaultValue: null
                  remarks: 内容
              - column:
                  name: sort
                  type: int
                  remarks: 排序
                  constraints:
                    nullable: true
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  remarks: 创建时间
                  constraints:
                    nullable: true
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  remarks: 更新时间
                  constraints:
                   nullable: true
              - column:
                  name: first_content
                  type: varchar(5000)
                  defaultValue: null
                  remarks: 首次修改内容
                  constraints:
                    nullable: true
              - column:
                  name: first_edit
                  type: tinyint(1)
                  defaultValue: 1
                  remarks: "0未改1改"
                  constraints:
                    nullable: true
  - changeSet:
      id: xjf-3-1737344496
      author: xjf
      changes:
        - addColumn:
            tableName: order_member
            columns:
              - column:
                  name: is_first_buy
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 是否首次购买标识
  - changeSet:
      id: xjf-4-1737421336
      author: xjf
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: channel_type
                  afterColumn: channel_name
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 渠道类型
  - changeSet:
      id: xjf-1737430549
      author: xjf
      changes:
        - sql:
            sql: |
              update order_table set channel_type = 2 where seed_code is not null and seed_code != '';
  - changeSet:
      id: xjf-1737515554
      author: xjf
      changes:
        - modifyDataType:
            tableName: order_video_refund
            columnName: refund_cause
            newDataType: varchar(1000)