databaseChangeLog:
  - logicalFilePath: 'changelog-fzw-1.0.yml'
  - changeSet:
      id: 1
      author: fzw
      changes:
        - sql:
            sql: show databases
            comment: initial change
  - changeSet:
      id: 1726832833
      author: fzw
      changes:
        - addDefaultValue:
            tableName: order_video_roast
            columnName: content
            columnDataType: varchar(300)
            defaultValue: ''
        - dropNotNullConstraint:
            columnName: object
            columnDataType: tinyint(1)
            tableName: order_video_roast
      comment: '吐槽对象取消不为空约束'
  - changeSet:
      id: 1728894483
      author: fzw
      changes:
        - addColumn:
            tableName: order_video_feed_back_material
            columns:
              - column:
                  name: download_status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  remarks: 文件下载状态 (0-否， 1-是)
      comment: '添加反馈材料下载状态字段'
  - changeSet:
      id: 1732524420
      author: fzw
      changes:
        - addColumn:
            tableName: order_invoice
            columns:
              - column:
                  name: audit_by
                  type: bigint(20)
                  constraints:
                    nullable: true
                  defaultValueNumeric: 0
                  remarks: 审核人
      comment: '添加发票审核人字段'
  - changeSet:
      id: 1732687987
      author: fzw
      changes:
        - addColumn:
            tableName: order_video_case
            columns:
              - column:
                  name: reason
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 拒绝原因
      comment: '添加匹配拒绝原因字段'
  - changeSet:
      id: 1733797852
      author: fzw
      changes:
        - addColumn:
            tableName: order_video_match
            columns:
              - column:
                  name: pause_reason
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 暂停原因
      comment: '添加匹配暂停原因'
  - changeSet:
      id: 1733800373
      author: fzw
      changes:
        - sql:
            ALTER TABLE order_video_operate MODIFY COLUMN event_content varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '事件内容';
      comment: '增加字段长度'

  - changeSet:
      id: 1733741987
      author: fzw
      changes:
        - sql:
            ALTER TABLE order_video_logistic MODIFY COLUMN `number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '物流单号';
      comment: '修改order_video_logistic表物流单号字段长度为100'
  - changeSet:
      id: 1734089378
      author: fzw
      changes:
        - createIndex:
            indexName: idx_video_match_preselect_model_match_id
            tableName: order_video_match_preselect_model
            unique: false
            columns:
              - column:
                  name: match_id
              - column:
                  name: video_id
              - column:
                  name: model_id
      comment: '创建order_video_match_preselect_model表索引'

  - changeSet:
      id: **********
      author: fzw
      changes:
        - createTable:
            tableName: order_payee_account_config_info
            remarks: 收款人账号配置表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: true
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: payee_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 收款信息id
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 状态 0-弃用 1-使用
              - column:
                  name: bank_account
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 收款账号名称/银行账号
              - column:
                  name: bank_name
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 开户行名称/银行所在地
              - column:
                  name: company_account_type
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 收款账号类型
              - column:
                  name: company_bank_code
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 银行代码
              - column:
                  name: company_bank_sub_code
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 分行代码
              - column:
                  name: company_bank_swift_code
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 分行代码
              - column:
                  name: company_bank_payee_address
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 收款人地址
              - column:
                  name: create_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建人姓名
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 创建人ID
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 更新人姓名
              - column:
                  name: update_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 更新人ID
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        - createTable:
            tableName: order_payee_account_config
            remarks: 收款人账号关联表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: account_name
                  type: varchar(500)
                  constraints:
                    nullable: false
                  remarks: 主体名称
              - column:
                  name: detail_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 详情id
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  remarks: 状态 0-弃用 1-使用
              - column:
                  name: type
                  type: tinyint(1)
                  constraints:
                      nullable: false
                  remarks: 1-微信,2-支付宝,3-全币种,4-对公账户
              - column:
                  name: create_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建人姓名
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 创建人ID
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 更新人姓名
              - column:
                  name: update_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 更新人ID
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        - createTable:
            tableName: order_payee_account_config_changelog
            remarks: 收款人账号关联表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 操作类型(1-新增 2-修改 3-变更)
              - column:
                  name: comments
                  type: varchar(1100)
                  constraints:
                    nullable: true
                  remarks: 变更详情
              - column:
                  name: config_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 类型(1-微信,2-支付宝,3-全币种,4-对公账户)
              - column:
                  name: create_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建人姓名
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 创建人ID
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 更新人姓名
              - column:
                  name: update_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 更新人ID
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  - changeSet:
      id: **********
      author: fzw
      changes:
        - addColumn:
            tableName: order_payee_account_config_info
            columns:
              - column:
                  name: account_name
                  type: varchar(500)
                  constraints:
                    nullable: false
                  remarks: 主体名称
  - changeSet:
      id: **********
      author: fzw
      changes:
        - addColumn:
            tableName: order_payee_account_config_info
            columns:
              - column:
                  name: type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 1-微信,2-支付宝,3-全币种,4-对公账户
  - changeSet:
      id: **********
      author: fzw
      changes:
        - sql:
            ALTER TABLE order_payee_account_config MODIFY COLUMN `type` tinyint(1) NOT NULL COMMENT '类型(1-微信,2-支付宝,7-全币种,6-对公账户)';
        - sql:
            ALTER TABLE order_payee_account_config_info MODIFY COLUMN `type` tinyint(1) NOT NULL COMMENT '类型(1-微信,2-支付宝,7-全币种,6-对公账户)';
        - sql:
            ALTER TABLE order_payee_account MODIFY COLUMN account_type tinyint(1) DEFAULT 0 NOT NULL COMMENT '类型(1-微信,2-支付宝,7-全币种,6-对公账户)';
        - sql:
            update order_payee_account set account_type = 6 where account_type = 2;
      comment: '修改收款人账号表字段类型'
  - changeSet:
      id: **********
      author: fzw
      changes:
        - sql:
            INSERT INTO order_payee_account_config_info (id, payee_id, status, bank_account, bank_name, company_account_type, company_bank_code, company_bank_sub_code, company_bank_swift_code, company_bank_payee_address, create_by, create_by_id, create_time, update_by, update_by_id, update_time, account_name, `type`) VALUES(1, 1, 1, '*****************', '中国农业银行股份有限公司南安东田支行', NULL, NULL, NULL, NULL, NULL, 'admin', 1, '2024-12-17 17:21:46', 'admin', 1, '2024-12-17 17:21:46', '泉州润一进出口贸易有限公司', 7);
        - sql:
            INSERT INTO order_payee_account_config_info (id, payee_id, status, bank_account, bank_name, company_account_type, company_bank_code, company_bank_sub_code, company_bank_swift_code, company_bank_payee_address, create_by, create_by_id, create_time, update_by, update_by_id, update_time, account_name, `type`) VALUES(2, 2, 1, '*********', '中国香港', '第三方企业账户', '006', '391', 'CITIHKHX（如果系统提示需要输入11位数，请在最后面加“XXX”凑够即可）', 'fu jian sheng quan zhou shi nan an shi mei lin liu mei nan lu 5 hao 9 lou B909,Mainland,China', 'admin', 1, '2024-12-17 17:22:56', 'admin', 1, '2024-12-17 17:22:56', 'Quanzhou Runyi Import Export Trade Co., Ltd.', 6);
        - sql:
            INSERT INTO order_payee_account_config_info (id, payee_id, status, bank_account, bank_name, company_account_type, company_bank_code, company_bank_sub_code, company_bank_swift_code, company_bank_payee_address, create_by, create_by_id, create_time, update_by, update_by_id, update_time, account_name, `type`) VALUES(3, 3, 1, '**********', NULL, NULL, NULL, NULL, NULL, NULL, 'admin', 1, '2024-12-17 17:24:13', 'admin', 1, '2024-12-17 17:24:13', '泉州润一进出口贸易有限公司', 1);
        - sql:
            INSERT INTO order_payee_account_config_info (id, payee_id, status, bank_account, bank_name, company_account_type, company_bank_code, company_bank_sub_code, company_bank_swift_code, company_bank_payee_address, create_by, create_by_id, create_time, update_by, update_by_id, update_time, account_name, `type`) VALUES(4, 4, 1, '****************', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-12-17 17:24:32', NULL, NULL, '2024-12-17 17:24:32', '泉州润一进出口贸易有限公司', 2);
        - sql:
            INSERT INTO order_payee_account_config (id, account_name, detail_id, status, `type`, create_by, create_by_id, create_time, update_by, update_by_id, update_time) VALUES(1, '泉州润一进出口贸易有限公司', 1, 1, 6, 'admin', 1, '2024-12-17 17:27:03', 'admin', 1, '2024-12-17 17:27:03');
        - sql:
            INSERT INTO order_payee_account_config (id, account_name, detail_id, status, `type`, create_by, create_by_id, create_time, update_by, update_by_id, update_time) VALUES(2, 'Quanzhou Runyi Import Export Trade Co., Ltd.', 2, 1, 7, 'admin', 1, '2024-12-17 17:27:03', 'admin', 1, '2024-12-17 17:27:03');
        - sql:
            INSERT INTO order_payee_account_config (id, account_name, detail_id, status, `type`, create_by, create_by_id, create_time, update_by, update_by_id, update_time) VALUES(3, '泉州润一进出口贸易有限公司', 3, 1, 1, 'admin', 1, '2024-12-17 17:27:03', 'admin', 1, '2024-12-17 17:27:03');
        - sql:
            INSERT INTO order_payee_account_config (id, account_name, detail_id, status, `type`, create_by, create_by_id, create_time, update_by, update_by_id, update_time) VALUES(4, '泉州润一进出口贸易有限公司', 4, 1, 2, 'admin', 1, '2024-12-17 17:27:03', 'admin', 1, '2024-12-17 17:27:03');
      comment: 添加表初始数据
  - changeSet:
      id: **********
      author: fzw
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: logistic_flag_remark
                  afterColumn: logistic_flag
                  type: varchar(150)
                  constraints:
                    nullable: true
                  remarks: 标记发货备注
              - column:
                  name: logistic_flag_time
                  afterColumn: logistic_flag_remark
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 标记发货时间
  - changeSet:
      id: **********
      author: fzw
      changes:
        - addColumn:
            tableName: order_payee_account_config_info
            columns:
              - column:
                  name: company_bank_name
                  afterColumn: company_bank_code
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 银行名称
              - column:
                  name: company_bank_address
                  afterColumn: company_bank_code
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 银行地址
  - changeSet:
      id: **********
      author: fzw
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: alipay_pay_app_id
                  afterColumn: payee_id
                  type: varchar(50)
                  constraints:
                    nullable: true
                  remarks: 支付宝支付主体appid
              - column:
                  name: wechat_pay_app_id
                  afterColumn: alipay_pay_app_id
                  type: varchar(50)
                  constraints:
                    nullable: true
                  remarks: 微信支付主体appid
  - changeSet:
      id: **********
      author: fzw
      changes:
        - addColumn:
            tableName: alipay_order_table
            columns:
              - column:
                  name: app_id
                  afterColumn: id
                  type: varchar(50)
                  constraints:
                    nullable: true
                  remarks: 支付宝应用id
        - addColumn:
            tableName: wechat_order_table
            columns:
              - column:
                  name: app_id
                  afterColumn: id
                  type: varchar(50)
                  constraints:
                    nullable: true
                  remarks: 微信应用id
  - changeSet:
      id: 1740455664
      author: fzw
      changes:
        - createIndex:
            tableName: order_table
            indexName: order_table_biz_user_id_IDX
            unique: false
            columns:
              - column:
                  name: biz_user_id
  - changeSet:
      id: 1755
      author: fzw
      changes:
        - sql:
            ALTER TABLE order_video_match MODIFY COLUMN pause_reason varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '暂停原因';
      comment: 因匹配淘汰原因，需要完整记录订单回滚原因，故修改字段长度
  - changeSet:
      id: 1745227424
      author: fzw
      changes:
        - createIndex:
            tableName: order_video_task
            indexName: order_video_task_video_id_IDX
            unique: false
            columns:
              - column:
                  name: video_id
        - createIndex:
            tableName: order_video_task_detail
            indexName: order_video_task_detail_task_id_IDX
            unique: false
            columns:
              - column:
                  name: task_id
              - column:
                  name: id
  - changeSet:
      id: 1747383198
      author: fzw
      changes:
        - createTable:
            tableName: order_base_price_config
            remarks: 订单基础价格配置表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: true
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: price_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 价格类型（1-服务费）
              - column:
                  name: origin_price
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 原金额(展示使用-非代理)
              - column:
                  name: origin_price_proxy
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 原金额(展示使用-代理)
              - column:
                  name: current_price
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 现金额
              - column:
                  name: current_price_proxy
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 代理现金额
              - column:
                  name: since_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 生效时间
              - column:
                  name: create_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建人姓名
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 创建人ID
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 更新人姓名
              - column:
                  name: update_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 更新人ID
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  - changeSet:
      id: 1747383200
      author: fzw
      changes:
        - createTable:
            tableName: order_base_price_config_changelog
            remarks: 订单基础价格配置表_更改记录
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: true
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: price_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 价格类型（1-服务费）
              - column:
                  name: origin_price
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 现原金额(展示使用-非代理)
              - column:
                  name: origin_price_previous
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 前原金额(展示使用-非代理)
              - column:
                  name: origin_price_proxy
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 原金额(展示使用-代理)
              - column:
                  name: origin_price_proxy_previous
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 前原金额(展示使用-代理)
              - column:
                  name: current_price
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 现金额
              - column:
                  name: current_price_previous
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 前现金额
              - column:
                  name: current_price_proxy
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 现代理现金额
              - column:
                  name: current_price_proxy_previous
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 前现代理现金额
              - column:
                  name: since_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 生效时间
              - column:
                  name: create_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建人姓名
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 创建人ID
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 更新人姓名
              - column:
                  name: update_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 更新人ID
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  - changeSet:
      id: 1748932703
      author: fzw
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: create_order_business_proxy_status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValue: 0
                  remarks: 创建订单商家代理类型(0:否,1:是)
                  afterColumn: create_order_business_id

  - changeSet:
      id: 1748932704
      author: fzw
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: model_cooperation_score
                  type: decimal(2,1)
                  constraints:
                    nullable: false
                  defaultValue: 0.0
                  remarks: 模特评分 (0.0-10.0)
                  afterColumn: model_cooperation
        - addColumn:
            tableName: order_video_model_change
            columns:
              - column:
                  name: cooperation_score
                  type: decimal(2,1)
                  constraints:
                    nullable: false
                  defaultValue: 0.0
                  remarks: 模特评分 (0.0-10.0)
                  afterColumn: cooperation
  - changeSet:
      id: 1750145027
      author: fzw
      changes:
        - addColumn:
            tableName: order_video_task_detail_process_record
            columns:
              - column:
                  name: object_keys
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 对象存储键值（逗号分隔的字符串）
                  afterColumn: content
  - changeSet:
      id: 1751350979
      author: fzw
      changes:
        - addColumn:
            tableName: order_video_match
            columns:
              - column:
                  name: shuffled_sort_key
                  type: BIGINT(20)
                  constraints:
                    nullable: true
                  remarks: 排序Key
                  afterColumn: issue_id
  - changeSet:
      id: 1751446065
      author: fzw
      changes:
        - createIndex:
            indexName: order_video_feed_back_video_id_create_time_IDX
            tableName: order_video_feed_back
            unique: false
            columns:
              - column:
                  name: video_id
              - column:
                  name: create_time
      comment: '创建order_video_feed_back表索引'
  - changeSet:
      id: 1751449188
      author: fzw
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: object_key
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 对象存储键值（逗号分隔的字符串）
                  afterColumn: distribution_result_time
  - changeSet:
      id: 1751967570
      author: fzw
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: first_match_time
                  type: datetime
                  constraints:
                      nullable: true
                  remarks: 首次匹配时间
                  afterColumn: is_gund
  - changeSet:
      id: 1751978389
      author: fzw
      changes:
        - sql:
            sql: UPDATE order_video ov
                  INNER JOIN (
                  SELECT
                  ovm.video_id,
                  MIN(ovm.create_time) AS first_match_creation_time
                  FROM
                  order_video_match ovm
                  GROUP BY
                  ovm.video_id
                  ) AS first_match_data ON ov.id = first_match_data.video_id
                  SET ov.first_match_time = first_match_data.first_match_creation_time
      comment: 初始化ov.first_match_time的数据
  - changeSet:
      id: 1752200423
      author: fzw
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: seed_member_status
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 种草官会员状态（0-非会员，1-会员）
                  afterColumn: seed_id
      comment: '添加种草官会员状态字段'
  - changeSet:
      id: 1752200424
      author: fzw
      changes:
        - modifyDataType:
            tableName: order_video_match_preselect_model
            columnName: shoot_attention
            newDataType: varchar(3000)
      comment: '修改order_video_match_preselect_model表shoot_attention字段长度为3000'
  - changeSet:
      id: 1752200425
      author: fzw
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: shoot_attention_object_key
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 拍摄注意事项对象存储键值（逗号分隔）
                  afterColumn: shoot_attention
      comment: '新增order_video_match_preselect_model表shoot_attention_object_key字段'
  - changeSet:
      id: 1752200426
      author: fzw
      changes:
        - modifyDataType:
            tableName: order_video_match_preselect_model
            columnName: shoot_attention
            newDataType: varchar(3500)
      comment: '修改order_video_match_preselect_model表shoot_attention字段长度为3500'
  - changeSet:
      id: 1752200427
      author: fzw
      changes:
        - addColumn:
            tableName: order_video_match
            columns:
              - column:
                  name: shoot_attention
                  type: varchar(3500)
                  constraints:
                    nullable: true
                  remarks: 拍摄注意事项
                  afterColumn: shoot_model_person_name
              - column:
                  name: shoot_attention_object_key
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 拍摄注意事项对象存储键值（逗号分隔）
                  afterColumn: shoot_attention
            comment: '新增order_video_match表shoot_attention与shoot_attention_object_key字段'
