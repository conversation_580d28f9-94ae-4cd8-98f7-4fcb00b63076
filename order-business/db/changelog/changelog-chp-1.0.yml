databaseChangeLog:
  - logicalFilePath: 'changelog-chp-1.0.yml'
  - changeSet:
      id: chp-order-1
      author: chp
      changes:
        - createTable:
            tableName: fy_order_table
            remarks: 富友订单表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
              - column:
                  name: mchnt_order_no
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 商户订单号
              - column:
                  name: order_number
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 内部订单号
              - column:
                  name: qrcode
                  type: varchar(100)
                  constraints:
                    nullable: false
                  remarks: 二维码
              - column:
                  name: platform
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 平台类型（1-微信支付，2-支付宝支付，3-云闪付/银联，4-数字人民币，5-未知支付方式）
              - column:
                  name: order_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 订单金额
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  remarks: 是否有效（1-有效， 0-无效）
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间
  - changeSet:
      id: chp-order-2
      author: chp
      changes:
        - addColumn:
            tableName: order_member
            columns:
              - column:
                  name: biz_user_id
                  type: bigint(20)
                  afterColumn: order_num
                  remarks: 创建人id biz_user.id
  - changeSet:
      id: chp-order-3
      author: chp
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: biz_user_id
                  type: bigint(20)
                  afterColumn: order_num
                  remarks: 创建人id biz_user.id


  - changeSet:
      id: chp-order-4
      author: chp
      changes:
        - createTable:
            tableName: order_member_flow
            isNotExists: true
            remarks: 订单_会员订单_流转记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: order_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 订单id (FK:order_table.id)
              - column:
                  name: user_id
                  type: bigint(20)
                  remarks: 处理人ID
              - column:
                  name: event_name
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 事件名称
              - column:
                  name: event_execute_object
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 事件执行对象（1:商家,9:系统）
              - column:
                  name: event_execute_user
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 事件执行人名称
              - column:
                  name: event_execute_nick_name
                  type: varchar(50)
                  constraints:
                    nullable: true
                  remarks: 事件执行人微信昵称
              - column:
                  name: event_execute_phone
                  type: varchar(15)
                  constraints:
                    nullable: true
                  remarks: 事件执行人手机号
              - column:
                  name: event_execute_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 事件执行时间
              - column:
                  name: origin_status
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 原先订单状态（1待支付、2待审核、3交易成功、4交易关闭）
              - column:
                  name: target_status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 目标订单状态（1待支付、2待审核、3交易成功、4交易关闭）
              - column:
                  name: remark
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 备注
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: chp-order-5
      author: chp
      changes:
        - renameColumn:
            tableName: order_member_flow
            oldColumnName: order_id
            columnDataType: varchar(30)
            newColumnName: order_num
            remarks: 订单号
  - changeSet:
      id: chp-order-6
      author: chp
      changes:
        - createTable:
            tableName: payee_account_config
            isNotExists: true
            remarks: 收款人账号配置表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: company_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 收款公司名称
              - column:
                  name: bank_name
                  type: varchar(64)
                  constraints:
                    nullable: false
                  remarks: 开户行名称
              - column:
                  name: bank_account
                  type: varchar(64)
                  constraints:
                    nullable: false
                  remarks: 开户行账号
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 状态（0-无效，1-有效）
                  defaultValueNumeric: 0
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                  remarks: 修改时间
                  constraints:
                    nullable: true
        - sql:
            sql: |
              INSERT INTO `payee_account_config`(`id`, `company_name`, `bank_name`,`bank_account`, `status`) VALUES (3001, '泉州润一进出口贸易有限公司', '中国农业银行股份有限公司南安东田支行', '*****************', 1);
  - changeSet:
      id: chp-order-7
      author: chp
      changes:
        - createTable:
            tableName: order_member_channel
            isNotExists: true
            remarks: 会员渠道记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: channel_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 渠道id（distribution_channel.id）
              - column:
                  name: channel_name
                  type: varchar(27)
                  constraints:
                    nullable: false
                  remarks: 渠道名称（distribution_channel.channel_name）
              - column:
                  name: channel_phone
                  type: varchar(15)
                  constraints:
                    nullable: false
                  remarks: 渠道手机号（biz_user.phone）
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家id（business.id）
              - column:
                  name: business_name
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 商家名称（business.name）
              - column:
                  name: member_code
                  type: varchar(10)
                  constraints:
                    nullable: false
                  remarks: 会员编码（business.member_code）
              - column:
                  name: member_package_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 套餐类型：0=季度会员,1=年度会员,2=三年会员
              - column:
                  name: settle_rage
                  type: decimal(12,1)
                  constraints:
                    nullable: false
                  remarks: 结算比例
                  defaultValue: 0.0
              - column:
                  name: settle_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 最终结算金额（结算比例 * 会员价格）
                  defaultValue: 0.00
              - column:
                  name: settle_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 结算时间
              - column:
                  name: settle_status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 状态（0-未结算，1-已结算）
                  defaultValueNumeric: 0
              - column:
                  name: settle_resource_url
                  type: varchar(64)
                  constraints:
                    nullable: false
                  remarks: 结算凭证地址
                  defaultValue: ''
              - column:
                  name: settle_user_name
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 结算人名字（sys_user.user_name）
                  defaultValue: ''
              - column:
                  name: settle_user_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 结算人id（sys_user.user_id）
              - column:
                  name: remark
                  type: varchar(100)
                  constraints:
                    nullable: false
                  remarks: 备注
                  defaultValue: ''
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 购买时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: chp-order-9
      author: chp
      changes:
        - addColumn:
            tableName: payee_account_config
            columns:
              - column:
                  name: account_type
                  type: tinyint(1)
                  afterColumn: id
                  remarks: 账号类型（0-默认类型, 1-银行卡账号, 2-对公账号）
                  defaultValueNumeric: 0
        - renameColumn:
            tableName: payee_account_config
            oldColumnName: company_name
            newColumnName: account_name
            columnDataType: varchar(32)
            remarks: 账号名称
        - sql:
            sql: |
              UPDATE payee_account_config SET account_type = 2 where 1=1;
              INSERT INTO `payee_account_config`(`account_type`,`account_name`, `bank_name`, `bank_account`, `status`) VALUES (1, '洪润馨', '中国农业银行厦门集美支行', '6228480070309850215', 1);

  - changeSet:
      id: chp-order-10
      author: chp
      changes:
        - addColumn:
            tableName: order_video_logistic
            columns:
              - column:
                  name: receipt_time
                  type: datetime
                  afterColumn: shipping_time
                  remarks: 收货时间
              - column:
                  name: receipt
                  type: tinyint(1)
                  afterColumn: shipping_time
                  remarks: 是否确认收货（0-未收货, 1-已收获）
                  defaultValueNumeric: 0


  - changeSet:
      id: chp-order-11
      author: chp
      changes:
        - addDefaultValue:
            columnDataType: tinyint(1)
            columnName: reply_content
            defaultValue: 0
            tableName: order_video_case
        - setColumnRemarks:
            tableName: order_video_case
            columnName: reply_content
            columnDataType: tinyint(1)
            remarks: 回复内容(0:待反馈,1:同意,2:不同意)

  - changeSet:
      id: chp-order-12
      author: chp
      changes:
        - addDefaultValue:
            columnDataType: tinyint(1)
            columnName: reply_content
            defaultValue: 0
            tableName: order_video_case

  - changeSet:
      id: chp-order-13
      author: chp
      changes:
        - createTable:
            tableName: wechat_order_table
            isNotExists: true
            remarks: 微信订单表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: mchnt_order_no
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 商户订单号
              - column:
                  name: order_num
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 内部订单号
              - column:
                  name: qrcode
                  type: varchar(100)
                  constraints:
                    nullable: false
                  remarks: 二维码
              - column:
                  name: order_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 订单金额
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 是否有效（1-有效， 0-无效）
                  defaultValue: 1
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP

  - changeSet:
      id: chp-order-14
      author: chp
      changes:
        - createTable:
            tableName: wechat_pay_log
            isNotExists: true
            remarks: 微信回调记录表
            columns:
              - column:
                  name: id
                  type: BIGINT
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: "主键，自增ID"
              - column:
                  name: appid
                  type: VARCHAR(32)
                  constraints:
                    nullable: false
                  remarks: "公众账号ID"
              - column:
                  name: mchid
                  type: VARCHAR(32)
                  constraints:
                    nullable: false
                  remarks: "商户号"
              - column:
                  name: out_trade_no
                  type: VARCHAR(32)
                  constraints:
                    nullable: false
                  remarks: "商户订单号"
              - column:
                  name: transaction_id
                  type: VARCHAR(32)
                  constraints:
                    nullable: false
                  remarks: "微信支付订单号"
              - column:
                  name: trade_type
                  type: VARCHAR(16)
                  constraints:
                    nullable: false
                  remarks: "交易类型:JSAPI：公众号支付, NATIVE：扫码支付, APP：APP支付, MICROPAY：付款码支付, MWEB：H5支付, FACEPAY：刷脸支付"
              - column:
                  name: trade_state
                  type: VARCHAR(32)
                  constraints:
                    nullable: false
                  remarks: "交易状态:SUCCESS：支付成功, REFUND：转入退款, NOTPAY：未支付, CLOSED：已关闭, REVOKED：已撤销（仅付款码支付会返回）, USERPAYING：用户支付中（仅付款码支付会返回）, PAYERROR：支付失败（仅付款码支付会返回）"
              - column:
                  name: trade_state_desc
                  type: VARCHAR(256)
                  constraints:
                    nullable: false
                  remarks: "交易状态描述"
              - column:
                  name: bank_type
                  type: VARCHAR(32)
                  constraints:
                    nullable: false
                  remarks: "付款银行"
              - column:
                  name: attach
                  type: VARCHAR(255)
                  remarks: "附加数据"
              - column:
                  name: success_time
                  type: VARCHAR(23)
                  constraints:
                    nullable: false
                  remarks: "支付完成时间"
              - column:
                  name: openid
                  type: VARCHAR(128)
                  constraints:
                    nullable: false
                  remarks: "用户标识"
              - column:
                  name: total
                  type: INTEGER
                  constraints:
                    nullable: false
                  remarks: "总金额"
              - column:
                  name: payer_total
                  type: INTEGER
                  constraints:
                    nullable: false
                  remarks: "付款人总金额"
              - column:
                  name: currency
                  type: VARCHAR(16)
                  constraints:
                    nullable: false
                  remarks: "货币种类"
              - column:
                  name: payer_currency
                  type: VARCHAR(16)
                  constraints:
                    nullable: false
                  remarks: "付款人货币种类"
              - column:
                  name: device_id
                  type: VARCHAR(32)
                  constraints:
                    nullable: false
                  remarks: "设备ID"
              - column:
                  name: promotion_detail
                  type: json
                  remarks: "优惠券ID"

  - changeSet:
      id: chp-order-15
      author: chp
      changes:
        - addColumn:
            tableName: wechat_pay_log
            columns:
              - column:
                  name: order_num
                  type: varchar(30)
                  afterColumn: id
                  remarks: 内部订单号
        - renameColumn:
            tableName: wechat_pay_log
            oldColumnName: promotion_detail
            columnDataType: json
            newColumnName: remark
            remarks: 备注
  - changeSet:
      id: chp-order-16
      author: chp
      changes:
        - modifyDataType:
            tableName: wechat_pay_log
            columnName: success_time
            newDataType: datetime
  - changeSet:
      id: chp-order-17
      author: chp
      changes:
        - addDefaultValue:
            columnDataType: varchar(16)
            columnName: trade_type
            defaultValue: ''
            tableName: wechat_pay_log
        - addDefaultValue:
            columnDataType: varchar(32)
            columnName: trade_state
            defaultValue: ''
            tableName: wechat_pay_log
        - addDefaultValue:
            columnDataType: varchar(256)
            columnName: trade_state_desc
            defaultValue: ''
            tableName: wechat_pay_log
        - addDefaultValue:
            columnDataType: varchar(32)
            columnName: bank_type
            defaultValue: ''
            tableName: wechat_pay_log
        - addDefaultValue:
            columnDataType: varchar(128)
            columnName: openid
            defaultValue: ''
            tableName: wechat_pay_log
        - addDefaultValue:
            columnDataType: int
            columnName: total
            defaultValue: 0
            tableName: wechat_pay_log
        - addDefaultValue:
            columnDataType: int
            columnName: payer_total
            defaultValue: 0
            tableName: wechat_pay_log
        - addDefaultValue:
            columnDataType: varchar(16)
            columnName: currency
            defaultValue: ''
            tableName: wechat_pay_log
        - addDefaultValue:
            columnDataType: varchar(16)
            columnName: payer_currency
            defaultValue: ''
            tableName: wechat_pay_log
        - addDefaultValue:
            columnDataType: varchar(32)
            columnName: device_id
            defaultValue: ''
            tableName: wechat_pay_log
  - changeSet:
      id: chp-order-20
      author: fzw
      changes:
        - createTable:
            tableName: order_payee_account
            isNotExists: true
            remarks: 订单收款账号表
            columns:
              - column:
                  name: id
                  type: BIGINT
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: "主键，自增ID"
              - column:
                  name: order_num
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 订单编号 (FK:order_table.order_num)
              - column:
                  name: account_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 账号类型（0-默认类型, 1-银行卡账号, 2-对公账号）
                  defaultValue: 0
              - column:
                  name: account_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 账号名称
                  defaultValue: ''
              - column:
                  name: bank_name
                  type: varchar(64)
                  constraints:
                    nullable: false
                  remarks: 开户行名称
                  defaultValue: ''
              - column:
                  name: bank_account
                  type: varchar(64)
                  constraints:
                    nullable: false
                  remarks: 开户行账号
                  defaultValue: ''
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                  remarks: 修改时间
                  constraints:
                    nullable: true
        - createIndex:
            tableName: order_payee_account
            indexName: uk_order_num
            unique: true
            columns:
              - column:
                  name: order_num
        - sql:
            sql: |
              INSERT INTO order_payee_account (order_num, account_type) SELECT order_num,(CASE WHEN pay_type = 5 THEN 1 WHEN pay_type = 15 THEN 1 WHEN pay_type = 6 THEN 2 WHEN pay_type = 16 THEN 2 ELSE 0 END ) account_type FROM order_table;
              UPDATE order_payee_account SET account_name = '洪润馨', bank_name = '中国农业银行厦门集美支行', bank_account = '6228480070309850215' WHERE account_type = 1;
              UPDATE order_payee_account SET account_name = '泉州润一进出口贸易有限公司', bank_name = '中国农业银行股份有限公司南安东田支行', bank_account = '*****************' WHERE account_type = 2;

  - changeSet:
      id: chp-order-*************
      author: chp
      changes:
        - setColumnRemarks:
            tableName: order_video_content
            columnName: content
            columnDataType: varchar(5000)
            constraints:
              nullable: true
            remarks: 内容
  - changeSet:
      id: chp-order-**********
      author: chp
      changes:
        - setColumnRemarks:
            tableName: video_cart_content
            columnName: content
            columnDataType: varchar(5000)
            constraints:
              nullable: false
            remarks: 内容


  - changeSet:
      id: chp-order-21
      author: chp
      changes:
        - setColumnRemarks:
            tableName: order_table
            columnName: audit_status
            columnDataType: tinyint(1)
            remarks: 财务审核状态（0:待审核,1:审核通过,2.审核异常,3.已关闭）

  - changeSet:
      id: chp-order-22
      author: chp
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: audit_user_name
                  type: varchar(30)
                  afterColumn: audit_status
                  remarks: 审核人员名称
              - column:
                  name: audit_user_id
                  type: bigint(20)
                  afterColumn: audit_status
                  remarks: 审核人员id FK sys_user.user_id
              - column:
                  name: audit_time
                  type: datetime
                  afterColumn: audit_status
                  remarks: 审核时间
  - changeSet:
      id: chp-order-23
      author: chp
      changes:
        - addDefaultValue:
            columnDataType: tinyint(1)
            columnName: audit_status
            defaultValue: 0
            tableName: order_table

  - changeSet:
      id: chp-order-1731479478
      author: chp
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: submit_credential_time
                  type: datetime
                  afterColumn: pay_type
                  remarks: 提交凭证时间
  - changeSet:
      id: chp-order-1732083082
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `order_video_operate`
              MODIFY COLUMN `event_execute_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '事件执行人用户名称' AFTER `event_execute_user_id`;
              ALTER TABLE `order_video_flow`
              MODIFY COLUMN `event_execute_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '事件执行人用户名称' AFTER `event_execute_user_id`;
              ALTER TABLE `order_member`
              MODIFY COLUMN `create_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建人名称： FK: business.name' AFTER `create_user_id`;
              ALTER TABLE `order_member`
              MODIFY COLUMN `update_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '修改人名称  FK: business.name' AFTER `update_user_id`;
              ALTER TABLE `order_member_flow`
              MODIFY COLUMN `event_execute_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '事件执行人名称' AFTER `event_execute_object`;

  - changeSet:
      id: chp-order-1731737953
      author: chp
      changes:
        - addColumn:
            tableName: order_member
            columns:
              - column:
                  name: presented_time
                  type: int(8)
                  afterColumn: member_start_time
                  remarks: 加赠时间（单位：月）
                  defaultValue: 0
        - addColumn:
            tableName: order_member
            columns:
              - column:
                  name: member_end_time
                  type: datetime
                  afterColumn: member_start_time
                  remarks: 订单结束时间
  - changeSet:
      id: chp-order-1732177394
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `order_invoice`
              MODIFY COLUMN `number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发票号' AFTER `content`;

  - changeSet:
      id: chp-order-1732263454
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `order_video_refund`
              MODIFY COLUMN `contact_id` bigint(16) NULL COMMENT '对接人id' AFTER `update_time`;

  - changeSet:
      id: chp-order-1734317548
      author: chp
      changes:
        - addColumn:
            tableName: order_video_task_detail
            columns:
              - column:
                  name: confirm_time
                  type: datetime
                  afterColumn: end_time
                  constraints:
                    nullable: true
                  remarks: 确认售后时间
        - sql:
            sql: |
              update order_video_task_detail a join order_video_task_detail_flow_record b on b.task_num = a.task_num and b.operate_type = 103 set a.confirm_time = b.create_time;
  - changeSet:
      id: chp-order-1734403928
      author: chp
      changes:
        - createTable:
            tableName: order_pay_log
            isNotExists: true
            remarks: 订单支付记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: order_num
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 订单号
              - column:
                  name: order_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 订单类型（0-视频订单，1-会员订单，3-预付款订单）
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家id（business.id）
              - column:
                  name: mchnt_order_no
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 商户订单号
              - column:
                  name: pay_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 支付方式(1:微信,2:支付宝支付,5:银行卡转账,6:对公转账,7:全币种)
              - column:
                  name: pay_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 支付时间
              - column:
                  name: pay_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 需支付金额
              - column:
                  name: real_pay_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 订单实付金额（单位：￥）
              - column:
                  name: use_balance
                  type: decimal(12,2)
                  defaultValueComputed: 0.00
                  constraints:
                    nullable: false
                  remarks: 使用余额（单位：￥）

        - createTable:
            tableName: order_audit_flow
            isNotExists: true
            remarks: 订单财务审核流水
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: order_num
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 订单号
              - column:
                  name: audit_status
                  type: tinyint(1)
                  remarks: 财务审核状态（0:待审核,1:审核通过,2.审核异常,3.已关闭）
              - column:
                  name: audit_user_name
                  type: varchar(32)
                  remarks: 审核人员名称
              - column:
                  name: audit_user_id
                  type: bigint(20)
                  remarks: 审核人员id FK sys_user.user_id
              - column:
                  name: audit_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  remarks: 审核时间
              - column:
                  name: order_document_resource
                  type: varchar(500)
                  remarks: 订单支付凭证
                  defaultValue: ""
              - column:
                  name: remark
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 备注
                  defaultValue: ""

  - changeSet:
      id: chp-order-1734484977
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `order_pay_log`
              MODIFY COLUMN `mchnt_order_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '商户订单号' AFTER `business_id`;

  - changeSet:
      id: chp-order-1734602013
      author: chp
      changes:
        - addColumn:
            tableName: order_pay_log
            columns:
              - column:
                  name: mchid
                  type: varchar(32)
                  afterColumn: mchnt_order_no
                  remarks: 商户号（wechat_pay_log.mchid，alipay_pay_log.app_id）
                  defaultValue: ""

  - changeSet:
      id: chp-order-1734678406
      author: chp
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: settle_rage
                  type: decimal(12,1)
                  afterColumn: seed_code
                  remarks: 会员折扣
                  defaultValue: 0.00
              - column:
                  name: channel_name
                  type: varchar(27)
                  afterColumn: seed_code_discount
                  remarks: 渠道名称
                  defaultValue: ''
  - changeSet:
      id: chp-order-**********
      author: chp
      changes:
        - sql:
            sql: |
              update `order_payee_account` set account_type = 7 where account_type = 3;
              update `order_payee_account` set account_type = 5 where account_type = 1;
  - changeSet:
      id: chp-order-**********
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `order_video_refund`
              MODIFY COLUMN `refund_num` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '退款审批号' AFTER `order_num`;

  - changeSet:
      id: chp-order-**********
      author: chp
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: use_balance
                  type: decimal(12,2)
                  afterColumn: amount_dollar
                  defaultValueComputed: 0.00
                  constraints:
                    nullable: false
                  remarks: 使用余额（单位：￥）
  - changeSet:
      id: chp-order-**********
      author: chp
      changes:
        - sql:
            sql: |
              update order_pay_log opl join order_table ot on ot.order_num = opl.order_num
              set opl.business_id = ot.merchant_id
              where opl.business_id = 0

  - changeSet:
      id: chp-order-1736324470
      author: chp
      changes:
        - addColumn:
            tableName: order_video_roast
            columns:
              - column:
                  name: roast_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValue: 0
                  remarks: 吐槽类型(0:视频吐槽,1:系统吐槽)
                  afterColumn: id
              - column:
                  name: handle_user_name
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 处理人名称（sys_user.user_name）
                  defaultValue: ''
                  afterColumn: content
              - column:
                  name: handle_user_id
                  type: bigint(20)
                  remarks: 处理人ID FK sys_user.user_id
                  afterColumn: content
              - column:
                  name: handle_time
                  type: datetime
                  remarks: 处理时间
                  afterColumn: content
              - column:
                  name: handle_result
                  type: varchar(1000)
                  constraints:
                    nullable: false
                  remarks: 处理结果
                  defaultValue: ''
                  afterColumn: content
              - column:
                  name: handle_status
                  type: tinyint(1)
                  remarks: 处理状态（0:待处理,1:已处理）
                  constraints:
                    nullable: false
                  defaultValue: 0
                  afterColumn: content
        - sql:
            sql: |
              ALTER TABLE `order_video_roast`
              MODIFY COLUMN `video_id` bigint(16) NULL COMMENT '视频id FK:order_video.id' AFTER `id`;

  - changeSet:
      id: chp-order-1736327767
      author: chp
      changes:
        - addColumn:
            tableName: order_video_roast
            columns:
              - column:
                  name: roast_user_name
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 吐槽用户名称
                  defaultValue: ''
                  afterColumn: id
              - column:
                  name: roast_user_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 吐槽用户微信名称
                  defaultValue: ''
                  afterColumn: id
              - column:
                  name: biz_user_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 吐糟账号ID FK:biz_user.id
                  afterColumn: id
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家ID FK:business.id
                  afterColumn: id

  - changeSet:
      id: chp-order-1736387349
      author: chp
      changes:
        - sql:
            sql: |
              update order_video_roast set handle_status = 1 where handle_status = 0;
              update order_video_roast ovr join order_video ov on ov.id = ovr.video_id
                            set ovr.business_id = ov.create_order_business_id where ovr.business_id = 0;


  - changeSet:
      id: chp-order-1737082558
      author: chp
      changes:
        - addColumn:
            tableName: order_member
            columns:
              - column:
                  name: presented_time_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  afterColumn: presented_time
                  remarks: 加赠时间类型（1-天,2-月,3-年）
                  defaultValue: 2
        - sql:
            sql: |
              ALTER TABLE `order_member`
              MODIFY COLUMN `presented_time` tinyint(4) NULL DEFAULT 0 COMMENT '加赠时间' AFTER `member_end_time`;


  - changeSet:
      id: chp-order-1737627644
      author: chp
      changes:
        - createIndex:
            tableName: order_member
            indexName: uk_order_num
            unique: true
            columns:
              - column:
                  name: order_num

  - changeSet:
      id: chp-order-1738978250
      author: chp
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: refund_pic_count
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  afterColumn: pic_count
                  remarks: 退款照片数量
                  defaultValue: 0
  - changeSet:
      id: chp-order-1738979094
      author: chp
      changes:
        - addColumn:
            tableName: order_video_refund
            columns:
              - column:
                  name: refund_pic_count
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  afterColumn: pic_count
                  remarks: 退款照片数量
                  defaultValue: 0
              - column:
                  name: is_full_refund_pic
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  afterColumn: is_full_refund
                  remarks: 照片选配是否全额退款
                  defaultValue: 1
  - changeSet:
      id: chp-order-1739151630
      author: chp
      changes:
        - sql:
            sql: |
              UPDATE order_video_refund
              SET refund_pic_count = 5
              WHERE
              	pic_count = 2
              	AND refund_status = 4
              	AND refund_type = 3
              	AND refund_pic_count = 0;
              UPDATE order_video_refund
              SET refund_pic_count = 2
              WHERE
              	pic_count = 1
              	AND refund_status = 4
              	AND refund_type = 3
              	AND refund_pic_count = 0;
              UPDATE order_video ov
              INNER JOIN order_video_refund ovr ON ovr.video_id = ov.id
              SET ov.refund_pic_count = ovr.refund_pic_count
              WHERE
              	ov.pic_count IS NOT NULL
              	AND ovr.refund_type = 3
              	AND ov.refund_pic_count = 0
              	AND ovr.refund_status = 4;
  - changeSet:
      id: chp-order-1739260101
      author: chp
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: order_time_sign
                  type: datetime
                  afterColumn: order_time
                  remarks: 订单时间标记（每次开启订单、创建订单时间）
              - column:
                  name: close_order_time
                  type: datetime
                  afterColumn: audit_user_name
                  remarks: 关闭订单时间
  - changeSet:
      id: chp-order-1739266277
      author: chp
      changes:
        - sql:
            sql: |
              UPDATE order_table SET order_time_sign = order_time;
              update order_table ot INNER JOIN(
              SELECT
              	order_num,
              	MAX(status_time) closeTime
              FROM
              	order_video
              	where status = 9
              	group by order_num
              ) ov on ot.order_num = ov.order_num
              set ot.close_order_time = ov.closeTime
              where ot.audit_status = 3;

  - changeSet:
      id: chp-order-1739324342
      author: chp
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: difference_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 差额
                  defaultValueComputed: 0.00
                  afterColumn: real_pay_amount_currency

  - changeSet:
      id: chp-order-1739325072
      author: chp
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: difference_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 差额
                  defaultValueComputed: 0.00
                  afterColumn: use_balance
  - changeSet:
      id: chp-order-1739339245
      author: chp
      changes:
        - addColumn:
            tableName: order_video_refund
            columns:
              - column:
                  name: real_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 实际视频金额
                  defaultValueComputed: 0.00
                  afterColumn: refund_amount
        - sql:
            sql: |
              update order_video_refund set real_amount = amount;

  - changeSet:
      id: chp-order-1739416909
      author: chp
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: is_auto_cancel
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 是否自动取消(0-否，1-是)
                  defaultValue: 0
                  afterColumn: close_order_time
              - column:
                  name: reopen_count
                  type: int(8)
                  remarks: 开启订单次数
                  defaultValue: 0
                  afterColumn: order_time_sign
  - changeSet:
      id: chp-order-1739785737
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `order_table`
              MODIFY COLUMN `difference_amount` decimal(12, 2) DEFAULT '0.00' COMMENT '差额' AFTER `real_pay_amount_currency`;


  - changeSet:
      id: chp-order-1739845074
      author: chp
      changes:
        - addColumn:
            tableName: order_video_roast
            columns:
              - column:
                  name: issue_id
                  type: bigint(16)
                  constraints:
                    nullable: true
                  remarks: 英文部客服id
                  afterColumn: content
              - column:
                  name: contact_id
                  type: bigint(16)
                  constraints:
                    nullable: true
                  remarks: 中文部客服id
                  afterColumn: content
  - changeSet:
      id: chp-order-1739857381
      author: chp
      changes:
        - sql:
            sql: |
              UPDATE order_video_roast ovr
              INNER JOIN order_video ov ON ov.id = ovr.video_id
              SET ovr.contact_id = ov.contact_id,
              ovr.issue_id = ov.issue_id

  - changeSet:
      id: chp-order-1740015114
      author: chp
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: ban_invoice
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 是否禁止发票(0-否，1-是)
                  defaultValue: 0
                  afterColumn: is_auto_cancel

  - changeSet:
      id: chp-order-1741251393
      author: chp
      changes:
        - addColumn:
            tableName: order_audit_flow
            columns:
              - column:
                  name: pay_num
                  type: varchar(30)
                  constraints:
                    nullable: true
                  remarks: 支付单号
                  afterColumn: order_num
  - changeSet:
      id: chp-order-1741316587
      author: chp
      changes:
        - sql: |
            ALTER TABLE `order_audit_flow`
            MODIFY COLUMN `order_num` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '订单号 (FK：order_table.order_num)' AFTER `id`;

  - changeSet:
      id: chp-order-1741327806
      author: chp
      changes:
        - addColumn:
            tableName: order_pay_log
            columns:
              - column:
                  name: pay_num
                  type: varchar(30)
                  constraints:
                    nullable: true
                  remarks: 支付单号
                  afterColumn: order_num
  - changeSet:
      id: chp-order-1741327889
      author: chp
      changes:
        - sql: |
            ALTER TABLE `order_pay_log`
            MODIFY COLUMN `order_num` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '订单号 (FK：order_table.order_num)' AFTER `id`;
  - changeSet:
      id: chp-order-1741571721
      author: chp
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: is_merge_order
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 是否合并订单(0-否，1-是)
                  defaultValue: 0
                  afterColumn: is_record

  - changeSet:
      id: chp-order-1741694694
      author: chp
      changes:
        - sql: |
            ALTER TABLE `order_audit_flow`
            MODIFY COLUMN `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '订单号 (FK：order_table.order_num)' AFTER `id`;
            ALTER TABLE `order_pay_log`
            MODIFY COLUMN `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单号 (FK：order_table.order_num)' AFTER `id`;

  - changeSet:
      id: chp-order-1741922278
      author: chp
      changes:
        - sql: |
            ALTER TABLE `order_invoice_operate`
            MODIFY COLUMN `object_key` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '图片URI' AFTER `content`;
            ALTER TABLE `order_invoice`
            MODIFY COLUMN `object_key` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发票文件URI' AFTER `number`;
            ALTER TABLE `order_invoice_red`
            MODIFY COLUMN `object_key` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发票文件URI' AFTER `invoice_red_invoicing_time`;
            ALTER TABLE `order_invoice_record`
            MODIFY COLUMN `object_key` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '发票URI' AFTER `invoicing_time`;

  - changeSet:
      id: chp-order-1741775329
      author: chp
      changes:
        - sql: |
            ALTER TABLE `order_video_content`
             MODIFY COLUMN `sort` int DEFAULT 0 COMMENT '排序';

  - changeSet:
      id: chp-order-1741834748
      author: chp
      changes:
        - sql: |
            ALTER TABLE `order_video_change_log_info`
            MODIFY COLUMN `field_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字段名称' AFTER `change_log_id`;

  - changeSet:
      id: chp-order-1741836993
      author: chp
      changes:
        - sql: |
            ALTER TABLE `order_video_content`
            MODIFY COLUMN `first_edit` tinyint(1) NULL DEFAULT 0 COMMENT '对比首次是否变更(1:变更了,0:未变更)' AFTER `first_content`;


  - changeSet:
      id: chp-order-1741850041
      author: chp
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: video_style
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 视频风格(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
                  afterColumn: platform
              - column:
                  name: particular_emphasis_pic_ids
                  type: varchar(700)
                  constraints:
                    nullable: true
                  remarks: 特别强调图片（FK:order_resource.id，多个用,隔开）
                  afterColumn: cautions_pic

  - changeSet:
      id: chp-order-1741851965
      author: chp
      changes:
        - sql: |
            update order_video set video_style = platform where video_style is null;



  - changeSet:
      id: chp-order-1742195570
      author: chp
      changes:
        - addColumn:
            tableName: order_video_match
            columns:
              - column:
                  name: selling_point_product_change
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 产品卖点变更(1:变更了,0:未变更)
                  defaultValue: 0
                  afterColumn: cautions_change
              - column:
                  name: order_specification_require_change
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 商品规格要求变更(1:变更了,0:未变更)
                  defaultValue: 0
                  afterColumn: cautions_change
              - column:
                  name: particular_emphasis_change
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 特别强调变更(1:变更了,0:未变更)
                  defaultValue: 0
                  afterColumn: cautions_change

  - changeSet:
      id: chp-order-1742197569
      author: chp
      changes:
        - sql: |
            ALTER TABLE `order_video_match`
            MODIFY COLUMN `shoot_required_change` tinyint(1) NULL DEFAULT 0 COMMENT '拍摄建议变更(原拍摄要求)(1:变更了,0:未变更)' AFTER `goods_info_change`;

  - changeSet:
      id: chp-order-1742198421
      author: chp
      changes:
        - createTable:
            tableName: promotion_activity
            remarks: 活动信息表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  remarks: 主键
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: activity_name
                  type: VARCHAR(50)
                  remarks: 活动名称
                  constraints:
                    nullable: false
              - column:
                  name: start_time
                  type: datetime
                  remarks: 开始时间
                  constraints:
                    nullable: false
              - column:
                  name: end_time
                  type: datetime
                  remarks: 结束时间
                  constraints:
                    nullable: false
              - column:
                  name: activity_status
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  defaultValue: 1
                  remarks: 活动状态（0-无效，1-有效）
              - column:
                  name: remark
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 备注
                  defaultValue: ""
              - column:
                  name: create_user_id
                  type: bigint(20)
                  remarks: 创建人id
                  constraints:
                    nullable: true
              - column:
                  name: create_user_name
                  type: VARCHAR(32)
                  remarks: 创建人名称
                  constraints:
                    nullable: true
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  remarks: 创建时间
                  constraints:
                    nullable: false
              - column:
                  name: update_user_id
                  type: bigint(20)
                  remarks: 修改人id
                  constraints:
                    nullable: true
              - column:
                  name: update_user_name
                  type: VARCHAR(32)
                  remarks: 修改人名称
                  constraints:
                    nullable: true
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  remarks: 修改时间
                  constraints:
                    nullable: false
        - createTable:
            tableName: order_promotion_detail
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  remarks: 主键
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: activity_id
                  type: bigint(20)
                  remarks: 活动ID
                  constraints:
                    nullable: false
              - column:
                  name: order_num
                  type: VARCHAR(30)
                  remarks: 订单号
                  constraints:
                    nullable: false
              - column:
                  name: video_id
                  type: bigint(20)
                  remarks: 视频ID
              - column:
                  name: video_code
                  type: VARCHAR(10)
                  remarks: 视频编码
              - column:
                  name: amount
                  type: DECIMAL(12,2)
                  remarks: 优惠金额
                  constraints:
                    nullable: false
              - column:
                  name: create_user_id
                  type: bigint(20)
                  remarks: 创建人id
                  constraints:
                    nullable: true
              - column:
                  name: create_user_name
                  type: VARCHAR(32)
                  remarks: 创建人名称
                  constraints:
                    nullable: true
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  remarks: 创建时间
                  constraints:
                    nullable: false
              - column:
                  name: update_user_id
                  type: bigint(20)
                  remarks: 修改人id
                  constraints:
                    nullable: true
              - column:
                  name: update_user_name
                  type: VARCHAR(32)
                  remarks: 修改人名称
                  constraints:
                    nullable: true
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  remarks: 修改时间
                  constraints:
                    nullable: false
        - createIndex:
            tableName: order_promotion_detail
            indexName: idx_order_num
            unique: false
            columns:
              - column:
                  name: order_num
        - createIndex:
            tableName: order_promotion_detail
            indexName: idx_activity_id
            unique: false
            columns:
              - column:
                  name: activity_id

  - changeSet:
      id: chp-order-1742369506
      author: chp
      changes:
        - sql: |
            INSERT INTO `promotion_activity`(`id`, `activity_name`, `start_time`, `end_time`, `activity_status`, `remark`, `create_user_id`, `create_user_name`, `create_time`, `update_user_id`, `update_user_name`, `update_time`) VALUES (1, '满5-100', '2025-03-19 15:30:42', '2410-03-19 15:30:47', 1, '', 1, 'admin', '2025-03-19 15:31:18', 1, 'admin', '2025-03-19 15:31:18');

  - changeSet:
      id: chp-order-1742369601
      author: chp
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: order_promotion_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 订单活动优惠总额
                  defaultValueComputed: 0.00
                  afterColumn: real_pay_amount_currency
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: video_promotion_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 视频活动优惠总额
                  defaultValueComputed: 0.00
                  afterColumn: use_balance
  - changeSet:
      id: chp-order-1742372746
      author: chp
      changes:
        - addColumn:
            tableName: promotion_activity
            columns:
              - column:
                  name: type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  afterColumn: id
                  remarks: 优惠类型（1-满5单减100）
  - changeSet:
      id: chp-order-1742457978
      author: chp
      changes:
        - sql: |
            update promotion_activity set type = 1 where id = 1;

  - changeSet:
      id: chp-order-1742543194
      author: chp
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: pay_amount_dollar
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 需支付金额（单位：￥）
                  defaultValueComputed: 0.00
                  afterColumn: amount_dollar
              - column:
                  name: pay_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 需支付金额（单位：$）
                  defaultValueComputed: 0.00
                  afterColumn: amount_dollar
  - changeSet:
      id: chp-order-1742543352
      author: chp
      changes:
        - sql: |
            update order_video set pay_amount_dollar = amount_dollar;
            update order_video set pay_amount = amount;
  - changeSet:
      id: chp-order-1742552859
      author: chp
      changes:
        - addColumn:
            tableName: order_video_refund
            columns:
              - column:
                  name: refund_amount_total
                  type: decimal(12, 2)
                  constraints:
                    nullable: false
                  defaultValue: 0.00
                  remarks: 视频已退金额
                  afterColumn: refund_amount
              - column:
                  name: remark
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 备注
  - changeSet:
      id: chp-order-1742778660
      author: chp
      changes:
        - sql: |
            UPDATE order_video_refund t
            JOIN (
                SELECT
                    id,
                    SUM(refund_amount) OVER (
                        PARTITION BY video_id
                        ORDER BY id
                        ROWS BETWEEN UNBOUNDED PRECEDING AND 1 PRECEDING
                    ) AS total_refunded
                FROM order_video_refund
                WHERE refund_status = 4
            ) s ON t.id = s.id
            SET t.refund_amount_total = COALESCE(s.total_refunded, 0);





  - changeSet:
      id: chp-order-1742457703
      author: chp
      changes:
        - sql: |
            ALTER TABLE `order_video`
            MODIFY COLUMN `video_style` tinyint(1) NULL DEFAULT NULL COMMENT '视频风格(0:Amazon,1:tiktok,2:APP/解说类)' AFTER `platform`;

  - changeSet:
      id: chp-order-1742810942
      author: chp
      changes:
        - addColumn:
            tableName: order_video_refund
            columns:
              - column:
                  name: is_cancel_order
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValue: 0
                  remarks: 是否是取消订单数据(0-否,1-是)
                  afterColumn: initiator_source
  - changeSet:
      id: chp-order-1742865184
      author: chp
      changes:
        - sql: |
            UPDATE order_video_refund t
            JOIN (
                SELECT
                    id,
                    SUM(
                        CASE WHEN refund_status = 4 THEN refund_amount ELSE 0 END
                    ) OVER (
                        PARTITION BY video_id
                        ORDER BY id
                        ROWS BETWEEN UNBOUNDED PRECEDING AND 1 PRECEDING
                    ) AS total_refunded
                FROM order_video_refund
            ) s ON t.id = s.id
            SET t.refund_amount_total = COALESCE(s.total_refunded, 0);

  - changeSet:
      id: chp-order-1743668794
      author: chp
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: order_amount_dollar
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 订单金额（单位：$）
                  defaultValueComputed: 0.00
                  afterColumn: order_amount
  - changeSet:
      id: chp-order-1743669703
      author: chp
      changes:
        - sql: |
            UPDATE order_table ot
            JOIN ( SELECT
            			order_num,
            			sum(amount_dollar) orderAmountDollar
            			FROM order_video GROUP BY order_num ) ov ON ov.order_num = ot.order_num
            SET ot.order_amount_dollar = ov.orderAmountDollar;

            UPDATE order_table ot
            join order_member om on ot.order_num  = om.order_num
            set ot.order_amount_dollar = 119.7
            where om.package_type = 0;

            UPDATE order_table ot
            join order_member om on ot.order_num  = om.order_num
            set ot.order_amount_dollar = 238.8
            where om.package_type = 1;

            UPDATE order_table ot
            join order_member om on ot.order_num  = om.order_num
            set ot.order_amount_dollar = 644.4
            where om.package_type = 2;


  - changeSet:
      id: chp-order-1744081195
      author: chp
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: is_pause_oust
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 是否暂停淘汰(0-否,1-是)
                  defaultValue: 0
                  afterColumn: oust_time

  - changeSet:
      id: chp-order-1745219743
      author: chp
      changes:
        - addColumn:
            tableName: order_invoice_red_order_video
            columns:
              - column:
                  name: order_num
                  type: VARCHAR(30)
                  remarks: 订单号
                  constraints:
                    nullable: true
                  defaultValue: ""
                  afterColumn: invoice_red_order_id
        - sql: |
            ALTER TABLE `order_invoice_red_order_video`
            MODIFY COLUMN `video_id` bigint(0) NULL COMMENT '视频订单ID (FK:order_video.id)' AFTER `invoice_red_order_id`,
            MODIFY COLUMN `video_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '视频编码' AFTER `video_id`,
            MODIFY COLUMN `refund_type` tinyint(1) NULL DEFAULT NULL COMMENT '退款类型（1:补偿,2:取消订单,3:取消选配,8:线上钱包充值）' AFTER `video_code`;

  - changeSet:
      id: chp-order-1745287247
      author: system
      changes:
        - createTable:
            tableName: order_video_logistic_follow
            remarks: 物流跟进表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  remarks: 主键
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: order_video_logistic_id
                  type: bigint(20)
                  remarks: 物流关联表id order_video_logistic.id
                  constraints:
                    nullable: true
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家id（business.id）
              - column:
                  name: member_code
                  type: varchar(10)
                  constraints:
                    nullable: false
                  remarks: 会员编码（business.member_code）
              - column:
                  name: video_code
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 视频编码
              - column:
                  name: video_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 视频id FK:order_video.id
              - column:
                  name: number
                  type: varchar(100)
                  remarks: 物流单号
                  constraints:
                    nullable: true
                  defaultValue: ''
              - column:
                  name: handle_status
                  type: tinyint(1)
                  defaultValue: 0
                  remarks: 处理状态枚举(0-未通知、1-已通知、2-延迟发货、3-延迟发货提醒、4-延迟发货已提醒、5-催发货提醒、6-催发货已提醒、7-地址变更通知、8-变更已通知)
                  constraints:
                    nullable: false
              - column:
                  name: logistic_status
                  type: tinyint(1)
                  defaultValue: 0
                  remarks: 物流状态(0-未发货、1-已发货)
                  constraints:
                    nullable: false
              - column:
                  name: follow_status
                  type: tinyint(1)
                  defaultValue: 1
                  remarks: 跟进状态码(1-需处理、2-暂不处理、10-已发货、11-需跟进、12-模特待确认、13-无需跟进、14-已结束)
              - column:
                  name: notify_time
                  type: datetime
                  remarks: 通知时间
              - column:
                  name: logistic_start_time
                  type: datetime
                  remarks: 发货时间
              - column:
                  name: is_default_logistic_start_time
                  type: tinyint(1)
                  defaultValue: 0
                  remarks: 是否默认发货时间：0-否，1-是
              - column:
                  name: latest_main_status
                  type: varchar(20)
                  constraints:
                    nullable: true
                  remarks: 物流最新主状态
              - column:
                  name: logistic_update_time
                  type: datetime
                  remarks: 物流系统同步时间
              - column:
                  name: model_result
                  type: tinyint(1)
                  remarks: 模特结果枚举(0-待处理、1-已询问、2-已收货、3-丢件)
                  defaultValue: 0
              - column:
                  name: sign_time
                  type: datetime
                  remarks: 实际签收时间
              - column:
                  name: latest_remark
                  type: varchar(300)
                  remarks: 最新处理说明
              - column:
                  name: create_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建人姓名
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 创建人ID
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 更新人姓名
              - column:
                  name: update_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 更新人ID
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                  remarks: 修改时间
                  constraints:
                    nullable: true

  - changeSet:
      id: chp-order-1745306666
      author: chp
      changes:
        - createTable:
            tableName: order_video_logistic_follow_record
            remarks: 跟进记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  remarks: 主键
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: follow_id
                  type: bigint(20)
                  remarks: 物流更新表id order_video_logistic_follow.id
                  constraints:
                    nullable: false
              - column:
                  name: resource_id
                  type: varchar(300)
                  remarks: 图片资源地址id
              - column:
                  name: event_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 事件名称
              - column:
                  name: event_content
                  type: varchar(1200)
                  constraints:
                    nullable: false
                  remarks: 事件内容
              - column:
                  name: event_execute_object
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 事件执行对象（1:商家,2:运营,3:模特,9:系统）
              - column:
                  name: event_execute_user_id
                  type: bigint(20)
                  remarks: 事件执行人用户id
              - column:
                  name: event_execute_user_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 事件执行人用户名称
              - column:
                  name: event_execute_nick_name
                  type: varchar(50)
                  constraints:
                    nullable: true
                  remarks: 事件执行人微信昵称
              - column:
                  name: event_execute_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 事件执行时间
  - changeSet:
      id: chp-order-1745465334
      author: chp
      changes:
        - sql: |
            ALTER TABLE `order_video_logistic_follow`
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_by_id`;
  - changeSet:
      id: chp-order-1745735097
      author: chp
      changes:
        - addColumn:
            tableName: order_video_logistic_follow
            columns:
              - column:
                  name: is_call_back
                  type: tinyint(1)
                  remarks: 是否回调（0-手动 1-系统）
                  constraints:
                    nullable: true
                  defaultValue: 1
                  afterColumn: logistic_update_time

  - changeSet:
      id: chp-order-1745736147
      author: chp
      changes:
        - addColumn:
            tableName: order_video_logistic
            columns:
              - column:
                  name: is_cancel
                  type: tinyint(1)
                  remarks: 是否作废（0-否 1-是）
                  constraints:
                    nullable: true
                  defaultValue: 0
                  afterColumn: logistic_flag_time
  - changeSet:
      id: chp-order-1745893838
      author: chp
      changes:
        - addColumn:
            tableName: order_video_logistic_follow
            columns:
              - column:
                  name: latest_resource_id
                  type: varchar(300)
                  remarks: 图片资源地址id
                  constraints:
                    nullable: true
                  afterColumn: latest_remark
  - changeSet:
      id: chp-order-1746496425
      author: chp
      changes:
        - addColumn:
            tableName: order_video_logistic_follow_record
            columns:
              - column:
                  name: remark
                  type: varchar(300)
                  remarks: 备注
                  constraints:
                    nullable: true
                  afterColumn: resource_id
  - changeSet:
      id: chp-order-1747100903
      author: chp
      changes:
        - addColumn:
            tableName: order_video_match
            columns:
              - column:
                  name: pic_count_change
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 照片数量变更(1:变更了,0:未变更)
                  defaultValue: 0
                  afterColumn: goods_info_change
              - column:
                  name: intention_model_change
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 意向模特变更(1:变更了,0:未变更)
                  defaultValue: 0
                  afterColumn: goods_info_change
  - changeSet:
      id: chp-1747279691
      author: chp
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: member_discount_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 会员结算类型（1-固定金额，2-固定比例）
                  defaultValue: 2
                  afterColumn: seed_code
  - changeSet:
      id: chp-1747360232
      author: chp
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: seed_id
                  type: varchar(16)
                  constraints:
                    nullable: false
                  remarks: 种草官ID
                  afterColumn: seed_code
                  defaultValue: ''
  - changeSet:
      id: chp-order-1747100903
      author: chp
      changes:
        - addColumn:
            tableName: order_video_match
            columns:
              - column:
                  name: pic_count_change
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 照片数量变更(1:变更了,0:未变更)
                  defaultValue: 0
                  afterColumn: goods_info_change
              - column:
                  name: intention_model_change
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 意向模特变更(1:变更了,0:未变更)
                  defaultValue: 0
                  afterColumn: goods_info_change
  - changeSet:
      id: chp-1748313692
      author: chp
      changes:
        - sql: |
            INSERT INTO `promotion_activity` (`id`, `type`, `activity_name`, `start_time`, `end_time`, `activity_status`, `create_user_id`, `create_user_name`, `create_time`, `update_user_id`, `update_user_name`, `update_time`)
            VALUES (5, 5, '裂变优惠', '2025-05-16 00:00:00', '3000-01-01 00:00:00', '1', '1', 'admin', NOW(), '1', 'admin', NOW());
            UPDATE `promotion_activity` SET `activity_name` = '渠道优惠' WHERE `id` = 3;
      comment: 初始化 裂变优惠活动 修改种草码优惠活动

  - changeSet:
      id: chp-order-1748338260
      author: chp
      changes:
        - addColumn:
            tableName: promotion_activity_amendment_record
            columns:
              - column:
                  name: settle_discount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 结算佣金
                  defaultValue: 0.00
                  afterColumn: currency
              - column:
                  name: settle_discount_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 结算佣金类型（1-固定金额，2-固定比例）
                  defaultValue: 1
                  afterColumn: currency

  - changeSet:
      id: chp-order-1749433043
      author: chp
      changes:
        - addColumn:
            tableName: order_video_logistic_follow
            columns:
              - column:
                  name: product_chinese
                  type: varchar(255)
                  constraints:
                    nullable: true
                  remarks: 产品中文名
              - column:
                  name: product_english
                  type: varchar(255)
                  constraints:
                    nullable: true
                  remarks: 产品英文名
              - column:
                  name: shoot_model_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 拍摄模特ID
              - column:
                  name: create_order_user_name
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建订单用户名称（订单运营）
              - column:
                  name: create_order_user_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建订单用户微信名称（订单运营）
              - column:
                  name: create_order_operation_user_name
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建订单用户名称（下单运营）
              - column:
                  name: create_order_operation_user_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建订单用户微信名称（下单运营）
              - column:
                  name: issue_id
                  type: bigint(16)
                  constraints:
                    nullable: true
                  remarks: 英文部客服id
              - column:
                  name: contact_id
                  type: bigint(16)
                  constraints:
                    nullable: true
                  remarks: 中文部客服id


  - changeSet:
      id: chp-order-1749436061
      author: chp
      changes:
        - addColumn:
            tableName: order_video_logistic_follow
            columns:
              - column:
                  name: product_pic
                  type: varchar(64)
                  constraints:
                    nullable: true
                  remarks: 产品图URI
              - column:
                  name: product_link
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 产品链接
  - changeSet:
      id: chp-order-1749439403
      author: chp
      changes:
        - sql:
            sql: |
              update order_video_logistic_follow ovlf
              inner join order_video ov on ov.id = ovlf.video_id
              set ovlf.product_chinese = ov.product_chinese,
              ovlf.product_english = ov.product_english,
              ovlf.shoot_model_id = ov.shoot_model_id,
              ovlf.create_order_user_name = ov.create_order_user_name,
              ovlf.create_order_user_nick_name = ov.create_order_user_nick_name,
              ovlf.create_order_operation_user_name = ov.create_order_operation_user_name,
              ovlf.create_order_operation_user_nick_name = ov.create_order_operation_user_nick_name,
              ovlf.issue_id = ov.issue_id,
              ovlf.contact_id = ov.contact_id,
              ovlf.product_pic = ov.product_pic,
              ovlf.product_link = ov.product_link
              where ovlf.follow_status = 14;


  - changeSet:
      id: chp-order-1749518090
      author: chp
      changes:
        - addColumn:
            tableName: order_video_logistic_follow
            columns:
              - column:
                  name: platform
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
        - sql:
            sql: |
              update order_video_logistic_follow ovlf
                            inner join order_video ov on ov.id = ovlf.video_id
                            set ovlf.platform = ov.platform
                            where ovlf.follow_status = 14;

  - changeSet:
      id: chp-order-1749541589
      author: chp
      changes:
        - sql:
            sql: |

              ALTER TABLE `order_video_logistic_follow`
              MODIFY COLUMN `handle_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '处理状态枚举(0-未通知、1-已通知、2-延迟发货、3-延迟发货提醒、4-延迟发货已提醒、5-催发货提醒、6-催发货已提醒、7-地址变更通知、8-变更已通知、9-补充说明、10-标记发货、11-标记发货提醒、12-标记发货已提醒、13-通知确认模特、14-已通知确认模特、15-催确认模特提醒、16-催确认模特已提醒)' AFTER `number`,
              MODIFY COLUMN `model_result` tinyint(1) NULL DEFAULT 0 COMMENT '模特结果枚举(0-待处理、1-已询问、2-已收货、3-丢件、4-订单回退、5-待通知拍摄、6-已通知拍摄)' AFTER `is_call_back`;


