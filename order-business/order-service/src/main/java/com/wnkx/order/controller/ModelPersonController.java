package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.OrderVideoOperateTypeEnum;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.dto.order.OrderModelMyOrderListDTO;
import com.ruoyi.system.api.domain.dto.order.UploadLinkDTO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.wnkx.order.annotations.OrderPermissions;
import com.wnkx.order.annotations.OrderVideoOperate;
import com.wnkx.order.service.ModelPersonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 模特端-个人主页
 *
 * <AUTHOR>
 * @date 2024/7/4 17:07
 */
@RestController
@RequestMapping("/model/person")
@Api(value = "模特端-个人主页", tags = "模特端-个人主页")
@RequiredArgsConstructor
@Validated
public class ModelPersonController extends BaseController {

    private final ModelPersonService modelPersonService;

    /**
     * 我的订单
     */
    @GetMapping("/my-order")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation(value = "我的订单", response = OrderModelMyOrderListVO.class)
    public R<PageInfo<OrderModelMyOrderListVO>> myOrder(@Valid OrderModelMyOrderListDTO dto) {
        List<OrderModelMyOrderListVO> list = modelPersonService.myOrder(dto);
        return R.ok(toPage(list));
    }

    /**
     * 售后订单
     */
    @GetMapping("/after-sales")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation("售后订单")
    public R<PageInfo<OrderModelWorkbenchAfterSalesListVO>> afterSales(@Valid OrderModelMyOrderListDTO dto) {
        List<OrderModelWorkbenchAfterSalesListVO> list = modelPersonService.afterSales(dto);
        return R.ok(toPage(list));
    }

    /**
     * 我的订单徽标
     */
    @GetMapping("/my-order-badge")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation(value = "我的订单徽标")
    public R<OrderModelMyOrderBadgeVO> myOrderBadge() {
        OrderModelMyOrderBadgeVO count = modelPersonService.myOrderBadge();
        return R.ok(count);
    }

    /**
     * 订单详情
     */
    @GetMapping("/get-order-model-normal-info/{videoId}")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation("订单详情")
    @OrderPermissions(orderId = "#videoId")
    public R<OrderModelNormalInfoVO> getOrderModelNormalInfo(@PathVariable("videoId") Long videoId) {
        OrderModelNormalInfoVO orderModelInfoVO = modelPersonService.getOrderModelNormalInfo(videoId);
        return R.ok(orderModelInfoVO);
    }

    @GetMapping("/getOrderModelNormalInfoByVideoCode/{videoCode}")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation("根据视频编码获取订单详情")
    public R<OrderModelNormalInfoVO> getOrderModelNormalInfoByVideoCode(@PathVariable("videoCode") String videoCode) {
        OrderModelNormalInfoVO orderModelInfoVO = modelPersonService.getOrderModelNormalInfoByVideoCode(videoCode);
        return R.ok(orderModelInfoVO);
    }

    /**
     * 售后订单详情
     */
    @GetMapping("/get-after-sales-detail-info/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation("售后订单详情")
    public R<OrderModelAfterSalesInfoVO> afterSalesDetailInfo(@ApiParam("售后单id（反馈素材id）") @PathVariable("id") Long id) {
        OrderModelAfterSalesInfoVO orderModelInfoVO = modelPersonService.afterSalesDetailInfo(id);
        return R.ok(orderModelInfoVO);
    }

    /**
     * 上传链接
     */
    @PostMapping("/upload-link")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation("上传链接")
    @OrderPermissions(orderId = "#dto.videoId")
    @OrderVideoOperate(operateType = OrderVideoOperateTypeEnum.MODEL_FEEDBACK_MATERIAL, videoId = "#dto.videoId")
    public R<String> uploadLink(@RequestBody @Validated UploadLinkDTO dto) {
        modelPersonService.uploadLink(dto);
        return R.ok();
    }

    /**
     * 上传链接记录
     */
    @GetMapping("/get-upload-link-record/{videoId}")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation("上传链接记录")
    @OrderPermissions(orderId = "#videoId")
    public R<List<OrderFeedBackMaterialVO>> getUploadLinkRecord(@PathVariable("videoId") Long videoId) {
        List<OrderFeedBackMaterialVO> list = modelPersonService.getUploadLinkRecord(videoId);
        return R.ok(list);
    }
    @GetMapping("/getUploadLinkRecordByVideoCode/{videoCode}")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation("根据视频编码获取上传链接记录")
    public R<List<OrderFeedBackMaterialVO>> getUploadLinkRecordByVideoCode(@PathVariable("videoCode") String videoCode) {
        List<OrderFeedBackMaterialVO> list = modelPersonService.getUploadLinkRecordByVideoCode(videoCode);
        return R.ok(list);
    }

    /**
     * 上传链接记录（售后）
     */
    @GetMapping("/get-upload-link-record-after-sale/{materialId}")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation("上传链接记录（售后）")
    public R<OrderFeedBackMaterialVO> getUploadLinkRecordAfterSale(@PathVariable("materialId") Long materialId) {
        OrderFeedBackMaterialVO orderFeedBackMaterialVO = modelPersonService.getUploadLinkRecordAfterSale(materialId);
        return R.ok(orderFeedBackMaterialVO);
    }

    /**
     * 模特选择记录
     */
    @GetMapping("/get-model-select-record")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation("模特选择记录")
    public R<PageInfo<OrderVideoModelSelectListVO>> getModelSelectRecord() {
        List<OrderVideoModelSelectListVO> list = modelPersonService.getModelSelectRecord();
        return R.ok(toPage(list));
    }

    /**
     * 模特撤销申请
     */
    @PostMapping("/cancel-apply")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation("模特撤销申请")
    public R<Void> cancelApply(@RequestParam("videoId") Long videoId) {
        modelPersonService.cancelApply(videoId);
        return R.ok();
    }
}
