package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.dto.order.OrderListDTO;
import com.ruoyi.system.api.domain.vo.order.CheckOrderMergeVO;
import com.ruoyi.system.api.domain.vo.order.OrderMergeListVO;
import com.wnkx.order.service.OrderMergeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-03-04 10:00:03
 */
@RestController
@Api(value = "订单合并服务", tags = "订单合并服务")
@RequestMapping("/merge")
@RequiredArgsConstructor
@Validated
public class OrderMergeController extends BaseController {

    private final OrderMergeService orderMergeService;

    /**
     * 合并订单
     */
    @PostMapping(value = "/merge-order")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "合并订单")
    public R<Long> mergeOrder(@RequestBody @Size(min = 2, message = "请选择至少2个订单") List<String> orderNums) {
        Long mergeId = orderMergeService.createOrderMerge(orderNums);
        return R.ok(mergeId);
    }

    /**
     * 商家端-需支付订单列表
     */
    @GetMapping(value = "/need-pay-order")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "商家端-需支付订单列表")
    public R<PageInfo<OrderMergeListVO>> selectNeedPayOrderListByCondition(@Validated OrderListDTO orderListDTO) {
        List<OrderMergeListVO> list = orderMergeService.selectNeedPayOrderListByCondition(orderListDTO);
        return R.ok(toPage(list));
    }

    /**
     * 获取取消合并校验会到期的订单
     */
    @GetMapping(value = "/check-cancel-merge")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "获取取消合并校验会到期的订单")
    public R<List<String>> checkCancelMerge(@RequestParam("mergeId") @ApiParam("合并单ID") Long mergeId) {
        List<String> expireOrderNums = orderMergeService.checkCancelMerge(mergeId);
        return R.ok(expireOrderNums);
    }

    /**
     * 取消合并
     */
    @PostMapping(value = "/cancel-merge")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "取消合并")
    public R<Void> cancelMerge(@RequestParam("mergeId") @ApiParam("合并单ID") Long mergeId) {
        orderMergeService.cancelMerge(mergeId, false);
        return R.ok();
    }

    /**
     * 校验订单是否已合并
     */
    @GetMapping(value = "/check-order-merge")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "校验订单是否已合并")
    public R<CheckOrderMergeVO> checkOrderMerge(@RequestParam @ApiParam("订单号") String orderNum) {
        return R.ok(orderMergeService.closeOrderCheckOrderMerge(orderNum));
    }
}
