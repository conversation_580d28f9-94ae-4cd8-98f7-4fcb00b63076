package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceOperate;
import com.ruoyi.system.api.domain.vo.order.*;
import com.wnkx.order.service.IOrderInvoiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:57
 */
@RestController
@RequestMapping("/invoice")
@Api(value = "发票服务", tags = "发票服务")
@RequiredArgsConstructor
@Validated
public class OrderInvoiceController extends BaseController {

    private final IOrderInvoiceService orderInvoiceService;

    /**
     * 商家端-发票管理-未开票列表
     */
    @GetMapping("/company-not-invoiced-list")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "商家端-发票管理-未开票列表", response = CompanyNotInvoicedListVO.class)
    public R<PageInfo<CompanyNotInvoicedListVO>> selectCompanyNotInvoicedListByCondition(@Validated CompanyNotInvoicedListDTO dto) {
        List<CompanyNotInvoicedListVO> list = orderInvoiceService.selectCompanyNotInvoicedListByCondition(dto);
        return R.ok(toPage(list));
    }


    @GetMapping("/company-not-invoiced-order-detail")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "商家端-发票管理-订单详情", response = CompanyNotInvoicedOrderDetailVO.class)
    public R<CompanyNotInvoicedOrderDetailVO> getCompanyNotInvoicedOrderDetail(@RequestParam("orderId") Long orderId, @RequestParam("orderType") Integer orderType) {
        CompanyNotInvoicedOrderDetailVO detailVO = orderInvoiceService.getCompanyNotInvoicedOrderDetail(orderId, orderType);
        return R.ok(detailVO);
    }

    /**
     * 商家端-发票管理-申请开票
     */
    @PostMapping("/apply-for-billing")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "商家端-发票管理-申请开票")
    public R<Void> applyForBilling(@RequestBody @Validated ApplyForBillingDTO dto) {
        orderInvoiceService.applyForBilling(dto);
        return R.ok();
    }

    /**
     * 商家/运营端-发票管理-待开票列表
     */
    @GetMapping("/to-be-invoiced-list")
    @LoginUserType(userTypes = {UserTypeConstants.USER_TYPE, UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "商家/运营端-发票管理-待开票列表", response = ToBeInvoicedListVO.class)
    public R<PageInfo<ToBeInvoicedListVO>> selectToBeInvoicedListByCondition(ToBeInvoicedListDTO dto) {
        List<ToBeInvoicedListVO> list = orderInvoiceService.selectToBeInvoicedListByCondition(dto);
        return R.ok(toPage(list));
    }

    /**
     * 运营端-发票管理-待开票列表-导出
     */
    @PostMapping("/to-be-invoiced-list-export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-发票管理-待开票列表-导出")
    @RequiresPermissions("finance:invoice:wait-export")
    public void exportToBeInvoicedList(ToBeInvoicedListDTO dto, HttpServletResponse response) {
        orderInvoiceService.exportToBeInvoicedList(dto, response);
    }

    /**
     * 商家端-发票管理-发票详情
     */
    @GetMapping("/company-invoice-detail")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "商家端-发票管理-发票详情", response = OrderInvoiceDetailVO.class)
    public R<OrderInvoiceDetailVO> getCompanyInvoiceDetail(@RequestParam("invoiceId") Long invoiceId) {
        OrderInvoiceDetailVO detailVO = orderInvoiceService.getCompanyInvoiceDetail(invoiceId);
        return R.ok(detailVO);
    }

    /**
     * 运营端-发票管理-发票详情
     */
    @GetMapping("/back-invoice-detail")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-发票管理-发票详情", response = OrderInvoiceDetailVO.class)
    @RequiresPermissions("finance:invoice:view")
    public R<OrderInvoiceDetailVO> getBackInvoiceVideoList(@RequestParam("invoiceId") Long invoiceId) {
        OrderInvoiceDetailVO detailVO = orderInvoiceService.getBackInvoiceVideoList(invoiceId);
        return R.ok(detailVO);
    }

    /**
     * 运营端-发票管理-流转记录
     */
    @GetMapping("/invoice-operate-record")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-发票管理-流转记录")
    @RequiresPermissions("finance:invoice:view")
    public R<List<OrderInvoiceOperate>> getInvoiceOperateRecord(@RequestParam("invoiceId") Long invoiceId) {
        List<OrderInvoiceOperate> list = orderInvoiceService.getInvoiceOperateRecord(invoiceId);
        return R.ok(list);
    }

    /**
     * 商家/运营端-发票管理-取消开票
     */
    @PostMapping("/cancel-invoice")
    @LoginUserType(userTypes = {UserTypeConstants.USER_TYPE, UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "商家/运营端-发票管理-取消开票")
    @RequiresPermissions("finance:invoice:cancel")
    public R<Void> cancelInvoice(@RequestParam("invoiceId") Long invoiceId) {
        orderInvoiceService.cancelInvoice(invoiceId);
        return R.ok();
    }

    /**
     * 运营端-发票管理-审核发票
     */
    @PostMapping("/audit-invoice")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-发票管理-审核发票")
    @RequiresPermissions("finance:invoice:audit")
    public R<Void> auditInvoice(@RequestBody @Validated AuditInvoiceDTO dto) {
        orderInvoiceService.auditInvoice(dto);
        return R.ok();
    }

    /**
     * 运营端-发票管理-修改发票
     */
    @PostMapping("/update-invoice")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-发票管理-修改发票")
    @RequiresPermissions("finance:invoice:edit")
    public R<Void> updateInvoice(@RequestBody @Validated AuditInvoiceDTO dto) {
        orderInvoiceService.updateInvoice(dto);
        return R.ok();
    }

    /**
     * 运营端-发票管理-上传发票
     */
    @PostMapping("/upload-invoice")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-发票管理-上传发票")
    @RequiresPermissions("finance:invoice:upload")
    public R<Void> uploadInvoice(@RequestBody @Validated UploadInvoiceDTO dto) {
        orderInvoiceService.uploadInvoice(dto);
        return R.ok();
    }

    /**
     * 运营端-发票管理-重新上传发票
     */
    @PostMapping("/re-upload-invoice")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-发票管理-重新上传发票")
    @RequiresPermissions("finance:invoice:upload")
    public R<Void> reUploadInvoice(@RequestBody @Validated UploadInvoiceDTO dto) {
        orderInvoiceService.reUploadInvoice(dto);
        return R.ok();
    }

    /**
     * 运营端-发票管理-确认发票
     */
    @PostMapping("/confirm-invoice")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-发票管理-确认发票")
    @RequiresPermissions("finance:invoice:confirm")
    public R<Void> confirmInvoice(@RequestParam("invoiceId") Long invoiceId) {
        orderInvoiceService.confirmInvoice(invoiceId);
        return R.ok();
    }

    /**
     * 运营端-发票管理-待红冲列表
     */
    @GetMapping("/to-be-red-invoice-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-发票管理-待红冲列表", response = ToBeRedInvoiceListVO.class)
    public R<PageInfo<ToBeRedInvoiceListVO>> selectToBeRedInvoiceListByCondition(ToBeRedInvoiceListDTO dto) {
        List<ToBeRedInvoiceListVO> list = orderInvoiceService.selectToBeRedInvoiceListByCondition(dto);
        return R.ok(toPage(list));
    }

    /**
     * 运营端-发票管理-待红冲列表-导出
     */
    @PostMapping("/to-be-red-invoice-list-export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-发票管理-待红冲列表-导出")
    @RequiresPermissions("finance:invoice:red-export")
    public void exportToBeRedInvoiceList(ToBeRedInvoiceListDTO dto, HttpServletResponse response) {
        orderInvoiceService.exportToBeRedInvoiceList(dto, response);
    }

    /**
     * 运营端-发票管理-开票记录
     */
    @GetMapping("/invoice-record")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-发票管理-开票记录", response = OrderInvoiceRecordVO.class)
    @RequiresPermissions("finance:invoice:view")
    public R<List<OrderInvoiceRecordVO>> getInvoiceRecord(@RequestParam("invoiceId") Long invoiceId) {
        List<OrderInvoiceRecordVO> list = orderInvoiceService.getInvoiceRecord(invoiceId);
        return R.ok(list);
    }

    /**
     * 运营端-发票管理-标记红冲
     */
    @PostMapping("/mark-red-invoice")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-发票管理-标记红冲")
    @RequiresPermissions("finance:invoice:sign")
    public R<Void> markRedInvoice(@RequestBody @Validated MarkRedInvoiceDTO dto) {
        orderInvoiceService.markRedInvoice(dto);
        return R.ok();
    }

    /**
     * 运营端-发票管理-红冲详情
     */
    @GetMapping("/red-invoice-detail")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-发票管理-红冲详情", response = OrderInvoiceRedDetailVO.class)
    @RequiresPermissions("finance:invoice:sign")
    public R<OrderInvoiceRedDetailVO> getRedInvoiceDetail(@RequestParam("invoiceRedId") Long invoiceRedId) {
        OrderInvoiceRedDetailVO detailVO = orderInvoiceService.getRedInvoiceDetail(invoiceRedId);
        return R.ok(detailVO);
    }

    /**
     * 商家/运营端-发票管理-已完成列表
     */
    @GetMapping("/invoice-finish-list")
    @LoginUserType(userTypes = {UserTypeConstants.USER_TYPE, UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "商家/运营端-发票管理-已完成列表", response = InvoiceFinishListVO.class)
    public R<PageInfo<InvoiceFinishListVO>> selectInvoiceFinishListByCondition(InvoiceFinishListDTO dto) {
        List<InvoiceFinishListVO> list = orderInvoiceService.selectInvoiceFinishListByCondition(dto);
        return R.ok(toPage(list));
    }

    /**
     * 运营端-发票管理-已完成列表-导出
     */
    @PostMapping("/invoice-finish-list-export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-发票管理-已完成列表-导出")
    @RequiresPermissions("finance:invoice:done-export")
    public void exportInvoiceFinishList(InvoiceFinishListDTO dto, HttpServletResponse response) {
        orderInvoiceService.exportInvoiceFinishList(dto, response);
    }

    /**
     * 商家端-发票管理-去除发票新标记
     */
    @GetMapping("/remove-invoice-new-flag")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "商家端-发票管理-去除发票新标记")
    public R<Void> removeInvoiceNewFlag(@RequestParam("invoiceId") Long invoiceId) {
        orderInvoiceService.removeInvoiceNewFlag(invoiceId);
        return R.ok();
    }

    /**
     * 商家端-发票管理-申请重开
     */
    @PostMapping("/apply-for-reopening")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "商家端-发票管理-申请重开")
    public R<Void> applyForReopening(@RequestBody @Validated ApplyForReopeningDTO dto) {
        orderInvoiceService.applyForReopening(dto);
        return R.ok();
    }

    /**
     * 商家端-发票管理-数量统计
     */
    @GetMapping("/company-invoice-statistics")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "商家端-发票管理-数量统计")
    public R<CompanyInvoiceStatisticsVO> companyInvoiceStatistics() {
        CompanyInvoiceStatisticsVO statisticsVO = orderInvoiceService.companyInvoiceStatistics();
        return R.ok(statisticsVO);
    }

    /**
     * 运营端-发票管理-数量统计
     */
    @GetMapping("/back-invoice-statistics")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-发票管理-数量统计")
    public R<BackInvoiceStatisticsVO> backInvoiceStatistics() {
        BackInvoiceStatisticsVO statisticsVO = orderInvoiceService.backInvoiceStatistics();
        return R.ok(statisticsVO);
    }

    /**
     * 运营端-发票管理-开票金额统计
     */
    @GetMapping("/invoice-amount-statistics")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-发票管理-开票金额统计")
    public R<InvoiceAmountStatisticsVO> invoiceAmountStatistics() {
        InvoiceAmountStatisticsVO statisticsVO = orderInvoiceService.invoiceAmountStatistics();
        return R.ok(statisticsVO);
    }

    /**
     * 提现申请通过后 对发票的处理
     */
    @PostMapping("/withdrawal-success")
    @InnerAuth
    public R<Boolean> withdrawalSuccess(@RequestBody List<WithdrawalSuccessDTO> dtoList) {
        orderInvoiceService.withdrawalSuccess(dtoList);
        return R.ok(true);
    }
}
