package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableOrderScheduledRecordDTO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableOrderScheduledRecordVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableOrderScheduledTagCountVO;
import com.wnkx.order.service.ModelDataTableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/9 15:02
 */
@RestController
@RequestMapping("/model-data-table")
@Api(value = "模特数据表信息服务", tags = "模特数据表信息服务")
@Validated
@RequiredArgsConstructor
public class ModelDataTableController extends BaseController {

    private final ModelDataTableService modelDataTableService;

    /**
     * 排单记录
     */
    @GetMapping("/order-scheduled-record")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "排单记录")
    public R<PageInfo<ModelDataTableOrderScheduledRecordVO>> selectModelDataTableOrderScheduledRecordList(@Validated ModelDataTableOrderScheduledRecordDTO dto) {
        List<ModelDataTableOrderScheduledRecordVO> list = modelDataTableService.selectModelDataTableOrderScheduledRecordList(dto);
        return R.ok(toPage(list));
    }

    /**
     * 排单记录-标签统计
     */
    @GetMapping("/order-scheduled-record/tag-count")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "排单记录-标签统计")
    public R<ModelDataTableOrderScheduledTagCountVO> getModelDataTableOrderScheduledTagCount(@RequestParam Long modelId) {
        ModelDataTableOrderScheduledTagCountVO modelDataTableOrderScheduledTagCount = modelDataTableService.getModelDataTableOrderScheduledTagCount(modelId);
        return R.ok(modelDataTableOrderScheduledTagCount);
    }
}
