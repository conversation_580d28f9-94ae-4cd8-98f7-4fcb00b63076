package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.vo.order.OrderModelWorkbenchForMeListVO;
import com.wnkx.order.dto.ModelPassDTO;
import com.wnkx.order.service.ModelWorkbenchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模特端-工作台
 *
 * <AUTHOR>
 * @date 2024/7/3
 */
@RestController
@RequestMapping("/model/workbench")
@Api(value = "模特端-工作台", tags = "模特端-工作台")
@RequiredArgsConstructor
@Validated
public class ModelWorkbenchController extends BaseController {

    private final ModelWorkbenchService modelWorkbenchService;

    /**
     * 给我的
     */
    @GetMapping("for-me")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation(value = "给我的", response = PageInfo.class)
    public R<PageInfo<OrderModelWorkbenchForMeListVO>> forMe() {
        List<OrderModelWorkbenchForMeListVO> list = modelWorkbenchService.forMe(null);
        return R.ok(toPage(list));
    }

    /**
     * 模特接单
     */
    @PostMapping("accept")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation(value = "模特接单")
    public R<String> accept(@ApiParam("视频订单id") @RequestParam("videoId") Long videoId,
                            @ApiParam("视频订单id") @RequestParam("preselectModelId") Long preselectModelId) {
        modelWorkbenchService.accept(videoId, preselectModelId);
        return R.ok();
    }

    /**
     * 模特拒绝订单
     */
    @PostMapping("pass")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation(value = "模特拒绝订单")
    public R<String> pass(@RequestBody @Validated ModelPassDTO dto) {
        modelWorkbenchService.pass(dto.getVideoId(), dto.getPreselectModelId(), dto.getRemark());
        return R.ok();
    }
}
