package com.wnkx.order.controller;


import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.BackUserTypeEnum;
import com.ruoyi.common.core.enums.OrderVideoOperateTypeEnum;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.biz.translate.TranslateBatchDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.wnkx.order.annotations.OrderPermissions;
import com.wnkx.order.annotations.OrderRefundVerify;
import com.wnkx.order.annotations.OrderVideoOperate;
import com.wnkx.order.service.IOrderVideoMatchPreselectModelService;
import com.wnkx.order.service.OrderVideoMatchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/11/16 15:44
 */
@RestController
@RequestMapping("/match")
@Api(value = "预选管理", tags = "预选管理")
@RequiredArgsConstructor
@Validated
public class OrderVideoMatchController extends BaseController {

    private final OrderVideoMatchService orderVideoMatchService;
    private final IOrderVideoMatchPreselectModelService orderVideoMatchPreselectModelService;

    /**
     * 预选管理-模特匹配-订单池
     */
    @GetMapping("/order-pool-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "预选管理-模特匹配-订单池")
    @RequiresPermissions("task:preselection:list")
    public R<PageInfo<OrderPoolListVO>> selectOrderPoolListByCondition(@Validated OrderPoolListDTO orderPoolListDTO) {
        List<OrderPoolListVO> list = orderVideoMatchService.selectOrderPoolListByCondition(orderPoolListDTO);
        return R.ok(toPage(list));
    }

    /**
     * 预选管理-模特匹配-我的预选-沟通中
     */
    @GetMapping("/my-preselect-docking-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "预选管理-模特匹配-我的预选-沟通中")
    @RequiresPermissions("task:preselection:list")
    public R<PageInfo<MyPreselectDockingListVO>> selectMyPreselectDockingList(@Validated MyPreselectDockingListDTO dto) {
        List<MyPreselectDockingListVO> list = orderVideoMatchService.selectMyPreselectDockingList(dto);
        return R.ok(toPage(list));
    }

    /**
     * 预选管理-模特匹配-我的预选-结束预选
     */
    @GetMapping("/my-preselect-end-match-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "预选管理-模特匹配-我的预选-结束预选")
    @RequiresPermissions("task:preselection:list")
    public R<PageInfo<MyPreselectEndMatchListVO>> selectMyPreselectEndMatchList(@Validated MyPreselectEndMatchListDTO dto) {
        List<MyPreselectEndMatchListVO> list = orderVideoMatchService.selectMyPreselectEndMatchList(dto);
        return R.ok(toPage(list));
    }

    /**
     * 历史预选模特列表
     */
    @GetMapping(value = "/history-match-list/{videoId}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "历史预选模特列表", response = OrderVideoMatchVO.class)
    @RequiresPermissions(value = {"preselection:order:history", "order:manage:preselection", "my:preselection:history", "preselection:distribute:history"}, logical = Logical.OR)
    public R<List<OrderVideoMatchVO>> selectHistoryMatchListByVideoId(@PathVariable Long videoId,
                                                                      @RequestParam(required = false) boolean isHistory) {
        List<OrderVideoMatchVO> preselectModelVOS = orderVideoMatchService.selectHistoryMatchListByVideoId(videoId, isHistory);
        return R.ok(preselectModelVOS);
    }

    /**
     * 查询当前匹配单活跃的预选模特
     */
    @GetMapping("/active-preselect-model-list/{matchId}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询当前匹配单活跃的预选模特", response = OrderVideoMatchPreselectModelVO.class)
    @RequiresPermissions("preselection:order:selected")
    public R<List<OrderVideoMatchPreselectModelVO>> selectActivePreselectModelListByVideoId(@PathVariable Long matchId) {
        List<OrderVideoMatchPreselectModelVO> list = orderVideoMatchService.selectActivePreselectModelListByMatchId(matchId);
        return R.ok(list);
    }

    /**
     * 结束预选-预选记录
     */
    @GetMapping("/preselection-record/{matchId}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "结束预选-预选记录", response = HistoryPreselectModelVO.class)
    @RequiresPermissions("my:preselection:record")
    public R<HistoryPreselectModelVO> preselectionRecord(@PathVariable Long matchId) {
        HistoryPreselectModelVO historyPreselectModelVO = orderVideoMatchService.preselectionRecord(matchId);
        return R.ok(historyPreselectModelVO);
    }

    /**
     * 添加预选模特
     */
    @PostMapping(value = "/add-preselect-model")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "添加预选模特")
    @RequiresPermissions(value = {"preselection:order:add", "my:preselection:add", "preselection:distribute:marker"}, logical = Logical.OR)
    @Log(title = "添加预选模特",businessType = BusinessType.UPDATE)
    public R<AddDistributionErrorVO> addPreselectModel(@RequestBody AddPreselectModelDTO dto) {
        AddDistributionErrorVO addDistributionErrorVO = orderVideoMatchService.addPreselectModel(dto);
        return R.ok(addDistributionErrorVO);
    }

    /**
     * 更改预选模特状态
     */
    @PostMapping(value = "/edit-preselect-model")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "更改预选模特状态")
    @RequiresPermissions(value = {"my:preselection:markers", "my:preselection:out", "my:preselection:select"}, logical = Logical.OR)
    @Log(title = "更改预选模特状态",businessType = BusinessType.UPDATE)
    public R<String> editPreselectModel(@RequestBody @Validated EditPreselectModelDTO editPreselectModelDTO) {
        orderVideoMatchService.editPreselectModel(editPreselectModelDTO);
        return R.ok();
    }

    /**
     * 选定模特信息
     */
    @GetMapping("/selected-model-info")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "选定模特信息")
    @RequiresPermissions(value = {"my:preselection:select", "my:preselection:update","wait:preselection:remark"}, logical = Logical.OR)
    public R<MarkOrderVO> selectedModelInfo(@ApiParam("匹配单ID") @RequestParam Long matchId,
                                            @ApiParam("模特ID") @RequestParam Long modelId) {
        MarkOrderVO result = orderVideoMatchService.selectedModelInfo(matchId, modelId);
        return R.ok(result);
    }

    /**
     * 选定模特信息-主携带订单下拉框
     */
    @GetMapping("/selected-model-main-carry-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "选定模特信息-主携带订单下拉框")
    @RequiresPermissions(value = {"my:preselection:select", "my:preselection:update"}, logical = Logical.OR)
    public R<List<ModelCarryVO>> selectedModelMainCarryListByMatchId(@ApiParam("匹配单ID") @RequestParam Long matchId,
                                                                     @ApiParam("模特ID") @RequestParam Long modelId) {
        List<ModelCarryVO> modelCarryVOS = orderVideoMatchService.selectedModelMainCarryListByMatchId(matchId, modelId);
        return R.ok(modelCarryVOS);
    }

    /**
     * 选定模特
     */
    @PostMapping("/selected-model")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "选定模特")
    @RequiresPermissions("my:preselection:select")
    @Log(title = "选定模特", businessType = BusinessType.UPDATE)
    public R<String> selectedModel(@RequestBody @Validated MarkOrderDTO markOrderDTO) {
        orderVideoMatchService.selectedModel(markOrderDTO);
        return R.ok();
    }

    /**
     * 修改选定模特
     */
    @PutMapping("/edit-selected-model")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "修改选定模特")
    @RequiresPermissions("my:preselection:update")
    @Log(title = "修改选定模特", businessType = BusinessType.UPDATE)
    public R<String> editSelectedModel(@RequestBody @Validated MarkOrderDTO markOrderDTO) {
        orderVideoMatchService.editSelectedModel(markOrderDTO);
        return R.ok();
    }

    /**
     * 确认提交预选模特
     */
    @PostMapping(value = "/submit-video")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "确认提交预选模特")
    @RequiresPermissions("my:preselection:submit")
    @Log(title = "确认提交预选模特",businessType = BusinessType.UPDATE)
    public R<String> submitPreselectModel(@RequestBody @Validated SubmitPreselectModelDTO dto) {
        orderVideoMatchService.submitPreselectModel(dto);
        return R.ok();
    }

    /**
     * 暂停匹配单
     */
    @PostMapping(value = "/pause-match")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "暂停匹配单")
    @RequiresPermissions("order:manage:stopMatch")
    @Log(title = "暂停匹配单",businessType = BusinessType.UPDATE)
    public R<String> pauseMatch(@RequestBody @Validated VideoPauseMatchDTO videoPauseMatchDTO) {
        orderVideoMatchService.pauseMatch(videoPauseMatchDTO);
        return R.ok();
    }

    /**
     * 继续模特匹配
     */
    @PutMapping(value = "/continue-match")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "继续模特匹配")
    @RequiresPermissions("order:manage:continueMatch")
    @OrderVideoOperate(operateType = OrderVideoOperateTypeEnum.CONTINUE_MODEL_MATCHING, videoId = "#videoId")
    @Log(title = "继续模特匹配",businessType = BusinessType.UPDATE)
    public R<String> continueMatch(@ApiParam("视频订单ID") @RequestParam Long videoId) {
        orderVideoMatchService.continueMatch(videoId);
        return R.ok();
    }

    /**
     * 修改订单信息
     */
    @PostMapping(value = "/edit-order-video")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "修改订单信息")
    @RequiresPermissions("order:manage:editMatchOrder")
    @OrderPermissions(orderId = "#orderOperationVideoDTO.id", backUserType = BackUserTypeEnum.CHINESE)
    @OrderRefundVerify(videoId = "#orderOperationVideoDTO.id")
    @OrderVideoOperate(operateType = OrderVideoOperateTypeEnum.MODIFY_ORDER_INFORMATION, videoId = "#orderOperationVideoDTO.id")
    @Log(title = "修改订单信息",businessType = BusinessType.UPDATE)
    public R<String> editOrderVideo(@RequestBody @Validated(OrderVideoDTO.ManagerTypeAuditOrAuditValidGroup.class) OrderOperationVideoDTO orderOperationVideoDTO) {
        orderVideoMatchService.editOrderVideo(orderOperationVideoDTO);
        return R.ok();
    }

    /**
     * 翻译匹配单拍摄要求并存储翻译结果
     */
    @PostMapping(value = "/translate-shoot-required/{matchId}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "翻译匹配单拍摄要求并存储翻译结果")
    @RequiresPermissions(value = {"preselection:order:shootRequired", "my:preselection:shootRequired", "preselection:distribute:shootRequired", "preselection:distribute:shootingSuggestion"}, logical = Logical.OR)
    public R<List<String>> translateShootRequired(@PathVariable Long matchId, @RequestBody TranslateBatchDTO translateBatchDTO) {
        List<String> list = orderVideoMatchService.translateShootRequired(matchId, translateBatchDTO);
        return R.ok(list);
    }

    /**
     * 订单列表-获取预选模特下拉框（运营端）
     */
    @GetMapping("/order-preselect-model-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("订单列表-获取预选模特下拉框（运营端）")
    @RequiresPermissions("order:manage:list")
    public R<List<ModelInfoVO>> orderPreselectModelSelect(@ApiParam("关键字") @RequestParam(required = false) String keyword,
                                                          @ApiParam("预选添加人id") @RequestParam(required = false) List<Long> backUserIds) {
        List<ModelInfoVO> select = orderVideoMatchService.orderPreselectModelSelect(keyword, backUserIds);
        return R.ok(select);
    }

    /**
     * 订单列表-获取预选添加人下拉框（运营端）
     */
    @GetMapping("/order-preselect-user-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("订单列表-获取预选添加人下拉框（运营端）")
    @RequiresPermissions("order:manage:list")
    public R<Set<String>> orderPreselectUserSelect(@RequestParam(required = false) String keyword) {
        Set<String> select = orderVideoMatchService.orderPreselectUserSelect(keyword);
        return R.ok(select);
    }

    /**
     * 更新预选模特列表为已淘汰
     */
    @PostMapping("/out-preselect-model")
    @InnerAuth
    public R<Boolean> outPreselectModel(@RequestBody List<OutPreselectModelDTO> dtoList) {
        try {
            orderVideoMatchService.outPreselectModel(dtoList);
            return R.ok(true);
        } catch (Exception e) {
            return R.ok(false);
        }
    }

    /**
     * 添加预选模特列表查询匹配单下非淘汰的模特
     */
    @PostMapping("/select-normal-preselect-model-by-match-id")
    @InnerAuth
    public R<Set<Long>> selectNormalPreselectModelByMatchId(@RequestParam Long matchId) {
        Set<Long> normalPreselectModelIds = orderVideoMatchService.selectNormalPreselectModelByMatchId(matchId);
        return R.ok(normalPreselectModelIds);
    }

    /**
     * 添加分发模特列表查询匹配单下的模特ID
     */
    @PostMapping("/select-preselect-model-by-match-id")
    @InnerAuth
    public R<Set<Long>> selectPreselectModelIdsByMatchId(@RequestParam Long matchId) {
        Set<Long> preselectModelIds = orderVideoMatchPreselectModelService.selectPreselectModelIdsByMatchId(matchId);
        return R.ok(preselectModelIds);
    }

    /**
     * 通过视频订单ID获取被商家驳回的模特ID
     */
    @GetMapping("/select-reject-model-id-by-video-id")
    @InnerAuth
    public R<Set<Long>> selectRejectModelIdByVideoId(@RequestParam Long videoId) {
        Set<Long> rejectModelIds = orderVideoMatchService.selectRejectModelIdByVideoId(videoId);
        return R.ok(rejectModelIds);
    }

    /**
     * 校验当前模特是否有其他的相同产品链接的订单
     */
    @GetMapping("/check-model-an-order-with-the-same-product-link-exists")
    public R<Boolean> checkModelAnOrderWithTheSameProductLinkExists(@RequestParam Long modelId, @RequestParam String productLink) {
        boolean result = orderVideoMatchService.checkModelAnOrderWithTheSameProductLinkExists(modelId, productLink);
        return R.ok(result);
    }

    /**
     * 我的预选-沟通中-预选模特下拉框
     */
    @GetMapping("/my-preselect-docking-model-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("我的预选-沟通中-预选模特下拉框")
    public R<List<ModelInfoVO>> myPreselectDockingModelSelect(@ApiParam("关键字") @RequestParam(required = false) String keyword) {
        List<ModelInfoVO> select = orderVideoMatchPreselectModelService.myPreselectDockingModelSelect(keyword);
        return R.ok(select);
    }

    /**
     * 我的预选-完成匹配-拍摄模特下拉框
     */
    @GetMapping("/my-preselect-shoot-model-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("我的预选-完成匹配-拍摄模特下拉框")
    public R<List<ModelInfoVO>> myPreselectShootModelSelect(@ApiParam("关键字") @RequestParam(required = false) String keyword) {
        List<ModelInfoVO> select = orderVideoMatchService.myPreselectShootModelSelect(keyword);
        return R.ok(select);
    }

    /**
     * 预选管理-查询预选模特拍摄注意事项
     */
    @GetMapping("/preselect-model-shoot-attention")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("预选管理-查询预选模特拍摄注意事项")
    public R<PreselectModelShootAttentionVO> preselectModelShootAttention(@RequestParam Long preselectModelId) {
        PreselectModelShootAttentionVO attention = orderVideoMatchPreselectModelService.getPreselectModelShootAttention(preselectModelId);
        return R.ok(attention);
    }

    /**
     * 预选管理-填写模特拍摄注意事项
     */
    @PostMapping("/preselect-model-shoot-attention")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("预选管理-填写模特拍摄注意事项")
    @RequiresPermissions("my:preselection:shootAttention")
    public R<Void> preselectModelShootAttention(@RequestBody @Validated PreselectModelShootAttentionDTO dto) {
        orderVideoMatchPreselectModelService.preselectModelShootAttention(dto);
        return R.ok();
    }

    /**
     * 预选管理-添加分发
     */
    @PostMapping("/add-distribution")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("预选管理-添加分发")
    public R<AddDistributionErrorVO> addDistribution(@RequestBody @Validated AddDistributionDTO dto) {
        AddDistributionErrorVO addDistributionErrorVO = orderVideoMatchService.addDistribution(dto);
        return R.ok(addDistributionErrorVO);
    }

    /**
     * 预选管理-我的预选-分发中
     */
    @GetMapping("/my-preselect-distribution-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("预选管理-我的预选-分发中")
    public R<PageInfo<MyPreselectDistributionListVO>> selectMyPreselectDistributionListByCondition(MyPreselectDistributionListDTO dto) {
        List<MyPreselectDistributionListVO> list = orderVideoMatchPreselectModelService.selectMyPreselectDistributionListByCondition(dto);
        return R.ok(toPage(list));
    }

    /**
     * 预选管理-我的预选-历史分发记录
     */
    @GetMapping("/distribution-history-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("预选管理-我的预选-历史分发记录")
    @RequiresPermissions(value = "preselection:distribute:record")
    public R<List<DistributionHistoryListVO>> selectDistributionHistoryListByModelId(@RequestParam Long modelId) {
        List<DistributionHistoryListVO> list = orderVideoMatchPreselectModelService.selectDistributionHistoryListByModelId(modelId);
        return R.ok(list);
    }

    /**
     * 预选管理-批量标记沟通
     */
    @PostMapping("/batch-mark-communication")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("预选管理-批量标记沟通")
    @RequiresPermissions(value = "preselection:distribute:markers")
    public R<Void> batchMarkCommunication(@RequestBody List<Long> preselectModelIds) {
        orderVideoMatchPreselectModelService.batchMarkCommunication(preselectModelIds);
        return R.ok();
    }

    /**
     * 预选管理-设置模特分发结果
     */
    @PostMapping("/set-model-distribution-result")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("预选管理-设置模特分发结果")
    @RequiresPermissions(value = "preselection:distribute:modelIntention")
    public R<Void> setModelDistributionResult(@RequestBody @Validated SetModelDistributionResultDTO dto) {
        orderVideoMatchPreselectModelService.setModelDistributionResult(dto);
        return R.ok();
    }

    /**
     * 我的预选-分发中-分发模特下拉框
     */
    @GetMapping("/my-preselect-distribution-model-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("我的预选-分发中-分发模特下拉框")
    public R<List<ModelInfoVO>> myPreselectDistributionModelSelect(@ApiParam("关键字") @RequestParam(required = false) String keyword) {
        List<ModelInfoVO> select = orderVideoMatchPreselectModelService.myPreselectDistributionModelSelect(keyword);
        return R.ok(select);
    }

    /**
     * 我的预选-分发中-英文部客服下拉框
     */
    @GetMapping("/my-preselect-distribution-english-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("我的预选-分发中-英文部客服下拉框")
    public R<List<UserVO>> myPreselectDistributionEnglishSelect(@ApiParam("关键字") @RequestParam(required = false) String keyword) {
        List<UserVO> select = orderVideoMatchPreselectModelService.myPreselectDistributionEnglishSelect(keyword);
        return R.ok(select);
    }

    /**
     * 设置已提醒模特拍摄注意事项
     */
    @PostMapping("/set-model-shoot-attention-remind")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation("设置已提醒模特拍摄注意事项")
    public R<Void> setModelShootAttentionRemind(@RequestParam Long preselectModelId) {
        orderVideoMatchPreselectModelService.setModelShootAttentionRemind(preselectModelId);
        return R.ok();
    }

    /**
     * 订单列表-获取出单人下拉框（运营端）
     */
    @GetMapping("/pool-issue-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("分发列表-获取分发中的英文部客服列表（运营端）")
    public R<List<UserVO>> orderIssueSelect() {
        return R.ok(orderVideoMatchPreselectModelService.distributionIssueList());
    }

}
