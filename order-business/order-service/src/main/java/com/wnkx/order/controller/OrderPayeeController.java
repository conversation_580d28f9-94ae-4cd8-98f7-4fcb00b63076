package com.wnkx.order.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigChangelogDTO;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigDTO;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigInfoDTO;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfigInfo;
import com.wnkx.order.service.OrderPayeeAccountConfigInfoService;
import com.wnkx.order.service.OrderPayeeAccountConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 收款人信息接口
 *
 * <AUTHOR>
 * @date 2024/12/17 14:34
 */
@RestController
@RequestMapping("/payee")
@Api(value = "收款人信息接口", tags = "收款人信息接口")
@RequiredArgsConstructor
public class OrderPayeeController {
    private final OrderPayeeAccountConfigService orderPayeeAccountConfigService;
    private final OrderPayeeAccountConfigInfoService orderPayeeAccountConfigInfoService;

    /**
     * 获取当前生效的收款人信息(根据类型)
     */
    @GetMapping("/type/{type}")
    @ApiOperation(value = "获取当前生效的收款人信息(根据类型)")
    public R<OrderPayeeAccountConfigInfoDTO> getCurrentActivePayeeInfo(@PathVariable("type") Integer type) {
        return R.ok(orderPayeeAccountConfigService.typeInfo(type));
    }

    /**
     * 获取收款人信息(根据id)
     */
    @GetMapping("/info/{id}")
    @ApiOperation(value = "获取收款人信息(根据id)")
    public R<OrderPayeeAccountConfigInfoDTO> getPayeeInfoById(@PathVariable("id") Long id) {
        return R.ok(orderPayeeAccountConfigService.getPayeeInfoById(id));
    }

    /**
     * 获取收款人信息(根据id)
     */
    @GetMapping("/info/detail/{id}")
    @ApiOperation(value = "获取收款人信息(根据id)")
    public R<OrderPayeeAccountConfigInfo> getPayeeInfoDetailById(@PathVariable("id") Long id) {
        return R.ok(orderPayeeAccountConfigInfoService.getById(id));
    }

    /**
     * 获取当前收款配置列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取当前收款配置列表")
    public R<List<OrderPayeeAccountConfigDTO>> list() {
        return R.ok(orderPayeeAccountConfigService.datalist());
    }

    /**
     * 获取当前收款配置列表
     */
    @GetMapping("/type/{type}/list")
    @ApiOperation(value = "获取类型下主体列表")
    public R<List<OrderPayeeAccountConfigDTO>> typeList(@PathVariable("type") Integer type) {
        return R.ok(orderPayeeAccountConfigService.typeList(type));
    }

    /**
     * 新增主体信息
     */
    @PostMapping("")
    @Log(title = "新增主体信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增主体信息")
    public R<String> newData(@RequestBody OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO) {
        orderPayeeAccountConfigService.newInfo(orderPayeeAccountConfigInfoDTO);
        return R.ok();
    }

    /**
     * 更新主体信息
     */
    @PutMapping("/info/{id}")
    @Log(title = "更新主体信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "更新主体信息")
    public R<String> updateData(@RequestBody OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO) {
        orderPayeeAccountConfigService.updateInfo(orderPayeeAccountConfigInfoDTO);
        return R.ok();
    }

    /**
     * 切换配置启用状态
     */
    @PutMapping("/active/{id}")
    @Log(title = "切换配置启用状态", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "切换配置启用状态")
    public R<String> changeActive(@PathVariable("id") Long id) {
        orderPayeeAccountConfigService.changeActive(id);
        return R.ok();
    }

    /**
     * 历史变更记录
     */
    @PutMapping("/changelog/type/{type}")
    @ApiOperation(value = "历史变更记录")
    public R<List<OrderPayeeAccountConfigChangelogDTO>> changelogByType(@PathVariable("type") Long type) {
        List<OrderPayeeAccountConfigChangelogDTO> ls =  orderPayeeAccountConfigService.historyLog(type);
        return R.ok(ls);
    }


}
