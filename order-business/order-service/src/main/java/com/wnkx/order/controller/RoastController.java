package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.validated.CommonValidatedGroup;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.order.OrderVideoRoastDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoRoastListDTO;
import com.ruoyi.system.api.domain.dto.order.task.OrderVideoRoastHandleDTO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoRoastStatisticsVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoRoastVO;
import com.wnkx.order.annotations.OrderPermissions;
import com.wnkx.order.annotations.OrderRefundVerify;
import com.wnkx.order.service.OrderVideoRoastService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 吐槽Controller
 *
 * <AUTHOR>
 * @date 2025/2/25
 */
@RestController
@RequestMapping("/roast")
@Api(value = "吐槽服务", tags = "吐槽服务")
@RequiredArgsConstructor
@Validated
public class RoastController extends BaseController {
    private final OrderVideoRoastService orderVideoRoastService;

    /**
     * 吐槽订单
     */
    @PostMapping(value = "")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "吐槽订单")
    @OrderPermissions(orderId = "#dto.videoId")
    @OrderRefundVerify(videoId = "#dto.videoId")
    public R<String> roast(@RequestBody @Validated(CommonValidatedGroup.SaveValidatedGroup.class) OrderVideoRoastDTO dto) {
        orderVideoRoastService.addOrderVideoRoast(dto);
        return R.ok();
    }

    @PostMapping(value = "/system")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "系统吐槽")
    public R<String> systemRoast(@RequestBody @Validated OrderVideoRoastDTO dto) {
        dto.setRoastType(StatusTypeEnum.YES.getCode());
        orderVideoRoastService.addOrderVideoRoast(dto);
        return R.ok();
    }

    @GetMapping("/list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "吐槽列表", response = PageInfo.class)
    @RequiresPermissions("system:tease:list")
    public R<PageInfo<OrderVideoRoastVO>> getOrderVideoRoastVO(OrderVideoRoastListDTO dto) {
        return R.ok(toPage(orderVideoRoastService.getOrderVideoRoastVO(dto)));
    }

    @GetMapping("/statistics")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "吐槽统计")
    @RequiresPermissions("system:tease:list")
    public R<OrderVideoRoastStatisticsVO> orderVideoRoastStatisticsVO() {
        return R.ok(orderVideoRoastService.orderVideoRoastStatisticsVO());
    }

    @PostMapping(value = "/handle")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "处理吐槽")
    @RequiresPermissions("system:tease:edit")
    public R<String> handleRoast(@RequestBody @Validated OrderVideoRoastHandleDTO dto) {
        orderVideoRoastService.handleRoast(dto);
        return R.ok();
    }

    /**
     * 订单列表-获取对接人下拉框（运营端）
     */
    @GetMapping("/contact-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("吐槽列表-获取中文部客服下拉框（运营端）")
    public R<List<UserVO>> roastContactSelect(@RequestParam(required = false) String keyword) {
        List<UserVO> select = orderVideoRoastService.roastContactSelect(keyword);
        return R.ok(select);
    }

    /**
     * 订单列表-获取出单人下拉框（运营端）
     */
    @GetMapping("/issue-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("吐槽列表-获取英文部客服下拉框（运营端）")
    public R<List<UserVO>> roastIssueSelect(@RequestParam(required = false) String keyword) {
        List<UserVO> select = orderVideoRoastService.roastIssueSelect(keyword);
        return R.ok(select);
    }


}
