package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.domain.vo.order.workbench.PauseMatchVO;
import com.ruoyi.system.api.domain.vo.order.workbench.WorkbenchTaskDetailVO;
import com.wnkx.order.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 运营端-工作台
 *
 * <AUTHOR>
 * @date 2025/4/1
 */
@RestController
@RequestMapping("/backend/workbench")
@Api(value = "运营端-工作台", tags = "运营端-工作台")
@RequiredArgsConstructor
@Validated
public class BackendWorkbenchController extends BaseController {

    private final IOrderVideoService orderVideoService;
    private final IOrderService orderService;
    private final IOrderMemberService orderMemberService;
    private final IOrderVideoFeedBackMaterialInfoService orderVideoFeedBackMaterialInfoService;
    private final IOrderVideoTaskDetailService orderVideoTaskDetailService;
    /**
     * 获取订单列表视频订单统计数量
     */
    @GetMapping("/videoStatistics")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "工作台统计")
    public R<WorkbenchVO> videoStatistics(Integer workbenchRoleType) {
        return R.ok(orderVideoService.getWorkbenchStatisticsVO(workbenchRoleType));
    }

    @GetMapping("/chinese/unConfirmList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "中文部-待审核列表", response = PageInfo.class)
    public R<PageInfo<OrderVideoVO>> chineseUnConfirmList() {
        PageUtils.startPage(1,5);
        return R.ok(toPage(orderVideoService.chineseUnConfirmList()));
    }

    @GetMapping("/english/unMatchList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "英文部-待匹配列表", response = PageInfo.class)
    public R<PageInfo<OrderVideoVO>> englishUnMatchList() {
        PageUtils.startPage(1,5);
        return R.ok(toPage(orderVideoService.englishUnMatchList()));
    }
    @GetMapping("/english/closeList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "英文部-交易关闭订单列表", response = PageInfo.class)
    public R<PageInfo<OrderVideoVO>> englishCloseList() {
        PageUtils.startPage(1,5);
        return R.ok(toPage(orderVideoService.englishCloseList()));
    }
    @GetMapping("/english/pauseMatchList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "英文部-暂停匹配列表", response = PageInfo.class)
    public R<PageInfo<PauseMatchVO>> englishPauseMatchList() {
        PageUtils.startPage(1,5);
        return R.ok(toPage(orderVideoService.englishPauseMatchList()));
    }

    @GetMapping("/finance/workbenchFinanceVideoList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "财务部-财务视频待审核列表", response = PageInfo.class)
    public R<PageInfo<OrderVideoAuditVO>> workbenchFinanceVideoList() {
        PageUtils.startPage(1,5);
        return R.ok(toPage(orderService.workbenchFinanceVideoList()));
    }
    @GetMapping("/finance/workbenchFinanceMemberList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "财务部-财务会员待审核列表", response = PageInfo.class)
    public R<PageInfo<OrderMemberVO>> workbenchFinanceMemberList() {
        PageUtils.startPage(1,5);
        return R.ok(toPage(orderMemberService.workbenchFinanceMemberList()));
    }


    @GetMapping("/edit/unGetList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑部-待领取素材列表", response = PageInfo.class)
    public R<PageInfo<MaterialInfoListVO>> selectUnGetMaterialList() {
        PageUtils.startPage(1,5);
        List<MaterialInfoListVO> materialInfoList = orderVideoFeedBackMaterialInfoService.selectUnGetMaterialList();
        return R.ok(toPage(materialInfoList));
    }

    @GetMapping("/edit/selectRefuseTaskList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑部-被拒绝任务单", response = PageInfo.class)
    public R<PageInfo<WorkbenchTaskDetailVO>> selectRefuseTaskList() {
        PageUtils.startPage(1,5);
        return R.ok(toPage(orderVideoTaskDetailService.selectWorkbenchRefuseTaskDetailList()));
    }

    @GetMapping("/edit/selectUnHandleTaskList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑部-待处理任务单", response = PageInfo.class)
    public R<PageInfo<WorkbenchTaskDetailVO>> selectWorkbenchTaskDetailList() {
        PageUtils.startPage(1,5);
        return R.ok(toPage(orderVideoTaskDetailService.selectWorkbenchTaskDetailList()));
    }

}
