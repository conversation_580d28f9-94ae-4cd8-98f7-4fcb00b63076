package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum;
import com.ruoyi.common.core.enums.OrderTaskTypeEnum;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.task.AfterSaleFlowDTO;
import com.ruoyi.system.api.domain.dto.order.task.AfterSaleOrderTaskListDTO;
import com.ruoyi.system.api.domain.dto.order.task.ConfirmAfterOrderDTO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.domain.vo.order.task.AfterSaleListVO;
import com.ruoyi.system.api.domain.vo.order.task.AfterSaleTaskDetailInfoVO;
import com.wnkx.order.annotations.OrderRefundVerify;
import com.wnkx.order.service.OrderVideoTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24 16:14
 */
@RestController
@RequestMapping("/task")
@Api(value = "任务单服务", tags = "任务单服务")
@RequiredArgsConstructor
@Validated
public class OrderVideoTaskController extends BaseController {
    private final OrderVideoTaskService orderTaskService;

    /**
     * 创建任务单
     */
    @PostMapping("/create-task")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "创建任务单")
    @OrderRefundVerify(videoId = "#orderTaskDTO.videoId")
    @RequiresPermissions(value = {"order:manage:create-work-order", "my:clip:creatWork"}, logical = Logical.OR)
    public R<String> createTask(@RequestBody @Validated OrderTaskDTO orderTaskDTO) {
        orderTaskService.createTask(orderTaskDTO);
        return R.ok();
    }

    /**
     * 校验任务单是否已存在
     */
    @PostMapping("/check-task-exist")
    @ApiOperation(value = "校验任务单是否已存在")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<Boolean> checkTaskExist(@RequestBody OrderTaskDTO orderTaskDTO) {
        return R.ok(orderTaskService.checkTaskExist(orderTaskDTO));
    }

    /**
     * 申请补偿退款 OR 补发物流 _获取视频订单相关联的任务单信息
     */
    @GetMapping("/get-refund-pending-task/{videoId}")
    @ApiOperation(value = "申请补偿退款 OR 补发物流 _获取视频订单相关联的任务单信息")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<List<OrderVideoTaskDetailVO>> getRefundPendingTask(@PathVariable Long videoId) {
        List<OrderVideoTaskDetailVO> orderVideoTaskDetailVOS = orderTaskService.getRefundPendingTask(videoId, null);
        return R.ok(orderVideoTaskDetailVOS);
    }

    /**
     * 帮模特反馈素材 _获取视频订单相关联的任务单信息
     */
    @GetMapping("/get-back-help-model-upload-material-pending-task/{videoId}")
    @ApiOperation(value = "帮模特反馈素材 _获取视频订单相关联的任务单信息")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<List<OrderVideoTaskDetailVO>> getBackHelpModelUploadMaterialPendingTask(@PathVariable Long videoId) {
        List<OrderVideoTaskDetailVO> orderVideoTaskDetailVOS = orderTaskService.getBackHelpModelUploadMaterialPendingTask(videoId, null);
        return R.ok(orderVideoTaskDetailVOS);
    }

    /**
     * 反馈素材给商家 _获取视频订单相关联的任务单信息
     */
    @GetMapping("/get-feedback-material-pending-task")
    @ApiOperation(value = "反馈素材给商家 _获取视频订单相关联的任务单信息")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<List<OrderVideoTaskDetailVO>> getFeedbackMaterialPendingTask(@ApiParam("视频订单ID") @RequestParam Long videoId,
                                                                          @ApiParam("售后分类（1：视频，2：照片）") @RequestParam List<Integer> afterSaleClass,
                                                                          @ApiParam("是否是待剪辑页面") @RequestParam(required = false) boolean isToBeEdited) {
        List<OrderVideoTaskDetailVO> orderVideoTaskDetailVOS = orderTaskService.getFeedbackMaterialPendingTask(videoId, null, afterSaleClass, isToBeEdited);
        return R.ok(orderVideoTaskDetailVOS);
    }

    /**
     * 工单-历史处理人下拉框
     */
    @PostMapping("/list/history/assignee/select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "工单-历史处理人下拉框")
    @RequiresPermissions("task:workOrder:list")
    public R<List<UserVO>> getHistoryAssigneeUserList() {
        List<UserVO> list = orderTaskService.getHistoryAssigneeUserList();
        return R.ok(list);
    }

    /**
     * 工单-工单列表
     */
    @GetMapping("/work-order-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "工单-工单列表")
    @RequiresPermissions("task:workOrder:list")
    public R<PageInfo<WorkOrderTaskListVO>> selectWorkOrderListByCondition(@Validated WorkOrderTaskListDTO dto) {
        List<WorkOrderTaskListVO> list = orderTaskService.selectWorkOrderListByCondition(dto);
        return R.ok(toPage(list));
    }

    /**
     * 工单-查看工单详情
     */
    @GetMapping("/get-work-order-info/{taskNum}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "工单-查看工单详情")
    @RequiresPermissions("task:workOrder:detail")
    public R<WorkOrderTaskInfoVO> getWorkOrderInfo(@PathVariable String taskNum) {
        WorkOrderTaskInfoVO orderTaskInfoVO = orderTaskService.getWorkOrderInfo(taskNum);
        return R.ok(orderTaskInfoVO);
    }

    /**
     * 工单-完结工单
     */
    @PostMapping("/finish-work-order")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "工单-完结工单")
    @RequiresPermissions("task:workOrder:completed")
    public R<String> finishWorkOrder(@RequestParam Long taskDetailId) {
        orderTaskService.finishWorkOrder(taskDetailId);
        return R.ok();
    }

    /**
     * 工单-指派处理人
     */
    @PostMapping("/assign-handler")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "工单-指派处理人")
    @RequiresPermissions("task:workOrder:assign")
    public R<String> assignHandler(@RequestBody @Validated AssignHandlerDTO dto) {
        orderTaskService.assignHandler(dto);
        return R.ok();
    }

    /**
     * 工单-拒绝工单
     */
    @PostMapping("/refuse-work-order")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "工单-拒绝工单")
    @RequiresPermissions("task:workOrder:reject")
    public R<String> rejectWorkOrder(@RequestBody @Validated TaskDetailOperateDTO dto) {
        orderTaskService.rejectWorkOrder(dto);
        return R.ok();
    }

    /**
     * 工单-关闭工单
     */
    @PostMapping("/close-work-order")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "工单-关闭工单")
    @RequiresPermissions("task:workOrder:close")
    public R<String> closeWorkOrder(@RequestParam Long taskDetailId) {
        orderTaskService.closeWorkOrder(taskDetailId);
        return R.ok();
    }

    /**
     * 工单-重新打开
     */
    @PostMapping("/reopen-work-order")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "工单-重新打开")
    @RequiresPermissions("task:workOrder:open")
    public R<String> reopenWorkOrder(@RequestBody @Validated TaskDetailOperateDTO dto) {
        orderTaskService.reopenWorkOrder(dto);
        return R.ok();
    }

    /**
     * 工单-新增处理记录
     */
    @PostMapping("/add-work-order-process-record")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "工单-新增处理记录")
    @RequiresPermissions("task:workOrder:records")
    public R<String> addWorkOrderProcessRecord(@RequestBody @Validated OrderVideoTaskDetailProcessRecordDTO dto) {
        orderTaskService.addWorkOrderProcessRecord(dto);
        return R.ok();
    }

    /**
     * 工单-查询处理记录
     */
    @GetMapping("/work-order-process-record/{taskNum}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "工单-查询处理记录")
    @RequiresPermissions("task:workOrder:records")
    public R<List<OrderVideoTaskDetailProcessRecordVO>> selectWorkOrderProcessRecordList(@PathVariable String taskNum) {
        List<OrderVideoTaskDetailProcessRecordVO> list = orderTaskService.selectWorkOrderProcessRecordList(taskNum);
        return R.ok(list);
    }

    @GetMapping("/backend/work-order-count")
    @ApiOperation(value = "获取工单统计")
    @RequiresPermissions("task:workOrder:list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<OrderTaskStatusVO> workOrderCount() {
        return R.ok(orderTaskService.getOrderTaskStatus(OrderTaskTypeEnum.WORK_ORDER.getCode()));
    }

    @GetMapping("/backend/after-sale-count")
    @ApiOperation(value = "获取售后单统计")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("task:afterSale:list")
    public R<OrderTaskStatusVO> afterSaleOrderCount() {
        return R.ok(orderTaskService.getOrderTaskStatus(OrderTaskTypeEnum.AFTER_SALE.getCode()));
    }

    @GetMapping("/backend/after-sale-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "售后单列表", response = PageInfo.class)
    @RequiresPermissions("task:afterSale:list")
    public R<PageInfo<AfterSaleListVO>> queryAfterSaleList(@Validated AfterSaleOrderTaskListDTO dto) {
        return R.ok(toPage(orderTaskService.queryAfterSaleList(dto)));
    }

    @GetMapping("/backend/get-after-sale-task-info/{taskNum}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查看售后单详情")
    @RequiresPermissions("task:afterSale:detail")
    public R<AfterSaleTaskDetailInfoVO> getAfterSaleTaskInfo(@PathVariable String taskNum) {
        return R.ok(orderTaskService.getAfterSaleTaskInfo(taskNum));
    }

    @PostMapping("/backend/afterSale/closeOrder")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "售后单-取消售后")
    @RequiresPermissions("task:afterSale:close")
    public R<String> closeAfterOrder(@RequestBody @Validated AfterSaleFlowDTO dto) {
        dto.setOperateType(OrderTaskDetailFlowOperateTypeEnum.CANCEL_AFTER_SALE.getCode());
        orderTaskService.afterSaleFlow(dto);
        return R.ok();
    }
    @PostMapping("/backend/afterSale/reopen")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "售后单-重新打开")
    @RequiresPermissions("task:afterSale:reopen")
    public R<String> reopenAfterOrder(@RequestBody @Validated AfterSaleFlowDTO dto) {
        dto.setOperateType(OrderTaskDetailFlowOperateTypeEnum.AFTER_SALE_REOPEN.getCode());
        orderTaskService.afterSaleFlow(dto);
        return R.ok();
    }
    @PostMapping("/backend/afterSale/confirm")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "售后单-确认售后")
    @RequiresPermissions("task:afterSale:confirm")
    public R<String> confirmAfterOrder(@RequestBody @Validated ConfirmAfterOrderDTO dto) {
        orderTaskService.confirmAfterOrder(dto);
        return R.ok();
    }
    @PostMapping("/backend/afterSale/refuse")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "售后单-拒绝售后")
    @RequiresPermissions("task:afterSale:refuse")
    public R<String> refuseAfterOrder(@RequestBody @Validated AfterSaleFlowDTO dto) {
        dto.setOperateType(OrderTaskDetailFlowOperateTypeEnum.REFUSE_AFTER_SALE.getCode());
        orderTaskService.afterSaleFlow(dto);
        return R.ok();
    }
    @PostMapping("/backend/afterSale/applicationForCancellation")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "售后单-申请取消")
    @RequiresPermissions("task:afterSale:applicationForCancellation")
    public R<String> applicationForCancellationAfterOrder(@RequestBody @Validated AfterSaleFlowDTO dto) {
        dto.setOperateType(OrderTaskDetailFlowOperateTypeEnum.APPLY_FOR_CANCELLATION.getCode());
        orderTaskService.afterSaleFlow(dto);
        return R.ok();
    }
    @PostMapping("/backend/afterSale/finish")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "售后单-完成售后")
    @RequiresPermissions("task:afterSale:finish")
    public R<String> finishAfterOrder(@RequestParam Long taskDetailId) {
        orderTaskService.finishAfterSale(taskDetailId);
        return R.ok();
    }
    @PostMapping("/backend/afterSale/cancelApplication")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "售后单-撤销申请")
    @RequiresPermissions("task:afterSale:cancelApplication")
    public R<String> cancelApplicationAfterOrder(@RequestParam Long taskDetailId) {
        orderTaskService.cancelApplicationAfterOrder(taskDetailId);
        return R.ok();
    }

    @PostMapping("/backend/afterSale/agreeCancelApplication")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "售后单-同意取消售后")
    @RequiresPermissions("task:afterSale:operateCancel")
    public R<String> agreeCancelAfterOrder(@RequestParam Long taskDetailId) {
        orderTaskService.agreeCancelAfterOrder(taskDetailId);
        return R.ok();
    }
    @PostMapping("/backend/afterSale/refuseCancelApplication")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "售后单-拒绝取消售后")
    @RequiresPermissions("task:afterSale:operateCancel")
    public R<String> refuseCancelAfterOrder(@RequestParam Long taskDetailId) {
        orderTaskService.refuseCancelAfterOrder(taskDetailId);
        return R.ok();
    }

    @PostMapping("/list/model/select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "模特下拉框")
    public R<List<ModelOrderSimpleVO>> getAfterSaleTaskInfo(@RequestParam Integer type) {
        return R.ok(orderTaskService.taskModelList(type));
    }

    @PostMapping("/list/assignee/select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "处理人")
    public R<List<UserVO>> getAssigneeList(@RequestParam Integer type) {
        return R.ok(orderTaskService.getAssigneeUserList(type));
    }

    @PostMapping("/list/submit/select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "提交人")
    public R<List<UserVO>> getSubmitList(@RequestParam Integer type) {
        return R.ok(orderTaskService.getSubmitUserList(type));
    }

    @PostMapping("/list/relevance/select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "关联人")
    public R<List<UserVO>> getRelevanceList(@RequestParam Integer type) {
        return R.ok(orderTaskService.getRelevanceList(type));
    }
}
