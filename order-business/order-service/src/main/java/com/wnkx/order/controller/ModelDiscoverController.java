package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.dto.order.OrderModelListDTO;
import com.ruoyi.system.api.domain.vo.order.ModelHomePageOrderStatisticsVO;
import com.ruoyi.system.api.domain.vo.order.OrderModelApplyInfoVO;
import com.ruoyi.system.api.domain.vo.order.OrderModelListVO;
import com.ruoyi.system.api.domain.vo.order.RecommendModelListVO;
import com.wnkx.order.service.ModelDiscoverService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 模特端-首页发现
 *
 * <AUTHOR>
 * @date 2024/7/2 9:25
 */
@RestController
@RequestMapping("/model/discover")
@Api(value = "模特端-首页发现", tags = "模特端-首页发现")
@RequiredArgsConstructor
@Validated
public class ModelDiscoverController extends BaseController {

    private final ModelDiscoverService modelDiscoverService;

    /**
     * 订单列表
     */
    @GetMapping("/list")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation(value = "订单列表", response = PageInfo.class)
    public R<PageInfo<OrderModelListVO>> selectOrderModelListByCondition(@Valid OrderModelListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
//        orderByDto.setField("ov.status_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        List<OrderModelListVO> list = modelDiscoverService.selectOrderModelListByCondition(dto);
        return R.ok(toPage(list));
    }

    /**
     * 订单详情（首页及forme的订单详情）
     */
    @GetMapping("/get-order-model-info")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation(value = "订单详情（首页及forme的订单详情）", response = OrderModelApplyInfoVO.class)
    public R<OrderModelApplyInfoVO> getOrderModelInfo(@RequestParam Long videoId,
                                                      @RequestParam(required = false) Long preselectModelId) {
        OrderModelApplyInfoVO info = modelDiscoverService.getOrderModelInfo(videoId, preselectModelId);
        return R.ok(info);
    }
    /**
     * 订单详情（首页及forme的订单详情）
     */
    @GetMapping("/getOrderModelInfoByVideoCode")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation(value = "订单详情（首页及forme的订单详情）", response = OrderModelApplyInfoVO.class)
    public R<OrderModelApplyInfoVO> getOrderModelInfoByVideoCode(@RequestParam String videoCode,
                                                      @RequestParam(required = false) Long preselectModelId) {
        OrderModelApplyInfoVO info = modelDiscoverService.getOrderModelInfoByVideoCode(videoCode, preselectModelId);
        return R.ok(info);
    }

    /**
     * 模特报名
     */
    @PostMapping("apply")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation(value = "模特报名")
    public R<Long> apply(@RequestParam Long videoId) {
        Long preselectModelId = modelDiscoverService.apply(videoId);
        return R.ok(preselectModelId);
    }

    /**
     * 首页订单数量统计
     */
    @GetMapping("/order-statistics")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation(value = "首页订单数量统计")
    public R<ModelHomePageOrderStatisticsVO> orderStatistics() {
        ModelHomePageOrderStatisticsVO orderStatistics = modelDiscoverService.orderStatistics();
        return R.ok(orderStatistics);
    }

    /**
     * RECOMMEND页面
     */
    @GetMapping("/recommend")
    @LoginUserType(userTypes = UserTypeConstants.MODEL_TYPE)
    @ApiOperation(value = "RECOMMEND页面")
    public R<PageInfo<RecommendModelListVO>> selectRecommendList() {
        List<RecommendModelListVO> list = modelDiscoverService.selectRecommendList();
        return R.ok(toPage(list));
    }
}
