package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.vo.order.CheckSeedCodeVO;
import com.ruoyi.system.api.domain.vo.order.CreatePayVo;
import com.ruoyi.system.api.domain.vo.order.Currency2RMBVO;
import com.ruoyi.system.api.domain.vo.order.OrderPayInfoVO;
import com.ruoyi.system.api.model.LoginBusiness;
import com.wnkx.order.service.AlipayService;
import com.wnkx.order.service.PayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * 支付服务
 *
 * <AUTHOR>
 * @date 2024/6/4
 */
@RestController
@RequestMapping("/pay")
@Api(value = "支付服务", tags = "支付服务")
@RequiredArgsConstructor
public class PayController {

    private final PayService payService;
    private final AlipayService alipayService;

    @PostMapping("/code")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "生成二维码")
    public R<CreatePayVo> generateQrcode(@RequestBody @Validated PayCodeDTO payCodeDTO) {
        CreatePayVo qrcode = payService.generateQrcode(payCodeDTO, false);
        return R.ok(qrcode);
    }

    @PostMapping("/check")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "检查订单状态")
    public R<OrderPayStatusDTO> checkStatus(@RequestBody @Validated CheckStatusDTO checkStatusDTO) {
        OrderPayStatusDTO orderStatus = payService.checkOrderStatus(checkStatusDTO);
        return R.ok(orderStatus);
    }

    @PostMapping("/weChat/callback")
    public R<String> weChatCallback() {
        payService.weChatCallback();
        return R.ok();
    }

    /**
     * 订单支付页信息
     */
    @PostMapping(value = "/pay-info")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "订单支付页信息")
    public R<OrderPayInfoVO> payInfo(@RequestBody @Validated PayInfoDTO payInfoDTO) {
        payInfoDTO.setIsAnother(false);
        OrderPayInfoVO orderPayInfoVO = payService.payInfo(payInfoDTO);

        LoginBusiness loginBusinessUser = SecurityUtils.getLoginBusinessUser();
        orderPayInfoVO.setIsBalanceLock(loginBusinessUser.getBusinessAccountVO().getBusinessVO().getIsBalanceLock());
        orderPayInfoVO.setValidBalance(loginBusinessUser.getBusinessAccountVO().getBusinessVO().getValidBalance());
        return R.ok(orderPayInfoVO);
    }

    @GetMapping(value = "/getTaxPoint")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "获取开票税点")
    public R<BigDecimal> getTaxPoint() {
        return R.ok(payService.getTaxPoint());
    }

    /**
     * 订单支付页信息
     */
    @GetMapping(value = "/pay-Member-info")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "订单支付页信息")
    public R<OrderPayInfoVO> payMemberInfo(PayMemberInfoDTO dto) {
        LoginBusiness loginBusinessUser = SecurityUtils.getLoginBusinessUser();
        OrderPayInfoVO orderPayInfoVO = payService.payMemberInfo(dto, false);
        orderPayInfoVO.setIsBalanceLock(loginBusinessUser.getBusinessAccountVO().getBusinessVO().getIsBalanceLock());
        orderPayInfoVO.setValidBalance(loginBusinessUser.getBusinessAccountVO().getBusinessVO().getValidBalance());
        return R.ok(orderPayInfoVO);
    }

    /**
     * 货币单位转换（美元 -> 人民币）
     */
    @PostMapping(value = "/dollar-to-rmb")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "货币单位转换（美元 -> 人民币）")
    public R<Currency2RMBVO> dollarToRmb(@RequestBody List<BigDecimal> dollars) {
        return R.ok(payService.dollarToRmb(dollars));
    }

//    /**
//     * 校验种草码
//     */
//    @PostMapping(value = "/check-seed-code")
//    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
//    @ApiOperation(value = "校验种草码")
//    public R<Boolean> checkSeedCode(@RequestParam String seedCode, @RequestParam String orderNum) {
//        return R.ok(payService.checkSeedCode(seedCode.toUpperCase(), orderNum));
//    }


    @PostMapping(value = "/checkSeedCodeV1")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "校验种草码")
    public R<CheckSeedCodeVO> checkSeedCodeV1(@RequestParam String seedCode, @RequestParam String orderNum) {
        return R.ok(payService.checkSeedCodeV1(seedCode.toUpperCase(), orderNum));
    }

    /**
     * 支付宝支付回调
     */
    @PostMapping(value = "/alipay-callback")
    public String alipayCallback(HttpServletRequest request) {
        return alipayService.alipayCallback(request);
    }

    /**
     * 下载请款清单
     */
    @PostMapping(value = "/download-pay-info")
    @LoginUserType(userTypes = {UserTypeConstants.USER_TYPE, UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "下载请款清单")
    @RequiresPermissions("order:manage:expense-invoice")
    public void downloadPayInfo(
            @ApiParam("合并单ID") @RequestParam(required = false) Long mergeId,
            @ApiParam("订单编号") @RequestParam(required = false) String orderNum,
            HttpServletResponse response
    ) throws IOException {
        payService.downloadPayInfo(mergeId, orderNum, false, response);
    }
}
