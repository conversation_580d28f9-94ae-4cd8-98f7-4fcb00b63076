package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.dto.biz.datastatistics.ModelOrderCommissionAnalysisDTO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.member.BusinessAnalysisVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.*;
import com.wnkx.order.service.BusinessDateService;
import com.wnkx.order.service.CustomerServiceDataStatisticsService;
import com.wnkx.order.service.ModelDataStatisticsService;
import com.wnkx.order.service.OrderVideoDataStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/9 10:35
 */
@RestController
@RequestMapping("/data-statistics")
@Api(value = "数据统计服务", tags = "数据统计服务")
@RequiredArgsConstructor
public class DataStatisticsController extends BaseController {

    private final ModelDataStatisticsService modelDataStatisticsService;
    private final CustomerServiceDataStatisticsService customerServiceDataStatisticsService;
    private final OrderVideoDataStatisticsService orderVideoDataStatisticsService;
    private final BusinessDateService businessDateService;

    /**
     * 模特数据-订单佣金分析 OR 合作深度佣金分析
     */
    @GetMapping("/model-order-commission-analysis")
    @ApiOperation("模特数据-订单佣金分析 OR 合作深度佣金分析")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<ModelOrderCommissionAnalysisVO> getModelOrderCommissionAnalysis(ModelOrderCommissionAnalysisDTO dto) {
        ModelOrderCommissionAnalysisVO modelOrderCommissionAnalysisVO = modelDataStatisticsService.getModelOrderCommissionAnalysis(dto);
        return R.ok(modelOrderCommissionAnalysisVO);
    }

    /**
     * 模特数据-成功匹配次数
     */
    @GetMapping("/model-success-match-count")
    @ApiOperation("模特数据-成功匹配次数")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<ModelSuccessMatchCountVO> getModelSuccessMatchCount() {
        ModelSuccessMatchCountVO modelSuccessMatchCount = modelDataStatisticsService.getModelSuccessMatchCount();
        return R.ok(modelSuccessMatchCount);
    }

    /**
     * 客服数据-基础看板
     */
    @GetMapping("/customer-service-base-board")
    @ApiOperation("客服数据-基础看板")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<CustomerServiceBaseBoardVO> getCustomerServiceBaseBoard() {
        CustomerServiceBaseBoardVO customerServiceBaseBoardVO = customerServiceDataStatisticsService.getCustomerServiceBaseBoard();
        return R.ok(customerServiceBaseBoardVO);
    }

    /**
     * 客服数据-中文部客服数据
     */
    @GetMapping("/chinese-customer-service-data")
    @ApiOperation("客服数据-中文部客服数据")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<List<ChineseCustomerServiceDataVO>> selectChineseCustomerServiceData(@ApiParam("开始时间") @RequestParam(required = false) Date beginTime,
                                                                                  @ApiParam("结束时间") @RequestParam(required = false) Date endTime) {
        List<ChineseCustomerServiceDataVO> chineseCustomerServiceDataVOS = customerServiceDataStatisticsService.selectChineseCustomerServiceData(beginTime, endTime);
        return R.ok(chineseCustomerServiceDataVOS);
    }

    /**
     * 客服数据-英文部客服数据
     */
    @GetMapping("/english-customer-service-data")
    @ApiOperation("客服数据-英文部客服数据")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<List<EnglishCustomerServiceDataVO>> selectEnglishCustomerServiceData(@ApiParam("开始时间") @RequestParam(required = false) Date beginTime,
                                                                                  @ApiParam("结束时间") @RequestParam(required = false) Date endTime) {
        List<EnglishCustomerServiceDataVO> englishCustomerServiceDataVOS = customerServiceDataStatisticsService.selectEnglishCustomerServiceData(beginTime, endTime);
        return R.ok(englishCustomerServiceDataVOS);
    }

    /**
     * 视频订单数据-基础看板
     */
    @GetMapping("/order-video-base-board")
    @ApiOperation("视频订单数据-基础看板")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<OrderVideoBaseBoardVO> getOrderVideoBaseBoard() {
        OrderVideoBaseBoardVO orderVideoBaseBoardVO = orderVideoDataStatisticsService.getOrderVideoBaseBoard();
        return R.ok(orderVideoBaseBoardVO);
    }

    /**
     * 视频订单数据-服务中订单数
     */
    @GetMapping("/order-video-service-count")
    @ApiOperation("视频订单数据-服务中订单数")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<OrderVideoServiceCountVO> getOrderVideoServiceCount() {
        OrderVideoServiceCountVO orderVideoServiceCountVO = orderVideoDataStatisticsService.getOrderVideoServiceCount();
        return R.ok(orderVideoServiceCountVO);
    }

    /**
     * 视频订单数据-订单趋势
     */
    @GetMapping("/order-video-trend")
    @ApiOperation("视频订单数据-订单趋势")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<OrderVideoTrendVO> getOrderVideoTrend(@ApiParam("开始时间") @RequestParam Date beginTime,
                                                   @ApiParam("结束时间") @RequestParam Date endTime) {
        OrderVideoTrendVO orderVideoTrendVO = orderVideoDataStatisticsService.getOrderVideoTrend(beginTime, endTime);
        return R.ok(orderVideoTrendVO);
    }

    /**
     * 视频订单数据-订单匹配时长数据分析
     */
    @GetMapping("/order-video-match-duration-data")
    @ApiOperation("视频订单数据-订单匹配时长数据分析")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<OrderVideoDurationDataVO> getOrderVideoMatchDurationData(@ApiParam("月份（yyyy-MM）") @RequestParam String date) {
        OrderVideoDurationDataVO orderVideoMatchDurationDataVO = orderVideoDataStatisticsService.getOrderVideoMatchDurationData(date);
        return R.ok(orderVideoMatchDurationDataVO);
    }

    /**
     * 视频订单数据-订单发货时长数据分析
     */
    @GetMapping("/order-video-delivery-duration-data")
    @ApiOperation("视频订单数据-订单发货时长数据分析")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<OrderVideoDurationDataVO> getOrderVideoDeliveryDurationData(@ApiParam("月份（yyyy-MM）") @RequestParam String date) {
        OrderVideoDurationDataVO orderVideoDeliveryDurationData = orderVideoDataStatisticsService.getOrderVideoDeliveryDurationData(date);
        return R.ok(orderVideoDeliveryDurationData);
    }

    /**
     * 视频订单数据-订单素材反馈时长数据分析
     */
    @GetMapping("/order-video-feedback-duration-data")
    @ApiOperation("视频订单数据-订单素材反馈时长数据分析")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<OrderVideoDurationDataVO> getOrderVideoFeedbackDurationData(@ApiParam("月份（yyyy-MM）") @RequestParam String date) {
        OrderVideoDurationDataVO orderVideoFeedbackDurationData = orderVideoDataStatisticsService.getOrderVideoFeedbackDurationData(date);
        return R.ok(orderVideoFeedbackDurationData);
    }

    /**
     * 视频订单数据-平均审单/服务时长、任务单数及占比、拖单数及占比、烂单数及占比
     */
    @GetMapping("/order-video-average-duration-data")
    @ApiOperation("视频订单数据-平均审单/服务时长、任务单数及占比、拖单数及占比、烂单数及占比")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<OrderVideoAverageDurationDataVO> getOrderVideoAverageDurationData() {
        OrderVideoAverageDurationDataVO orderVideoAverageDurationData = orderVideoDataStatisticsService.getOrderVideoAverageDurationData();
        return R.ok(orderVideoAverageDurationData);
    }

    /**
     * 视频订单数据-售后类型分析
     */
    @GetMapping("/order-video-after-sale-type-analysis")
    @ApiOperation("视频订单数据-售后类型分析")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<OrderVideoAfterSaleTypeAnalysisVO> getOrderVideoAfterSaleTypeAnalysis(@ApiParam("月份（yyyy-MM）") @RequestParam String date) {
        OrderVideoAfterSaleTypeAnalysisVO orderVideoAfterSaleTypeAnalysisVO = orderVideoDataStatisticsService.getOrderVideoAfterSaleTypeAnalysis(date);
        return R.ok(orderVideoAfterSaleTypeAnalysisVO);
    }

    /**
     * 视频订单数据-补偿订单情况
     */
    @GetMapping("/order-video-compensation-order-situation")
    @ApiOperation("视频订单数据-补偿订单情况")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<OrderVideoCompensationOrderSituationVO> getOrderVideoCompensationOrderSituation(@ApiParam("月份（yyyy-MM）") @RequestParam String date) {
        OrderVideoCompensationOrderSituationVO orderVideoCompensationOrderSituationVO = orderVideoDataStatisticsService.getOrderVideoCompensationOrderSituation(date);
        return R.ok(orderVideoCompensationOrderSituationVO);
    }


    @GetMapping("/business-member-discount-situation")
    @ApiOperation("会员数据-优惠分析")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    // @RequiresPermissions("system:text:list")
    public R<BusinessAnalysisVO> getBusinessMemberDiscountAnalysis(@ApiParam("开始时间") @RequestParam(required = false) Date beginTime,
                                                                         @ApiParam("结束时间") @RequestParam(required = false) Date endTime) {
        BusinessAnalysisVO businessMemberDiscountAnalysis = businessDateService.getBusinessMemberDiscountAnalysis(beginTime, endTime);
        return R.ok(businessMemberDiscountAnalysis);
    }
}
