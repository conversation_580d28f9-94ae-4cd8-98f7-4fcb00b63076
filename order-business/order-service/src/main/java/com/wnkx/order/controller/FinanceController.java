package com.wnkx.order.controller;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.finance.FinancialVerificationExportDTO;
import com.ruoyi.system.api.domain.vo.order.OrderAuditInfoVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.domain.vo.order.finace.*;
import com.wnkx.order.service.IOrderInvoiceService;
import com.wnkx.order.service.IOrderMemberService;
import com.wnkx.order.service.IOrderService;
import com.wnkx.order.service.IOrderVideoRefundService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/17 16:08
 */
@RestController
@RequestMapping("/finance")
@Api(value = "财务管理服务", tags = "财务管理服务")
@RequiredArgsConstructor
@Validated
public class FinanceController extends BaseController {

    private final IOrderInvoiceService orderInvoiceService;

    private final IOrderService orderService;

    private final IOrderMemberService orderMemberService;
    private final IOrderVideoRefundService orderVideoRefundService;


    /**
     * 应收审批-视频订单
     */
    @GetMapping("/receivableAuditList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "应收审批-视频订单", response = PageInfo.class)
    @RequiresPermissions("receivable:approve:video:list")
    public R<PageInfo<OrderVideoAuditVO>> receivableAuditList(OrderListDTO orderListDTO) {
        List<OrderVideoAuditVO> list = orderService.receivableAuditListV1(orderListDTO);
        return R.ok(toPage(list));
    }

    @GetMapping("/orderAuditStatusStatistics")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "视频应收审批统计")
    @RequiresPermissions("receivable:approve:video:list")
    public R<OrderAuditStatusStatisticsVO> orderAuditStatusStatistics() {
        return R.ok(orderService.orderAuditStatusStatistics());
    }
    @GetMapping("/unApproveStatusStatistics")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "应收审批未审核数据统计")
    @RequiresPermissions(value = {
            "receivable:approve:video:list",
            "receivable:approve:vip:list",
            "finance:prepay:list",
    }, logical = Logical.OR)
    public R<UnApproveStatusStatistics> unApproveStatusStatistics() {
        return R.ok(orderService.unApproveStatusStatistics());
    }

    @Log(title = "导出应收审核列表", businessType = BusinessType.EXPORT)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "应收审批-视频订单-导出")
    @PostMapping("/receivableAuditList/export")
    @RequiresPermissions("receivable:approve:video:export")
    public void exportReceivableAuditList(OrderListDTO orderListDTO, HttpServletResponse response) {
        List<OrderListVO> list = orderService.receivableAuditList(orderListDTO);
        List<ReceivableAuditListExportVO> receivableAuditListExportVOList = orderService.exportReceivableAuditList(list);
        ExcelUtil<ReceivableAuditListExportVO> util = new ExcelUtil<>(ReceivableAuditListExportVO.class);
        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "应收审核导出");
        util.exportExcel(response, receivableAuditListExportVOList, "应收审核导出");
    }

    @GetMapping("/member/receivableAuditList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询会员应收审核列表", response = PageInfo.class)
    @RequiresPermissions("receivable:approve:vip:list")
    public R<PageInfo<OrderMemberVO>> selectOrderMemberList(OrderMemberListDTO dto) {
        //需要待审核状态数据
        dto.setIsReceivableAudit(StatusTypeEnum.YES.getCode());
        return R.ok(toPage(orderMemberService.getOrderMemberList(dto)));
    }


    @GetMapping("/member/orderAuditStatusStatistics")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "会员订单审核状态统计")
    @RequiresPermissions("receivable:approve:vip:list")
    public R<OrderAuditStatusStatisticsVO> orderMemberAuditStatusStatistics() {
        return R.ok(orderService.orderMemberAuditStatusStatistics());
    }

    @Log(title = "导出会员应收审核列表", businessType = BusinessType.EXPORT)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "导出会员应收审核列表")
    @PostMapping("/member/receivableAuditList/export")
    @RequiresPermissions("receivable:approve:vip:export")
    public void exportMemberReceivableAuditList(OrderMemberListDTO dto, HttpServletResponse response) {
        dto.setIsReceivableAudit(StatusTypeEnum.YES.getCode());
        List<OrderMemberVO> orderMemberList = orderMemberService.getOrderMemberList(dto);
        List<MemberReceivableAuditListExportVO> memberReceivableAuditListExportVOS = orderMemberService.getOrderMemberExportList(orderMemberList);

        ExcelUtil<MemberReceivableAuditListExportVO> util = new ExcelUtil<>(MemberReceivableAuditListExportVO.class);
        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "会员应收审核导出");
        util.exportExcel(response, memberReceivableAuditListExportVOS, "会员应收审核导出");
    }

    @GetMapping("/orderPayDetailList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询订单收支明细", response = OrderPayDetailVO.class)
    @RequiresPermissions("finance:order:list")
    public R<PageInfo<OrderPayDetailVO>> orderPayDetailList(OrderPayDetailDTO dto) {
        return R.ok(toPage(orderService.orderPayDetailList(dto)));
    }

    @PostMapping("/orderPayDetailList/export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "导出订单收支明细")
    @RequiresPermissions("finance:order:export")
    public void exportOrderPayDetailList(OrderPayDetailDTO dto, HttpServletResponse response) {
        List<OrderPayDetailExportVO> orderMemberExportList = orderService.getOrderPayDetailExportVos(dto);

        ExcelUtil<OrderPayDetailExportVO> util = new ExcelUtil<>(OrderPayDetailExportVO.class);
        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "订单收支明细导出");
        util.exportExcel(response, orderMemberExportList, "订单收支明细导出");
    }

    @GetMapping("/orderPayDetailVideoList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询订单收支明细 - 视频明细", response = OrderPayVideoDetailVO.class)
    @RequiresPermissions("finance:video:list")
    public R<PageInfo<OrderPayVideoDetailVO>> orderPayDetailVideoList(OrderPayDetailVideoListDTO dto) {
        return R.ok(toPage(orderService.orderPayDetailVideoList(dto)));
    }

    @PostMapping("/orderPayDetailVideoList/export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询订单收支明细 - 视频明细 导出")
    @RequiresPermissions("finance:order:detail:export")
    public void exportOrderPayDetailVideoList(OrderPayDetailVideoListDTO dto, HttpServletResponse response) {
        List<OrderPayVideoDetailVO> orderPayVideoDetailVOS = orderService.orderPayDetailVideoList(dto);
        ExcelUtil<OrderPayVideoDetailExportVO> util = new ExcelUtil<>(OrderPayVideoDetailExportVO.class);
        List<OrderPayVideoDetailExportVO> orderPayVideoDetailExportVOS = BeanUtil.copyToList(orderPayVideoDetailVOS, OrderPayVideoDetailExportVO.class);
        ExcelUtil.setAttachmentResponseHeader(response, "查询订单收支明细-视频明细导出");
        util.exportExcel(response, orderPayVideoDetailExportVOS, "查询订单收支明细-视频明细导出");
    }


    /**
     * 查询应收审核详情
     */
    @GetMapping("/receivableAuditDetail/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询应收审核详情", response = PageInfo.class)
    @RequiresPermissions(value = {
            "receivable:approve:video:info",
            "receivable:approve:video:audit",
            "receivable:approve:video:reaudit",
            "receivable:approve:video:view",
            "receivable:approve:vip:info",
            "receivable:approve:vip:audit",
            "receivable:approve:vip:reaudit",
            "receivable:approve:vip:view"
    }, logical = Logical.OR)
    public R<OrderVO> receivableAuditDetail(@PathVariable Long id) {
        OrderVO orderVO = orderService.receivableAuditDetail(id);
        return R.ok(orderVO);
    }

    @GetMapping("/receivableAuditInfo/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询应收审核详情", response = PageInfo.class)
    public R<OrderAuditInfoVO> receivableAuditInfo(@PathVariable Long id) {
        return R.ok(orderService.receivableAuditInfo(id));
    }
    /**
     * 应收审核流程
     */
    @Log(title = "应收审核流程", businessType = BusinessType.INSERT)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @PostMapping("/auditOrder")
    @ApiOperation(value = "应收审核流程")
    @RequiresPermissions(value = {
            "receivable:approve:video:info",
            "receivable:approve:video:audit",
            "receivable:approve:video:reaudit",
            "receivable:approve:vip:info",
            "receivable:approve:vip:audit",
            "receivable:approve:vip:reaudit"
    },logical = Logical.OR)
    public R<String> auditOrder(@RequestBody @Valid OrderAuditDto dto) {
        orderService.auditOrder(dto);
        return R.ok();
    }

    /**
     * 财务对账-退款记录列表
     */
    @GetMapping("/refund-success-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "财务对账-退款记录列表", response = OrderVideoRefundSuccessVO.class)
    @RequiresPermissions("finance:refund:list")
    public R<PageInfo<OrderVideoRefundSuccessVO>> refundSuccessList(OrderVideoRefundSuccessListDTO dto) {
        List<OrderVideoRefundSuccessVO> list = orderVideoRefundService.refundSuccessList(dto);
        return R.ok(toPage(list));
    }

    /**
     * 财务对账-退款记录列表-导出
     */
    @PostMapping("/refund-success-list/export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "财务对账-退款记录列表-导出")
    @RequiresPermissions("finance:refund:export")
    public void refundSuccessListExport(OrderVideoRefundSuccessListDTO dto, HttpServletResponse response) {
        orderVideoRefundService.refundSuccessListExport(dto, response);
    }

    @PostMapping("/exportFinancialVerification/export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "财务对账-导出")
    public void exportFinancialVerification(FinancialVerificationExportDTO dto, HttpServletResponse response) {
        orderService.exportFinancialVerificationList(dto, response);
    }

}
