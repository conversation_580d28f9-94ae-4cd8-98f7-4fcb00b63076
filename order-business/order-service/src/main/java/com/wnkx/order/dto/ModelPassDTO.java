package com.wnkx.order.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("模特拒绝订单DTO")
public class ModelPassDTO {
    @NotNull(message = "视频订单id不能为空")
    @ApiModelProperty("视频订单id")
    private Long videoId;

    @NotNull(message = "预选模特id不能为空")
    @ApiModelProperty("预选模特id")
    private Long preselectModelId;

    @ApiModelProperty("备注")
    private String remark;
}
