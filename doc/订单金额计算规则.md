前景：

原order_table.order_amount 在存在[税点费用]以及[运营主动修改订单金额]时，会更新此字段

现调整：

order_table.order_amount保留原始金额（即创建订单时的金额）
返回给前端 计算order_table.order_amount + order_table.tax_point_cost - order_table.seed_code_discount + order_table.back_modify_amount = 最终价格

调用接口计算：com.wnkx.order.service.IOrderService#getOrderFinalPrice

---------------------------------------------------------------------------
创建订单：
金额组成：order_video
视频总金额 = video_price + pic_price + exchange_price + service_price
video_price：	默认29.9美金
pic_price： 	照片数量：两图-10，五图-20 无图-0
exchange_price：	（视频价格 + 图片价格）* 手续费抽成 + 基础手续费 0.49
service_price：	根据是否代理 非代理有3美金服务费
-----------------------------------------------------------------------------
修改订单金额：支付前可使用
修改的是视频金额：video_price
同步影响到手续费：exchange_price     （视频价格 + 图片价格）* 手续费抽成 + 基础手续费 0.49
此时需要重新计算：视频总金额（order_video.amount） 			video_price + pic_price + exchange_price + service_price
最后计算最终支付金额（order_table.pay_amount）：订单初始金额（创建后固定不变） + 运营修改的订单金额

---------------------------------------------------------------------------------
开始支付：
参数：使用余额（立即支付锁定）、支付类型、是否存在种草码
种草码优惠（第一次使用的会员订单）：
条件：使用有效种草码
结果：order_video.pay_amount * 种草码折扣
税点费用：
条件：对公转账 + 使用余额 < order_video.pay_amount
结果：税点 * （order_video.pay_amount - 使用余额）
运营修改金额：
支付前确认：order_table.back_modify_amount
需要确定：
pay_amount（最终需支付金额）：原支付金额 - 种草码优惠 + 税点费用
tax_point_cost（税点费用）：税点 * （order_video.pay_amount - 使用余额）
------------------------------------------------------------------------------------
支付成功：
富友回调确定：
支付时间：
入账时间：
财务审核状态：
种草码优惠金额：
支付金额：order_video.order_amount + order_table.tax_point_cost + order_table.seed_code_discount + order_table.back_modify_amount
订单实付金额：支付金额 - 使用余额
审核通过:
订单实付金额:
支付时间:
财务审核状态:
是否入账:
入账时间：
---------------------------------------------------------------------------------------
特殊规则：
获取二维码：com.wnkx.order.service.impl.PayServiceImpl#generateQrcodeV1
确定字段：种草码、支付人
修改订单金额：com.wnkx.order.service.impl.OrderVideoServiceImpl.updateOrderVideoPrice
重新计算金额、释放余额锁定数据
富有回调：
是否入账：根据富友返回入账时间、定时更新
    