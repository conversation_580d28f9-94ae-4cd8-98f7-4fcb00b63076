biz端余额修改接口处理
请求参数 ： 	useBalance（处理余额）
			origin（余额使用类型：取消订单收入、补偿订单收入....）
			auditStatus（财务审核状态：0-待审核,1-审核通过,2-审核异常）
			balanceAuditStatus（余额提现审核状态：0-待处理,1-已提现,2-已取消）
			isBalancePay（是否余额支付）;
-各种类型处理
	补偿订单收入、取消选配收入（余额使用类型）：
		处理方案：						添加余额 		 business.balance + useBalance
	取消订单收入（余额使用类型）：
		auditStatus（财务审核状态）：
			审核通过：					锁定余额 		 business.balance + useBalance
			审核未通过：					余额解锁 		 business.use_balance - useBalance
	视频订单支出、会员订单支出（余额使用类型）：
		auditStatus（财务审核状态）：
			余额支付（isBalancePay）：		减余额			business.balance - useBalance
			待审核：						余额锁定			business.use_balance + useBalance
			审核通过：					余额解锁、减余额	business.balance - useBalance; business.use_balance - useBalance
	提现支出（余额使用类型）：
		balanceAuditStatus（余额提现审核状态：0-待处理,1-已提现,2-已取消）
			已提现：						余额解锁、减余额   business.balance - useBalance; business.use_balance - useBalance
			已取消：						余额解锁			business.use_balance - useBalance
			申请提现：					锁定余额			business.balance + useBalance

order端余额处理：
请求参数： 	使用余额
			余额使用类型（取消订单收入、补偿订单收入....）	
			订单数据（order：）
			订单状态枚举（待审核、待确认、交易关闭）
 	根据订单数据：财务审核状态（order.auditStatus）		——》auditStatus（余额提现审核状态：0-待处理,1-已提现,2-已取消）
				支付类型（order.payType）				——》isBalancePay（是否余额支付）
	参数	 使用余额									——》isBalancePay（是否余额支付）
		余额使用类型									——》origin（余额使用类型：取消订单收入、补偿订单收入....）	

balanceAuditStatus 无权限处理