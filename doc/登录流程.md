#  登录流程添加 business_account-登录key关系
--   登录成功：
        创建token：login_tokens:UUID，登录信息
        获取登录账号数量
            数量大于等于5：删除最早的数据
            数量小于5：添加登录账号  business_account，login_tokens:UUID

--   拦截器校验：
        刷新token有效时间：login_tokens:UUID，登录信息
        刷新登录账号token有效时间

--   退出登录：
        清除token
        清除登录账号token

--   禁用商家、禁用账号：
        获取商家下所有账号，根据账号ID列表获取登录账号（business_account，login_tokens:UUID）数据删除token（login_tokens:UUID，登录信息）
        根据账号获取登录账号（business_account，login_tokens:UUID）数据删除token（login_tokens:UUID，登录信息）

--   更新商家、子账号数据
        同禁用商家、账号获取token信息使用最新数据更新token
        
--  定时任务每日3点删除过期token 可以暂不处理
    
-------------------------------------------------------------------------------------------
# 数据入库处理
        token、LoginUser、登录时间（用于30天过期）、business_account
        登录:
                获取数据库数据，token加载入redis
                获取登录账号数量
                        数量大于等于5：删除最早的数据
                        数量小于5：添加登录账号  business_account，login_tokens:UUID
        拦截器校验
                刷新redis的token有效时间
        退出登录：
                清楚redistoken
                清楚数据库token
        禁用商家：
                除获取数据由数据库获取其他不变
        更新商家、子账号数据
                有操作redis改为操作数据库

    
