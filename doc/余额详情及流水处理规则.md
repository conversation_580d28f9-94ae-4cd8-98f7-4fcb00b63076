1.预付款收入：
	直接初始化：余额详情、余额流水  使用余额参数：本次预付金额
2.发起退款（取消订单、补偿、取消选配）：
	条件：
		视频编码下的余额详情流水:business_balance_detail_flow
			退款统计：
			使用余额统计：
		视频订单数据：order_video
			订单金额
			使用余额
			使用现金
	处理数据：
		1.使用余额统计 == 0 代表未使用余额
			直接初始化：使用余额参数：本次退款金额
		2.使用余额
			2.1：使用现金 > 0 且 使用现金 > 退款统计 代表现金未退款完毕
				2.1.1：退款统计 + 本次退款金额 >=  订单金额   本次退款超过订单金额  
					初始化：使用余额参数： 本次退款金额 - 视频订单使用余额
					还需退款 = 视频订单使用余额
					执行3流程
				2.1.2：退款统计 + 本次退款金额 < 订单金额	本次退款还在订单内
					2.1.2.1: 现金 - 退款统计 > 本次退款  代表 本次退款还在现金内
						初始化：使用余额参数：本次退款
						还需退款 = 0
					2.1.2.2：现金 - 退款统计 <= 本次退款 代表本次退款超过现金
						初始化：使用余额参数：现金 - 退款统计
						还需退款 = 本次退款 - （现金 - 退款统计）
						执行3流程
			2.2：无使用现金 	退款统计 + 本次退款金额 >=  订单金额
				初始化：使用余额参数：本次退款金额 - （订单金额 - 退款统计）
				还需退款 = 本次退款 - （本次退款金额 - （订单金额 - 退款统计））= 订单金额 - 退款统计
				执行3流程
3.余额退款：执行上述流程后 可退金额 > 0
	获取所有使用余额详情流水统计：
		TK000001 -200 = -300（使用余额） + 100（上次退款金额）
		TK000002 -100
		TK000003 -100
	得到可退金额：-400
	本次退款：300
	for循环：先退TK000001  200，后退TK000002 100
3.视频订单支出
	获取每个视频订单使用余额数据
	获取每个余额详情有效余额数据
	先分配每个余额详情使用多少余额并记录每个余额详情使用余额 一对多
		TK000001  300
		TK000002  200
		TK000003  100

		MNDS1	400
		MNDS2	200
	
	流水：
		TK000001 MNDS1 -300
		TK000002 MNDS1 -100
		TK000002 MNDS2 -100
		TK000003 MNDS2 -100
	详情全部设置为0
4.会员订单支出
	获取每个余额详情有效余额数据
	分配每个余额使用多少余额并生成流水  一对一

5.线下提现
	根据锁定数据处理余额详情
		1.锁定余额 = 现有锁定余额 - 提现金额
		2.审核
			审核取消：有效金额 = 现有有效金额 + 锁定余额
			审核通过：
				有效金额 = 现有使用余额 + 锁定余额
				初始化流水数据
			