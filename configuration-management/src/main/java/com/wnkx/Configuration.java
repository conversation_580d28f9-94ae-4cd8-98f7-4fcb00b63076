package com.wnkx;

import org.apache.commons.cli.*;

public class Configuration {

    private final String address;
    private final String username;
    private final String password;
    private final String env;
    private final String namespaceId;
    private final String group;

    public Configuration(String[] args) throws ParseException {
        Options options = new Options();
        options.addOption("a", "address", true, "nacos服务器地址，示例：http://***********:8848/nacos");
        options.addOption("u", "user", true, "nacos用户名");
        options.addOption("p", "password", true, "nacos服务器端口号");
        options.addOption("n", "namespace", true, "nacos namespace id");
        options.addOption("e", "env", true, "要发布的环境");
        options.addOption("g", "group", true, "nacos group");
        CommandLineParser parser = new DefaultParser();
        CommandLine cmd = parser.parse(options,args);

        address = parseAddress(cmd);
        username = parseUsername(cmd);
        password = parsePassword(cmd);
        namespaceId = parseNameSpace(cmd);
        env = parseEnv(cmd);
        group = parseGroup(cmd);
    }

    private String parseGroup(CommandLine cmd) {
        final String namespaceId;
        if (cmd.hasOption("g")) {
            namespaceId = cmd.getOptionValue("g");
        } else {
            namespaceId = getPropertyFromSystem("nacos_group");
        }
        if (namespaceId == null || namespaceId.length() < 1) {
            return "DEFAULT_GROUP";
        }
        return namespaceId;
    }

    private String parseNameSpace(CommandLine cmd) {
        final String namespaceId;
        if (cmd.hasOption("n")) {
            namespaceId = cmd.getOptionValue("n");
        } else {
            namespaceId = getPropertyFromSystem("nacos_namespace");
        }
        return namespaceId;
    }

    private String parseEnv(CommandLine cmd) {
        final String env;
        if (cmd.hasOption("e")) {
            env = cmd.getOptionValue("e");
        } else {
            env = getPropertyFromSystem("env");
        }
        if (env == null || env.length() < 1) {
            throw new IllegalArgumentException("要发布的环境未设置," +
                    " 请通过命令行参数： -e dev|test|prod 设置环境");
        }
        return env;
    }

    public String getAddress() {
        return address;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public String getEnv() {
        return env;
    }

    public String getNamespaceId() {
        return namespaceId;
    }

    public String getGroup() {
        return group;
    }

    private String getPropertyFromSystem(String key) {
        String property = System.getProperty(key);
        if (property != null && property.length() > 0) {
            return property;
        }
        return System.getenv(key.toUpperCase());
    }

    private String parsePassword(CommandLine cmd) {
        final String password;
        if (cmd.hasOption("p")) {
            password = cmd.getOptionValue("p");
        } else {
            password = getPropertyFromSystem("nacos_psw");
        }
        if (password == null || password.length() < 1) {
            throw new IllegalArgumentException("nacos 用户密码未设置," +
                    " 请通过命令行参数： -p nacos_password 设置 nacos 用户密码");
        }
        return password;
    }

    private String parseUsername(CommandLine cmd) {
        final String username;
        if (cmd.hasOption("u")) {
            username = cmd.getOptionValue("u");
        } else {
            username = getPropertyFromSystem("nacos_usr");
        }
        if (username == null || username.length() < 1) {
            throw new IllegalArgumentException("nacos 用户名未设置," +
                    " 请通过命令行参数： -u nacos_username 设置 nacos 用户名");
        }
        return username;
    }

    private String parseAddress(CommandLine cmd) throws ParseException {
        String nacosAddress;
        if (cmd.hasOption("address")) {
            nacosAddress = cmd.getOptionValue("address");
        } else {
            nacosAddress = getPropertyFromSystem("nacos_address");
        }
        if (nacosAddress == null || nacosAddress.length() < 1) {
            throw new IllegalArgumentException("nacos address 未设置," +
                    " 请通过命令行参数： -a http://nacosaddress 设置 nacos 地址");
        }
        return nacosAddress;
    }

    public static void main(String[] args) {
        System.out.println(new Option("n", "address", true, "").isRequired());
    }
}
