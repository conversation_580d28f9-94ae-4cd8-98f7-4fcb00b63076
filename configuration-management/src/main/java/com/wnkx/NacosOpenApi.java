package com.wnkx;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.Asserts;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

public class NacosOpenApi {
    private final String baseUrl;
    private final CloseableHttpClient httpClient;
    private String token;
    private final ObjectMapper mapper = new ObjectMapper();

    public NacosOpenApi(String baseUrl) {
        this.baseUrl = baseUrl;
        httpClient = HttpClients.createDefault();
    }

    public void login(String username, String password) throws IOException {
        HttpPost httpPost = new HttpPost(String.format("%s/v1/auth/login", baseUrl));
        UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(
                Arrays.asList(new BasicNameValuePair("username", username),
                        new BasicNameValuePair("password", password)),
                StandardCharsets.UTF_8
        );
        httpPost.setEntity(urlEncodedFormEntity);
        try(CloseableHttpResponse response = httpClient.execute(httpPost)){
            Asserts.check(response.getStatusLine().getStatusCode() == 200, "登陆请求失败");
            String responseData = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            Map<String, String> body = mapper.readValue(responseData, new TypeReference<Map<String, String>>() {});
            this.token = body.get("accessToken");
        }
    }

    public String getConfig(String dataId, String group, String namespaceId) throws URISyntaxException, IOException {
        URI uri = new URIBuilder(String.format("%s/v1/cs/configs", baseUrl))
                .setParameter("dataId", dataId)
                .setParameter("group", group)
                .setParameter("tenant", namespaceId)
                .setParameter("accessToken", this.token)
                .build();
        HttpGet request = new HttpGet(uri);
        try(CloseableHttpResponse response = httpClient.execute(request)){
            Asserts.check(response.getStatusLine().getStatusCode() == 200, "读取配置请求失败");
            return EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        }
    }

    public void publishConfig(String dataId, String group, String namespaceId, String content) throws IOException {
        HttpPost request = new HttpPost(String.format("%s/v1/cs/configs", baseUrl));
        UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(
                Arrays.asList(
                        new BasicNameValuePair("tenant", namespaceId),
                        new BasicNameValuePair("dataId", dataId),
                        new BasicNameValuePair("group", group),
                        new BasicNameValuePair("content", content),
                        new BasicNameValuePair("type", "yaml"),
                        new BasicNameValuePair("accessToken", this.token)
                ),
                StandardCharsets.UTF_8
        );
        request.setEntity(urlEncodedFormEntity);
        try(CloseableHttpResponse response = httpClient.execute(request)){
            Asserts.check(response.getStatusLine().getStatusCode() == 200, "发布配置请求失败");
            String result = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            Asserts.check(Objects.equals(result, "true"), "发布配置失败");
        }
    }
}
