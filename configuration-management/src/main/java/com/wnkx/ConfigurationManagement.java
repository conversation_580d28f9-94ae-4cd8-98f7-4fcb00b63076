package com.wnkx;


import cn.hutool.core.io.IoUtil;
import org.apache.commons.cli.ParseException;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import java.io.IOException;
import java.nio.charset.StandardCharsets;


public class ConfigurationManagement {

    public static void main(String[] args) throws ParseException, IOException {
        Configuration configuration = new Configuration(args);
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource[] configFiles = resolver.getResources(configuration.getEnv() + "/*.*");

        NacosOpenApi api = new NacosOpenApi(configuration.getAddress());
        api.login(configuration.getUsername(), configuration.getPassword());
        for (Resource config : configFiles) {
            System.out.printf("环境：[%s], 发布：%s%n", configuration.getEnv(), config.getFilename());
            String content = IoUtil.read(config.getInputStream(), StandardCharsets.UTF_8);
            api.publishConfig(config.getFilename(), configuration.getGroup(), configuration.getNamespaceId(), content);
        }
    }
}
