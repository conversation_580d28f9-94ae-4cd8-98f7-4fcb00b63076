spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  cloud:
    config:
      override-none: true
    sentinel:
      filter:
        enabled: false
  servlet:
    multipart:
      max-file-size: 400MB
      max-request-size: 500MB
  redis:
    database: 6
    host: **********
    port: 6379
    password: 188288
    timeout: 5000
redisson:
  #  连接池大小
  connectionPoolSize: 5
  #  最小空闲连接数
  connectionMinimumIdleSize: 1
  #  Redisson的线程数
  threads: 2
  #  Netty的线程数
  nettyThreads: 2

# feign 配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 20000
        readTimeout: 20000
  compression:
    request:
      enabled: true
    response:
      enabled: true
      useGzipDecoder: true

# 暴露监控端点
management:
  endpoints:
    web:
      exposure:
        include: '*'
  metrics:
    # 在 Prometheus 中添加特别的 Labels
    # 必须加上对应的应用名，因为需要以应用的维度来查看对应的监控
    tags:
      application: ${spring.application.name}
    # 下面选项建议打开，以监控 http 请求的 P99/P95 等，具体的时间分布可以根据实际情况设置
    distribution:
      sla:
        http:
          server:
            requests: 1ms,5ms,10ms,50ms,100ms,200ms,500ms,1s,5s
      percentiles-histogram:
        http:
          server:
            requests: true # 开启 http server 的请求监控

wnkx:
  ds:
    ip: **********
    username: root
    password: U4LsNiAkvGbmsmAK09
    port: 4000
    biz-db: biz-center-uat
    order-db: order-center-uat
    user-db: user-center-uat
    job-db: wnkx-job-uat
  loadbalance:
    isolation:
      enabled: true
s3:
  # 上传类型  minio、tencentCos
  uploadType: tencentCos
  # 账号 minio的账号 tencentCos的SecretId
  accessKey: AKID31rCjHmHfSMVftGb3eJAz1xjeVZo1GRi
  # 密码 minio的密码 tencentCos的SecretKey
  secretKey: c3LUYfpKmZJB7qQp14HZybNH948Kg1Lj
  # url
  endpoint: runyi-1311733061.cos.ap-guangzhou.myqcloud.com
  #  回显前端地址
  echoUrl: https://pstatic.woniu.video/
  # 桶名
  bucketName: runyi-1311733061
  # 区域 tencentCos的区域
  region: ap-guangzhou
  # 文件前缀 例 存储入桶 前缀test 存入桶后 test/fileName.txt
  prefix: uat
  # tencentCos 密钥有效时间 单位秒
  durationSeconds: 1800
  # tencentCos 预签名url有效时间 单位秒
  signedSeconds: 1800

logistic:
  17-token: A95582ACAF26B3F474DEEA58F49E0047
  url: https://api.17track.net/track/v2.2/register

selenium:
  url: http://***********:14444/wd/hub
  proxy:
    enable: true
    url: http://***********:20171
    info:
      host: ***********
      port: 20171
  ocrUrl: http://***********:25391

pay:
  debugger:
    enable: true
  fuyou:
    orderPrefix: 16543
    mchntId: 0003970F7356577
    mchntKey: 16e59f50639c11ef961e3fb543acff98
    serverIp: ************
    notifyUrl: https://api.fklwnkbum.uat.woniu.video/order/pay/callback
    baseUrl: https://aipay-cloud.fuioupay.com/aggregatePay/
  anotherpay:
    linkPrefix: https://customer.wbckal5h.uat.woniu.video/another/pay?code=
    timeLimit: 1

tencent:
  translate:
    ak: AKIDrtAd9sqonv5nMqJl7Obimpq7OE3KmNAx
    sk: w2KlBmgdcfmss9Pdt5KVm0CLNLF3rbHs
  sms:
    ak: AKID31rCjHmHfSMVftGb3eJAz1xjeVZo1GRi
    sk: c3LUYfpKmZJB7qQp14HZybNH948Kg1Lj
  wechat:
    subscribe:
      appId: wx552e48004afb1d3e
      secret: e88d899124f65f9a62693004109db81e
      redirectUrl: https%3A%2F%2Fwxg.woniu.video%2Fuat%2Fwechat%2Faccredit
      redirectPlusUrl: https%3A%2F%2Fwxg.woniu.video%2Fuat%2Fwechat%2Flogin
      type: 2
      url: https://work.weixin.qq.com/ct/wcde6d311c9b12aaf3de9f5e7de666c4ad6a
    work:
      debugger: true
      remarkEnable: false
      corpId: ww2432208455bd86fe
      encodingAESKey: ocvI35tpHwXBVLkC695lVfilJyWnBzte6R7JuHadcVo
      token: m2bawBgcOTU9B
      corpSecret: hJ0uVdXXQzftcs0VmAKrgtHn6XTI2uhtdBrRZSwDm34
      contactUser:
        - YeYuJiBei
        - RuYan
      contactUserName:
        - 夜雨寄北
        - 张翠华
      distribution: etPyXlDQAAUiqKqrRgieiPEQj7xqSFAg
      marketing: etPyXlDQAA35iL_EbCe_4iDfNTybTa0w

sms:
  url: https://dfsns.market.alicloudapi.com/data/send_sms
  appCode: AppCode 1fc508012f294f1693ac3b6d3a25a871
  templateId: CST_fvqnolafuopm11068
  content: code:%s
  debugger:
    enable: false
    platform: ww
  tencent:
    sdkAppId: 1400945631
    signName: 泉州润一进出口
    templateId: 2322743


order:
  video:
    #    订单自动完成时间（单位：小时，24小时制）测试调整原1080
    orderAutoFinishedTime: 1
    #    视频订单释放到模特端订单列表 前几个小时优先推送给优质模特（单位：小时，24小时制）（测试调整，原24）
    releaseOverTime: 1
    #    预选模特未对接超时时间（单位：小时，24小时制）（测试调整，原24）
    preselectModelOverTime: 72
    #    订单进入待确认状态 商家在几天后可以申请取消退款（单位：小时，24小时制）（测试调整，原168）
    merchantApplyRefundOverTime: 1
    #    订单进入待完成状态，商家在几天后可以催一催（单位：小时，24小时制）（测试调整，原240）
    reminderTime: 1
    #    订单进入待完成状态，模特逾期未反馈素材时间（单位：小时，24小时制）（测试调整，原360）
    orderFeedbackOverdueTime: 9999
    #    订单自动关闭订单时间
    closeOrderHours: 720
    #    展示关闭订单时间（单位：天）
    showReopenOrder: 30
    #    预选模特坑位数
    preselectModelNumberOfPits: 2
    #    视频订单审核通过后，几个小时内运营先处理后释放到模特池（单位：小时，24小时制）
    preselectModelReserveTime: 24
         #    模特端新版48小时逻辑旧数据结束时间
    modelTerminalAllListOldDataEndTime: 2025-06-19 00:00:00
  pay:
    #    开票税点 单位（%）
    taxPoint: 5
    #    种草码折扣 单位（%）
    grassCodeDiscount: 75
    #    全币种支付美金上限
    fullCurrencyUpperLimit: 50
  member:
    #    定时关闭订单时间 单位（小时）（测试调整，原720）
    closeOrderHours: 24
  logistic:
    #    无需跟进未处理自动更新时间 单位（天）
    noFollowNeedRefreshDays: 10
    #    已发货未处理自动更新时间 单位（秒）
    shipRefreshSeconds: 10800
  invoice:
    #    发票旧数据结束时间
    oldDataEndTime: 2025-01-17 00:00:00
  edit:
    #    剪辑管理上传账号
    uploadAccount:
      - 45号Karissa Lorett
      - 47号Lisset  Hieber
      - 48号Isabella Ann Henry
      - 50号Alejandro
      - 51号Cameron
      - 53号Clea Wilson
      - 54号Tammy Flynn
      - 55号Eli Reilin
    #    剪辑管理旧数据结束时间
    oldDataEndTime: 2025-03-26 00:00:00

logging:
  level:
    com.wnkx.common.lb.chooser: debug
    com.wnkx.logistic.service.impl: debug
    com.wnkx.order.service.impl: debug
    com.wnkx.order.service.core.impl.AsyncTaskServiceImpl: debug
    com.wnkx.biz.wechat.service.impl.WorkWechatApiServiceImpl: debug
    com.wnkx.order.service.impl.WeChatServiceImpl: debug
    com.wnkx.order.service.impl.AlipayServiceImpl: debug

xxl:
  job:
    admin:
      addresses: http://***********:25589/xxl-job-admin
model:
  baseUrl: https://model.fklwnkbum.uat.woniu.video/?creator=
  #  模特每日可申请订单数
  applyOrderNum:
    #    优质模特
    quality: 10
    #    中度模特
    moderate: 2
    #    一般模特
    ordinary: 5

exchangeRate:
  max: 8.0
  min: 6.5

channel:
  marketing:
    debugger: true
    remarkEnable: false
    officialWebsitePrefixURL: https://www.fklwnkbum.uat.woniu.video/?c=
    enterpriseMicroCustomerServicePrefixURL: https://customer.wbckal5h.uat.woniu.video/channel/wechat/add/
    businessWebsitePrefixURL: https://customer.wbckal5h.uat.woniu.video/?c=
    welcomeSms: "您好~\n我们是专注于提供高品质海外视频拍摄服务的蜗牛海拍团队。在这里，千名优质海外模特任您挑选，所有视频服务一口价29.9美金。我们注重每一个细节，确保视频的质量与服务双重保障。\n期待与您携手合作，共同出海！如果有任何疑问或需求，请随时与我联系。\n蜗牛海拍平台链接：%s%s（建议使用电脑打开此链接，体验更佳）"
  distribution:
    channelUrl: https://customer.wbckal5h.uat.woniu.video/channel/init
    videoNumRemark: 0
  poster:
    templateType: 1
    templateModelPath: /poster/overall_model_3x.png
    templateModelPath20250407: /poster/overall_model_20250428_3x.png
    templatePath: /poster/overall_3x.png
    templatePath20250407: /poster/overall_20250428_3x.png
    templatePath2025052301: /poster/overall_model_20250523_01_3x.png
    templatePath2025052302: /poster/overall_model_20250523_02_3x.png
    templatePath2025052311: /poster/overall_model_20250523_11_3x.png
    templatePath2025052312: /poster/overall_model_20250523_12_3x.png
    templatePath2025060401: /poster/overall_model_20250604_01_3x.png
    templatePath2025060402: /poster/overall_model_20250604_02_3x.png
    templatePath2025060411: /poster/overall_model_20250604_11_3x.png
    templatePath2025060412: /poster/overall_model_20250604_12_3x.png
    pixelMultiple: 3
  member-seed-record:
    payAccount:
      - 蜗蜗支付宝
      - 润一支付宝
      - 蜗蜗公户
      - 润一公户


thread-pool:
  #  核心线程数
  corePoolSize: 10
  #  最大线程数
  maxPoolSize: 100
  #  缓存队列
  queueCapacity: 50
  #  线程的空闲时间（单位：秒）
  keepAliveSeconds: 200

biz:
  prepay:
    prefix: YF
    initPrepayNum: 100000
    minRecharge: 10000
    minRechargePresented: 500
    maxRecharge: 50000
    maxRechargePresented: 3000

aliyun:
  accessKeyID: LTAI5tLiMsfiLJsd9y1N746u
  accessKeySecret: ******************************
  url: https://customer.wbckal5h.uat.woniu.video/
  origin: https://customer.wbckal5h.uat.woniu.video
  sceneCode: *****************
  endpoint: dypnsapi.aliyuncs.com
url:
  api: https://api.fklwnkbum.uat.woniu.video
  customer: https://customer.wbckal5h.uat.woniu.video
  run: https://run.fklwnkbum.uat.woniu.video
  model: https://model.fklwnkbum.uat.woniu.video
  channel: https://customer.wbckal5h.uat.woniu.video/channel/init

message:
  redisModelSelectTopic: redis-model-select

youdao:
  gateway: https://openapi.youdao.com/api
  appKey: 5c54c0f87deb7926
  appSecret: xM55rutnx5WRQfzZHudgi0uoIekEiIDu
  maxLength: 5000

customer-service:
  #  中文部部门ID
  chineseCustomerServiceDeptId: 3002
  #  英文部部门ID
  englishCustomerServiceDeptId: 3000