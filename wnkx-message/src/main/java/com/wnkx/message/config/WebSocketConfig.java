package com.wnkx.message.config;

import com.wnkx.message.websocket.AuthHandshakeInterceptor;
import com.wnkx.message.websocket.CustomWebSocketHandler;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :WebSocket配置
 * @create :2025-02-21 14:27
 **/
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    @Resource
    private AuthHandshakeInterceptor authInterceptor;
    @Resource
    private CustomWebSocketHandler customWebSocketHandler;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(customWebSocketHandler, "/ws-endpoint")
                .addInterceptors(authInterceptor)
                .setAllowedOrigins("*");
    }
}
