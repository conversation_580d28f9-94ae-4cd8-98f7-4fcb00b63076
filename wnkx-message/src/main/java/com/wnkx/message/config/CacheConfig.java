package com.wnkx.message.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Scheduler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Configuration
public class CacheConfig {

    private static final int DEFAULT_INITIAL_CAPACITY = 100;

    /**
     * 消息缓存
     *
     * @return
     */
    @Bean(name = "msgCache")
    public Cache<String, String> msgCache() {
        return Caffeine.newBuilder()
                .scheduler(Scheduler.forScheduledExecutorService(Executors.newScheduledThreadPool(1)))
                .initialCapacity(DEFAULT_INITIAL_CAPACITY)
                .maximumSize(1000)
                .expireAfterAccess(60, TimeUnit.MINUTES)
                .build();
    }

}
