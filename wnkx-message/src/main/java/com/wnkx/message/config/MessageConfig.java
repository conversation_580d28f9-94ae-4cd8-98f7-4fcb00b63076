package com.wnkx.message.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/8/28
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "message")
public class MessageConfig {
    /**
     * redis 模特选定订单 topic
     */
    String redisModelSelectTopic;

    /**
     * websocket 模特选定订单 topic
     */
    String websocketModelSelectTopic;

    /**
     * token 安全验证接口
     */
    String tokenSecurityUrl;
}
