package com.wnkx.message.config;

import lombok.RequiredArgsConstructor;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * okhttp配置类
 *
 * <AUTHOR>
 * @date 2022/4/5
 **/
@Component
@RequiredArgsConstructor
public class OkHttpConfig {

    @Bean
    public OkHttpClient okHttpClient() {
        OkHttpClient okHttpClient = new OkHttpClient().newBuilder()
                .connectTimeout(5, TimeUnit.SECONDS)
                .readTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .build();
        okHttpClient.dispatcher().setMaxRequests(100);
        okHttpClient.dispatcher().setMaxRequestsPerHost(100);
        return okHttpClient;
    }
}
