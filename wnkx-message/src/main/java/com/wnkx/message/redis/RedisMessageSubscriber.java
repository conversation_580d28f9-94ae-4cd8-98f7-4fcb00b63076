package com.wnkx.message.redis;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.wnkx.message.websocket.WebSocketSessionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-02-21 16:20
 **/
@Component
@Slf4j
public class RedisMessageSubscriber implements MessageListener {
    @Resource
    private WebSocketSessionManager webSocketSessionManager;

    @Resource
    private ThreadPoolTaskExecutor messagePoolTaskExecutor;

    @Override
    public void onMessage(Message message, byte[] pattern) {
        if (ObjectUtil.isNull(message.getBody())){
            log.error("redis订阅数据为空");
            return;
        }
        String payload = new String(message.getBody());
        JSONObject msgObj = JSONObject.parseObject(payload);
        if (ObjectUtil.isNull(msgObj)){
            log.error("redis订阅数据为空");
            return;
        }
        String userId = msgObj.getString("userId");
        String data = msgObj.getString("data");
        // 定向推送消息
        List<WebSocketSession> sessionList = webSocketSessionManager.getSessionList(userId);
        if (CollUtil.isEmpty(sessionList)){
            return;
        }
        CompletableFuture.runAsync(()->{
            for (WebSocketSession session : sessionList) {
                if (session.isOpen()) {
                    try {
                        Future<?> sendFuture = messagePoolTaskExecutor.submit(() -> {
                            try {
                                session.sendMessage(new TextMessage(data));
                            } catch (IOException e) {
                                log.error("推送消息失败", e);
                            }
                        });
                        // 超时控制 不需要超时处理
//                        sendFuture.get(1000, TimeUnit.MILLISECONDS);
                    } catch (Exception e) {
                        log.error("推送消息失败", e);
                    }
                }else {
                    webSocketSessionManager.removeSession(userId);
                }
            }
        }, messagePoolTaskExecutor);

    }
}
