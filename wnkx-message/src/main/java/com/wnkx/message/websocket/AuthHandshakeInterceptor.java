package com.wnkx.message.websocket;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.wnkx.message.config.MessageConfig;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :WebSocket安全拦截器
 * @create :2025-02-21 17:59
 **/
@Slf4j
@Component
public class AuthHandshakeInterceptor implements HandshakeInterceptor {

    private String AUTHORIZATION_HEADER = "Authorization";
    @Resource
    private MessageConfig messageConfig;

    @Resource
    private Cache<String, String> cache;

    /**
     * 建立WebSocket链接之前执行这个方法
     *
     * @param request    请求对象
     * @param response   响应对象
     * @param wsHandler  处理器
     * @param attributes 可以将参数传递到WebSocketSession中
     */
    @Override
    public boolean beforeHandshake(ServerHttpRequest request,
                                   ServerHttpResponse response,
                                   WebSocketHandler wsHandler,
                                   Map<String, Object> attributes) {
        //获取token
        String token = getTokenByParam(request);
        if (StrUtil.isBlank(token)) {
            log.error("获取token失败");
            return false;
        }
        //获取浏览器指纹
        String fingerprint = getFingerprintByParam(request);
        if (StrUtil.isBlank(fingerprint)) {
            log.error("获取浏览器指纹失败");
            return false;
        }
        if (cache.getIfPresent(token) != null){
            log.error("token已失效");
            return false;
        }
        String userInfo = getUserInfo(token);
        if (StrUtil.isBlank(userInfo)) {
            log.error("获取用户信息失败");
            return false;
        }
        JSONObject user = JSONObject.parseObject(userInfo);
        JSONObject sysUser = user.getJSONObject("user");
        if (ObjectUtil.isNull(sysUser)) {
            log.info("用户信息：{}", userInfo);
            log.error("获取用户信息用户数据为空");
            cache.put(token, "1");
            return false;
        }
        String userId = sysUser.getString("userId");
        if (StrUtil.isBlank(userId)) {
            log.error("获取用户信息用户数据userId为空");
            return false;
        }
//        JSONArray roles = sysUser.getJSONArray("roles");
//        if (ObjectUtil.isNull(roles) || ObjectUtil.isEmpty(roles)) {
//            log.error("获取用户信息角色信息失败");
//            return false;
//        }
//        String roleName = roles.getJSONObject(0).getString("roleName");
//        if (StrUtil.isBlank(roleName) || !roleName.equals("英文部客服")) {
//            log.error("非英文部客服无需连接websocket");
//            return false;
//        }

        attributes.put("MESSAGE_USER_ID", userId);
        attributes.put("MESSAGE_FINGERPRINT", fingerprint);

        return true;
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Exception exception) {

    }

    private String getToken(ServerHttpRequest request) {
        // 支持Header和Query两种方式
        String header = request.getHeaders()
                .getFirst(AUTHORIZATION_HEADER);
        if (header != null && header.startsWith("Bearer ")) {
            return header.substring(7);
        }
        return null;
    }

    private String getTokenByParam(ServerHttpRequest request) {
        String[] split = request.getURI().getQuery().split("&");
        for (String item : split) {
            if (item.startsWith("authorization") || item.startsWith("Authorization")) {
                if (item.split("=")[1].startsWith("Bearer ")) {
                    return item.split("=")[1].substring(7);
                } else {
                    return item.split("=")[1];
                }
            }
        }
        return null;
    }

    private String getFingerprintByParam(ServerHttpRequest request) {
        String[] split = request.getURI().getQuery().split("&");
        for (String item : split) {
            if (item.startsWith("fp")) {
                return item.split("=")[1];
            }
        }
        return null;
    }

    public String getUserInfo(String token) {
        if (StrUtil.isBlank(token)) {
            return null;
        }
        OkHttpClient client = new OkHttpClient();

        Request request = new Request.Builder()
                .url(messageConfig.getTokenSecurityUrl())
                .method("GET", null)
                .addHeader(HttpHeaders.CONTENT_TYPE, org.springframework.http.MediaType.APPLICATION_JSON_VALUE)
                .addHeader("Authorization", token)
                .build();
        Response response = null;
        try {
            response = client.newCall(request).execute();
            if (null != response.body()) {
                return response.body().string();
            }
            log.info("获取用户信息失败：返回状态码：{}", response.code());

            if (!response.isSuccessful()) {
                String errorMsg = getErrorMsg(response);
                log.error("获取用户信息失败：错误消息：{}", errorMsg);
            }
        } catch (Exception e) {
            log.error("获取用户信息失败：{}", e.getMessage());
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return null;
    }

    private String getErrorMsg(Response response) throws IOException {
        if (response.body() == null) {
            return "";
        }
        //  响应体字符串
        String rpBodyStr = response.body().string();
        JSONObject rpBodyStrJb = JSONObject.parseObject(rpBodyStr);
        //  获取data对象
        JSONObject data = rpBodyStrJb.getJSONObject("data");
        //  获取错误信息 是个数组
        String msg = data.getString("msg");
        //  获取错误信息
        return msg;
    }
}
