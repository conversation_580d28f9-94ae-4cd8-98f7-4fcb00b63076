package com.wnkx.message.websocket;

import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.ConcurrentWebSocketSessionDecorator;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;


/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会话映射管理
 * @create :2025-02-23 22:32
 **/
@Component
public class WebSocketSessionManager {
    /**
     * Key: 客户端唯一标识（如用户ID）, Value: WebSocketSession
     */
    private final ConcurrentHashMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();

    /**
     * 添加websocket连接会话
     *
     * @param userId    用户ID
     * @param fingerprint 指纹信息
     * @param session   WebSocketSession
     */
    public void addSession(String userId, String fingerprint, WebSocketSession session) {
        if (CollUtil.isNotEmpty(sessions)) {
            List<String> closeKey = new ArrayList<>();
            for (String item : sessions.keySet()) {
                if (item.substring(item.indexOf("-") + 1).equals(fingerprint)) {
                    WebSocketSession webSocketSession = sessions.get(item);
                    try {
                        webSocketSession.close(CloseStatus.NORMAL.withReason("同一浏览器新连接，关闭原连接~"));
                        closeKey.add(item);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }

            if (CollUtil.isNotEmpty(closeKey)) {
                for (String item : closeKey) {
                    sessions.remove(item);
                }
            }
        }

        // 包装 WebSocketSession 为 ConcurrentWebSocketSessionDecorator
        WebSocketSession safeSession = new ConcurrentWebSocketSessionDecorator(session, 10 * 1000, 512 * 1024);
        sessions.put(userId + "-" + fingerprint, safeSession);
    }

    public void removeSession(String Key) {
        sessions.remove(Key);
    }

    public WebSocketSession getSession(String Key) {
        return sessions.get(Key);
    }
    public List<WebSocketSession> getSessionList(String userId) {
        List<WebSocketSession> webSocketSessionList = new ArrayList<>();
        if (CollUtil.isEmpty(sessions.keySet())){
            return List.of();
        }
        for (String item : sessions.keySet()) {
            if (item.startsWith(userId + "-")) {
                webSocketSessionList.add(sessions.get(item));
            }
        }

        return webSocketSessionList;
    }
}

