package com.wnkx.message.websocket;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-02-23 23:07
 **/
@Slf4j
@Component
public class CustomWebSocketHandler extends TextWebSocketHandler {
    @Resource
    private WebSocketSessionManager webSocketSessionManager;

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        String userId = (String) session.getAttributes().get("MESSAGE_USER_ID");
        String fingerprint = (String) session.getAttributes().get("MESSAGE_FINGERPRINT");
        webSocketSessionManager.addSession(userId, fingerprint, session);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session,
                                      CloseStatus status) {
        String userId = (String) session.getAttributes().get("MESSAGE_USER_ID");
        String fingerprint = (String) session.getAttributes().get("MESSAGE_FINGERPRINT");
        webSocketSessionManager.removeSession(userId + "-" + fingerprint);
    }

    @Override
    public void handleTransportError(WebSocketSession session,
                                     Throwable exception) {
        log.error("连接异常: {}", exception.getMessage());
        String userId = (String) session.getAttributes().get("MESSAGE_USER_ID");
        String fingerprint = (String) session.getAttributes().get("MESSAGE_FINGERPRINT");
        webSocketSessionManager.removeSession(userId + "-" + fingerprint);
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        JSONObject jsonObject = null;
        try {
            jsonObject = JSONObject.parseObject(message.getPayload());
        } catch (Exception e) {
            if (session.isOpen()){
                session.sendMessage(new TextMessage("格式错误~"));
            }
        }
        if (ObjectUtil.isNotNull(jsonObject)
                && StrUtil.isNotBlank(jsonObject.getString("type"))
                && "HEARTBEAT".equals(jsonObject.getString("type"))) {
            try {
                JSONObject result = new JSONObject();
                result.put("result", "HB_ACK");
                if (session.isOpen()){
                    session.sendMessage(new TextMessage(result.toJSONString()));
                }
            } catch (IOException e) {
                log.error("推送消息失败", e);
            }
            return;
        }
    }
}

