server:
  port: 19019
spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  redis:
    database: 0
    host: ***********
    port: 6379
    password: Lp5cSj&G
    timeout: 5000
  application:
    name: wnkx-message
# 暴露监控端点
management:
  endpoints:
    web:
      exposure:
        include: '*'
  metrics:
    # 在 Prometheus 中添加特别的 Labels
    # 必须加上对应的应用名，因为需要以应用的维度来查看对应的监控
    tags:
      application: ${spring.application.name}
    # 下面选项建议打开，以监控 http 请求的 P99/P95 等，具体的时间分布可以根据实际情况设置
    distribution:
      sla:
        http:
          server:
            requests: 1ms,5ms,10ms,50ms,100ms,200ms,500ms,1s,5s
      percentiles-histogram:
        http:
          server:
            requests: true # 开启 http server 的请求监控

message:
  redisModelSelectTopic: redis-model-select
  websocketModelSelectTopic: websocket-model-select
  tokenSecurityUrl: http://**********:28080/system/user/getInfo

thread-pool:
  #  核心线程数
  corePoolSize: 10
  #  最大线程数
  maxPoolSize: 100
  #  缓存队列
  queueCapacity: 50
  #  线程的空闲时间（单位：秒）
  keepAliveSeconds: 200

