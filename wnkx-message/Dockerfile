FROM nexus.dev.woniu.world:28086/wnkx/baseline/eclipse-temurin:11-jdk-alpine-wnkx

LABEL MAINTAINER=CI
WORKDIR /apps
ADD target/wnkx-message.jar /apps/
ENV TZ=Asia/Shanghai

CMD java -server $JVM_ARGS -javaagent:/apps/opentelemetry-javaagent.jar \
         -Dotel.resource.attributes=service.name=wnkx-message,token=$APM_TOKEN\
         -Dotel.exporter.otlp.endpoint=http://pl.ap-guangzhou.apm.tencentcs.com:4317 \
         -jar wnkx-message.jar $XXL_ARGS
