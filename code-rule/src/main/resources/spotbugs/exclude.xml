<?xml version="1.0" encoding="UTF-8"?>
<FindBugsFilter
        xmlns="https://github.com/spotbugs/filter/3.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="https://github.com/spotbugs/filter/3.0.0
         https://raw.githubusercontent.com/spotbugs/spotbugs/3.1.0/spotbugs/etc/findbugsfilter.xsd">
  <Match>
    <Bug pattern="PATH_TRAVERSAL_IN"/>
  </Match>
  <Match>
    <Bug pattern="TEMPLATE_INJECTION_FREEMARKER"/>
  </Match>
  <Match>
    <Bug pattern="URLCONNECTION_SSRF_FD"/>
  </Match>
<!--  前后端分离的项目，不太容易出现 XSS 漏洞，并且此模式很容易命中导致误报-->
  <Match>
    <Bug pattern="XSS_SERVLET"/>
  </Match>
</FindBugsFilter>
