<?xml version="1.0"?>

<ruleset name="Custom Rules"
         xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 https://pmd.sourceforge.io/ruleset_2_0_0.xsd">

  <description>
    rules
  </description>
  <!-- Java错误倾向规范 -->
  <rule ref="category/java/errorprone.xml"/>

  <!-- Java最佳实践规范 -->
  <rule ref="category/java/bestpractices.xml"/>


  <!-- Java设计规范 避免深层嵌套的if语句: 减少代码的复杂度和混乱，提高代码可读性和可维护性 -->
  <rule ref="category/java/design.xml/AvoidDeeplyNestedIfStmts" />
  <!-- Java设计规范 避免重新抛出异常: 应该处理异常并采取适当的措施，而不是重新抛出相同的异常 -->
  <rule ref="category/java/design.xml/AvoidRethrowingException" />
  <!-- Java设计规范 避免抛出新的相同异常实例: 应该重用现有的异常实例，而不是创建新的相同类型的异常实例 -->
  <rule ref="category/java/design.xml/AvoidThrowingNewInstanceOfSameException" />
  <!-- Java设计规范 避免抛出NullPointerException异常: 应该进行空值检查，而不是故意抛出NullPointerException -->
  <rule ref="category/java/design.xml/AvoidThrowingNullPointerException" />
  <!-- Java设计规范 方法签名中避免使用未经检查的异常: 应该在方法签名中明确声明可能抛出的受检异常 -->
  <rule ref="category/java/design.xml/AvoidUncheckedExceptionsInSignatures" />
  <!-- Java设计规范 只有私有构造函数的类应该是final的: 如果类只有私有构造函数，则不需要被继承，应该将其声明为final -->
  <rule ref="category/java/design.xml/ClassWithOnlyPrivateConstructorsShouldBeFinal" />
  <!-- Java设计规范 认知复杂度: 用于度量代码的认知复杂性，即理解和阅读代码所需的认知负荷 -->
  <rule ref="category/java/design.xml/CognitiveComplexity" />
  <!-- Java设计规范 可折叠的if语句: 提倡简化复杂的if语句，减少代码嵌套层级 -->
  <rule ref="category/java/design.xml/CollapsibleIfStatements" />
  <!-- Java设计规范 圈复杂度: 用于度量方法的复杂性，即方法中的分支和决策路径数 -->
  <rule ref="category/java/design.xml/CyclomaticComplexity" />
  <!-- Java设计规范 不要继承Java Lang Error: 应该避免直接继承Java Lang Error类，而是继承RuntimeException或Exception -->
  <rule ref="category/java/design.xml/DoNotExtendJavaLangError" />
  <!-- Java设计规范 类长度过长: 类的长度超过了可接受的范围，可能存在设计问题 -->
  <rule ref="category/java/design.xml/ExcessiveClassLength" />
  <!-- Java设计规范 过多的导入语句: 类中导入的包过多，可能导致代码难以维护 -->
  <rule ref="category/java/design.xml/ExcessiveImports" />


  <!-- Java设计规范 方法长度过长: 方法的长度超过了可接受的范围，可能存在设计问题 -->
  <rule ref="category/java/design.xml/ExcessiveMethodLength" />
  <!-- Java设计规范 参数列表过长: 方法的参数列表过长，可能导致代码难以理解和维护 -->
  <rule ref="category/java/design.xml/ExcessiveParameterList" />
  <!-- Java设计规范 公共方法过多: 类中公共方法过多，可能存在设计问题，应考虑重构代码 -->
  <rule ref="category/java/design.xml/ExcessivePublicCount" />
  <!-- Java设计规范 可以将非final的字段声明为static: 如果非final字段在类的所有实例中是相同的，则可以将其声明为静态字段 -->
  <rule ref="category/java/design.xml/FinalFieldCouldBeStatic" />
  <!-- Java设计规范 类过于复杂: 类的职责过于庞大，建议将其拆分为多个更小的类 -->
  <rule ref="category/java/design.xml/GodClass" />
  <!-- Java设计规范 可变字段: 建议将字段声明为不可变的，以避免在多线程环境中出现问题 -->
  <rule ref="category/java/design.xml/ImmutableField" />
  <!-- Java设计规范 逻辑反转: 建议使用更直接的逻辑表达式来避免逻辑反转的情况 -->
  <rule ref="category/java/design.xml/LogicInversion" />
  <!-- Java设计规范 可变静态状态: 静态字段保存的状态可能会被修改，建议尽量避免在静态字段中保存可变状态 -->
  <rule ref="category/java/design.xml/MutableStaticState" />
  <!-- Java设计规范 NcssCount: 代码的非注释行数，用于度量代码的复杂性和可读性 -->
  <rule ref="category/java/design.xml/NcssCount" />
  <!-- Java设计规范 NPath复杂度: 用于度量方法的执行路径数，用于评估方法的复杂性 -->
  <rule ref="category/java/design.xml/NPathComplexity" />
  <!-- Java设计规范 简化布尔表达式: 建议简化复杂的布尔表达式，提高代码可读性 -->
  <rule ref="category/java/design.xml/SimplifyBooleanExpressions" />
  <!-- Java设计规范 简化布尔返回值: 建议简化复杂的布尔返回值逻辑，提高代码可读性 -->
  <rule ref="category/java/design.xml/SimplifyBooleanReturns" />
  <!-- Java设计规范 简化条件语句: 建议简化复杂的条件语句，提高代码可读性 -->
  <rule ref="category/java/design.xml/SimplifyConditional" />
  <!-- Java设计规范 单一字段: 类中只有一个字段，可能存在设计问题，应考虑是否需要增加更多字段 -->
  <rule ref="category/java/design.xml/SingularField" />
  <!-- Java设计规范 无用的重写方法: 方法重写没有实际改变父类方法的行为，可能是多余的 -->
  <rule ref="category/java/design.xml/UselessOverridingMethod" />
  <!-- Java设计规范 使用实用类: 建议使用实用类来组织和共享通用的静态方法 -->
  <rule ref="category/java/design.xml/UseUtilityClass" />
  <!-- Java文档规范 未注释的空构造函数: 空构造函数缺乏注释说明，建议添加注释说明构造函数的目的和用途 -->
  <rule ref="category/java/documentation.xml/UncommentedEmptyConstructor" />
  <!-- Java文档规范 未注释的空方法体: 空方法体缺乏注释说明，建议添加注释说明方法的用途和预期行为 -->
  <rule ref="category/java/documentation.xml/UncommentedEmptyMethodBody" />

  <!-- Java多线程规范 -->
  <rule ref="category/java/multithreading.xml" >
    <exclude name="AvoidUsingVolatile"/> <!-- 排除规则：避免使用 volatile -->
    <exclude name="DoNotUseThreads"/> <!-- 排除规则：不要直接使用 Threads 类 -->
  </rule>

  <!-- Java性能规范 添加空字符串: 建议使用空字符串字面量 "" 替代使用 new String("") 来提高性能 -->
  <rule ref="category/java/performance.xml/AddEmptyString" />
  <!-- Java性能规范 使用char类型进行字符追加: 建议使用 StringBuilder.append(char) 方法来提高性能 -->
  <rule ref="category/java/performance.xml/AppendCharacterWithChar" />
  <!-- Java性能规范 避免使用数组循环: 建议使用增强型for循环或迭代器来遍历数组，而不是使用传统的for循环 -->
  <rule ref="category/java/performance.xml/AvoidArrayLoops" />
  <!-- Java性能规范 避免创建多余的Calendar实例: 建议避免在循环或重复执行的代码中重复创建 Calendar 对象 -->
  <rule ref="category/java/performance.xml/AvoidCalendarDateCreation" />
  <!-- Java性能规范 避免使用FileStream: 建议使用BufferedInputStream或FileChannel等替代FileStream来提高性能 -->
  <rule ref="category/java/performance.xml/AvoidFileStream" />
  <!-- Java性能规范 BigInteger实例化: 建议使用 BigInteger.valueOf 方法来实例化 BigInteger 对象，而不是使用 new BigInteger -->
  <rule ref="category/java/performance.xml/BigIntegerInstantiation" />
  <!-- Java性能规范 连续的追加操作应复用StringBuilder: 建议在连续的追加操作中重复使用同一个 StringBuilder 对象，而不是每次都创建新的对象 -->
  <rule ref="category/java/performance.xml/ConsecutiveAppendsShouldReuse" />
  <!-- Java性能规范 连续的字面量追加: 建议将连续的字面量追加操作合并为一个追加操作，以减少不必要的性能开销 -->
  <rule ref="category/java/performance.xml/ConsecutiveLiteralAppends" />
  <!-- Java性能规范 低效的空字符串检查: 建议使用 StringUtils.isEmpty 或 "".equals 来判断字符串是否为空，而不是使用字符串长度为0的判断 -->
  <rule ref="category/java/performance.xml/InefficientEmptyStringCheck" />
  <!-- Java性能规范 低效的字符串缓冲: 建议避免在循环中频繁创建和修改字符串缓冲区，可以使用 StringBuilder 来提高性能 -->
  <rule ref="category/java/performance.xml/InefficientStringBuffering" />
  <!-- Java性能规范 StringBuffer声明不足: 建议在创建 StringBuffer 对象时指定初始容量，以避免扩容操作 -->
  <rule ref="category/java/performance.xml/InsufficientStringBufferDeclaration" />
  <!-- Java性能规范 可优化的toArray调用: 建议使用 List.toArray(T[]) 方法来优化 toArray 调用，避免创建额外的数组对象 -->
  <rule ref="category/java/performance.xml/OptimizableToArrayCall" />
  <!-- Java性能规范 冗余的字段初始化: 建议删除不必要的字段初始化，因为 Java 默认会对字段进行默认初始化 -->
  <rule ref="category/java/performance.xml/RedundantFieldInitializer" />
  <!-- Java性能规范 字符串实例化: 建议使用字符串字面量进行实例化，而不是使用 new String() 或 new String(char[]) -->
  <rule ref="category/java/performance.xml/StringInstantiation" />
  <!-- Java性能规范 字符串转换为字符串: 建议避免将字符串对象转换为字符串，因为它没有实际的意义 -->
  <rule ref="category/java/performance.xml/StringToString" />
  <!-- Java性能规范 switch语句分支过少: 建议使用 if-else 结构替代 switch 语句，当分支数量较少时可以提高性能 -->
  <rule ref="category/java/performance.xml/TooFewBranchesForASwitchStatement" />
  <!-- Java性能规范 使用ArrayList代替Vector: 建议使用 ArrayList 来替代 Vector，因为 ArrayList 在大多数情况下性能更好 -->
  <rule ref="category/java/performance.xml/UseArrayListInsteadOfVector" />
  <!-- Java性能规范 使用Arrays.asList: 建议使用 Arrays.asList 方法来创建 List 对象，以简化代码并提高性能 -->
  <rule ref="category/java/performance.xml/UseArraysAsList" />
  <!-- Java性能规范 使用indexOf(char): 建议使用 String.indexOf(char) 方法来查找字符在字符串中的位置，而不是将字符转换为字符串再使用 indexOf -->
  <rule ref="category/java/performance.xml/UseIndexOfChar" />
  <!-- Java性能规范 使用Apache Commons FileItem的IO流: 建议使用 Apache Commons FileItem 提供的 IO 流来处理文件上传，以提高性能 -->
  <rule ref="category/java/performance.xml/UseIOStreamsWithApacheCommonsFileItem" />
  <!-- Java性能规范 无用的valueOf调用: 建议避免不必要的 String.valueOf 方法调用，因为 Java 编译器会自动处理字符串的转换 -->
  <rule ref="category/java/performance.xml/UselessStringValueOf" />
  <!-- Java性能规范 使用StringBuffer进行字符串追加: 建议使用 StringBuffer 进行频繁的字符串追加操作，以提高性能 -->
  <rule ref="category/java/performance.xml/UseStringBufferForStringAppends" />
  <!-- Java性能规范 使用StringBuffer的length方法: 建议使用 StringBuffer 的 length() 方法来获取字符串长度，而不是将其转换为字符串再使用 length() -->
  <rule ref="category/java/performance.xml/UseStringBufferLength" />

</ruleset>
