#  XXL-JOB 构建同步发布

此模块自动将xxl-job发布环境对应的注册中心去


## 使用说明

在xxljob目录下新建.py文件，内容如下：
    
```python
from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType,
    MisfireStrategy

if __name__ == '__main__':
    
    # 这里填入对应的执行器 可选为 biz-job/order-job
    with Executor(app_name='biz-job') as executor:
        # 这里名称与需要执行的bean名称相同
        weedingOutOverduePreselectModel = Task(
            # title与 xxljob填写的名称相同
            title='淘汰已过期的预选模特',
            # 作者
            author='dwy',
            # 执行bean的名称
            executor_handler='weedingOutOverduePreselectModel',
            #  固定字段
            glue_type=GlueType.BEAN,
            # 调度类型 可选 ScheduleType.CRON 或 ScheduleType.NONE
            schedule_type=ScheduleType.CRON,
            # 调度时间顺序
            schedule_conf='0 * * * * ?',
            # 执行参数
            executor_param='',
            # 调度顺序 非必要不需要改动
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            # 任务 在线/不在线
            online=True
        )


        # 以下为执行一次的任务案例
        initBusinessDetailHandler = Task(
            title='初始化钱包详情数据',
            author='chp',
            executor_handler='initBusinessDetailHandler',
            glue_type=GlueType.BEAN,
            # 调度类型 可选 ScheduleType.CRON 或 ScheduleType.NONE
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )


        # 可以一次在脚本中执行多个脚本
        executor.submit(weedingOutOverduePreselectModel)
        executor.submit(initBusinessDetailHandler)
```

在构建期中会自动发布到xxl-job中心 ，暂不支持手动发布