from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType, \
    MisfireStrategy

if __name__ == '__main__':
    with Executor(app_name='order-job') as executor:

        fillOrderVideoMatchPreselectModelSnapshot = Task(
            title=' （一次性）补充order_video_match_preselect_model模特快照数据',
            author='dwy',
            executor_handler='fillOrderVideoMatchPreselectModelSnapshot',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=False
        )
        executor.submit(fillOrderVideoMatchPreselectModelSnapshot)