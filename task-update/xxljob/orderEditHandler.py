from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType, \
    MisfireStrategy

if __name__ == '__main__':
    with Executor(app_name='order-job') as executor:

        initOrderVideoUploadLinkAsin = Task(
            title='(一次性)初始化order_video_upload_link.asin',
            author='dwy',
            executor_handler='initOrderVideoUploadLinkAsin',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=False
        )
        resetOrderVideoUploadLinkScore = Task(
            title='（一次性）重新设置评分',
            author='dwy',
            executor_handler='resetOrderVideoUploadLinkScore',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=False
        )
        resetOrderVideoTaskDetailFlowRecordAndOrderVideoTaskDetailProcessRecord = Task(
            title='（一次性）设置order_video_task_detail_flow_record和order_video_task_detail_process_record 操作类型为订单回退的 operate_by_type设为3',
            author='dwy',
            executor_handler='resetOrderVideoTaskDetailFlowRecordAndOrderVideoTaskDetailProcessRecord',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=False
        )
        orderVideoUploadLinkHistoryUploadRecord = Task(
            title='（一次性）补全order_video_upload_link的上传记录',
            author='dwy',
            executor_handler='orderVideoUploadLinkHistoryUploadRecord',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=False
        )
        initOrderVideoFirstInfoVideoTitle = Task(
            title='（一次性）初始化order_video_upload_link.video_title_first',
            author='dwy',
            executor_handler='initOrderVideoFirstInfoVideoTitle',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=False
        )
        executor.submit(initOrderVideoUploadLinkAsin)
        executor.submit(resetOrderVideoUploadLinkScore)
        executor.submit(resetOrderVideoTaskDetailFlowRecordAndOrderVideoTaskDetailProcessRecord)
        executor.submit(orderVideoUploadLinkHistoryUploadRecord)
        executor.submit(initOrderVideoFirstInfoVideoTitle)