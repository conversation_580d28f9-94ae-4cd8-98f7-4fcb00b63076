from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType, \
    MisfireStrategy

if __name__ == '__main__':
    with Executor(app_name='order-job') as executor:
        refreshOrderRecordStatusHandler = Task(
            title='更新订单入账状态',
            author='chp',
            executor_handler='refreshOrderRecordStatusHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 10 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        orderClearReminderHandler = Task(
            title='清空催一催次数',
            author='dwy',
            executor_handler='orderClearReminderHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 0 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        closeMemberOrderHandler = Task(
            title='定时取消会员订单',
            author='chp',
            executor_handler='closeMemberOrderHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 59 23 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        weedingOutOverduePreselectModel = Task(
            title='淘汰已过期的预选模特',
            author='dwy',
            executor_handler='weedingOutOverduePreselectModel',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 * * * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        closeOrderHandler = Task(
            title='定时取消订单',
            author='chp',
            executor_handler='closeOrderHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 59 23 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        orderCloseAnotherPayHandler = Task(
            title='关闭已失效的代付链接',
            author='dwy',
            executor_handler='orderCloseAnotherPayHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 * * * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        refreshCurrentExchangeHandle = Task(
            title='刷新当前汇率',
            author='chp',
            executor_handler='refreshCurrentExchangeHandle',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 0/3 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        executor.submit(refreshCurrentExchangeHandle)
        executor.submit(orderCloseAnotherPayHandler)
        executor.submit(closeOrderHandler)
        executor.submit(weedingOutOverduePreselectModel)
        executor.submit(closeMemberOrderHandler)
        executor.submit(orderClearReminderHandler)
        executor.submit(refreshOrderRecordStatusHandler)