from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType, \
    MisfireStrategy

if __name__ == '__main__':
    with Executor(app_name='order-job') as executor:

        initOrderSeedMemberStatusHandler = Task(
            title='初始化订单种草官会员状态',
            author='fzw',
            executor_handler='initOrderSeedMemberStatusHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        executor.submit(initOrderSeedMemberStatusHandler)