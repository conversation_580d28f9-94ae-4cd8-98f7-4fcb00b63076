from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType, \
    MisfireStrategy

if __name__ == '__main__':
    with Executor(app_name='biz-job') as executor:
        amazonImageHistoryHandler = Task(
            title='更新AmazonHistoryImage',
            author='fzw',
            executor_handler='amazonImageHistoryHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        verifyAmazonCookieStatus = Task(
            title='验证AmazonCookie',
            author='fzw',
            executor_handler='verifyAmazonCookieStatus',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 0/3 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        executor.submit(amazonImageHistoryHandler)
        executor.submit(verifyAmazonCookieStatus)

