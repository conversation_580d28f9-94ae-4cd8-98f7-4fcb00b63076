from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType, \
    MisfireStrategy

if __name__ == '__main__':
    with Executor(app_name='order-job') as executor:

        expireCloseMergeHandler = Task(
            title='到期自动关闭合并单',
            author='dwy',
            executor_handler='expireCloseMergeHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 59 23 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        orderVideoMatchHandler = Task(
            title='（一次性）补全order_video_match的选定信息',
            author='dwy',
            executor_handler='orderVideoMatchHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=False
        )
        orderVideoModelChangeHandler = Task(
            title='（一次性）补全order_video_model_change的选定信息',
            author='dwy',
            executor_handler='orderVideoModelChangeHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=False
        )
        orderVideoModelChangeHandler2 = Task(
            title=' (一次性)更新order_video_model_change数据',
            author='dwy',
            executor_handler='orderVideoModelChangeHandler2',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=False
        )

        autoCompleteOrderVideoHandler = Task(
            title='自动完成视频订单',
            author='dwy',
            executor_handler='autoCompleteOrderVideoHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 0 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        updateOrderVideoCreateOrderUserNameAndNickNameHandler = Task(
            title='定时更新order_video.create_order_user_name、create_order_user_nick_name',
            author='dwy',
            executor_handler='updateOrderVideoCreateOrderUserNameAndNickNameHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0/33 * * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        initOrderTableOrderUserNameAndNickNameHandler = Task(
            title='（一次性）初始化order_table.order_user_name、order_user_nick_name',
            author='dwy',
            executor_handler='initOrderTableOrderUserNameAndNickNameHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        initOrderVideoCreateOrderUserNameAndNickNameHandler = Task(
            title='（一次性）初始化order_video.create_order_operation_user_name、create_order_operation_user_nick_name',
            author='dwy',
            executor_handler='initOrderVideoCreateOrderUserNameAndNickNameHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        initOrderMergeMergeNickByHandler = Task(
            title='（一次性）初始化order_merge.merge_nick_by',
            author='dwy',
            executor_handler='initOrderMergeMergeNickByHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        initOrderAnotherPayCreateNickByHandler = Task(
            title='（一次性）初始化order_another_pay.create_nick_by',
            author='dwy',
            executor_handler='initOrderAnotherPayCreateNickByHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        initOrderPromotionDetailHandler = Task(
            title='（一次性）初始化种草码和满5减100的活动使用详情记录',
            author='dwy',
            executor_handler='initOrderPromotionDetailHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        resetOrderTablePayAmountDollar = Task(
            title='（一次性）重新设置有种草码优惠的order_table.pay_amount_dollar',
            author='dwy',
            executor_handler='resetOrderTablePayAmountDollar',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        customerServiceDataStatisticsDayHandler = Task(
            title='客服数据统计-按天统计客服数据',
            author='dwy',
            executor_handler='customerServiceDataStatisticsDayHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 1 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        customerServiceDataStatisticsMonthHandler = Task(
            title='客服数据统计-按月统计客服数据',
            author='dwy',
            executor_handler='customerServiceDataStatisticsMonthHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 1 1 * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        orderVideoDataStatisticsDayHandler = Task(
            title='视频订单数据统计-按天统计视频订单数据',
            author='dwy',
            executor_handler='orderVideoDataStatisticsDayHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 1 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        executor.submit(orderVideoMatchHandler)
        executor.submit(orderVideoModelChangeHandler)
        executor.submit(expireCloseMergeHandler)
        executor.submit(orderVideoModelChangeHandler2)
        executor.submit(autoCompleteOrderVideoHandler)
        executor.submit(updateOrderVideoCreateOrderUserNameAndNickNameHandler)
        executor.submit(initOrderTableOrderUserNameAndNickNameHandler)
        executor.submit(initOrderVideoCreateOrderUserNameAndNickNameHandler)
        executor.submit(initOrderMergeMergeNickByHandler)
        executor.submit(initOrderAnotherPayCreateNickByHandler)
        executor.submit(initOrderPromotionDetailHandler)
        executor.submit(resetOrderTablePayAmountDollar)
        executor.submit(customerServiceDataStatisticsDayHandler)
        executor.submit(customerServiceDataStatisticsMonthHandler)
        executor.submit(orderVideoDataStatisticsDayHandler)
