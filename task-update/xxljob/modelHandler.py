from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType, \
    MisfireStrategy

if __name__ == '__main__':
    with Executor(app_name='biz-job') as executor:

        deleteModelDailyApplyOrderCountHandler = Task(
            title='删除模特每日申请单量',
            author='dwy',
            executor_handler='deleteModelDailyApplyOrderCountHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 8 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        initModelStatusTimeHandler = Task(
            title='（一次性）初始化model.status_time',
            author='dwy',
            executor_handler='initModelStatusTimeHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        modelDataStatisticsDayHandler = Task(
            title='模特数据统计-按天统计模特数据',
            author='dwy',
            executor_handler='modelDataStatisticsDayHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 1 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        modelDataStatisticsMonthHandler = Task(
            title='模特数据统计-按月统计模特数据',
            author='dwy',
            executor_handler='modelDataStatisticsMonthHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 1 1 * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        initModelRankingListHandler = Task(
            title='（一次性）初始化模特数据-模特接单排行榜',
            author='dwy',
            executor_handler='initModelRankingListHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        initModelVideoResourceHandler = Task(
            title='（一次性）将model.amazon_video、tiktok_video 数据移到model_video_resource表中',
            author='dwy',
            executor_handler='initModelVideoResourceHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        executor.submit(deleteModelDailyApplyOrderCountHandler)
        executor.submit(initModelStatusTimeHandler)
        executor.submit(modelDataStatisticsDayHandler)
        executor.submit(modelDataStatisticsMonthHandler)
        executor.submit(initModelRankingListHandler)
        executor.submit(initModelVideoResourceHandler)
