from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType, \
    MisfireStrategy

if __name__ == '__main__':
    with Executor(app_name='biz-job') as executor:
        updateSubAccountWechatUrl = Task(
            title='更新子账号邀请时添加客服与主账号添加客服一致',
            author='fzw',
            executor_handler='updateSubAccountWechatUrl',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        initWechatContactData = Task(
            title='初始化数据库中企业微信联系人记录',
            author='fzw',
            executor_handler='initWechatContactData',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        checkOutRedisContactUser = Task(
            title='动态切换企业微信联系人缓存',
            author='fzw',
            executor_handler='checkOutRedisContactUser',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0/5 * * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        initChannelContactUser = Task(
            title='初始化用户添加渠道数据',
            author='fzw',
            executor_handler='initChannelContactUser',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        executor.submit(initChannelContactUser)
        executor.submit(updateSubAccountWechatUrl)
        executor.submit(initWechatContactData)
        executor.submit(checkOutRedisContactUser)
