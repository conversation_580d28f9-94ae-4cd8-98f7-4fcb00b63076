from enum import Enum, unique
from typing import Union, Optional

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(f'{os.path.dirname(os.path.dirname(os.path.abspath(__file__)))}/sdk')

from config import xxl_job_admin_address, xxl_job_user_usr, xxl_job_user_psw
from sdk import api


@unique
class ScheduleType(Enum):
    """
    调度类型
    """
    CRON = 'CRON'
    FIX_RATE = 'FIX_RATE'
    NONE = 'NONE'


@unique
class GlueType(Enum):
    """
    运行模式
    """
    BEAN = 'BEAN'


@unique
class RouteStrategy(Enum):
    """
    路由策略
    """
    FIRST = 'FIRST'
    LAST = 'LAST'
    ROUND = 'ROUND'
    RANDOM = 'RANDOM'
    CONSISTENT_HASH = 'CONSISTENT_HASH'
    LEAST_FREQUENTLY_USED = 'LEAST_FREQUENTLY_USED'
    LEAST_RECENTLY_USED = 'LEAST_RECENTLY_USED'
    FAILOVER = 'FAILOVER'
    BUSYOVER = 'BUSYOVER'
    SHARDING_BROADCAST = 'SHARDING_BROADCAST'


@unique
class MisfireStrategy(Enum):
    """
    调度过期策略
    """
    FIRE_ONCE_NOW = 'FIRE_ONCE_NOW'
    DO_NOTHING = 'DO_NOTHING'


@unique
class ExecutorBlockStrategy(Enum):
    """
    阻塞处理策略
    """
    SERIAL_EXECUTION = 'SERIAL_EXECUTION'
    DISCARD_LATER = 'DISCARD_LATER'
    COVER_EARLY = 'COVER_EARLY'


class Task:
    title: str
    author: str
    alarmEmail: str
    scheduleType: ScheduleType
    scheduleConf: str
    glueType: Union[GlueType, str]
    executorHandler: str
    executorParam: str
    executorRouteStrategy: RouteStrategy
    childJobId: str
    misfireStrategy: MisfireStrategy
    executorBlockStrategy: ExecutorBlockStrategy
    executorTimeout: int
    executorFailRetryCount: int
    glueRemark: str
    glueSource: str
    online: bool

    def __init__(self,
                 title: str,
                 author: str,
                 executor_handler: str,
                 glue_type: Union[GlueType, str] = GlueType.BEAN,
                 alarm_email: Optional[str] = '',
                 schedule_type: ScheduleType = ScheduleType.CRON,
                 schedule_conf: Optional[str] = None,
                 executor_param: Optional[str] = None,
                 executor_route_strategy: RouteStrategy = RouteStrategy.LEAST_FREQUENTLY_USED,
                 child_job_id: Optional[str] = None,
                 misfire_strategy: MisfireStrategy = MisfireStrategy.DO_NOTHING,
                 executor_block_strategy: ExecutorBlockStrategy = ExecutorBlockStrategy.SERIAL_EXECUTION,
                 executor_timeout: int = 0,
                 executor_rail_retry_count: int = 0,
                 glue_remark: Optional[str] = '',
                 glue_source: Optional[str] = '',
                 online=True):
        self.alarmEmail = alarm_email
        self.glueSource = glue_source
        self.glueRemark = glue_remark
        self.executorFailRetryCount = executor_rail_retry_count
        self.executorTimeout = executor_timeout
        self.executorBlockStrategy = executor_block_strategy
        self.misfireStrategy = misfire_strategy
        self.childJobId = child_job_id
        self.executorRouteStrategy = executor_route_strategy
        self.executorParam = executor_param
        self.glueType = glue_type
        self.scheduleConf = schedule_conf
        self.scheduleType = schedule_type
        self.executorHandler = executor_handler
        self.title = title
        self.author = author
        self.online = online
        if schedule_type == ScheduleType.NONE:
            self.online = False


class Executor:
    name: str
    api = None
    executor_id = None
    tasks = []

    def __init__(self, app_name):
        self.name = app_name

    def __enter__(self):
        self.api = api.create(xxl_job_admin_address)
        self.api.login(xxl_job_user_usr, xxl_job_user_psw)
        executor_id = self.api.find_executor_id(self.name)
        assert executor_id
        self.executor_id = executor_id
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        for task in self.tasks:
            self.__submit(task)
        self.api = None
        self.executor_id = None
        return self

    def submit(self, task: Task):
        for exists_task in self.tasks:
            assert exists_task.title != task.title, f"检测到重复的任务 title：{task.title}，任务 title 属性不可重复"
        self.tasks.append(task)


    def __submit(self, task: Task):
        task_id = self.api.find_task_id(self.executor_id, task.title)
        if task_id:
            self.api.update_task({
                'jobGroup': self.executor_id,
                'jobDesc': task.title,
                'author': task.author,
                'alarmEmail': task.alarmEmail,
                'scheduleType': task.scheduleType.value,
                'scheduleConf': task.scheduleConf,
                'executorHandler': task.executorHandler,
                'executorParam': task.executorParam,
                'executorRouteStrategy': task.executorRouteStrategy.value,
                'childJobId': task.childJobId,
                'misfireStrategy': task.misfireStrategy.value,
                'executorBlockStrategy': task.executorBlockStrategy.value,
                'executorTimeout': task.executorTimeout,
                'executorFailRetryCount': task.executorFailRetryCount,
                'id': task_id,
            })
        else:
            task_id = self.api.add_task({
                'jobGroup': self.executor_id,
                'jobDesc': task.title,
                'author': task.author,
                'alarmEmail': task.alarmEmail,
                'scheduleType': task.scheduleType.value,
                'scheduleConf': task.scheduleConf,
                'glueType': task.glueType if isinstance(task.glueType, str) else task.glueType.value,
                'executorHandler': task.executorHandler,
                'executorParam': task.executorParam,
                'executorRouteStrategy': task.executorRouteStrategy.value,
                'childJobId': task.childJobId,
                'misfireStrategy': task.misfireStrategy.value,
                'executorBlockStrategy': task.executorBlockStrategy.value,
                'executorTimeout': task.executorTimeout,
                'executorFailRetryCount': task.executorFailRetryCount,
                'glueRemark': task.glueRemark,
                'glueSource': task.glueSource,
            })
        if task.online:
            self.api.start_task(task_id)
        else:
            self.api.stop_task(task_id)
        return task_id

