import requests


class XXLJobApi:
    session = None
    base_url = None

    def __init__(self, base_url):
        self.base_url = base_url
        self.session = requests.session()

    def login(self, username, password):
        response = self.session.post(f'{self.base_url}/login', data={
            'userName': username,
            'password': password
        })
        assert response.status_code == 200

    def find_executor_id(self, app_name):
        """
        返回执行器的 id
        :param app_name:  app name
        :return: id
        """
        response = self.session.post(f'{self.base_url}/jobgroup/pageList', data={
            'appname': app_name
        })
        assert response.status_code == 200
        data = response.json()
        rows = data.get('data', [])
        if rows:
            return rows[0].get('id')

    def find_task_id(self, executor_id, title):
        response = self.session.post(f'{self.base_url}/jobinfo/pageList', data={
            'jobDesc': title,
            'jobGroup': executor_id,
            'triggerStatus': -1,
            'start': 0,
            'length': 10000
        })
        assert response.status_code == 200
        data = response.json()
        rows = data.get('data', [])
        if rows:
            match_row = list(filter(lambda r: r.get('jobDesc') == title, rows))
            if match_row:
                assert len(match_row) == 1
                return match_row[0].get('id')

    def update_task(self, task: dict):
        """
        任务属性：
        {
            'jobGroup': 2,
            'jobDesc': '批量执行导入TechCool原料列表',
            'author': 'westo',
            'alarmEmail': '',
            'scheduleType': 'CRON',
            'scheduleConf': '0 0/3 * * * ?',
            'cronGen_display': '0 0/3 * * * ?',
            'schedule_conf_CRON': '0 0/3 * * * ?',
            'schedule_conf_FIX_RATE': '',
            'schedule_conf_FIX_DELAY': '',
            'executorHandler': 'demoHandler',
            'executorParam': '',
            'executorRouteStrategy': 'FIRST',
            'childJobId': '',
            'misfireStrategy': 'DO_NOTHING',
            'executorBlockStrategy': 'SERIAL_EXECUTION',
            'executorTimeout': 0,
            'executorFailRetryCount': 0,
            'id': 30038,
        }
        :param task:
        :return:
        """
        response = self.session.post(f'{self.base_url}/jobinfo/update', data=task)
        assert response.status_code == 200
        assert response.json().get('code') == 200

    def add_task(self, task: dict):
        """
        任务属性：
        {
            'jobGroup': 2,
            'jobDesc': '批量执行导入TechCool原料列表',
            'author': 'westo',
            'alarmEmail': '',
            'scheduleType': 'CRON',
            'scheduleConf': '0 0/3 * * * ?',
            'cronGen_display': '0 0/3 * * * ?',
            'schedule_conf_CRON': '0 0/3 * * * ?',
            'schedule_conf_FIX_RATE': '',
            'schedule_conf_FIX_DELAY': '',
            'glueType': 'BEAN'
            'executorHandler': 'demoHandler',
            'executorParam': '',
            'executorRouteStrategy': 'FIRST',
            'childJobId': '',
            'misfireStrategy': 'DO_NOTHING',
            'executorBlockStrategy': 'SERIAL_EXECUTION',
            'executorTimeout': 0,
            'executorFailRetryCount': 0,
            'glueRemark': '',
            'glueSource': ''
        }
        :param task:
        :return: task id
        """
        response = self.session.post(f'{self.base_url}/jobinfo/add', data=task)
        assert response.status_code == 200, response.reason
        data = response.json()
        assert data.get('code') == 200, data
        return int(data.get('content'))

    def start_task(self, task_id):
        response = self.session.post(f'{self.base_url}/jobinfo/start', data={'id': task_id})
        assert response.status_code == 200, response.reason
        data = response.json()
        assert data.get('code') == 200, data

    def stop_task(self, task_id):
        response = self.session.post(f'{self.base_url}/jobinfo/stop', data={'id': task_id})
        assert response.status_code == 200, response.reason
        data = response.json()
        assert data.get('code') == 200, data


def create(base_url) -> XXLJobApi:
    """
    创建并返回一个 xxl job 的客户端实例
    :param base_url xxl job admin 地址
    :return: xxl job 的客户端实例
    """
    return XXLJobApi(base_url)


if __name__ == '__main__':
    api = XXLJobApi('http://10.160.64.1:25388/xxl-job-admin')
    api.login('admin', '123456')
    api.update_task({
        'jobGroup': 2,
        'jobDesc': 'test update',
        'author': 'test',
        'alarmEmail': '',
        'scheduleType': 'CRON',
        'scheduleConf': '0 1 0 * * ?',
        'executorHandler': 'test',
        'executorParam': 'test',
        'executorRouteStrategy': 'FIRST',
        'childJobId': '',
        'misfireStrategy': 'DO_NOTHING',
        'executorBlockStrategy': 'SERIAL_EXECUTION',
        'executorTimeout': 0,
        'executorFailRetryCount': 0,
        'id': 60038,
    })

