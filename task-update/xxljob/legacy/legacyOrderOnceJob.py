from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType, \
    MisfireStrategy

if __name__ == '__main__':
    with Executor(app_name='order-job') as executor:
        saveMemberValidityFlowHandler = Task(
            title='	初始化商家会员有效期修改流水',
            author='chp',
            executor_handler='saveMemberValidityFlowHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        completeOrderVideoMatch = Task(
            title='补全order_video_match',
            author='dwy',
            executor_handler='completeOrderVideoMatch',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        completeOrderVideoMatchPreselectModel = Task(
            title='补全order_video_match_preselect_model的选择信息',
            author='dwy',
            executor_handler='completeOrderVideoMatchPreselectModel',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        completeOrderVideoMatchIsNull = Task(
            title='补全order_video_match待匹配状态下匹配单为空的数据',
            author='dwy',
            executor_handler='completeOrderVideoMatchIsNull',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        rectifyOrderVideoTaskFlowRecord = Task(
            title='矫正 订单_视频_工单任务_流转记录表 创建工单的处理人ID为提交人ID',
            author='dwy',
            executor_handler='rectifyOrderVideoTaskFlowRecord',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        completeOrderVideoMatchIssueId = Task(
            title='补全order_video_match的issue_id数据',
            author='dwy',
            executor_handler='completeOrderVideoMatchIssueId',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        updateCreateOrderUserName = Task(
            title='将create_order_user_name为空的数据设置为create_order_user_n',
            author='dwy',
            executor_handler='updateCreateOrderUserName',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        saveDistributionChannelOrderHandler = Task(
            title='保存分销渠道订单',
            author='chp',
            executor_handler='saveDistributionChannelOrderHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        fillOrderPayAmountDollarHandler = Task(
            title='补全order_table.pay_amount_dollar',
            author='dwy',
            executor_handler='fillOrderPayAmountDollarHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        fillOrderVideoAmountDollarHandler = Task(
            title='补全order_video。amount_dollar',
            author='dwy',
            executor_handler='fillOrderVideoAmountDollarHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        fillOrderVideoMatchModelSnapShot = Task(
            title='补全order_video_match、order_video_match_preselect_mo',
            author='dwy',
            executor_handler='fillOrderVideoMatchModelSnapShot',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        initOrderSeedCodeHandler = Task(
            title='补全order_table：settle_rage、channel_name',
            author='dwy',
            executor_handler='initOrderSeedCodeHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        fillOrderVideoMainCarryVideoIdHandler = Task(
            title='（一次性）补全order_video.main_carry_video_id',
            author='dwy',
            executor_handler='fillOrderVideoMainCarryVideoIdHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        fillOrderVideoModelShippingAddressField = Task(
            title='（一次性）补全order_video_model_shipping_address.shipping',
            author='dwy',
            executor_handler='fillOrderVideoModelShippingAddressField',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        fillOrderVideoLogisticShippingAddressIdHandler = Task(
            title='	（一次性）补全order_video_logistic.shipping_address_id数据',
            author='dwy',
            executor_handler='fillOrderVideoLogisticShippingAddressIdHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        fillOrderVideoModelShippingAddressLogisticFlagHandler = Task(
            title='	（一次性）补全order_video_model_shipping_address.logistic',
            author='dwy',
            executor_handler='fillOrderVideoModelShippingAddressLogisticFlagHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        executor.submit(fillOrderVideoModelShippingAddressLogisticFlagHandler)
        executor.submit(fillOrderVideoLogisticShippingAddressIdHandler)
        executor.submit(fillOrderVideoModelShippingAddressField)
        executor.submit(fillOrderVideoMainCarryVideoIdHandler)
        executor.submit(initOrderSeedCodeHandler)
        executor.submit(fillOrderVideoMatchModelSnapShot)
        executor.submit(fillOrderVideoAmountDollarHandler)
        executor.submit(fillOrderPayAmountDollarHandler)
        executor.submit(saveDistributionChannelOrderHandler)
        executor.submit(updateCreateOrderUserName)
        executor.submit(completeOrderVideoMatchIssueId)
        executor.submit(rectifyOrderVideoTaskFlowRecord)
        executor.submit(completeOrderVideoMatchIsNull)
        executor.submit(completeOrderVideoMatchPreselectModel)
        executor.submit(completeOrderVideoMatch)
        executor.submit(saveMemberValidityFlowHandler)