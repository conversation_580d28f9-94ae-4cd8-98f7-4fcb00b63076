from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType, \
    MisfireStrategy

if __name__ == '__main__':
    with Executor(app_name='order-job') as executor:
        refreshLogisticFollowHandle = Task(
            title='更新物流跟进数据',
            author='chp',
            executor_handler='refreshLogisticFollowHandle',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 1 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        refreshShipLogisticFollowHandle = Task(
            title='更新物流跟进已发货数据',
            author='chp',
            executor_handler='refreshShipLogisticFollowHandle',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 0/12 * * ? ',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        refreshLogisticUpdateTimeHandle = Task(
                    title='（一次性）处理物流跟进表：物流系统同步时间',
                    author='chp',
                    executor_handler='refreshLogisticUpdateTimeHandle',
                    glue_type=GlueType.BEAN,
                    schedule_type=ScheduleType.NONE,
                    schedule_conf='',
                    executor_param='',
                    executor_route_strategy=RouteStrategy.ROUND,
                    misfire_strategy=MisfireStrategy.DO_NOTHING,
                    executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
                    online=False
                )


        executor.submit(refreshLogisticFollowHandle)
        executor.submit(refreshShipLogisticFollowHandle)
        executor.submit(refreshLogisticUpdateTimeHandle)

