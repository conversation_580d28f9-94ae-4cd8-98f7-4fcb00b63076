from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType, \
    MisfireStrategy

if __name__ == '__main__':
    with Executor(app_name='biz-job') as executor:
        refreshSettleStatusHandler = Task(
            title='刷新结算状态',
            author='chp',
            executor_handler='refreshSettleStatusHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 * * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        updatePaySucceed = Task(
            title='初始化商家是否支付完成（一次）',
            author='chp',
            executor_handler='updatePaySucceed',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )


        refreshMemberSeedRecordStatusHandler = Task(
            title='刷新种草记录结算状态',
            author='chp',
            executor_handler='refreshMemberSeedRecordStatusHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 * * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        initMemberSeedRecord = Task(
            title='初始化分销结算记录（一次）',
            author='chp',
            executor_handler='initMemberSeedRecord',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        executor.submit(refreshSettleStatusHandler)
        executor.submit(updatePaySucceed)
        executor.submit(refreshMemberSeedRecordStatusHandler)
        executor.submit(initMemberSeedRecord)
