from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType, \
    MisfireStrategy

if __name__ == '__main__':
    with Executor(app_name='biz-job') as executor:
        recordBusinessMemberData = Task(
            title='记录商家会员统计数据',
            author='chp',
            executor_handler='recordBusinessMemberData',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 10 0 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        recordBusinessOrderData = Task(
            title='记录商家订单统计数据',
            author='chp',
            executor_handler='recordBusinessOrderData',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 10 0 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )


        initBusinessMemberValidityFlowRechargeCount = Task(
            title='初始化商家会员有效期修改流水充值数量字段（一次性）',
            author='chp',
            executor_handler='initBusinessMemberValidityFlowRechargeCount',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )


        executor.submit(recordBusinessMemberData)
        executor.submit(recordBusinessOrderData)
        executor.submit(initBusinessMemberValidityFlowRechargeCount)
