from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType, \
    MisfireStrategy

if __name__ == '__main__':
    with Executor(app_name='biz-job') as executor:

        updateRecentOrderHandler = Task(
            title='修改商家30天内是否存在订单数据',
            author='chp',
            executor_handler='updateRecentOrderHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 1 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        updateMemberStatus = Task(
            title='修改会员状态',
            author='chp',
            executor_handler='updateMemberStatus',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 5 0 * * ? ',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        updateModelOvertimeRateAndAfterSaleRateHandler = Task(
            title='定时更新模特库超时率、售后率',
            author='chp',
            executor_handler='updateModelOvertimeRateAndAfterSaleRateHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 1 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        updateModelTravelStatusHandler = Task(
            title='模特行程时间开始与结束更新模特状态',
            author='dwy',
            executor_handler='updateModelTravelStatusHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 0 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        updateWeChatTag = Task(
            title='刷新企微标签数据',
            author='chp',
            executor_handler='updateWeChatTag',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 0 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )

        loadMarketingChannelVisitHandler = Task(
            title='加载独立访客数据',
            author='chp',
            executor_handler='loadMarketingChannelVisitHandler',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 * * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        executor.submit(updateRecentOrderHandler)
        executor.submit(updateMemberStatus)
        executor.submit(updateModelOvertimeRateAndAfterSaleRateHandler)
        executor.submit(updateModelTravelStatusHandler)
        executor.submit(updateWeChatTag)
        executor.submit(loadMarketingChannelVisitHandler)