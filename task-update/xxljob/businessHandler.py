from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType, \
    MisfireStrategy

if __name__ == '__main__':
    with Executor(app_name='biz-job') as executor:

        updateBusinessAccount = Task(
            title='（一次性）补全business_account.owner_account,business_account.owner_account_biz_user_id',
            author='dwy',
            executor_handler='updateBusinessAccount',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.NONE,
            schedule_conf='',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=False
        )
        executor.submit(updateBusinessAccount)