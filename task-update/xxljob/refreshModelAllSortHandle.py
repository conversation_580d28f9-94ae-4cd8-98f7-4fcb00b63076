from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType, \
    MisfireStrategy

if __name__ == '__main__':
    with Executor(app_name='order-job') as executor:

        refreshModelAllSortHandle = Task(
            title='刷新所有模特的排序',
            author='fzw',
            executor_handler='refreshModelAllSortHandle',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 0 */3 * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        executor.submit(refreshModelAllSortHandle)