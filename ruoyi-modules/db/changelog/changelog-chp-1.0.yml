databaseChangeLog:
  - logicalFilePath: 'changelog-chp-1.0.yml'
  - changeSet:
      id: chp-1
      author: chp
      changes:
        - sql:
            sql: |
              INSERT INTO `sys_config`(`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '会员加赠时间', 'sys.member.presented.time', '1', 'Y', 'admin', '2024-08-02 16:47:30', NULL, NULL, '会员加赠时间单位：月');
              INSERT INTO `sys_config`(`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '会员加赠日期', 'sys.member.presented.date', '1-20', 'Y', 'admin', '2024-08-02 16:54:12', NULL, NULL, '会员加赠日期：1-20代表每月1号到10号');

  - changeSet:
      id: chp-1733133471
      author: chp
      changes:
        - sql:
            sql: |
              INSERT INTO `sys_config`(`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '渠道会员折扣', 'sys.channel.member.discount', '88', 'Y', 'admin', '2024-12-02 17:00:15', 'admin', '2024-12-02 17:00:20', '渠道会员折扣：1-99（单位：%）');

  - changeSet:
      id: chp-1736921544
      author: chp
      changes:
        - sql:
            sql: |
              INSERT INTO `sys_config`(`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, '裂变渠道会员折扣', 'sys.channel.fission.member.discount', '88', 'Y', 'admin', '2025-01-15 17:00:15', 'admin', '2025-01-15 17:00:15', '裂变渠道会员折扣：1-99（单位：%）');

  - changeSet:
      id: chp-1736990790
      author: chp
      changes:
        - sql:
            sql: |
              INSERT INTO `sys_config`(`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (9, '裂变渠道是否展示', 'sys.channel.fission.show', 'true', 'Y', 'admin', '2025-01-15 17:00:15', 'admin', '2025-01-15 17:00:15', '是否开启裂变活动功能（true开启，false关闭）');

  - changeSet:
      id: chp-1743383740
      author: chp
      changes:
        - addColumn:
            tableName: sys_user
            columns:
              - column:
                  name: workbench_role_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValue: 0
                  remarks: 工作台角色类型(0-无,1-中文部,2-英文部,3-财务部,4-剪辑部)
                  afterColumn: user_type
  - changeSet:
      id: chp-1744948125
      author: chp
      changes:
        - sql:
            sql: |
              UPDATE `sys_menu` SET `menu_name` = '钱包充值审批' WHERE `menu_id` = 92033;
              UPDATE `sys_menu` SET `menu_name` = '导出钱包充值' WHERE `menu_id` = 92034;
              UPDATE `sys_menu` SET `menu_name` = '钱包充值详情' WHERE `menu_id` = 92035;
  - changeSet:
      id: chp-1745208043
      author: chp
      changes:
        - sql:
            sql: |
              UPDATE `sys_menu` SET `menu_name` = '增加钱包余额' WHERE `menu_id` = 92036;
  - changeSet:
      id: chp-1747389542
      author: chp
      changes:
        - sql:
            sql: |
              INSERT INTO `sys_config`(`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (12, '裂变渠道会员折扣', 'sys.channel.fission.member.discount.v1', '{"channelType":7,"endTime":"2025-12-31 23:59:59","memberDiscount":200,"memberDiscountType":1,"settleDiscount":200,"settleDiscountType":1,"startTime":"2025-01-21 18:00:00"}', 'Y', 'admin', '2025-05-16 18:00:00', 'admin', '2025-05-16 18:00:00', '裂变渠道会员折扣 内含json');
  - changeSet:
      id: chp-1748591884
      author: chp
      changes:
        - sql:
            sql: |
              INSERT INTO `sys_config`(`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (13, '分销渠道会员折扣', 'sys.channel.member.discount.v1', '{"channelType":2,"endTime":"3000-01-01 00:00:00","memberDiscount":88,"memberDiscountType":2,"startTime":"2024-01-01 00:00:00"}', 'Y', 'admin', '2025-05-16 18:00:00', 'admin', '2025-05-16 18:00:00', '分销渠道会员折扣 内含json');


