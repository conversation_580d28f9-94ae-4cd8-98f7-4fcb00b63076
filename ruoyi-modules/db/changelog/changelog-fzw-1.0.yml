databaseChangeLog:
  - logicalFilePath: 'changelog-fzw-1.0.yml'
  - changeSet:
      id: 1
      author: fzw
      changes:
        - sql:
            sql: show databases
            comment: initial change
  - changeSet:
      id: 1728458154
      author: fzw
      changes:
        - addDefaultValue:
            tableName: sys_user
            columnName: avatar
            columnDataType: varchar(100)
            defaultValue: 'static/profile.webp'
      comment: 默认头像设置为蜗牛logo
  - changeSet:
      id: **********
      author: fzw
      changes:
        - sql:
            sql: |
              INSERT INTO sys_config (config_name,config_key,config_value,config_type,create_by,create_time,update_by,update_time,remark) VALUES ('添加好友方式','sys.account.addUserWay','1','Y','admin','2025-03-17 00:00:0','admin','2025-03-17 00:00:0','添加好友方式（1-扫码添加，2-获客链接(一元版本)）')
  - changeSet:
      id: **********
      author: fzw
      changes:
        - sql:
            sql: |
              ALTER TABLE sys_oper_log MODIFY COLUMN `method` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' NULL COMMENT '方法名称';
      comment: 扩展日志表的字段长度