package com.ruoyi.file.service.impl;

import cn.hutool.core.lang.Assert;
import com.ruoyi.common.core.utils.file.MimeTypeUtils;
import com.ruoyi.file.domain.vo.DirectUploadInfo;
import com.ruoyi.file.utils.FileUploadUtils;
import com.ruoyi.system.api.domain.entity.biz.common.BizResource;
import com.ruoyi.system.api.domain.vo.FileUploadLinkVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * 文件上传抽象类
 *
 * <AUTHOR>
 * @date 2024/8/8
 */
@Slf4j
public abstract class AbstractFileService {

    /**
     * 上传文件
     *
     * @param file       文件
     * @param bucketName 桶名称
     * @return 文件访问路径
     */
    public BizResource uploadFile(MultipartFile file, String bucketName) {
        final String extension = FileUploadUtils.getExtension(file);
        Assert.isTrue(FileUploadUtils.isAllowedExtension(extension, MimeTypeUtils.PIC_EXTENSION),
                "文件格式不支持");
        return upload(bucketName, file.getOriginalFilename(), file);
    }


    public abstract BizResource upload(String bucketName, String fileName, MultipartFile file);

    public abstract BizResource upload(String bucketName, String fileName, InputStream in, Long contentSize);
    public abstract String upload(String bucketName, String fileName, Long contentSize, InputStream in);

    public abstract DirectUploadInfo directUpload(String bucket, String fileName);

    public abstract String getfileUrl(String objectKey);

    public abstract String uploadUrlFile(FileUploadLinkVo fileUploadLinkVo);
}
