package com.ruoyi.file.config;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.region.Region;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * 腾讯云cos配置
 *
 * <AUTHOR>
 * @date 2024/8/8
 */
@Component
@RequiredArgsConstructor
public class CosConfig {

    private final S3Config s3Config;

    @Bean
    public COSClient cosClient() {
        COSCredentials cosCredentials = new BasicCOSCredentials(s3Config.getAccessKey(), s3Config.getSecretKey());
        ClientConfig clientConfig = new ClientConfig();
        clientConfig.setRegion(new Region(s3Config.getRegion()));
        return new COSClient(cosCredentials, clientConfig);
    }
}
