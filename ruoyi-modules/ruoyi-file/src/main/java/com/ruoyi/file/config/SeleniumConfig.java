package com.ruoyi.file.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Selenium配置文件
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "selenium")
@Data
public class SeleniumConfig {
    /**
     * 远程服务的selenium地址
     */
    @Value(value = "${selenium.url:''}")
    private String url;
    /**
     * 代理url
     */
    @Value(value = "${selenium.proxy.url:''}")
    private String proxyUrl;
    /**
     * 代理启用状态
     */
    @Value(value = "${selenium.proxy.enable:false}")
    private String proxyEnable;

    /**
     * ocr地址
     */
    @Value(value = "${selenium.ocrUrl:''}")
    private String ocrUrl;

    /**
     * host地址
     */
    @Value(value = "${selenium.proxy.info.host:''}")
    private String proxyHost;

    /**
     * port地址
     */
    @Value(value = "${selenium.proxy.info.port}")
    private Integer proxyPort;
}
