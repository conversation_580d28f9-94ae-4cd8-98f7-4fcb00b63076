package com.ruoyi.file.utils;

import com.ruoyi.file.domain.vo.HttpImageVo;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

@Slf4j
public class HttpImgUtils {
    /**
     * 获取网络图片转成字节流
     *
     * @param strUrl 完整图片地址
     * @return 图片字节流
     */
    public static HttpImageVo getNetImgByUrl(String strUrl) {
        if (!isURL(strUrl)) {
            return null;
        }
        try {
            URL url = new URL(strUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(4 * 1000);
            // 通过输入流获取图片数据
            InputStream inStream = conn.getInputStream();
            final Long contentLength = (long) conn.getContentLength();
            return HttpImageVo.builder()
                    .inputStream(inStream)
                    .contentLength(contentLength)
                    .build();
        } catch (Exception e) {
            log.error("获取图片字节流失败", e);
        }
        return null;
    }

    public static boolean isURL(String str) {
        str = str.toLowerCase();
        String regex = "^((https|http|ftp|rtsp|mms)?://)"
                + "?(([0-9a-z_!~*'().&=+$%-]+: )?[0-9a-z_!~*'().&=+$%-]+@)?"
                + "(([0-9]{1,3}\\.){3}[0-9]{1,3}"
                + "|"
                + "([0-9a-z_!~*'()-]+\\.)*"
                + "([0-9a-z][0-9a-z-]{0,61})?[0-9a-z]\\."
                + "[a-z]{2,6})"
                + "(:[0-9]{1,5})?"
                + "((/?)|"
                + "(/[0-9a-z_!~*'().;?:@&=+$,%#-]+)+/?)$";
        return str.matches(regex);
    }
}
