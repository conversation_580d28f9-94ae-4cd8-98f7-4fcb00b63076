package com.ruoyi.file.utils;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.ruoyi.file.config.SeleniumConfig;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.net.MalformedURLException;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * amazon图片下载工具
 *
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class AmazonImageDownloader {
    private SeleniumConfig seleniumConfig;

    public static String downloader(String url, SeleniumConfig config) {
        seleniumConfig = config;
        ChromeOptions options = new ChromeOptions();

        // 设置浏览器选项
        options.addArguments("window-size=1920x1080");
        options.addArguments("--disable-gpu");
        options.addArguments("--hide-scrollbars");
        options.addArguments("--disable-javascript");
        options.addArguments("blink-settings=imagesEnabled=false");
        options.addArguments("--headless");
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        if (seleniumConfig.getProxyEnable().equals(Boolean.TRUE.toString())) {
            options.addArguments("--proxy-server=" + seleniumConfig.getProxyUrl());
        }
        // 创建 WebDriver
        log.debug("创建 WebDriver");
        WebDriver driver = null;
        try {
            driver = new RemoteWebDriver(new URL(seleniumConfig.getUrl()), options);
        } catch (MalformedURLException e) {
            log.warn("WebDriver 创建失败", e);
        }
        try {
            return loadData(driver, url);
        } catch (Exception e) {
            log.error("抓取图片失败{}，url={}", e.getMessage(), url);
        } finally {
            // 关闭 WebDriver
            if (driver != null) {
                driver.close();
                driver.quit();
            }
        }
        return "";

    }

    private static String loadData(WebDriver driver, String url) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        String time = now.format(formatter);
        // 加载页面
        driver.get(url);
        log.debug("页面加载完成：" + time);
        // 查找元素
        WebElement element = null;
        int deep = 1;
        while (true) {
            try {
                element = driver.findElement(By.cssSelector("#landingImage"));
                break;
            } catch (NoSuchElementException e) {
                if (deep > 2) {
                    break;
                }
                log.debug("未找到元素，出现验证码");
                verify(driver);
                deep++;
            }
        }
        if (element == null) {
            return "";
        }
        // 获取图像 URL
        String imageUrl = element.getAttribute("src")
                .replace("SX300_SY300_QL70_FMwebp", "SL_1500");
        log.debug("图像 URL 获取成功：" + time);
        // 打印图像 URL
        log.info(imageUrl);
        // 打印元素文本
        log.info(element.getText());

        return imageUrl;
    }

    private static void verify(WebDriver webDriver) {
        final String pageSource = webDriver.getPageSource();
        final int start = pageSource.indexOf("https://images-na.ssl-images-amazon.com/captcha/");
        final int end = pageSource.indexOf("\">", start);
        final String url = pageSource.substring(start, end);
        log.info("验证码地址：{}", url);
        final String body = HttpRequest.get(seleniumConfig.getOcrUrl() + "/image2text/url?image_url=" + url).execute().body();
        final String code = JSON.parseObject(body).getString("message");
        log.info("验证码：{}", code);
        webDriver.findElement(By.id("captchacharacters")).sendKeys(code);
        webDriver.findElement(By.id("captchacharacters")).submit();
        log.info("尝试填写验证码成功");
    }
}
