package com.ruoyi.file.config;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.constant.Constants;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

import javax.annotation.Resource;
import java.net.URI;

/**
 * S3文件上传配置文件
 *
 * @author: dwy
 */
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "s3")
@Data
public class S3Config {
    /**
     * 上传类型 minio tencentCos
     */
    @Value(value = "${s3.uploadType}")
    private String uploadType;
    /**
     * 账号 minio的账号 tencentCos的SecretId
     */
    @Value(value = "${s3.accessKey}")
    private String accessKey;
    /**
     * 密码 minio的密码 tencentCos的SecretKey
     */
    @Value(value = "${s3.secretKey}")
    private String secretKey;
    /**
     * url
     */
    @Value(value = "${s3.endpoint}")
    private String endpoint;
    /**
     * 回显前端地址
     */
    @Value(value = "${s3.echoUrl}")
    private String echoUrl;
    /**
     * 桶名
     */
    @Value(value = "${s3.bucketName}")
    private String bucketName;
    /**
     * 文件前缀 例 存储入桶 前缀test 存入桶后 test/fileName.txt
     */
    @Value(value = "${s3.prefix}")
    private String prefix = "";
    /**
     * 区域 tencentCos的区域
     */
    @Value(value = "${s3.region}")
    private String region = "";
    /**
     * tencentCos   临时密钥有效时间 单位秒
     */
    @Value(value = "${s3.durationSeconds}")
    private Integer durationSeconds = 0;
    /**
     * tencentCos   预签名url 有效时间 单位秒
     */
    @Value(value = "${s3.signedSeconds}")
    private Integer signedSeconds = 0;

    @Bean
    public S3Client initS3() {
        if (!endpoint.startsWith(Constants.STR_HTTP)) {
            endpoint = Constants.HTTP + endpoint;
        }
        if (!endpoint.endsWith(StrUtil.SLASH)) {
            endpoint = endpoint.concat(StrUtil.SLASH);
        }
        AwsBasicCredentials awsCreds = AwsBasicCredentials.create(accessKey, secretKey);
        URI uri = URI.create(endpoint);

        return S3Client.builder()
                .region(Region.CN_NORTH_1)
                .endpointOverride(uri)
                .credentialsProvider(() -> awsCreds)
                .build();
    }
}
