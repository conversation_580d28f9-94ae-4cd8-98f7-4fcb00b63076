package com.ruoyi.file.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.file.config.S3Config;
import com.ruoyi.file.domain.vo.DirectUploadInfo;
import com.ruoyi.file.domain.vo.HttpImageVo;
import com.ruoyi.file.service.TencentStsService;
import com.ruoyi.system.api.RemoteResourceService;
import com.ruoyi.system.api.domain.entity.biz.common.BizResource;
import com.ruoyi.system.api.domain.vo.FileUploadLinkVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/8
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class S3FileService extends AbstractFileService {

    private final RemoteResourceService remoteResourceService;

    private final S3Config s3Config;

    private final S3Client s3Client;

    private final TencentStsService tencentStsService;

    private OkHttpClient proxyOkhttpClient;

    /**
     * host地址
     */
    @Value(value = "${selenium.proxy.info.host:''}")
    private String proxyHost;

    /**
     * port地址
     */
    @Value(value = "${selenium.proxy.info.port}")
    private Integer proxyPort;

    @PostConstruct
    public void setProxyOkhttpClient() {
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort));
        proxyOkhttpClient = new OkHttpClient.Builder()
                .proxy(proxy)
                .build();
    }


    @Override
    public BizResource upload(String bucketName, String fileName, MultipartFile file) {
        try {
            final InputStream inputStream = file.getInputStream();
            return upload(bucketName, fileName, inputStream, file.getSize());
        } catch (IOException e) {
            log.info("上传文件失败{}", e.getMessage());
            throw new ServiceException("上传文件失败");
        }
    }

    @Override
    public BizResource upload(String bucketName, String fileName, InputStream in, Long contentSize) {
        String objectKey = reFormatObjectKey(bucketName, fileName);
        PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                .bucket(s3Config.getPrefix())
                .key(objectKey)
                .contentType(MediaType.APPLICATION_OCTET_STREAM_VALUE)
                .contentLength(contentSize)
                .build();
        s3Client.putObject(putObjectRequest, RequestBody.fromInputStream(in, contentSize));
        String path = String.format("%s%s/%s", s3Config.getEchoUrl(), s3Config.getPrefix(), objectKey);
        String objectName = String.format("%s/%s", s3Config.getPrefix(), objectKey);
        log.info("上传成功,流上传,对象路径为{}", path);
        return saveRemoteResource(fileName, objectName);
    }

    @Override
    public String upload(String bucketName, String fileName, Long contentSize, InputStream in) {
        String objectKey = reFormatObjectKey(bucketName, fileName);
        PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                .bucket(s3Config.getPrefix())
                .key(objectKey)
                .contentType(MediaType.APPLICATION_OCTET_STREAM_VALUE)
                .contentLength(contentSize)
                .build();
        s3Client.putObject(putObjectRequest, RequestBody.fromInputStream(in, contentSize));
        String path = String.format("%s%s/%s", s3Config.getEchoUrl(), s3Config.getPrefix(), objectKey);
        log.info("上传成功,流上传,对象路径为{}", path);
        return String.format("%s/%s", s3Config.getPrefix(), objectKey);
    }

    @Override
    public DirectUploadInfo directUpload(String bucket, String fileName) {
        final String newFileName = reFormatFileName(fileName);
        final String objectKey = String.format("%s/%s", s3Config.getPrefix(), reFormatObjectKey(bucket, newFileName));
        return DirectUploadInfo.builder()
                .displayUrl(s3Config.getEchoUrl() + objectKey)
                .objectKey(objectKey)
                .uploadUrl(tencentStsService.getUploadTempUrl(objectKey))
                .build();
    }

    @Override
    public String getfileUrl(String objectKey) {
        return tencentStsService.getDownloadTempUrl(objectKey);
    }

    /**
     * 重新格式化文件名
     * <p>
     * xxx.jpg  = > 73fb364b0610444d9f68a1f11c5364f1.jpg
     * </p>
     *
     * @param fileName 文件名
     * @return 新文件名
     */
    private static String reFormatFileName(String fileName) {
        return IdUtil.fastSimpleUUID() + "." + FileNameUtil.getSuffix(fileName);
    }

    /**
     * 重新格式化对象路径
     * <p>
     * bucket=1, fileName=xxx.jpg  = > 1/73fb364b0610444d9f68a1f11c5364f1.jpg
     * </p>
     * <p>
     * bucket=null, fileName=xxx.jpg  = > 73fb364b0610444d9f68a1f11c5364f1.jpg
     * </p>
     * <p>
     * bucket= , fileName=xxx.jpg  = > 73fb364b0610444d9f68a1f11c5364f1.jpg
     * </p>
     *
     * @param bucket   桶名称
     * @param fileName 文件名
     * @return 对象路径
     */
    private static String reFormatObjectKey(String bucket, String fileName) {
        return StringUtils.isNotBlank(bucket) ?
                String.format("%s/%s", bucket, reFormatFileName(fileName)) :
                String.format("%s", reFormatFileName(fileName));
    }

    /**
     * 保存远程资源
     *
     * @param objectName 对象名称
     * @param uri        资源地址
     * @return 资源
     */
    public BizResource saveRemoteResource(String objectName, String uri) {
        BizResource bizResource = BizResource.builder().objectKey(uri).build();
        List<BizResource> r = remoteResourceService.saveBatchBizResource(Collections.singletonList(bizResource), SecurityConstants.INNER);
        Assert.notNull(r, "新增图片视频资源失败");
        bizResource.setId(r.get(0).getId());
        return bizResource;
    }

    @Override
    public String uploadUrlFile(FileUploadLinkVo fileUploadLinkVo) {
        if (!fileUploadLinkVo.getUseProxy()){
            throw new ServiceException("暂未实现");
        }
        // 创建请求
        Request request = new Request.Builder()
                .url(fileUploadLinkVo.getFileUrl())
                .build();

        try (Response response = proxyOkhttpClient.newCall(request).execute()) {
            // 检查响应是否成功
            if (!response.isSuccessful()) {
                throw new IOException("Failed to download image: " + response.code() + " " + response.message());
            }

            // 获取响应体的内容长度
            long contentLength = response.body().contentLength();

            // 获取响应体的输入流
            InputStream inputStream = response.body().byteStream();

            // 返回 HttpImageVo 对象
            HttpImageVo netImgByUrl = HttpImageVo.builder()
                    .inputStream(inputStream)
                    .contentLength(contentLength)
                    .build();
            String suffix = FileUtil.getSuffix(fileUploadLinkVo.getFileUrl()).split("\\?")[0];
            return upload(fileUploadLinkVo.getBucketName(), String.format("%s.%s", IdUtil.fastSimpleUUID(), suffix), netImgByUrl.getContentLength(), netImgByUrl.getInputStream());
        } catch (Exception e) {
            log.warn("下载文件失败,文件url={}", fileUploadLinkVo.getFileUrl());
            return "";
        }
    }
}
