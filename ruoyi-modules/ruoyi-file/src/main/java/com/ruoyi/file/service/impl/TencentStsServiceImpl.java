package com.ruoyi.file.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.http.HttpMethodName;
import com.ruoyi.file.config.S3Config;
import com.ruoyi.file.service.TencentStsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.Date;

/**
 * Tencent oss特殊服务
 * 与本地计算不同，sts得请求服务器后下发
 *
 * <AUTHOR>
 * @date 2024/8/8
 */
@Service
@RequiredArgsConstructor
public class TencentStsServiceImpl implements TencentStsService {

    private final COSClient client;

    private final S3Config s3Config;

    @Override
    public String getUploadTempUrl(String objectKey) {
        final URL url = client.generatePresignedUrl(s3Config.getBucketName(), objectKey, DateUtil.offset(new Date(), DateField.SECOND, s3Config.getSignedSeconds()),
                HttpMethodName.PUT);
        return url.toString();
    }

    @Override
    public String getDownloadTempUrl(String objectKey) {
        return client.generatePresignedUrl(s3Config.getBucketName(), objectKey, DateUtil.offset(new Date(), DateField.SECOND, s3Config.getSignedSeconds()),
                HttpMethodName.GET).toString();
    }
}
