package com.ruoyi.system.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.file.domain.vo.DirectUploadInfo;
import com.ruoyi.file.service.impl.AbstractFileService;
import com.ruoyi.system.api.domain.entity.biz.common.BizResource;
import com.ruoyi.system.api.domain.vo.FileUploadLinkVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


/**
 * 文件请求处理
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Api(value = "文件请求处理", tags = "文件请求处理")
public class SysFileController {
    private final AbstractFileService fileService;

    /**
     * 文件上传(指定桶)
     */
    @PostMapping(value = "/upload")
    @ApiOperation("文件上传")
    public R<BizResource> upload(MultipartFile file, @RequestParam(value = "bucketName", required = false) String bucketName) {
        BizResource bizResource = fileService.uploadFile(file, bucketName);
        return R.ok(bizResource);
    }

    /**
     * 根据链接上传图片
     */
    @PostMapping(value = "/upload/link/proxy")
    @InnerAuth
    @ApiOperation("文件上传-根据链接")
    public R<String> upload(@RequestBody FileUploadLinkVo fileUploadLinkVo) {
        return R.ok(fileService.uploadUrlFile(fileUploadLinkVo));
    }



    /**
     * 获取签名过的直传链接
     */
    @GetMapping(value = "/sign")
    @ApiOperation("获取签名过的直传链接")
    public R<DirectUploadInfo> sign(@RequestParam(required = false, defaultValue = "") String bucket, String fileName) {
        return R.ok(fileService.directUpload(bucket, fileName));
    }

    /**
     * 获取文件下载链接
     * <p>注: 重要文件需内部调用且记录下载日志</p>
     *
     * @param objectKey 文件key
     */
    @GetMapping(value = "/download")
    @ApiOperation("获取文件下载链接")
    @Log(title = "文件下载", businessType = BusinessType.OTHER)
    public R<String> download(@RequestParam String objectKey) {
        return R.ok(fileService.getfileUrl(objectKey));
    }

}
