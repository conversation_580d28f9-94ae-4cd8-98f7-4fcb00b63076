package com.ruoyi.system.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.constant.ConfigConstants;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.EditFissionChannelDiscountDTO;
import com.ruoyi.system.api.domain.dto.system.EditMemberDiscountDTO;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelBrokeRageVO;
import com.ruoyi.system.domain.SysConfig;
import com.ruoyi.system.service.ISysConfigService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 参数配置 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/config")
@RequiredArgsConstructor
public class SysConfigController extends BaseController {
    private final ISysConfigService configService;

    /**
     * 获取参数配置列表
     */
    @RequiresPermissions("system:config:list")
    @GetMapping("/list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public TableDataInfo list(SysConfig config) {
        startPage();
        List<SysConfig> list = configService.selectConfigList(config);
        return getDataTable(list);
    }

    @Log(title = "参数管理", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:config:export")
    @PostMapping("/export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public void export(HttpServletResponse response, SysConfig config) {
        List<SysConfig> list = configService.selectConfigList(config);
        ExcelUtil<SysConfig> util = new ExcelUtil<>(SysConfig.class);
        util.exportExcel(response, list, "参数数据");
    }

    /**
     * 根据参数编号获取详细信息
     */
    @GetMapping(value = "/{configId}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public AjaxResult getInfo(@PathVariable Long configId) {
        return AjaxResult.success(configService.selectConfigById(configId));
    }

    /**
     * 根据参数键名查询参数值
     */
    @GetMapping(value = "/configKey/{configKey}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public AjaxResult getConfigKey(@PathVariable String configKey) {
        return AjaxResult.success(configService.selectConfigByKey(configKey));
    }
    /**
     * 根据参数键名查询参数值
     */
    @GetMapping(value = "/inner/configKey/{configKey}")
    @InnerAuth
    public AjaxResult innerGetConfigKey(@PathVariable String configKey) {
        return AjaxResult.success("获取成功", configService.selectConfigByKey(configKey));
    }

    /**
     * 新增参数配置
     */
    @RequiresPermissions("system:config:add")
    @Log(title = "参数管理", businessType = BusinessType.INSERT)
    @PostMapping
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public AjaxResult add(@Validated @RequestBody SysConfig config) {
        if (UserConstants.NOT_UNIQUE.equals(configService.checkConfigKeyUnique(config))) {
            return AjaxResult.error("新增参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        config.setCreateBy(SecurityUtils.getUsername());
        return toAjax(configService.insertConfig(config));
    }

    /**
     * 修改参数配置
     */
    @RequiresPermissions("system:config:edit")
    @Log(title = "参数管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public AjaxResult edit(@Validated @RequestBody SysConfig config) {
        if (UserConstants.NOT_UNIQUE.equals(configService.checkConfigKeyUnique(config))) {
            return AjaxResult.error("修改参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        config.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(configService.updateConfig(config));
    }

    /**
     * 删除参数配置
     */
    @RequiresPermissions("system:config:remove")
    @Log(title = "参数管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{configIds}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public AjaxResult remove(@PathVariable Long[] configIds) {
        configService.deleteConfigByIds(configIds);
        return success();
    }

    /**
     * 刷新参数缓存
     */
    @RequiresPermissions("system:config:remove")
    @Log(title = "参数管理", businessType = BusinessType.CLEAN)
    @DeleteMapping("/refreshCache")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public AjaxResult refreshCache() {
        configService.resetConfigCache();
        return AjaxResult.success();
    }


    @PutMapping("/inner/editMemberDiscount")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
//    @RequiresPermissions("system:config:editMemberDiscount")
    @ApiOperation(value = "修改会员折扣", response = PageInfo.class)
    @InnerAuth
    public R<String> editMemberDiscount(@Validated @RequestBody EditMemberDiscountDTO dto) {
        SysConfig config = configService.selectConfigEntityByKey(ConfigConstants.SYS_CHANNEL_MEMBER_DISCOUNT_V1);
        Assert.notNull(config, "折扣数据不存在，请联系管理员添加~");
        config.setUpdateBy(SecurityUtils.getUsername());
        ChannelBrokeRageVO channelBrokeRageVO = BeanUtil.copyProperties(dto, ChannelBrokeRageVO.class);
        channelBrokeRageVO.setStartTime("2024-01-01 00:00:00");
        channelBrokeRageVO.setEndTime("3000-01-01 00:00:00");
        config.setConfigValue(JSON.toJSONString(channelBrokeRageVO));
        configService.updateConfig(config);
        return R.ok("修改成功");
    }
//    @PutMapping("/inner/editFissionMemberDiscount")
//    @InnerAuth
//    @ApiOperation(value = "修改裂变会员折扣")
//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    public AjaxResult editFissionMemberDiscount(@Validated @RequestBody EditMemberDiscountDTO dto) {
//        SysConfig config = configService.selectConfigEntityByKey(ConfigConstants.SYS_CHANNEL_FISSION_MEMBER_DISCOUNT);
//        Assert.notNull(config, "折扣数据不存在，请联系管理员添加~");
//        config.setUpdateBy(SecurityUtils.getUsername());
//        config.setConfigValue(Convert.toStr(dto.getDiscount()));
//        return toAjax(configService.updateConfig(config));
//    }
    @PutMapping("/inner/editFissionMemberDiscount/V1")
    @InnerAuth
    @ApiOperation(value = "修改裂变会员折扣")
    public R<String> editFissionMemberDiscountV1(@RequestBody EditFissionChannelDiscountDTO dto) {
        SysConfig config = configService.selectConfigEntityByKey(ConfigConstants.SYS_CHANNEL_FISSION_MEMBER_DISCOUNT_V1);
        Assert.notNull(config, "折扣数据不存在，请联系管理员添加~");
        config.setUpdateBy(SecurityUtils.getUsername());
        ChannelBrokeRageVO channelBrokeRageVO = BeanUtil.copyProperties(dto, ChannelBrokeRageVO.class);
        channelBrokeRageVO.setStartTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, dto.getStartTime()));
        channelBrokeRageVO.setEndTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, dto.getEndTime()));
        config.setConfigValue(JSON.toJSONString(channelBrokeRageVO));
        configService.updateConfig(config);
        return R.ok("修改成功");
    }

    @GetMapping("/getFissionMemberDiscount")
//    @LoginUserType(userTypes = {UserTypeConstants.USER_TYPE,UserTypeConstants.MANAGER_TYPE, UserTypeConstants.CHANNEL_TYPE})
    @ApiOperation(value = "获取裂变会员折扣")
    public R<ChannelBrokeRageVO> getFissionMemberDiscount() {
        String result = configService.selectConfigByKey(ConfigConstants.SYS_CHANNEL_FISSION_MEMBER_DISCOUNT_V1);
        return R.ok(JSON.parseObject(result, ChannelBrokeRageVO.class));
    }

    @GetMapping("/getMemberDiscount")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("system:config:editMemberDiscount")
    @ApiOperation(value = "获取会员折扣", response = PageInfo.class)
    public R<ChannelBrokeRageVO> getMemberDiscount() {
        String result = configService.selectConfigByKey(ConfigConstants.SYS_CHANNEL_MEMBER_DISCOUNT_V1);
        return R.ok(JSON.parseObject(result, ChannelBrokeRageVO.class));
    }


    @GetMapping("/fissionActivity")
    @ApiOperation(value = "获取商家裂变活动是否展示")
    public R<Boolean> fissionActivity() {
        return R.ok(Convert.toBool(configService.selectConfigByKey(ConfigConstants.SYS_CHANNEL_FISSION_SHOW)));
    }
    @GetMapping("/phone/{phone}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("system:config:phone")
    @ApiOperation(value = "添加手机号验证码")
    @Log(title = "手动添加验证码", businessType = BusinessType.INSERT)
    public R<String> addPhone(@PathVariable String phone){
        return R.ok(configService.newPhoneVerifyNumber(phone));
    }


    @GetMapping("/exchange")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("system:config:exchangeRate")
    @ApiOperation(value = "手动更新数据库汇率")
    @Log(title = "手动更新数据库汇率", businessType = BusinessType.INSERT)
    public R<String> exchangeRate(@RequestParam String exchangeRate){
        return R.ok(configService.newExchangeRate(exchangeRate));
    }

}
