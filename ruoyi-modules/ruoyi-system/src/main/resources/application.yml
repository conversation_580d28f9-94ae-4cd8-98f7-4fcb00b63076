server:
  port: 9201
spring:
  application:
    name: ruoyi-system
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${wnkx.ds.ip}:${wnkx.ds.port}/${wnkx.ds.user-db:user-center}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: ${wnkx.ds.username}
    password: ${wnkx.ds.password}
# mybatis配置
mybatis:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.system
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath:mapper/**/*.xml

# swagger配置
swagger:
  title: 系统模块接口文档
  license: Powered By ruoyi
  licenseUrl: https://ruoyi.vip
