<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ruoyi</groupId>
        <artifactId>ruoyi</artifactId>
        <version>3.5.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>ruoyi-system</module>
        <module>ruoyi-gen</module>
        <module>ruoyi-file</module>
    </modules>
    <properties>
        <db.name>user-center</db.name>
        <db.ip>**********</db.ip>
        <db.port>4000</db.port>
    </properties>

    <artifactId>ruoyi-modules</artifactId>
    <packaging>pom</packaging>

    <description>
        ruoyi-modules业务模块
    </description>

    <profiles>
        <profile>
            <id>liquibase</id>
            <build>
                <resources>
                    <resource>
                        <directory>db</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
                <plugins>
                    <plugin>
                        <groupId>org.liquibase</groupId>
                        <artifactId>liquibase-maven-plugin</artifactId>
                        <version>4.17.0</version>
                        <inherited>false</inherited>
                        <configuration>
                            <propertyFile>liquibase.properties</propertyFile>
                            <promptOnNonLocalDatabase>false</promptOnNonLocalDatabase>
                        </configuration>
                        <dependencies>
                            <dependency>
                                <groupId>mysql</groupId>
                                <artifactId>mysql-connector-java</artifactId>
                                <version>8.0.29</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>


</project>
