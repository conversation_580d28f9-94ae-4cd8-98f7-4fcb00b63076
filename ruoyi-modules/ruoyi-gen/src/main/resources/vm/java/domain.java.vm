package ${packageName}.domain;

#foreach ($import in $importList)
import ${import};
#end
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import javax.validation.constraints.NotNull;
import io.swagger.annotations.ApiModel;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
#if($table.crud || $table.sub)
import com.ruoyi.common.core.web.domain.BaseEntity;
#elseif($table.tree)
import com.ruoyi.common.core.web.domain.TreeEntity;

#end

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
#if($table.crud || $table.sub)
#set($Entity="BaseEntity")
#elseif($table.tree)
#set($Entity="TreeEntity")
#end
@ApiModel(value = "${functionName}对象 ${tableName}")
@TableName("${tableName}")
@Data
public class ${ClassName} extends ${Entity}
{
    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
#if($column.list)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#elseif($column.columnComment.indexOf("(") != -1)
#set($parentheseIndex=$column.columnComment.indexOf("("))
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#set($rightParentheseIndex=$column.columnComment.indexOf("）"))
#if($rightParentheseIndex != -1)
#set($parentheseIndex=$parentheseIndex+1)
#set($notes=$column.columnComment.substring($parentheseIndex, $rightParentheseIndex))
#elseif($column.columnComment.indexOf(")") != -1)
#set($rightParentheseIndex=$column.columnComment.indexOf(")"))
#set($parentheseIndex=$parentheseIndex+1)
#set($notes=$column.columnComment.substring($parentheseIndex, $rightParentheseIndex))
#else
#set($notes=$column.columnComment)
#end
#end
    #if($column.javaField == 'id')
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    #elseif($parentheseIndex != -1)
        #if($column.isRequired == '1')
    @ApiModelProperty(value = "${column.columnComment}",notes = "$notes",required = true)
    @NotNull(message = "[${comment}]不能为空")
            #else
    @ApiModelProperty(value = "${column.columnComment}",notes = "$notes")
        #end
    @Excel(name = "${comment}", readConverterExp = "$notes")
    #elseif($column.javaType == 'Date')
        #if($column.isRequired == '1')
    @NotNull(message = "[${comment}]不能为空")
    @ApiModelProperty(value = "${column.columnComment}",required = true)
        #else
    @ApiModelProperty(value = "${column.columnComment}")
        #end
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    #else
        #if($column.isRequired == '1')
    @NotNull(message = "[${comment}]不能为空")
    @ApiModelProperty(value = "${column.columnComment}",required = true)
        #else
    @ApiModelProperty(value = "${column.columnComment}")
        #end
    @Excel(name = "${comment}")
    #end
    private $column.javaType $column.javaField;

#end
#end
}
