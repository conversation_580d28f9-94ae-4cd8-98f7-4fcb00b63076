package ${packageName}.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageInfo;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import org.springframework.validation.annotation.Validated;
#if($table.crud || $table.sub)
#elseif($table.tree)
#end

/**
 * ${functionName}Controller
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@RestController
@RequestMapping("/${businessName}")
@Api(value = "${functionName}服务",tags = "${functionName}服务")
@RequiredArgsConstructor
public class ${ClassName}Controller extends BaseController
{
    private final I${ClassName}Service ${className}Service;

    /**
     * 查询${functionName}列表
     */
    @RequiresPermissions("${permissionPrefix}:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询${functionName}列表",response = PageInfo.class)
#if($table.crud || $table.sub)
    public R<PageInfo<${ClassName}>> list(${ClassName} ${className})
    {
        startPage();
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return R.ok(toPage(list));
    }
#elseif($table.tree)
    public R<List<${ClassName}>> list(${ClassName} ${className})
    {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return R.ok(list);
    }
#end

    /**
     * 导出${functionName}列表
     */
    @RequiresPermissions("${permissionPrefix}:export")
    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出${functionName}列表")
    public void export(HttpServletResponse response, ${ClassName} ${className})
    {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        ExcelUtil<${ClassName}> util = new ExcelUtil<${ClassName}>(${ClassName}.class);
        util.exportExcel(response, list, "${functionName}数据");
    }

    /**
     * 获取${functionName}详细信息
     */
    @RequiresPermissions("${permissionPrefix}:query")
    @GetMapping(value = "/{${pkColumn.javaField}}")
    @ApiOperation(value = "获取${functionName}详细信息",response = ${ClassName}.class)
    public R<${ClassName}> getInfo(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField})
    {
        return R.ok(${className}Service.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField}));
    }

    /**
     * 新增${functionName}
     */
    @RequiresPermissions("${permissionPrefix}:add")
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增${functionName}")
    public R add(@RequestBody @Validated ${ClassName} ${className})
    {
        return R.toAjax(${className}Service.insert${ClassName}(${className}));
    }

    /**
     * 修改${functionName}
     */
    @RequiresPermissions("${permissionPrefix}:edit")
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改${functionName}")
    public R edit(@RequestBody @Validated ${ClassName} ${className})
    {
        return R.toAjax(${className}Service.update${ClassName}(${className}));
    }

    /**
     * 删除${functionName}
     */
    @RequiresPermissions("${permissionPrefix}:remove")
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
	@DeleteMapping("/{${pkColumn.javaField}s}")
    @ApiOperation(value = "删除${functionName}")
    public R remove(@PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s)
    {
        return R.toAjax(${className}Service.delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaField}s));
    }
}
