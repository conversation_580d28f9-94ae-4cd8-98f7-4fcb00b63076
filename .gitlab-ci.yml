stages:
  - build
  - test
  - db-deploy
  - publish-config
  - pre-script-execute
  - deploy
  - alarm
variables:
  TZ: "Asia/Shanghai"
  BUILD_PREFIX_INFO: "$CI_COMMIT_BRANCH:$CI_COMMIT_SHORT_SHA 构建开始\n构建创建者：$GITLAB_USER_NAME\n分支提交者：$CI_COMMIT_AUTHOR\n $CI_COMMIT_MESSAGE"
  IMAGE_TAG: $NEXUS_HOST/wnkx
image-build-dev:
  stage: build  
  only:
    - main
  tags:
    - java
  script:
    - echo "$BUILD_PREFIX_INFO"
    - mvn -B clean package "$@"
    - docker login -u $NEXUS_USER_NAME -p $NEXUS_USER_PASSWORD http://$NEXUS_HOST
    - docker build -t $IMAGE_TAG/order-service:latest  order-business/order-service
    - docker push $IMAGE_TAG/order-service:latest
    - docker build -t $IMAGE_TAG/order-job:latest  order-business/order-job
    - docker push $IMAGE_TAG/order-job:latest
    - docker build -t $IMAGE_TAG/biz-center:latest wnkx-business/biz-center
    - docker push $IMAGE_TAG/biz-center:latest
    - docker build -t $IMAGE_TAG/biz-job:latest wnkx-business/biz-job
    - docker push $IMAGE_TAG/biz-job:latest
    - docker build -t $IMAGE_TAG/ruoyi-auth:latest ruoyi-auth
    - docker push $IMAGE_TAG/ruoyi-auth:latest
    - docker build -t $IMAGE_TAG/ruoyi-gateway:latest ruoyi-gateway
    - docker push $IMAGE_TAG/ruoyi-gateway:latest
    - docker build -t $IMAGE_TAG/ruoyi-system:latest ruoyi-modules/ruoyi-system
    - docker push $IMAGE_TAG/ruoyi-system:latest
    - docker build -t $IMAGE_TAG/job-admin:latest wnkx-job/xxl-job-admin
    - docker push $IMAGE_TAG/job-admin:latest
    - docker build -t $IMAGE_TAG/wnkx-message:latest wnkx-message
    - docker push $IMAGE_TAG/wnkx-message:latest
image-build-test:
  stage: build
  only:
    - test
  script:
    - echo "$BUILD_PREFIX_INFO"
    - mvn -B clean package "$@"
    - docker login -u $NEXUS_USER_NAME -p $NEXUS_USER_PASSWORD http://$NEXUS_HOST
    - docker build -t $IMAGE_TAG/order-service:latest-test  order-business/order-service
    - docker push $IMAGE_TAG/order-service:latest-test
    - docker build -t $IMAGE_TAG/order-job:latest-test  order-business/order-job
    - docker push $IMAGE_TAG/order-job:latest-test
    - docker build -t $IMAGE_TAG/biz-center:latest-test wnkx-business/biz-center
    - docker push $IMAGE_TAG/biz-center:latest-test
    - docker build -t $IMAGE_TAG/biz-job:latest-test wnkx-business/biz-job
    - docker push $IMAGE_TAG/biz-job:latest-test
    - docker build -t $IMAGE_TAG/ruoyi-auth:latest-test ruoyi-auth
    - docker push $IMAGE_TAG/ruoyi-auth:latest-test
    - docker build -t $IMAGE_TAG/ruoyi-gateway:latest-test ruoyi-gateway
    - docker push $IMAGE_TAG/ruoyi-gateway:latest-test
    - docker build -t $IMAGE_TAG/ruoyi-system:latest-test ruoyi-modules/ruoyi-system
    - docker push $IMAGE_TAG/ruoyi-system:latest-test
    - docker build -t $IMAGE_TAG/job-admin:latest-test wnkx-job/xxl-job-admin
    - docker push $IMAGE_TAG/job-admin:latest-test
    - docker build -t $IMAGE_TAG/wnkx-message:latest-test wnkx-message
    - docker push $IMAGE_TAG/wnkx-message:latest-test
  tags:
    - java
image-build-daily:
  stage: build
  only:
    - daily
  script:
    - echo "$BUILD_PREFIX_INFO"
    - mvn -B clean package "$@"
    - docker login -u $NEXUS_USER_NAME -p $NEXUS_USER_PASSWORD http://$NEXUS_HOST
    - docker build -t $IMAGE_TAG/order-service:latest-daily  order-business/order-service
    - docker push $IMAGE_TAG/order-service:latest-daily
    - docker build -t $IMAGE_TAG/order-job:latest-daily  order-business/order-job
    - docker push $IMAGE_TAG/order-job:latest-daily
    - docker build -t $IMAGE_TAG/biz-center:latest-daily wnkx-business/biz-center
    - docker push $IMAGE_TAG/biz-center:latest-daily
    - docker build -t $IMAGE_TAG/biz-job:latest-daily wnkx-business/biz-job
    - docker push $IMAGE_TAG/biz-job:latest-daily
    - docker build -t $IMAGE_TAG/ruoyi-auth:latest-daily ruoyi-auth
    - docker push $IMAGE_TAG/ruoyi-auth:latest-daily
    - docker build -t $IMAGE_TAG/ruoyi-gateway:latest-daily ruoyi-gateway
    - docker push $IMAGE_TAG/ruoyi-gateway:latest-daily
    - docker build -t $IMAGE_TAG/ruoyi-system:latest-daily ruoyi-modules/ruoyi-system
    - docker push $IMAGE_TAG/ruoyi-system:latest-daily
    - docker build -t $IMAGE_TAG/job-admin:latest-daily wnkx-job/xxl-job-admin
    - docker push $IMAGE_TAG/job-admin:latest-daily
    - docker build -t $IMAGE_TAG/wnkx-message:latest-daily wnkx-message
    - docker push $IMAGE_TAG/wnkx-message:latest-daily
  tags:
    - java
image-build-uat:
  stage: build
  only:
    - uat
  script:
    - echo "$BUILD_PREFIX_INFO"
    - mvn -B clean package "$@"
    - docker login -u $NEXUS_USER_NAME -p $NEXUS_USER_PASSWORD http://$NEXUS_HOST
    - docker build -t $IMAGE_TAG/order-service:latest-uat  order-business/order-service
    - docker push $IMAGE_TAG/order-service:latest-uat
    - docker build -t $IMAGE_TAG/order-job:latest-uat  order-business/order-job
    - docker push $IMAGE_TAG/order-job:latest-uat
    - docker build -t $IMAGE_TAG/biz-center:latest-uat wnkx-business/biz-center
    - docker push $IMAGE_TAG/biz-center:latest-uat
    - docker build -t $IMAGE_TAG/biz-job:latest-uat wnkx-business/biz-job
    - docker push $IMAGE_TAG/biz-job:latest-uat
    - docker build -t $IMAGE_TAG/ruoyi-auth:latest-uat ruoyi-auth
    - docker push $IMAGE_TAG/ruoyi-auth:latest-uat
    - docker build -t $IMAGE_TAG/ruoyi-gateway:latest-uat ruoyi-gateway
    - docker push $IMAGE_TAG/ruoyi-gateway:latest-uat
    - docker build -t $IMAGE_TAG/ruoyi-system:latest-uat ruoyi-modules/ruoyi-system
    - docker push $IMAGE_TAG/ruoyi-system:latest-uat
    - docker build -t $IMAGE_TAG/job-admin:latest-uat wnkx-job/xxl-job-admin
    - docker push $IMAGE_TAG/job-admin:latest-uat
    - docker build -t $IMAGE_TAG/wnkx-message:latest-uat wnkx-message
    - docker push $IMAGE_TAG/wnkx-message:latest-uat
  tags:
    - java
image-build-prod:
  stage: build
  only:
    - prod
  script:
    - echo "$BUILD_PREFIX_INFO"
    - mvn -B clean package "$@"
    - docker login -u $NEXUS_USER_NAME -p $NEXUS_USER_PASSWORD http://$NEXUS_HOST
    - docker build -t $IMAGE_TAG/order-service:latest-prod  order-business/order-service
    - docker push $IMAGE_TAG/order-service:latest-prod
    - docker build -t $IMAGE_TAG/order-job:latest-prod  order-business/order-job
    - docker push $IMAGE_TAG/order-job:latest-prod
    - docker build -t $IMAGE_TAG/biz-center:latest-prod wnkx-business/biz-center
    - docker push $IMAGE_TAG/biz-center:latest-prod
    - docker build -t $IMAGE_TAG/biz-job:latest-prod wnkx-business/biz-job
    - docker push $IMAGE_TAG/biz-job:latest-prod
    - docker build -t $IMAGE_TAG/ruoyi-auth:latest-prod ruoyi-auth
    - docker push $IMAGE_TAG/ruoyi-auth:latest-prod
    - docker build -t $IMAGE_TAG/ruoyi-gateway:latest-prod ruoyi-gateway
    - docker push $IMAGE_TAG/ruoyi-gateway:latest-prod
    - docker build -t $IMAGE_TAG/ruoyi-system:latest-prod ruoyi-modules/ruoyi-system
    - docker push $IMAGE_TAG/ruoyi-system:latest-prod
    - docker build -t $IMAGE_TAG/job-admin:latest-prod wnkx-job/xxl-job-admin
    - docker push $IMAGE_TAG/job-admin:latest-prod
    - docker build -t $IMAGE_TAG/wnkx-message:latest-prod wnkx-message
    - docker push $IMAGE_TAG/wnkx-message:latest-prod
  tags:
    - java
migrate-db-dev:
  stage: db-deploy
  script:
    - echo "开始执行数据库迁移"
    - mvn -B resources:resources liquibase:validate liquibase:updateSQL -pl com.ruoyi:ruoyi-modules -Pliquibase
    - mvn -B resources:resources liquibase:validate liquibase:update -pl com.ruoyi:ruoyi-modules -Pliquibase
    - mvn -B resources:resources liquibase:validate liquibase:updateSQL -pl com.wnkx:wnkx-business -Pliquibase
    - mvn -B resources:resources liquibase:validate liquibase:update -pl com.wnkx:wnkx-business -Pliquibase
    - mvn -B resources:resources liquibase:validate liquibase:updateSQL -pl com.wnkx:order-business -Pliquibase
    - mvn -B resources:resources liquibase:validate liquibase:update -pl com.wnkx:order-business -Pliquibase
  only:
    - main
  artifacts:
    paths:
      - order-business/target/liquibase/migrate.sql
      - ruoyi-modules/target/liquibase/migrate.sql
      - wnkx-business/target/liquibase/migrate.sql
  needs: ['image-build-dev']
migrate-db-test:
  stage: db-deploy
  script:
    - echo "开始执行数据库迁移"
    - mvn -B resources:resources liquibase:validate liquibase:updateSQL -pl com.ruoyi:ruoyi-modules -Pliquibase -Ddb.name=user-center-test
    - mvn -B resources:resources liquibase:validate liquibase:update -pl com.ruoyi:ruoyi-modules -Pliquibase -Ddb.name=user-center-test
    - mvn -B resources:resources liquibase:validate liquibase:updateSQL -pl com.wnkx:wnkx-business -Pliquibase -Ddb.name=biz-center-test
    - mvn -B resources:resources liquibase:validate liquibase:update -pl com.wnkx:wnkx-business -Pliquibase -Ddb.name=biz-center-test
    - mvn -B resources:resources liquibase:validate liquibase:updateSQL -pl com.wnkx:order-business -Pliquibase -Ddb.name=order-center-test
    - mvn -B resources:resources liquibase:validate liquibase:update -pl com.wnkx:order-business -Pliquibase -Ddb.name=order-center-test
  only:
    - test
  needs: ['image-build-test']
  artifacts:
    paths:
      - order-business/target/liquibase/migrate.sql
      - ruoyi-modules/target/liquibase/migrate.sql
      - wnkx-business/target/liquibase/migrate.sql
migrate-db-daily:
  stage: db-deploy
  script:
    - echo "开始执行数据库迁移"
    - mvn -B resources:resources liquibase:validate liquibase:updateSQL -pl com.ruoyi:ruoyi-modules -Pliquibase -Ddb.name=user-center-daily
    - mvn -B resources:resources liquibase:validate liquibase:update -pl com.ruoyi:ruoyi-modules -Pliquibase -Ddb.name=user-center-daily
    - mvn -B resources:resources liquibase:validate liquibase:updateSQL -pl com.wnkx:wnkx-business -Pliquibase -Ddb.name=biz-center-daily
    - mvn -B resources:resources liquibase:validate liquibase:update -pl com.wnkx:wnkx-business -Pliquibase -Ddb.name=biz-center-daily
    - mvn -B resources:resources liquibase:validate liquibase:updateSQL -pl com.wnkx:order-business -Pliquibase -Ddb.name=order-center-daily
    - mvn -B resources:resources liquibase:validate liquibase:update -pl com.wnkx:order-business -Pliquibase -Ddb.name=order-center-daily
  only:
    - daily
  needs: ['image-build-daily']
  artifacts:
    paths:
      - order-business/target/liquibase/migrate.sql
      - ruoyi-modules/target/liquibase/migrate.sql
      - wnkx-business/target/liquibase/migrate.sql


migrate-db-uat:
  stage: db-deploy
  script:
    - echo "开始执行数据库迁移"
    - mvn -B resources:resources liquibase:validate liquibase:updateSQL -pl com.ruoyi:ruoyi-modules -Pliquibase -Ddb.name=user-center-uat
    - mvn -B resources:resources liquibase:validate liquibase:update -pl com.ruoyi:ruoyi-modules -Pliquibase -Ddb.name=user-center-uat
    - mvn -B resources:resources liquibase:validate liquibase:updateSQL -pl com.wnkx:wnkx-business -Pliquibase -Ddb.name=biz-center-uat
    - mvn -B resources:resources liquibase:validate liquibase:update -pl com.wnkx:wnkx-business -Pliquibase -Ddb.name=biz-center-uat
    - mvn -B resources:resources liquibase:validate liquibase:updateSQL -pl com.wnkx:order-business -Pliquibase -Ddb.name=order-center-uat
    - mvn -B resources:resources liquibase:validate liquibase:update -pl com.wnkx:order-business -Pliquibase -Ddb.name=order-center-uat
  only:
    - uat
  needs: ['image-build-uat']
  artifacts:
    paths:
      - order-business/target/liquibase/migrate.sql
      - ruoyi-modules/target/liquibase/migrate.sql
      - wnkx-business/target/liquibase/migrate.sql
migrate-db-prod-prepare:
  only:
    - prod
  tags:
    - prod
  stage: pre-script-execute
  script:
    - echo "开始执行数据库迁移"
    - mvn -B resources:resources liquibase:validate liquibase:updateSQL -pl com.ruoyi:ruoyi-modules -Pliquibase -Ddb.ip=$DB_CREDENTIALS_ADDRESS -Ddb.port=3306 -Dliquibase.username=$DB_CREDENTIALS_USR -Dliquibase.password=$DB_CREDENTIALS_PSW
    - mvn -B resources:resources liquibase:validate liquibase:updateSQL -pl com.wnkx:wnkx-business -Pliquibase -Ddb.ip=$DB_CREDENTIALS_ADDRESS -Ddb.port=3306 -Dliquibase.username=$DB_CREDENTIALS_USR -Dliquibase.password=$DB_CREDENTIALS_PSW
    - mvn -B resources:resources liquibase:validate liquibase:updateSQL -pl com.wnkx:order-business -Pliquibase -Ddb.ip=$DB_CREDENTIALS_ADDRESS -Ddb.port=3306 -Dliquibase.username=$DB_CREDENTIALS_USR -Dliquibase.password=$DB_CREDENTIALS_PSW
  needs: ['image-build-prod']
  artifacts:
    paths:
      - order-business/target/liquibase/migrate.sql
      - ruoyi-modules/target/liquibase/migrate.sql
      - wnkx-business/target/liquibase/migrate.sql
notify-prepare-confirm:
  stage: pre-script-execute
  only:
    - prod
  tags:
    - prod
  needs: ['image-build-prod']
  script:
    - echo "发送构建通知"
    - curl --location --request POST "$ALARM_HOST" --header 'Content-Type:application/json' --data-raw "$NOTIFY_TEXT"
migrate-db-prod-execute:
  only:
    - prod
  tags:
    - prod
  needs: ['notify-prepare-confirm']
  when: manual
  stage: deploy
  script:
    - echo "开始执行数据库迁移"
    - mvn -B resources:resources liquibase:validate liquibase:update -pl com.ruoyi:ruoyi-modules -Pliquibase -Ddb.ip=$DB_CREDENTIALS_ADDRESS -Ddb.port=3306 -Dliquibase.username=$DB_CREDENTIALS_USR -Dliquibase.password=$DB_CREDENTIALS_PSW
    - mvn -B resources:resources liquibase:validate liquibase:update -pl com.wnkx:wnkx-business -Pliquibase -Ddb.ip=$DB_CREDENTIALS_ADDRESS -Ddb.port=3306 -Dliquibase.username=$DB_CREDENTIALS_USR -Dliquibase.password=$DB_CREDENTIALS_PSW
    - mvn -B resources:resources liquibase:validate liquibase:update -pl com.wnkx:order-business -Pliquibase -Ddb.ip=$DB_CREDENTIALS_ADDRESS -Ddb.port=3306 -Dliquibase.username=$DB_CREDENTIALS_USR -Dliquibase.password=$DB_CREDENTIALS_PSW
  artifacts:
    paths:
      - order-business/target/liquibase/migrate.sql
      - ruoyi-modules/target/liquibase/migrate.sql
      - wnkx-business/target/liquibase/migrate.sql

publish-configure-dev:
  stage: publish-config
  script:
    - echo "开始发布nacos配置"
    - mvn resources:resources compile exec:java -f configuration-management/pom.xml
  only:
    - main
  needs: ['migrate-db-dev']
publish-configure-test:
  stage: publish-config
  script:
    - echo "开始发布nacos配置"
    - mvn resources:resources compile exec:java -f configuration-management/pom.xml -Denv=test -Dnacos_namespace=test
  only:
    - test
  needs: ['migrate-db-test']
publish-configure-daily:
  stage: publish-config
  script:
    - echo "开始发布nacos配置"
    - mvn resources:resources compile exec:java -f configuration-management/pom.xml -Denv=daily -Dnacos_namespace=daily
  only:
    - daily
  needs: ['migrate-db-daily']
publish-configure-uat:
  stage: publish-config
  script:
    - echo "开始发布uat环境nacos配置"
    - mvn resources:resources compile exec:java -f configuration-management/pom.xml -Denv=uat -Dnacos_namespace=uat
  only:
    - uat
  needs: ['migrate-db-uat']
publish-configure-prod:
  needs:
    - image-build-prod
  stage: publish-config
  when: manual
  only:
    - prod
  tags:
    - prod
  script:
    - echo "开始发布prod环境nacos配置"
    - mvn resources:resources compile exec:java -f configuration-management/pom.xml -Dnacos.address=http://***********:8848/nacos/ -Denv=prod
update-xxl-job-dev:
  stage: deploy
  image: $IMAGE_TAG/baseline/python:3.9.21-bookworm
  only:
    - main
  variables:
    XXL_JOB_ADMIN_ADDRESS: $XXL_JOB_ADMIN_ADDRESS_DEV
  script:
    - cd task-update
    - sh './submit_all_xxl_job_task.sh'
    - echo "上传xxl脚本成功"
  needs: ['publish-configure-dev']
update-xxl-job-test:
  stage: deploy
  image: $IMAGE_TAG/baseline/python:3.9.21-bookworm
  only:
    - test
  variables:
    XXL_JOB_ADMIN_ADDRESS: $XXL_JOB_ADMIN_ADDRESS_TEST
  script:
    - cd task-update
    - sh './submit_all_xxl_job_task.sh'
    - echo "上传xxl脚本成功"
  needs: ['publish-configure-test']
update-xxl-job-uat:
  stage: deploy
  image: $IMAGE_TAG/baseline/python:3.9.21-bookworm
  only:
    - uat
  variables:
    XXL_JOB_ADMIN_ADDRESS: $XXL_JOB_ADMIN_ADDRESS_UAT
  script:
    - cd task-update
    - sh './submit_all_xxl_job_task.sh'
    - echo "上传xxl脚本成功"
  needs: ['publish-configure-uat']
update-xxl-job-daily:
  stage: deploy
  image: $IMAGE_TAG/baseline/python:3.9.21-bookworm
  only:
    - daily
  variables:
    XXL_JOB_ADMIN_ADDRESS: $XXL_JOB_ADMIN_ADDRESS_DAILY
  script:
    - cd task-update
    - sh './submit_all_xxl_job_task.sh'
    - echo "上传xxl脚本成功"
  needs: ['publish-configure-daily']
update-xxl-job-prod:
  stage: deploy
  image: $IMAGE_TAG/baseline/python:3.9.21-bookworm
  needs: ['publish-configure-prod']
  when: manual
  only:
    - prod
  variables:
    XXL_JOB_ADMIN_ADDRESS: $XXL_JOB_ADMIN_ADDRESS_PROD
  script:
    - cd task-update
    - sh './submit_all_xxl_job_task.sh'
    - echo "上传xxl脚本成功"
update-container-dev:
  stage: deploy
  image: docker:26.1.0-dind
  variables:
    DEPLOY_ARGS:
    JVM_ARGS: "-Xms256m -Xmx512m --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED"
    MIGRATE_DB_ARG: $CI_REGISTRY_IMAGE
    CONFIG_MANAGE_ARGS: $CI_REGISTRY_IMAGE
    DEPLOYMENT_FILE: $CI_REGISTRY_IMAGE
    APM_TOKEN: $DEV_APM_TOKEN
  only:
    - main
  needs: ['publish-configure-dev','update-xxl-job-dev']
  environment:
    name: dev
    url: https://$DEV_HOST
  script:
    - echo "开始更新容器"
    - cd docker
    - docker compose pull
    - docker compose stop
    - docker compose --compatibility up -d
update-container-uat:
  stage: deploy
  image: docker:26.1.0-dind
  variables:
    XXL_ARGS: "--spring.profiles.active=uat"
    DEPLOY_ARGS: "--spring.cloud.nacos.discovery.namespace=uat --spring.cloud.nacos.config.namespace=uat"
    JVM_ARGS: "-Xms256m -Xmx512m --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED"
    MIGRATE_DB_ARG: $CI_REGISTRY_IMAGE
    CONFIG_MANAGE_ARGS: $CI_REGISTRY_IMAGE
    DEPLOYMENT_FILE: $CI_REGISTRY_IMAGE
    ODS_ARGS: "-XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:InitiatingHeapOccupancyPercent=40 -XX:G1HeapRegionSize=16m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m "
    APM_TOKEN: $UAT_APM_TOKEN
  only:
    - uat
  environment:
    name: UAT
    url: https://$UAT_HOST
  needs: ['publish-configure-uat','update-xxl-job-uat']
  script:
    - echo "开始更新容器"
    - cd docker/uat
    - docker compose pull
    - docker compose stop
    - docker compose --compatibility up -d
update-container-test:
  stage: deploy
  image: docker:26.1.0-dind
  variables:
    XXL_ARGS: "--spring.profiles.active=test"
    DEPLOY_ARGS: "--spring.cloud.nacos.discovery.namespace=test --spring.cloud.nacos.config.namespace=test"
    JVM_ARGS: "-Xms256m -Xmx512m --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED"
    MIGRATE_DB_ARG: $CI_REGISTRY_IMAGE
    CONFIG_MANAGE_ARGS: $CI_REGISTRY_IMAGE
    DEPLOYMENT_FILE: $CI_REGISTRY_IMAGE
    APM_TOKEN: $UAT_APM_TOKEN
  only:
    - test
  environment:
    name: test
    url: https://$UAT_HOST
  needs: ['publish-configure-test','update-xxl-job-test']
  script:
    - echo "开始更新容器"
    - cd docker/test
    - docker compose pull
    - docker compose stop
    - docker compose --compatibility up -d
update-container-daily:
  stage: deploy
  image: docker:26.1.0-dind
  variables:
    XXL_ARGS: "--spring.profiles.active=daily"
    DEPLOY_ARGS: "--spring.cloud.nacos.discovery.namespace=daily --spring.cloud.nacos.config.namespace=daily"
    JVM_ARGS: "-Xms256m -Xmx512m --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED"
    MIGRATE_DB_ARG: $CI_REGISTRY_IMAGE
    CONFIG_MANAGE_ARGS: $CI_REGISTRY_IMAGE
    DEPLOYMENT_FILE: $CI_REGISTRY_IMAGE
    ODS_ARGS: "-XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:InitiatingHeapOccupancyPercent=40 -XX:G1HeapRegionSize=16m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m "
    APM_TOKEN: $UAT_APM_TOKEN
  only:
    - daily
  environment:
    name: daily
    url: https://$DAILY_HOST
  needs: ['publish-configure-daily','update-xxl-job-daily']
  script:
    - echo "开始更新容器"
    - cd docker/daily
    - docker compose pull
    - docker compose stop
    - docker compose --compatibility up -d
update-container-prod:
  only:
    - prod
  tags:
    - prod
  needs: ['publish-configure-prod']
  when: manual
  stage: deploy
  image: docker:26.1.0-dind
  variables:
    DEPLOY_ARGS: ""
    XXL_ARGS: "--spring.profiles.active=prod"
    JVM_ARGS: "--add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED"
    MIGRATE_DB_ARG: $CI_REGISTRY_IMAGE
    CONFIG_MANAGE_ARGS: $CI_REGISTRY_IMAGE
    DEPLOYMENT_FILE: $CI_REGISTRY_IMAGE
    APM_TOKEN: $PROD_APM_TOKEN
    ODS_ARGS: "-Xms1900m -Xmx1900m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:InitiatingHeapOccupancyPercent=40 -XX:G1HeapRegionSize=16m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m "
  environment:
    name: prod
    url: https://customer.woniu.video
  script:
    - echo "开始更新容器"
    - docker stack deploy -c docker/prod/docker-compose.yml prod
alarm:
  stage: alarm
  script:
    - echo "构建失败"
    - curl --location --request POST "$ALARM_HOST" --header 'Content-Type:application/json' --data-raw "$ALARM_TEXT"
  when: on_failure
