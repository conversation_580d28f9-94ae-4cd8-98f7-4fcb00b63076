package com.ruoyi.gateway;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 网关启动程序
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@RestController
public class RuoYiGatewayApplication {
    public static void main(String[] args) throws UnknownHostException {
        ConfigurableApplicationContext application = SpringApplication.run(RuoYiGatewayApplication.class, args);
        Environment env = application.getEnvironment();
        log.info("----------------------------------------------------------\n" +
                        "若依网关启动成功\n" +
                        "Swagger文档:\t\thttp://{}:{}{}/doc.html\n" +
                        "----------------------------------------------------------" ,
                InetAddress.getLocalHost().getHostAddress(),
                env.getProperty("server.port" , "8080"),
                env.getProperty("server.servlet.context-path" , ""));
    }
    @GetMapping("/hello")
    public String hello() {
        return "hello";
    }
}
