server:
  port: 8080
  shutdown: graceful
spring:
  application:
    name: ruoyi-gateway
  cloud:
    gateway:
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证中心
        - id: ruoyi-auth
          uri: lb://ruoyi-auth
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: ruoyi-system
          uri: lb://ruoyi-system
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1
        # 文件服务
        - id: ruoyi-system-file
          uri: lb://ruoyi-system
          predicates:
            - Path=/file/**
          filters:
            - StripPrefix=1
        # 订单服务
        - id: order-service
          uri: lb://order-service
          predicates:
            - Path=/order/**
          filters:
            - StripPrefix=1
        # 业务服务
        - id: biz-center
          uri: lb://biz-center
          predicates:
            - Path=/biz/**
          filters:
            - StripPrefix=1
# 安全配置
security:
  # 验证码
  captcha:
    enabled: true
    type: char
  # 防止XSS攻击
  xss:
    enabled: true
    excludeUrls:
      - /order/feedback/model/reject
      - /biz/text/add
      - /biz/text/edit
      - /order/order/add-cart
      - /biz/text/help/edit
      - /biz/text/help/add
      - /biz/logistic/notify
  # 不校验白名单
  ignore:
    verify:
      - /auth/register/sms
      - /auth/sms
    whites:
      #      登录认证类
      - /auth/**/logout
      - /auth/**/login
      - /auth/**/wechat/qrcode
      - /auth/**/phoneLogin
      - /auth/**/phoneLogin
      - /auth/register/sms
      - /auth/sms
      #      - /auth/register 关闭注册通道
      - /*/v2/api-docs
      - /csrf
      #      物流信息回调
      - /biz-center/logistic/notify
      - /biz/logistic/notify
      #      微信授权
      - /biz/auth/wechat/oauth2
      - /biz/auth/wechat/channelLogin
      - /biz/auth/wechat/channel/login
      - /biz/auth/wechat/check
      - /biz/auth/mobile/wechat/check
      - /biz/auth/wechat/business/check
      - /biz/auth/wechat/checkWechat
      - /biz/auth/wechat/checkPhone
      - /biz/auth/wechat/joinBusiness
      - /biz/business/updatePassword
      #      企业微信
      - /biz/callback/workWechat/api
      #      支付回调
      - /order/pay/callback
      - /order/pay/weChat/callback
      - /order/pay/alipay-callback
      #      文本协议
      - /biz/text/user-agreement
      - /biz/text/payment-agreement
      - /biz/text/privacy-agreement
      - /biz/text/fission-agreement
      - /biz/text/platform-agreement
      #     商家端未登录模特列表
      - /biz/model/reference-list
      - /biz/model/referenceModelInfo/{id}
      #      通过专属链接code获取专属企微二维码
      - /biz/marketing-channel/qrcode/{dedicatedLinkCode}
      - /biz/marketing-channel/qrcode
      #      代付
      - /order/another-pay/payee/type/{type}
      - /order/another-pay/payee/info/{id}
      - /order/another-pay/payee/info/detail/{id}
      - /order/another-pay/get-by-code
      - /order/another-pay/code
      - /order/another-pay/check
      - /order/another-pay/pay-info
      - /order/another-pay/pay-Member-info
      - /order/another-pay/submit-credential
      - /order/another-pay/getValidConfig
      - /order/another-pay/payLock
      - /order/another-pay/download-pay-info
      - /order/another-pay/businessBalancePrepay/submit-credential
      - /order/another-pay/businessBalancePrepay/getOnlineeDetail
      - /file/sign
      #     市场渠道访问统计
      - /biz/marketing-channel/visit
      #    阿里云一键登录
      - /biz/aliyun/getAuthToken
      - /biz/aliyun/getPhoneWithToken
      - /biz/auth/phoneLogin
      #     商家配置数据白名单
      - /biz/business/getMemberConfig
      - /biz/business/member/activity/list
      #      标签管理
      - /biz/tag/rank
      - /order/order/cancel-member-order-by-sub-account
      #      优惠活动服务
      - /order/promotion/get-valid-promotion-activity-list
      #      裂变活动是否展示
      - /system/config/fissionActivity
      - /system/config/getFissionMemberDiscount
      - /biz/fission-channel/getFissionDiscountV1
      #      商家登录
      - /biz/account/phone/check