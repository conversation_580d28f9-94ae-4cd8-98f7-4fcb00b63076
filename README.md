#  蜗牛跨境系统

## 基本框架信息
若依微服务V3.5.0（Java11+Springboot2.6）


## 开发环境准备

1. 安装 JDK11，配置好相关环境变量
2. 安装 maven，配置好相关环境变量
3. 检出代码

## 开发规范

[参见润一代码规约](http://git.dev.woniu.world/runyi/code-conventions.git)

## 业务及产品文档

产品文档见**蓝湖与飞书**

开发信息见[TAPD](https://www.tapd.cn/63098101/documents/file_list)

代码逻辑见woniu-world/doc目录

## 开发者手册

### 配置管理

`configuration-management` 模块用于管理多环境下的应用配置.

[使用配置管理](configuration-management/README.md)



### 单元测试

- 测试使用 junit5 或 spockframework 编写
- 单测不要依赖外部资源，如数据库
- 尽量不要在单测中引导 spring 容器，这会大幅增加单测运行时间

****


## 目录结构
```markdown
├─code-rule                 ---     规则检查
├─configuration-management  ---     环境配置(使用说明见readme.md)
├─doc                       ---     文档
├─docker                    ---     部署相关配置
├─order-business            ---     订单服务
│  ├─db                     ---     liquibase 数据库变更记录
│  ├─order-core             ---     订单服务核心模块
│  ├─order-job              ---     订单服务定时任务
│  └─order-service          ---     订单服务对外接口层
├─ruoyi-api                 ---     公共实体及对象存放
├─ruoyi-auth                ---     登录认证服务
├─ruoyi-common
│  ├─ruoyi-common-core      ---     基础核心组件
│  ├─ruoyi-common-datascope ---     数据库组件
│  ├─ruoyi-common-log       ---     日志记录组件
│  ├─ruoyi-common-redis     ---     redis组件
│  ├─ruoyi-common-security  ---     安全组件
│  ├─ruoyi-common-swagger   ---     swagger组件
│  ├─wnkx-common-db         ---     数据库增强组件
│  └─wnkx-common-loadbalancer-spring-boot-starter   ---     自定义路由选择组件
├─ruoyi-gateway             ---     网关
├─ruoyi-modules
│  ├─db                     ---     liquibase 数据库变更记录
│  ├─ruoyi-file             ---     文件组件
│  ├─ruoyi-gen              ---     代码生成组件(基本已经无用)
│  └─ruoyi-system           ---     内部用户模块(权限菜单等)
├─script                    ---     部署相关
├─sql
│  └─init                   ---     初始化sql
├─task-update               ---     xxl-job自动部署模块(使用说明见readme.md)
├─wnkx-business             ---     核心业务服务
│  ├─biz-center             ---     核心业务服务对外接口层
│  ├─biz-center-core        ---     核心业务服务核心模块
│  ├─biz-job                ---     核心业务服务定时任务
└─wnkx-job                  ---     xxl-job定时任务
.github-ci.yml              ---     github ci配置
```