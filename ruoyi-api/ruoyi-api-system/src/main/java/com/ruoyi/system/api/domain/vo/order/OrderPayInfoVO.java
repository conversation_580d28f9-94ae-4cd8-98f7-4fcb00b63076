package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/12 10:37
 */
@Data
public class OrderPayInfoVO implements Serializable {
    private static final long serialVersionUID = -4296762447412276488L;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 视频价格（单位：$）
     */
    @ApiModelProperty(value = "视频价格（单位：$）")
    private BigDecimal videoPrice;

    /**
     * 图片费用（单位：$）
     */
    @ApiModelProperty(value = "图片费用（单位：$）")
    private BigDecimal picPrice;

    /**
     * 手续费（单位：$）
     */
    @ApiModelProperty(value = "手续费（单位：$）")
    private BigDecimal exchangePrice;

    /**
     * 服务费用（单位：$）
     */
    @ApiModelProperty(value = "服务费用（单位：$）")
    private BigDecimal servicePrice;

    /**
     * 当前汇率
     */
    @ApiModelProperty(value = "当前汇率")
    private BigDecimal currentExchangeRate;

    /**
     * 税点（单位：%）
     */
    // @ApiModelProperty(value = "税点（单位：%）")
    // private BigDecimal taxPoint;

    /**
     * 税点费用（单位：￥）
     */
    // @ApiModelProperty(value = "税点费用（单位：￥）")
    // private BigDecimal taxPointCost;

    /**
     * 原需支付金额，不计算优惠与额外收费，即order.pay_amount（单位：￥）
     */
    @ApiModelProperty(value = "原需支付金额，不计算优惠与额外收费（单位：￥）")
    private BigDecimal oldNeedPayAmount;

    /**
     * 原需支付金额，不计算优惠与额外收费，即order.pay_amount_dollar（单位：$）
     */
    @ApiModelProperty(value = "原需支付金额，不计算优惠与额外收费（单位：$）")
    private BigDecimal oldNeedPayAmountDollar;

    @ApiModelProperty(value = "原价优惠后价格（单位：￥）")
    private BigDecimal afterPromotionAmount;

    @ApiModelProperty(value = "原价优惠后价格（单位：$）")
    private BigDecimal afterPromotionAmountDollar;

    /**
     * 还需支付金额，计算优惠与额外收费（单位：￥）
     */
    @ApiModelProperty(value = "还需支付金额，计算优惠与额外收费（单位：￥）")
    private BigDecimal payAmount;

    /**
     * 还需支付金额，计算优惠与额外收费（单位：$）
     */
    @ApiModelProperty(value = "还需支付金额，计算优惠与额外收费（单位：$）")
    private BigDecimal payAmountDollar;

    @ApiModelProperty(value = "有效余额")
    private BigDecimal validBalance;

    @ApiModelProperty(value = "是否余额锁定")
    private Integer isBalanceLock;

    @ApiModelProperty(value = "支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额）")
    private Integer payType;

    @ApiModelProperty(value = "订单余额")
    private BigDecimal orderBalance;

    @ApiModelProperty(value = "代理优惠")
    private BigDecimal discountsAmount;

    @ApiModelProperty(value = "订单原价(单位：$)")
    private BigDecimal orderOriginAmount;

    @ApiModelProperty(value = "订单最后金额(单位：$)")
    private BigDecimal orderAmountResult;

    @ApiModelProperty(value = "订单优惠金额(单位：￥")
    private BigDecimal orderPromotionAmountSum;

    @ApiModelProperty(value = "是否可以输入种草码 true:可以")
    private boolean canInputSeedCode;

    @ApiModelProperty(value = "种草码优惠金额（单位：￥）")
    private BigDecimal seedCodeDiscount;

    @ApiModelProperty(value = "种草码优惠后金额（单位：￥）")
    private BigDecimal afterSeedCodeDiscount;

    @ApiModelProperty(value = "种草码优惠后金额（单位：$）")
    private BigDecimal afterSeedCodeDiscountDollar;

    /**
     * 是否使用的默认汇率（true 默认汇率）
     */
    @ApiModelProperty(value = "是否使用的默认汇率（true 默认汇率）")
    private boolean isDefaultExchangeRate;

    /**
     * 收款信息id
     */
    @ApiModelProperty(value = "收款信息id")
    private Long payeeId;

    /**
     * 合并单ID
     */
    @ApiModelProperty(value = "合并单ID")
    private Long mergeId;

    /**
     * 是否合并单
     */
    @ApiModelProperty(value = "是否合并单")
    private Boolean isMerge;

    /**
     * 订单费用明细
     */
    @ApiModelProperty(value = "订单费用明细")
    private List<OrderPayInfoDetailVO> orderPayInfoDetailVOS = new ArrayList<>();

    /**
     * 订单参与的活动以及对应的优惠
     */
    @ApiModelProperty(value = "订单参与的活动以及对应的优惠")
    private List<OrderDiscountDetailVO> orderDiscountDetailVOS;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}
