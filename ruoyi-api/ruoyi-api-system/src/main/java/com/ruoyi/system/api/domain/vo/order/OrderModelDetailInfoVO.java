package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单详情（详细信息）
 *
 * <AUTHOR>
 * @date 2024/7/2 9:38
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderModelDetailInfoVO extends OrderModelBaseInfoVO {
    private static final long serialVersionUID = -2374610378394736980L;
    /**
     * 收件人（模特）
     */
    @ApiModelProperty(value = "收件人（模特）")
    private String recipient;

    /**
     * 城市（模特）
     */
    @ApiModelProperty(value = "城市（模特）")
    private String city;

    /**
     * 州（模特）
     */
    @ApiModelProperty(value = "州（模特）")
    private String state;

    /**
     * 邮编（模特）
     */
    @ApiModelProperty(value = "邮编（模特）")
    private String zipcode;

    /**
     * 详细地址（模特）
     */
    @ApiModelProperty(value = "详细地址（模特）")
    private String detailAddress;
}
