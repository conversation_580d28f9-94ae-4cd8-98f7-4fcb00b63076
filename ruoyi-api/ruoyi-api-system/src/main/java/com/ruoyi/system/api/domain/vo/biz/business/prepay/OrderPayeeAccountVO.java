package com.ruoyi.system.api.domain.vo.biz.business.prepay;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-12-17 14:39
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class OrderPayeeAccountVO implements Serializable {
    private static final long serialVersionUID = -1696399746554198332L;

    @ApiModelProperty("订单编号")
    private String orderNum;

    @ApiModelProperty("收款公司名称")
    private String accountName;

    @ApiModelProperty("开户行名称")
    private String bankName;

    @ApiModelProperty("收款银行账号")
    private String bankAccount;

    @ApiModelProperty("收款账号类型")
    private String companyAccountType;

    @ApiModelProperty("账号类型（0-默认类型, 1-银行卡账号, 2-对公账号,3-全币种账号）")
    private Integer accountType;
}
