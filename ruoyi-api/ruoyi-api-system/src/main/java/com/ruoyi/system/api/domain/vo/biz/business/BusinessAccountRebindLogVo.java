package com.ruoyi.system.api.domain.vo.biz.business;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class BusinessAccountRebindLogVo {
    private Long applyId;

    private Long bizUserId;

    private Long businessId;

    private String nickName;

    /**
     * 员工名称
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 售前微信
     */
    private String connectUserName;

    /**
     * 申请加入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 子账号状态
     */
    private Integer status;

    private String account;
}
