package com.ruoyi.system.api.domain.vo.biz.business;

import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家表
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessBalanceExportVO implements Serializable {

    private static final long serialVersionUID = -1262577170635983267L;

    @ApiModelProperty("商家主账号（FK：business_account.account）")
    private String ownerAccount;

    @ApiModelProperty("会员编码")
    @Excel(name = "会员编码")
    private String memberCode;


    @ApiModelProperty("微信名称")
    @Excel(name = "微信名称")
    private String nickName;

    @ApiModelProperty("公司名称")
    @Excel(name = "公司名称")
    private String name;

    @ApiModelProperty("商家创建时间（注册时间）")
    @Excel(name = "注册时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("是否为代理(0:否,1:是)")
    @Excel(name = "是否为代理", readConverterExp = "0=否,1=是")
    private Integer isProxy;


    @ApiModelProperty("账号状态（0正常 1禁用）")
    @Excel(name = "账号状态", readConverterExp = "0=正常,1=禁用")
    private Integer status;

    @ApiModelProperty("会员状态：0-非会员1-正常，2-即将过期，3-已过期")
    @Excel(name = "会员状态", readConverterExp = "0=非会员,1=正常,2=即将过期,3=已过期")
    private Integer memberStatus;

    @ApiModelProperty("帐号余额")
    @Excel(name = "帐号余额")
    private BigDecimal balance;

    @Excel(name = "钱包充值合计")
    private BigDecimal prepayAmountTotal;

    @Excel(name = "退款合计")
    private BigDecimal payoutTotal;

    @Excel(name = "-余额支付")
    private BigDecimal balancePayTotal;

    @Excel(name = "-提现合计")
    private BigDecimal withdrawTotal;

    @Excel(name = "锁定金额")
    private BigDecimal useBalance;

}