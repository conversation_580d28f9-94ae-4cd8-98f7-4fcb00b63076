package com.ruoyi.system.api.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/18
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "tencent.wechat.work")
public class WorkWeChatConfig {

    /**
     * 企业id
     */
    String corpId;

    /**
     * 应用加密的key
     */
    String encodingAESKey;

    /**
     * 应用token
     */
    String token;

    /**
     * 应用secret
     */
    String corpSecret;

    /**
     * 联系人
     */
    List<String> contactUser;

    /**
     * 分销渠道分组id
     */
    String distribution;

    /**
     * 市场渠道分组id
     */
    String marketing;

    List<String> contactUserName;

//    String subContactUser;

}
