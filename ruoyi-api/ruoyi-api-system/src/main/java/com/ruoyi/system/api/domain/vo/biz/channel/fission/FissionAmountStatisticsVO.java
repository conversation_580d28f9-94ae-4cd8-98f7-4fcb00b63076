package com.ruoyi.system.api.domain.vo.biz.channel.fission;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 裂变统计VO
 * @create :2025-05-19 09:21
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FissionAmountStatisticsVO implements Serializable {
    private static final long serialVersionUID = -3820839087823151962L;

    @ApiModelProperty("总收益")
    private BigDecimal totalAmount;

    @ApiModelProperty("待入账")
    private BigDecimal pendingDepositAmount;

    @ApiModelProperty("可提现")
    private BigDecimal canWithdrawAmount;

    @ApiModelProperty("待审核")
    private BigDecimal underReviewAmount;

    @ApiModelProperty("待打款")
    private BigDecimal pendingTransferAmount;

    @ApiModelProperty("提现中")
    private BigDecimal withdrawAuditAmount;

    @ApiModelProperty("已打款")
    private BigDecimal withdrawSuccessAmount;
}
