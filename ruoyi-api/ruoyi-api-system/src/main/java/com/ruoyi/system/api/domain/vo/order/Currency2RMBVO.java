package com.ruoyi.system.api.domain.vo.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/2 15:01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Currency2RMBVO implements Serializable {
    private static final long serialVersionUID = 5826960891388571378L;
    private BigDecimal currentExchange;
    private BigDecimal RMB;
}
