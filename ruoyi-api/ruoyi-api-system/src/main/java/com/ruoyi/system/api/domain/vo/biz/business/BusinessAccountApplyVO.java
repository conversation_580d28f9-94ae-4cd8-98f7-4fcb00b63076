package com.ruoyi.system.api.domain.vo.biz.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 账号申请表
 *
 * <AUTHOR>
 * @TableName business_account_apply
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessAccountApplyVO implements Serializable {

    private static final long serialVersionUID = 8664247117512676376L;
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("主账号")
    private String ownerAccount;

    @ApiModelProperty("员工名称")
    private String name;

    @ApiModelProperty("微信昵称")
    private String nickName;

    @ApiModelProperty("头像")
    private String pic;

    @ApiModelProperty("审核状态（0:待审核,1:审核通过,2.拒绝）")
    private Integer auditStatus;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
