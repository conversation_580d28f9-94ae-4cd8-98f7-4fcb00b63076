package com.ruoyi.system.api.domain.vo.biz.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家表
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "入驻商家列表（运营端使用）")
public class ResidentBusinessExportVO implements Serializable {
    private static final long serialVersionUID = 1316203364895435869L;

    private Long id;

    private String ownerAccount;

    @Excel(name = "公司名称")
    private String name;

    @Excel(name = "注册时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "会员编码")
    private String memberCode;

    @Excel(name = "对接客服")
    private String waiterName;

    @Excel(name = "会员状态", readConverterExp = "0=非会员,1=正常,2=即将过期,3=已过期")
    private Integer memberStatus;

    @Excel(name = "会员首次购买", width = 30, height = 28)
    private String memberFirst;

    @Excel(name = "会员有效期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date memberValidity;
}