package com.ruoyi.system.api.domain.vo.biz.channel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-29 15:57
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChannelInviteExportVO implements Serializable {
    private static final long serialVersionUID = -726638003545246779L;

    @Excel(name ="账号ID")
    private Long bizUserId;

    @Excel(name ="手机号")
    private String phone;

    @Excel(name ="昵称")
    private String nickName;

    @Excel(name ="注册渠道时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date registerTime;

    @Excel(name ="会员类型", readConverterExp = "0=季度会员,1=年度会员,2=三年会员,3=非该渠道邀请")
    private Integer memberType;

    @Excel(name ="购买会员时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name ="会员金额")
    private BigDecimal realPayAmount;
}
