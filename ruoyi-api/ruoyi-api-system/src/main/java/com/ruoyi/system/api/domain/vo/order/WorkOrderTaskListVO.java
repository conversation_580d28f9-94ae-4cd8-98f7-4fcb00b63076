package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
public class WorkOrderTaskListVO implements Serializable {
    private static final long serialVersionUID = -3940420846516984272L;

    /**
     * 任务单主键 order_video_task.id
     */
    @ApiModelProperty(value = "任务单主键 order_video_task.id")
    private Long id;

    /**
     * 视频订单id
     */
    @ApiModelProperty(value = "视频订单id")
    private Long videoId;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 产品中文名
     */
    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    private String productLink;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）")
    private Integer picCount;

    /**
     * 创建订单用户id
     */
    @ApiModelProperty(value = "创建订单用户id")
    @JsonIgnore
    private Long createOrderUserId;

    /**
     * 创建订单用户名称
     */
    @ApiModelProperty(value = "创建订单用户名称")
    private String createOrderUserName;

    /**
     * 创建订单用户微信名称
     */
    @ApiModelProperty(value = "创建订单用户微信名称")
    private String createOrderUserNickName;

    /**
     * 拍摄模特id
     */
    @ApiModelProperty(value = "拍摄模特id")
    @JsonIgnore
    private Long shootModelId;

    /**
     * 拍摄模特
     */
    @ApiModelProperty(value = "拍摄模特")
    private ModelOrderSimpleVO shootModel;

    /**
     * 对接人ID
     */
    @ApiModelProperty(value = "对接人ID")
    private Long contactId;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）")
    private Integer status;

    /**
     * 工单列表
     */
    @ApiModelProperty(value = "工单列表")
    private List<WorkOrderTaskDetailListVO> workOrderTaskDetailListVOS;
}
