package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
* 收款人账号配置表
* <AUTHOR>
 * @TableName payee_account_config
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PayeeAccountConfigVO implements Serializable {

    private static final long serialVersionUID = 7951348975216804220L;
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("收款账号名称")
    private String accountName;

    @ApiModelProperty("账号类型（0-默认类型, 1-银行卡账号, 2-对公账号）")
    private Integer accountType;

    @ApiModelProperty("开户行名称")
    private String bankName;

    @ApiModelProperty("开户行账号")
    private String bankAccount;

    @ApiModelProperty("收款账号类型")
    private String companyAccountType;

}
