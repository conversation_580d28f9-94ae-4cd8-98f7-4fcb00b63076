package com.ruoyi.system.api.domain.vo.biz.business.balance;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.annotation.Excels;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 余额提现审核表
 *
 * <AUTHOR>
 * @TableName business_balance_audit_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessBalanceAuditFlowDetailExportVO implements Serializable {
    private static final long serialVersionUID = -6568371496275660604L;

    @Excel(name = "提现单号")
    private String withdrawNumber;

    @Excel(name = "退款单号")
    private String balanceNumber;

    @Excel(name = "视频编码")
    private String videoCode;

    @Excel(name = "是否开票", readConverterExp = "0=否,1=是", defaultValue = "否")
    private String isInvoice;

    @Excel(name = "退款金额")
    private BigDecimal totalBalance;

    @Excel(name = "已使用金额")
    private BigDecimal useBalance;

    @Excel(name = "可提现金额")
    private BigDecimal payOutAmount;

    @Excels({
            @Excel(name = "商家名称", targetAttr = "name"),
            @Excel(name = "公司名称", targetAttr = "businessName"),
            @Excel(name = "会员开通时间", targetAttr = "businessCreateTime", dateFormat = "yyyy-MM-dd HH:mm"),
            @Excel(name = "会员编码", targetAttr = "memberCode")
    })
    private BusinessAccountDetailVO businessAccountDetailVO;

    @Excel(name = "合计提现金额")
    private BigDecimal amount;

    @Excel(name = "实际提现金额")
    private BigDecimal realAmount;

    @Excel(name = "提现方式", readConverterExp = "1=微信,2=支付宝,3=银行卡,4=境外汇款,5=其他,6=对公转账")
    private Integer withdrawWay;

    @Excel(name = "提现申请备注")
    private String applyRemark;

    @Excel(name = "状态", readConverterExp = "0=待处理,1=已提现,2=已取消")
    private Integer auditStatus;

    @Excel(name = "发起人")
    private String createUserName;

    @Excel(name = "发起时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "审批人")
    private String auditUserName;

    @Excel(name = "审批时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("商家ID")
    private Long businessId;

    @ApiModelProperty("商家关联客服Id")
    private Long businessWaiterId;

    @ApiModelProperty("商家关联客服名称")
    private String businessServiceName;

    @ApiModelProperty("视频编码（单号列表）")
    private List<String> numbers;

    @ApiModelProperty("支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @ApiModelProperty("图片资源地址url")
    private String resourceUrl;
    @ApiModelProperty("审核人员id FK sys_user.user_id")
    private Long auditUserId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("发起人id FK sys_user.user_id")
    private Long createUserId;

    @ApiModelProperty("提现通知状态 0-未通知 1-已通知")
    private Integer notifyStatus;

    @ApiModelProperty("通知时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date notifyTime;

}
