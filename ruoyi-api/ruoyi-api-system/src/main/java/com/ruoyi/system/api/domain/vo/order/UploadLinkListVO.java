package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/24 10:53
 */
@Data
public class UploadLinkListVO implements Serializable {
    private static final long serialVersionUID = 9009774441762152607L;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 视频订单ID
     */
    @ApiModelProperty(value = "视频订单ID")
    private Long videoId;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 产品中文名
     */
    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    private String productLink;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)", notes = "0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private Integer platform;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人")
    private Integer modelType;

    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", notes = "1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国")
    private Integer shootingCountry;

    /**
     * 视频格式（1:横屏16：9,2:竖屏9：16）
     */
    @ApiModelProperty(value = "视频格式（1:横屏16：9,2:竖屏9：16）", notes = "1:横屏16：9,2:竖屏9：16")
    private Integer videoFormat;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）", notes = "1:2张/$10,2:5张/$20")
    private Integer picCount;

    /**
     * 参考图片（关联资源id）
     */
    @ApiModelProperty(value = "参考图片（关联资源id）")
    @JsonIgnore
    private String referencePicId;

    /**
     * 参考图片
     */
    @ApiModelProperty(value = "参考图片")
    private List<String> referencePic = new ArrayList<>();

    /**
     * 退款照片数量
     */
    @ApiModelProperty(value = "退款照片数量")
    private Integer refundPicCount;

    /**
     * 订单退款信息
     */
    @ApiModelProperty(value = "订单退款信息")
    private OrderVideoRefundSimpleVO orderVideoRefund;

    /**
     * 订单退款信息(用于前端判断是否补偿订单或照片退款）
     */
    @ApiModelProperty(value = "订单退款信息(用于前端判断是否补偿订单或照片退款）")
    private List<OrderVideoRefundSimpleVO> orderVideoRefundList;

    /**
     * 拍摄模特id
     */
    @ApiModelProperty(value = "拍摄模特id")
    @JsonIgnore
    private Long shootModelId;

    /**
     * 拍摄模特
     */
    @ApiModelProperty(value = "拍摄模特")
    private ModelOrderSimpleVO shootModel;

    /**
     * 对接人id
     */
    @ApiModelProperty(value = "对接人id")
    @JsonIgnore
    private Long contactId;

    /**
     * 对接人
     */
    @ApiModelProperty(value = "对接人")
    private UserVO contact;

    /**
     * 出单人id
     */
    @ApiModelProperty(value = "出单人id")
    @JsonIgnore
    private Long issueId;

    /**
     * 出单人
     */
    @ApiModelProperty(value = "出单人")
    private UserVO issue;

    /**
     * 需要上传的链接
     */
    @ApiModelProperty(value = "需要上传的链接")
    private String needUploadLink;

    /**
     * asin
     */
    @ApiModelProperty(value = "asin")
    private String asin;

    /**
     * 视频标题-首次信息
     */
    @JsonIgnore
    private String videoTitleFirst;

    /**
     * 视频标题
     */
    @ApiModelProperty(value = "视频标题")
    private String videoTitle;

    /**
     * 视频封面图URI-首次信息
     */
    @JsonIgnore
    private String videoCoverFirst;

    /**
     * 视频封面图URI
     */
    @ApiModelProperty(value = "视频封面图URI")
    private String videoCover;

    /**
     * 客服备注
     */
    @ApiModelProperty(value = "上传备注")
    private String remark;

    /**
     * 上传账号
     */
    @ApiModelProperty(value = "上传账号")
    private String uploadAccount;

    /**
     * 上传用户id
     */
    @ApiModelProperty(value = "上传用户id")
    @JsonIgnore
    private Long uploadUserId;

    /**
     * 上传用户名称
     */
    @ApiModelProperty(value = "上传用户名称")
    private String uploadUserName;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date uploadTime;

    /**
     * 上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传）
     */
    @ApiModelProperty(value = "上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传）")
    private Integer status;

    /**
     * 历史剪辑记录数量
     */
    @ApiModelProperty(value = "历史剪辑记录数量")
    private Integer historyClipRecord;

    /**
     * 上传次数
     */
    @ApiModelProperty(value = "上传次数")
    private Integer count;

    /**
     * 售后单状态
     */
    @ApiModelProperty(value = "售后单状态（1：待处理，4：已完成）")
    private Integer afterSaleTaskStatus;

    /**
     * 工单状态
     */
    @ApiModelProperty(value = "工单状态（1：待处理，4：已完成）")
    private Integer workOrderTaskStatus;

    /**
     * 封面图和标题是否有更改
     */
    @ApiModelProperty(value = "封面图和标题是否有更改")
    private Boolean coverAndTitleChange;
}
