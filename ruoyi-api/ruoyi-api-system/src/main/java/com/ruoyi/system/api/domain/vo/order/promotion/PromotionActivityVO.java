package com.ruoyi.system.api.domain.vo.order.promotion;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/18 9:57
 */
@Data
public class PromotionActivityVO implements Serializable {
    private static final long serialVersionUID = -5316939446998878247L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 活动名称
     */
    @ApiModelProperty("活动名称")
    @JsonIgnore
    private String activityName;

    /**
     * 优惠活动类型（1-满5单减100，2-会员订单临期续费半价优惠，3-种草码优惠，4-每月首单立减）
     */
    @ApiModelProperty("优惠活动类型（1-满5单减100，2-会员订单临期续费半价优惠，3-种草码优惠，4-每月首单立减）")
    private Integer type;

    /**
     * 优惠扣减类型（1：直减，2：折扣）
     */
    @ApiModelProperty(value = "优惠扣减类型（1：直减，2：折扣）")
    private Integer discountType;

    /**
     * 优惠数值
     */
    @ApiModelProperty(value = "优惠数值")
    private BigDecimal amount;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date endTime;

    /**
     * 活动状态（0-无效，1-有效）
     */
    @ApiModelProperty("活动状态（0-无效，1-有效）")
    @JsonIgnore
    private Integer activityStatus;
}
