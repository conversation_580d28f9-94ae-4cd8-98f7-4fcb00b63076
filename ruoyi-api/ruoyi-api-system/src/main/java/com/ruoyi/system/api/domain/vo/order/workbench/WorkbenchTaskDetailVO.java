package com.ruoyi.system.api.domain.vo.order.workbench;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-04-01 20:47
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WorkbenchTaskDetailVO implements Serializable {
    private static final long serialVersionUID = 3189435689091347740L;


    @ApiModelProperty(value = "任务单ID")
    private Long taskId;

    @ApiModelProperty(value = "任务详情ID")
    private Long taskDetailId;

    @ApiModelProperty(value = "任务单号")
    private String taskNum;

    @ApiModelProperty(value = "任务单类型（1：售后单，2：工单）")
    private Integer taskType;


    @ApiModelProperty(value = "售后分类（1：视频，2：照片）")
    private Integer afterSaleClass;

    @ApiModelProperty(value = "售后视频类型（1：重拍视频，2：补拍视频，3：要高清视频，4：原素材，5：重新上传，6：重新剪辑）")
    private Integer afterSaleVideoType;

    @ApiModelProperty(value = "售后照片类型（1：重拍照片，2：要高清照片，3：补拍照片，4：原素材）")
    private Integer afterSalePicType;

    @ApiModelProperty(value = "工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）")
    private Integer workOrderType;

    @ApiModelProperty(value = "问题描述")
    private String content;

    @ApiModelProperty(value = "问题图片id")
    private String issuePicId;

    @ApiModelProperty(value = "提交人")
    private String submitBy;

    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTime;

    @ApiModelProperty(value = "优先级（1:紧急,2:一般）")
    private Integer priority;

    @ApiModelProperty(value = "问题图片")
    private List<String> issuePic = new ArrayList<>();

    @ApiModelProperty(value = "拒绝理由")
    private String refuseRemark;

    @ApiModelProperty(value = "拒绝人")
    private String refuseOperateBy;

    @ApiModelProperty(value = "拒绝时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private String refuseTime;
}
