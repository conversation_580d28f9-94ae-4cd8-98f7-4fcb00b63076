package com.ruoyi.system.api.domain.vo.biz.channel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-29 10:28
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "渠道统计")
@Builder
public class UserChannelStatisticsVO implements Serializable {
    private static final long serialVersionUID = -5504709664712700674L;

    @ApiModelProperty(value = "渠道ID")
    private Long channelId;

    @ApiModelProperty(value = "渠道类型")
    private Integer channelType;

    @ApiModelProperty(value = "邀请注册数量")
    private Long registerNum;

    @ApiModelProperty(value = "注册微信数量")
    private Long wechatNum;

    @ApiModelProperty(value = "激活数量")
    private Long activateNum;
}
