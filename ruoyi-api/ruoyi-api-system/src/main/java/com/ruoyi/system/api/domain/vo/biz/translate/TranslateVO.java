package com.ruoyi.system.api.domain.vo.biz.translate;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class TranslateVO implements Serializable {

    private static final long serialVersionUID = -3407887961490250698L;
    /**
     * 原文
     */
    @ApiModelProperty("原文")
    String originText;
    /**
     * 译文
     */
    @ApiModelProperty("译文")
    String targetText;

    @ApiModelProperty("原文加密结果")
    String sha256Hex;

    @ApiModelProperty("是否来自数据库：0-否，1-是")
    Integer isFromTable;

    public TranslateVO(String originText, String targetText) {
        this.originText = originText;
        this.targetText = targetText;
    }
}
