package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 视频订单费用明细
 * @create :2024-11-06 11:19
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderVideoPayInfoVO implements Serializable {
    private static final long serialVersionUID = 6593530122675310361L;

    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    @ApiModelProperty(value = "小计金额（单位：￥）")
    private BigDecimal amount;

    @ApiModelProperty(value = "小计金额（单位：$）")
    private BigDecimal amountDollar;

    @ApiModelProperty(value = "视频价格（单位：$）")
    private BigDecimal videoPrice;

    @ApiModelProperty(value = "图片费用（单位：$）")
    private BigDecimal picPrice;

    /**
     * 佣金代缴税费（单位：$）
     */
    @ApiModelProperty(value = "佣金代缴税费（单位：$）")
    private BigDecimal commissionPaysTaxes;

    @ApiModelProperty(value = "手续费（单位：$）")
    private BigDecimal exchangePrice;

    @ApiModelProperty(value = "服务费用（单位：$）")
    private BigDecimal servicePrice;

    @ApiModelProperty(value = "当前汇率")
    private BigDecimal currentExchangeRate;

}
