package com.ruoyi.system.api.domain.vo.biz.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:25
 */
@Data
public class TextVO implements Serializable {

    private static final long serialVersionUID = 6241708374971888L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 文本名称
     */
    @ApiModelProperty(value = "文本名称")
    private String name;

    /**
     * 文本内容
     */
    @ApiModelProperty(value = "文本内容")
    private String content;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否可删除（0:可以,1:不可以）
     */
    @ApiModelProperty(value = "是否可删除（0:可以,1:不可以）")
    private Integer canDelete;
}
