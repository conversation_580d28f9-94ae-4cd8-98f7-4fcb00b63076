package com.ruoyi.system.api.domain.vo.biz.datastatistics.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会员充值基础数据
 * @create :2025-06-17 11:56
 **/
@Data
public class MemberExpireCountVO implements Serializable {
    private static final long serialVersionUID = -2158442784301959311L;

    @ApiModelProperty(value = "已到期会员数")
    private Long overMemberCount;

    @ApiModelProperty(value = "30天内到期")
    private Long noExpireMemberCount;

    @ApiModelProperty(value = "昨天到期")
    private Long yesterdayExpireMemberCount;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "昨天更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date yesterdayUpdateTime;


    public static MemberExpireCountVO init(){
        MemberExpireCountVO rechargeCountVO = new MemberExpireCountVO();
        rechargeCountVO.setOverMemberCount(0L);
        rechargeCountVO.setNoExpireMemberCount(0L);
        rechargeCountVO.setYesterdayExpireMemberCount(0L);
        rechargeCountVO.setUpdateTime(new Date());
        return rechargeCountVO;
    }
}
