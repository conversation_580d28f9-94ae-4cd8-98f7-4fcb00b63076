package com.ruoyi.system.api.domain.vo.biz.model;

import com.ruoyi.common.core.domain.vo.UserVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ModelChangeVO extends ModelBaseVO {
    private static final long serialVersionUID = -123225259511237987L;
    /**
     * 合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "合作深度（0:一般模特,1:优质模特,2:中度模特）", notes = "0:一般模特,1:优质模特,2:中度模特")
    private Integer cooperation;

    /**
     * 模特评分 (0.0-10.0)
     */
    @ApiModelProperty(value = "模特评分 (0.0-10.0)", notes = "模特评分 (0.0-10.0)", required = true)
    private BigDecimal cooperationScore;

    /**
     * 关联对接人员
     */
    @ApiModelProperty(value = "关联对接人员")
    private List<UserVO> persons = new ArrayList<>();
}
