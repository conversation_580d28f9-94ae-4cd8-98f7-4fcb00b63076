package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.api.domain.dto.order.VideoContentDTO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/7 15:20
 */
@Data
public class VideoCartVO implements Serializable {
    private static final long serialVersionUID = 7147803226690354339L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)", notes = "0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private Integer platform;

    /**
     * 视频格式（1:横屏16：9,2:竖屏9：16）
     */
    @ApiModelProperty(value = "视频格式（1:横屏16：9,2:竖屏9：16）", notes = "1:横屏16：9,2:竖屏9：16")
    private Integer videoFormat;

    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", notes = "1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国")
    private Integer shootingCountry;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人")
    private Integer modelType;

    /**
     * 产品中文名
     */
    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    private String productLink;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 意向模特id
     */
    @ApiModelProperty(value = "意向模特id")
    @JsonIgnore
    private Long intentionModelId;

    /**
     * 意向模特
     */
    @ApiModelProperty(value = "意向模特")
    private ModelInfoVO intentionModel;

    /**
     * 拍摄建议（原拍摄要求）
     */
    @ApiModelProperty("拍摄建议（原拍摄要求）")
    @Valid
    private List<VideoContentDTO> shootRequired = new ArrayList<>();

    /**
     * 参考视频链接
     */
    @ApiModelProperty(value = "参考视频链接")
    private String referenceVideoLink;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）", notes = "1:2张/$10,2:5张/$20")
    private Integer picCount;

    /**
     * 参考图片
     */
    @ApiModelProperty(value = "参考图片")
    @JsonIgnore
    private String referencePicId;

    /**
     * 参考图片
     */
    @ApiModelProperty(value = "参考图片")
    private List<String> referencePic = new ArrayList<>();

    /**
     * 视频金额（单位：$）
     */
    @ApiModelProperty(value = "视频金额（单位：$）")
    private BigDecimal amount;

    /**
     * 视频价格（单位：$）
     */
    @ApiModelProperty(value = "视频价格（单位：$）")
    private BigDecimal videoPrice;

    /**
     * 图片费用（单位：$）
     */
    @ApiModelProperty(value = "图片费用（单位：$）")
    private BigDecimal picPrice;

    /**
     * 佣金代缴税费（单位：$）
     */
    @ApiModelProperty(value = "佣金代缴税费（单位：$）")
    private BigDecimal commissionPaysTaxes;

    /**
     * 手续费（单位：$）
     */
    @ApiModelProperty(value = "手续费（单位：$）")
    private BigDecimal exchangePrice;

    /**
     * 服务费用（单位：$）
     */
    @ApiModelProperty(value = "服务费用（单位：$）")
    private BigDecimal servicePrice;

    /**
     * 创建订单用户账号
     */
    @ApiModelProperty(value = "创建订单用户账号")
    private String createOrderUserAccount;

    /**
     * 创建订单用户名称
     */
    @ApiModelProperty(value = "创建订单用户名称")
    private String createOrderUserName;

    /**
     * 创建订单用户微信名称
     */
    @ApiModelProperty(value = "创建订单用户微信名称")
    private String createOrderUserNickName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
