package com.ruoyi.system.api.domain.vo.order.finace;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.api.domain.vo.order.PayeeAccountConfigVO;
import com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单收支明细
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@ApiModel(value = "订单收支明细VO")
@Data
public class OrderPayDetailVO implements Serializable {
    private static final long serialVersionUID = 734687070967988311L;

    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @ApiModelProperty(value = "入账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recordTime;

    @ApiModelProperty(value = "订单号")
    private String orderNum;

    @ApiModelProperty(value = "支付单号")
    private String payNum;

    @ApiModelProperty(value = "交易流水号")
    private String mchntOrderNo;

    @ApiModelProperty(value = "商家ID")
    private Long merchantId;

    @ApiModelProperty(value = "编码")
    private String merchantCode;

    @ApiModelProperty(value = "商家名称")
    private String businessName;

    @ApiModelProperty(value = "商家微信名称")
    private String businessNickName;

    @ApiModelProperty(value = "商家账号")
    private String businessAccount;

    @ApiModelProperty(value = "订单类型（0-视频订单，1-会员订单）")
    private Integer orderType;

    @ApiModelProperty(value = "支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额,11:微信+余额,12:支付宝+余额,13:云闪付/银联+余额,14.数字人民币+余额,15.银行+余额,16:对公+余额,17:全币种+余额）")
    private Integer payType;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private Integer payTypeDetail;

    @ApiModelProperty(value = "订单原价（单位：￥）")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "支付金额（单位：￥）")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "支付金额（单位：$）")
    private BigDecimal payAmountDollar;

    @ApiModelProperty(value = "订单实付金额（单位：￥）")
    private BigDecimal realPayAmount;

    @ApiModelProperty(value = "订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    @ApiModelProperty(value = "差额（单位：￥）")
    private BigDecimal differenceAmount;

    @ApiModelProperty(value = "订单活动优惠总额（单位：￥）")
    private BigDecimal orderPromotionAmount;

    @ApiModelProperty(value = "币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

    @ApiModelProperty(value = "使用余额（单位：￥）")
    private BigDecimal useBalance;

    @ApiModelProperty(value = "剩余支付金额")
    private BigDecimal surplusAmount;

    @ApiModelProperty(value = "种草码优惠金额")
    private BigDecimal seedCodeDiscount;

    @ApiModelProperty(value = "运营修改的订单金额")
    private BigDecimal backModifyAmount;

    @ApiModelProperty(value = "当前汇率")
    private BigDecimal currentExchangeRate;

    @ApiModelProperty(value = "审核状态：")
    private Integer auditStatus;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @ApiModelProperty(value = "是否入账")
    private Integer isRecord;

    @ApiModelProperty(value = "税点费用（单位：￥）")
    private BigDecimal taxPointCost;

    @ApiModelProperty(value = "支付账户（对公）")
    private String payAccount;

    @ApiModelProperty(value = "收款账号")
    private String bankAccount;

    @ApiModelProperty(value = "order_id")
    private Long orderId;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    @ApiModelProperty(value = "下单用户ID")
    private Long orderUserId;

    @ApiModelProperty(value = "视频订单明细")
    private OrderDetailVideoAmountVO videoAmount;

    @ApiModelProperty(value = "会员订单明细")
    private OrderDetailMemberAmountVO memberAmount;

    @ApiModelProperty(value = "预付款订单明细")
    private OrderDetailPrepayAmountVO prepayAmount;

    @ApiModelProperty(value = "收款账号")
    private PayeeAccountConfigVO payeeAccountConfig;

    /**
     * 订单参与的活动以及对应的优惠
     */
    @ApiModelProperty(value = "订单参与的活动以及对应的优惠")
    private List<OrderDiscountDetailVO> orderDiscountDetailVOS;

}
