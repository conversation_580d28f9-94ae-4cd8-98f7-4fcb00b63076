package com.ruoyi.system.api.domain.vo.biz.datastatistics;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/30 17:09
 */
@Data
public class ModelOrderRankingVO implements Serializable {
    private static final long serialVersionUID = 2144799331309805343L;

    /**
     * 记录截止时间
     */
    @ApiModelProperty("记录截止时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date writeTimeEnd;

    /**
     * 模特排行榜数据
     */
    @ApiModelProperty("模特排行榜数据")
    private List<ModelOrderRankingListVO> modelOrderRankingListVOS;
}
