package com.ruoyi.system.api.domain.vo.biz.datastatistics;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.enums.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12 10:31
 */
@Data
public class ModelTypeAnalysisVO implements Serializable {
    private static final long serialVersionUID = -7683008262777161061L;

    /**
     * 实时更新时间
     */
    @ApiModelProperty("实时更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date dateTime;

    /**
     * 模特数量
     */
    @ApiModelProperty("模特数量")
    private Long modelCount;

    /**
     * 模特类型饼图
     */
    @ApiModelProperty("模特类型饼图")
    private List<PieChartVO> modelTypePieChartVOS;

    /**
     * 模特性别饼图
     */
    @ApiModelProperty("模特性别饼图")
    private List<PieChartVO> modelSexPieChartVOS;

    /**
     * 模特合作深度饼图
     */
    @ApiModelProperty("模特合作深度饼图")
    private List<PieChartVO> modelCooperationPieChartVOS;

    /**
     * 模特国家饼图
     */
    @ApiModelProperty("模特国家饼图")
    private List<PieChartVO> modelNationPieChartVOS;

    /**
     * 模特佣金饼图
     */
    @ApiModelProperty("模特佣金饼图")
    private List<PieChartVO> modelCommissionPieChartVOS;

    /**
     * 模特年龄层饼图
     */
    @ApiModelProperty("模特年龄层饼图")
    private List<PieChartVO> modelAgeGroupPieChartVOS;

    /**
     * 模特排单情况饼图
     */
    @ApiModelProperty("模特排单情况饼图")
    private List<PieChartVO> modelOrderScheduledPieChartVOS;

    public ModelTypeAnalysisVO echo() {
        if (CollUtil.isNotEmpty(modelTypePieChartVOS)) {
            for (PieChartVO pieChartVO : modelTypePieChartVOS) {
                pieChartVO.echo();
            }
        } else {
            modelTypePieChartVOS = List.of(
                    PieChartVO.init(ModelTypeEnum.INFLUENT.getLabel()),
                    PieChartVO.init(ModelTypeEnum.AVERAGE_PEOPLE.getLabel())
            );
        }
        if (CollUtil.isNotEmpty(modelSexPieChartVOS)) {
            for (PieChartVO pieChartVO : modelSexPieChartVOS) {
                pieChartVO.echo();
            }
        } else {
            modelSexPieChartVOS = List.of(
                    PieChartVO.init(SexEnum.MAN.getLabel()),
                    PieChartVO.init(SexEnum.WOMAN.getLabel())
            );
        }
        if (CollUtil.isNotEmpty(modelCooperationPieChartVOS)) {
            for (PieChartVO pieChartVO : modelCooperationPieChartVOS) {
                pieChartVO.echo();
            }
        } else {
            modelCooperationPieChartVOS = List.of(
                    PieChartVO.init(ModelCooperationEnum.ORDINARY.getLabel()),
                    // PieChartVO.init(ModelCooperationEnum.MODERATE.getLabel()),
                    PieChartVO.init(ModelCooperationEnum.QUALITY.getLabel())
            );
        }
        if (CollUtil.isNotEmpty(modelNationPieChartVOS)) {
            for (PieChartVO pieChartVO : modelNationPieChartVOS) {
                pieChartVO.echo();
            }
        } else {
            modelNationPieChartVOS = List.of(
                    PieChartVO.init(NationEnum.USA.getLabel()),
                    PieChartVO.init(NationEnum.CANADA.getLabel()),
                    PieChartVO.init(NationEnum.GERMANY.getLabel()),
                    PieChartVO.init(NationEnum.FRANCE.getLabel()),
                    PieChartVO.init(NationEnum.IT.getLabel()),
                    PieChartVO.init(NationEnum.ES.getLabel()),
                    PieChartVO.init(NationEnum.UK.getLabel())
            );
        }
        if (CollUtil.isNotEmpty(modelCommissionPieChartVOS)) {
            for (PieChartVO pieChartVO : modelCommissionPieChartVOS) {
                pieChartVO.echo();
                if (CollUtil.isNotEmpty(pieChartVO.getPieChartVOS())) {
                    for (PieChartVO chartVO : pieChartVO.getPieChartVOS()) {
                        chartVO.echo();
                    }
                }
            }
        } else {
            modelCommissionPieChartVOS = Collections.emptyList();
        }
        if (CollUtil.isNotEmpty(modelAgeGroupPieChartVOS)) {
            for (PieChartVO pieChartVO : modelAgeGroupPieChartVOS) {
                pieChartVO.echo();
            }
        } else {
            modelAgeGroupPieChartVOS = List.of(
                    PieChartVO.init(ModelAgeGroupEnum.INFANT.getLabel()),
                    PieChartVO.init(ModelAgeGroupEnum.CHILD.getLabel()),
                    PieChartVO.init(ModelAgeGroupEnum.ADULT.getLabel()),
                    PieChartVO.init(ModelAgeGroupEnum.AGED.getLabel())
            );
        }
        if (CollUtil.isNotEmpty(modelOrderScheduledPieChartVOS)) {
            for (PieChartVO pieChartVO : modelOrderScheduledPieChartVOS) {
                pieChartVO.echo();
            }
        } else {
            modelOrderScheduledPieChartVOS = List.of(
                    PieChartVO.init(ModelOrderScheduledDataEnum.FIRST_GEAR.getLabel()),
                    PieChartVO.init(ModelOrderScheduledDataEnum.SECOND_GEAR.getLabel()),
                    PieChartVO.init(ModelOrderScheduledDataEnum.THIRD_GEAR.getLabel()),
                    PieChartVO.init(ModelOrderScheduledDataEnum.FOURTH_GEAR.getLabel()),
                    PieChartVO.init(ModelOrderScheduledDataEnum.FIFTH_GEAR.getLabel()),
                    PieChartVO.init(ModelOrderScheduledDataEnum.SIXTH_GEAR.getLabel()),
                    PieChartVO.init(ModelOrderScheduledDataEnum.SEVENTH_GEAR.getLabel())
            );
        }
        modelCount = modelCount == null ? 0L : modelCount;
        return this;
    }
}
