package com.ruoyi.system.api.domain.vo.biz.business.balance;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :商家余额详情统计
 * @create :2024-12-28 18:05
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalanceDetailStatisticsVO implements Serializable {
    private static final long serialVersionUID = -7213513293038285638L;

    @ApiModelProperty("总余额")
    private BigDecimal balanceTotal;

    @ApiModelProperty("总使用余额")
    private BigDecimal useBalanceTotal;
}
