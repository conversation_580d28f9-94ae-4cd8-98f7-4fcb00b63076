package com.ruoyi.system.api.domain.vo.order;

import com.ruoyi.system.api.domain.vo.ModelEndLogisticVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单详情（非售后状态）
 *
 * <AUTHOR>
 * @date 2024/7/2 9:38
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderModelNormalInfoVO extends OrderModelDetailInfoVO {
    private static final long serialVersionUID = 6532098378504392284L;
    /**
     * 订单状态
     */
    @ApiModelProperty("订单状态（6:Pending completion,8:Completed,9:Seller Cancellation）")
    private Integer status;

    /**
     * 是否有产生售后（非0表示有售后）
     */
    @ApiModelProperty(value = "是否有产生售后（非0表示有售后）")
    private Integer inAfterSale;

    /**
     * 物流信息
     */
    @ApiModelProperty("物流信息")
    private List<ModelEndLogisticVO> logistics = new ArrayList<>();
}
