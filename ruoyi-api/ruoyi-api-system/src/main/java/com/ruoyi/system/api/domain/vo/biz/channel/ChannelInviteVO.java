package com.ruoyi.system.api.domain.vo.biz.channel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-29 15:57
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChannelInviteVO implements Serializable {
    private static final long serialVersionUID = -726638003545246779L;

    @ApiModelProperty("账号ID")
    private Long bizUserId;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("昵称")
    private String nickName;

    @ApiModelProperty("头像")
    private String pic;

    @ApiModelProperty("注册渠道类型")
    private Integer registerChannelType;

    @ApiModelProperty("注册渠道ID")
    private Long registerChannelId;

    @ApiModelProperty("是否注册渠道：1-是，0-否")
    private Integer isRegisterChannel;

    @ApiModelProperty("注册渠道时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registerTime;

    @ApiModelProperty("账号类型")
    private Integer accountType;

    @ApiModelProperty("企微渠道类型")
    private Integer wechatChannelType;

    @ApiModelProperty("企微渠道ID")
    private Long wechatChannelId;

    @ApiModelProperty("添加微信时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addWechatTime;

    @ApiModelProperty("是否微信渠道：1-是，0-否")
    private Integer isWechatChannel;

    @ApiModelProperty("会员类型")
    private Integer memberType;

    @ApiModelProperty("充值会员的渠道ID")
    private Long memberChannelId;

    @ApiModelProperty("是否渠道会员：1-是，0-否")
    private Integer isChannelMember;

    @ApiModelProperty("购买会员时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("会员金额")
    private BigDecimal realPayAmount;
}
