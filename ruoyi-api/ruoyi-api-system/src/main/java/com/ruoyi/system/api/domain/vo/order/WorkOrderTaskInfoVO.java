package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkOrderTaskInfoVO extends WorkOrderTaskDetailListVO {
    private static final long serialVersionUID = -8327426220268884136L;

    /**
     * 关闭/完成时间
     */
    @ApiModelProperty(value = "关闭/完成时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date endTime;


    /**
     * 流转记录
     */
    @ApiModelProperty(value = "流转记录")
    private List<OrderTaskFlowRecordVO> records;
}
