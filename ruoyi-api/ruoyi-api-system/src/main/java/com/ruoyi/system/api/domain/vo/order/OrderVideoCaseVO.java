package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单_视频_匹配情况反馈返回对象
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@ApiModel(value = "订单_视频_匹配情况反馈返回对象")
@Data
public class OrderVideoCaseVO implements Serializable {
    private static final long serialVersionUID = -1107785928010028676L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 视频id
     */
    @ApiModelProperty(value = "视频id")
    private Long videoId;

    /**
     * 发送人id
     */
    @ApiModelProperty(value = "发送人id")
    private Long sendId;

    /**
     * 发送人
     */
    @ApiModelProperty(value = "发送人")
    private UserVO sendUser;

    /**
     * 发送内容
     */
    @ApiModelProperty(value = "发送内容")
    private String sendContent;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 回复人id
     */
    @ApiModelProperty(value = "回复人id")
    private Long replyId;

    /**
     * 回复人
     */
    @ApiModelProperty(value = "回复人")
    private BusinessAccountDetailVO replyUser;

    /**
     * 回复时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date replyTime;

    /**
     * 回复内容(0:待反馈,1:同意,2:不同意)
     */
    @ApiModelProperty(value = "回复内容(0:待反馈,1:同意,2:不同意)", notes = "0:待反馈,1:同意,2:不同意")
    private Integer replyContent;

    /** 运营是否修改了订单（1:修改了,0:还没修改） */
    @ApiModelProperty(value = "运营是否修改了订单（1:修改了,0:还没修改）")
    private Integer operateEdit;

    /**
     * 原因
     */
    private String reason;
}
