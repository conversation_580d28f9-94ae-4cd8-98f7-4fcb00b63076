package com.ruoyi.system.api.domain.vo.order.finace;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :应收审批统计vo
 * @create :2024-11-05 11:48
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderAuditStatusStatisticsVO implements Serializable {
    private static final long serialVersionUID = 7658168314885953449L;

    @ApiModelProperty("待审核数量")
    private Integer unCheckNum;

    @ApiModelProperty("审核通过数量")
    private Integer approveNum;

    @ApiModelProperty("审核异常数量")
    private Integer exceptionNum;

    @ApiModelProperty("关闭订单数量")
    private Integer closeNum;
}
