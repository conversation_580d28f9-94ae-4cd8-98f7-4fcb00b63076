package com.ruoyi.system.api.domain.vo.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/17 15:19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckUnableSettlementVO implements Serializable {
    private static final long serialVersionUID = -5877634836672710284L;

    /**
     * 能否申请
     */
    @ApiModelProperty("能否申请")
    private Boolean canApply;

    /**
     * 不能申请原因
     */
    @ApiModelProperty("不能申请原因（1：已申请，2：存在进行中订单，3：已超7天）")
    private Integer reason;
}
