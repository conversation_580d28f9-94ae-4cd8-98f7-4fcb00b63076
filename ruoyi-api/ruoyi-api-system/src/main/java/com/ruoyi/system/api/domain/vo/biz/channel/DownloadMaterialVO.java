package com.ruoyi.system.api.domain.vo.biz.channel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 市场渠道下载物流返回对象VO
 *
 * <AUTHOR>
 * @date 2024/9/25 16:03
 */
@Data
public class DownloadMaterialVO implements Serializable {
    private static final long serialVersionUID = -2229227299878489579L;
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("市场渠道名称")
    private String marketingChannelName;

    @ApiModelProperty("落地形式（1:官网首页，2:添加企微客服）")
    private Integer landingForm;

    @ApiModelProperty("投流链接")
    private String dedicatedLinkCode;

    @ApiModelProperty("客服二维码")
    private String wechatCode;
}
