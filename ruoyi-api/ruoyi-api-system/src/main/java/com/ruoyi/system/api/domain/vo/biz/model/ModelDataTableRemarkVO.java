package com.ruoyi.system.api.domain.vo.biz.model;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/8 20:44
 */
@Data
public class ModelDataTableRemarkVO implements Serializable {
    private static final long serialVersionUID = 5464205844588257398L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 模特ID（FK：model.id）
     */
    @ApiModelProperty(value = "模特ID（FK：model.id）")
    private Long modelId;

    /**
     * 备注内容
     */
    @ApiModelProperty(value = "备注内容")
    private String remark;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}
