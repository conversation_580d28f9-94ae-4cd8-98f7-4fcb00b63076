package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.TagVO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoContent;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/15
 */
@ApiModel(value = "订单_视频简单对象VO")
@Data
public class OrderVideoSimpleVO implements Serializable {
    private static final long serialVersionUID = -2564497089042269421L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）")
    private Integer status;

    /**
     * 订单进入新状态的时间
     */
    @ApiModelProperty(value = "订单进入新状态的时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date statusTime;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 产品中文名
     */
    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 产品品类
     */
    @ApiModelProperty(value = "产品品类")
    private List<TagVO> productCategory;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    private String productLink;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)", notes = "0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private Integer platform;

    @ApiModelProperty(value = "视频风格(0:Amazon,1:tiktok,2:APP/解说类)")
    private Integer videoStyle;

    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", notes = "1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国")
    private Integer shootingCountry;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人")
    private Integer modelType;

    /**
     * 视频格式（1:横屏16：9,2:竖屏9：16）
     */
    @ApiModelProperty(value = "视频格式（1:横屏16：9,2:竖屏9：16）", notes = "1:横屏16：9,2:竖屏9：16")
    private Integer videoFormat;

    /** 视频时长 */
    @ApiModelProperty(value = "视频时长")
    private Integer videoDuration;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）", notes = "1:2张/$10,2:5张/$20")
    private Integer picCount;

    @ApiModelProperty(value = "退款照片数量")
    private Integer refundPicCount;

    @ApiModelProperty(value = "剩余退款照片数量")
    private Integer surplusPicCount;

    /**
     * 参考图片
     */
    @ApiModelProperty(value = "参考图片(关联资源id)")
    @JsonIgnore
    private String referencePicId;

    /**
     * 参考图片
     */
    @ApiModelProperty(value = "参考图片")
    private List<String> referencePic = new ArrayList<>();

    /**
     * 意向模特id
     */
    @ApiModelProperty(value = "意向模特id")
    @JsonIgnore
    private Long intentionModelId;

    /**
     * 意向模特
     */
    @ApiModelProperty(value = "意向模特")
    private ModelOrderSimpleVO intentionModel;

    /**
     * 拍摄模特id
     */
    @ApiModelProperty(value = "拍摄模特id")
    @JsonIgnore
    private Long shootModelId;

    /**
     * 拍摄模特
     */
    @ApiModelProperty(value = "拍摄模特")
    private ModelOrderSimpleVO shootModel;

    /**
     * 参考视频链接
     */
    @ApiModelProperty(value = "参考视频链接")
    private String referenceVideoLink;

    /**
     * 拍摄建议（原拍摄要求）
     */
    @ApiModelProperty("拍摄建议（原拍摄要求）")
    private List<OrderVideoContent> shootRequired;

    /**
     * 产品卖点
     */
    @ApiModelProperty("产品卖点")
    private String sellingPointProduct;

    /**
     * 模特要求（原匹配模特注意事项）
     */
    @ApiModelProperty("模特要求（原匹配模特注意事项）")
    private OrderVideoCautionsVO orderVideoCautionsVO;

    /**
     * 剪辑要求
     */
    @ApiModelProperty(value = "剪辑要求")
    private List<OrderVideoContent> clipsRequired;

    @ApiModelProperty(value = "商品规格要求")
    private String orderSpecificationRequire;

    @ApiModelProperty(value = "特别强调")
    private String particularEmphasis;

    @ApiModelProperty(value = "特别强调图片")
    @JsonIgnore
    private String particularEmphasisPicIds;

    @ApiModelProperty(value = "特别强调图片")
    private List<String> particularEmphasisPic;

    /**
     * 视频金额（人民币）
     */
    @ApiModelProperty(value = "视频金额（人民币）")
    private BigDecimal amount;

    /**
     * 视频金额（单位：$）
     */
    @ApiModelProperty(value = "视频金额（单位：$）")
    private BigDecimal amountDollar;

    @ApiModelProperty(value = "需支付金额（单位：￥）")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "需支付金额（单位：$）")
    private BigDecimal payAmountDollar;

    @ApiModelProperty(value = "视频活动优惠金额")
    private BigDecimal videoPromotionAmount;

    /**
     * 视频价格（单位：$）
     */
    @ApiModelProperty(value = "视频价格（单位：$）")
    private BigDecimal videoPrice;

    /**
     * 图片费用（单位：$）
     */
    @ApiModelProperty(value = "图片费用（单位：$）")
    private BigDecimal picPrice;

    /**
     * 佣金代缴税费（单位：$）
     */
    @ApiModelProperty(value = "佣金代缴税费（单位：$）")
    private BigDecimal commissionPaysTaxes;

    /**
     * 手续费（单位：$）
     */
    @ApiModelProperty(value = "手续费（单位：$）")
    private BigDecimal exchangePrice;

    /**
     * 服务费用（单位：$）
     */
    @ApiModelProperty(value = "服务费用（单位：$）")
    private BigDecimal servicePrice;

    /**
     * 视频订单自动完成时间
     */
    @ApiModelProperty(value = "视频订单自动完成时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date autoCompleteTime;

    /**
     * 视频订单最后变更时间
     */
    @ApiModelProperty(value = "视频订单最后变更时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date lastChangeTime;

    /**
     * 创建订单用户id
     */
    @ApiModelProperty(value = "创建订单用户id")
    @JsonIgnore
    private Long createOrderUserId;

    /**
     * 创建订单用户账号
     */
    @ApiModelProperty(value = "创建订单用户账号")
    private String createOrderUserAccount;

    /**
     * 创建订单用户名称
     */
    @ApiModelProperty(value = "创建订单用户名称")
    private String createOrderUserName;

    /**
     * 创建订单用户微信名称
     */
    @ApiModelProperty(value = "创建订单用户微信名称")
    private String createOrderUserNickName;

    /**
     * 实物（0:是,1:不是）
     */
    @ApiModelProperty(value = "实物（0:是,1:不是）", notes = "0:是,1:不是")
    private Integer isObject;

    /**
     * 订单进入待确认的时间
     */
    @ApiModelProperty(value = "订单进入待确认的时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date unConfirmTime;

    /**
     * 订单退款信息
     */
    @ApiModelProperty(value = "订单退款信息")
    private OrderVideoRefundSimpleVO orderVideoRefund;

    /**
     * 售后单状态
     */
    @ApiModelProperty(value = "售后单状态（1：待处理，4：已完成）")
    private Integer afterSaleTaskStatus;

    /**
     * 工单状态
     */
    @ApiModelProperty(value = "工单状态（1：待处理，4：已完成）")
    private Integer workOrderTaskStatus;

    /**
     * 订单参与的活动以及对应的优惠
     */
    @ApiModelProperty(value = "订单参与的活动以及对应的优惠")
    private List<OrderDiscountDetailVO> orderDiscountDetailVOS;

    /**
     * 是通品（1：是，0：不是）
     */
    @ApiModelProperty(value = "是通品（1：是，0：不是）")
    private Integer isGund;
}
