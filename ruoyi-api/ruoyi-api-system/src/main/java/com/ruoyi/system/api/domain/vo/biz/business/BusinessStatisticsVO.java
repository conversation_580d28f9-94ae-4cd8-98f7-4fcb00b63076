package com.ruoyi.system.api.domain.vo.biz.business;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-24 19:06
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "商家统计数据")
public class BusinessStatisticsVO implements Serializable {
    private static final long serialVersionUID = 9111542444242304540L;

    @ApiModelProperty(value = "商家总数")
    private Integer businessTotal;

    @ApiModelProperty(value = "历史会员总数")
    private Integer historyMemberTotal;

    @ApiModelProperty(value = "当前会员数")
    private Integer memberTotal;

    @ApiModelProperty(value = "已到期会员数")
    private Integer expireMemberTotal;

    @ApiModelProperty(value = "视频总数量")
    private Integer orderVideoTotal;

    @ApiModelProperty(value = "近30天排单量")
    private Integer recentOrderTotal;

    @ApiModelProperty(value = "待完成订单")
    private Integer preFinishOrderTotal;

}
