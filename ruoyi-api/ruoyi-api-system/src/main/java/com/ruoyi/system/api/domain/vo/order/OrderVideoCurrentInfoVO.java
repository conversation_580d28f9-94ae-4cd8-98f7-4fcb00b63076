package com.ruoyi.system.api.domain.vo.order;

import com.ruoyi.system.api.domain.dto.order.VideoContentDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/28 15:16
 */
@Data
public class OrderVideoCurrentInfoVO implements Serializable {
    private static final long serialVersionUID = 192913249196213713L;
    private Long id;
    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)", notes = "0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private Integer platform;

    /**
     * 视频格式（1:横屏16：9,2:竖屏9：16）
     */
    @ApiModelProperty(value = "视频格式（1:横屏16：9,2:竖屏9：16）", notes = "1:横屏16：9,2:竖屏9：16")
    private Integer videoFormat;

    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", notes = "1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国")
    private Integer shootingCountry;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人")
    private Integer modelType;

    /**
     * 产品中文名
     */
    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    private String productLink;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 参考视频链接
     */
    @ApiModelProperty(value = "参考视频链接")
    private String referenceVideoLink;

    /**
     * 意向模特id
     */
    @ApiModelProperty(value = "意向模特id")
    private Long intentionModelId;

    /**
     * 意向模特名称
     */
    @ApiModelProperty(value = "意向模特名称")
    private String intentionModelName;

    /**
     * 视频时长
     */
    @ApiModelProperty(value = "视频时长")
    private Integer videoDuration;

    /**
     * 拍摄建议（原拍摄要求）
     */
    @ApiModelProperty("拍摄建议（原拍摄要求）")
    private List<VideoContentDTO> shootRequired;

    /**
     * 注意事项 图片
     */
    @ApiModelProperty("注意事项 图片")
    private List<String> cautionsPics;

    /**
     * 模特要求（原匹配模特注意事项）
     */
    @ApiModelProperty("模特要求（原匹配模特注意事项）")
    private List<VideoContentDTO> cautions;

    /**
     * 剪辑要求
     */
    @ApiModelProperty(value = "剪辑要求")
    private List<VideoContentDTO> clipsRequired;

    @ApiModelProperty(value = "商品规格要求")
    private String orderSpecificationRequire;

    @ApiModelProperty(value = "特别强调")
    private String particularEmphasis;

    @ApiModelProperty(value = "特别强调图片")
    private List<String> particularEmphasisPic;
    /**
     * 产品品类
     */
    @ApiModelProperty(value = "产品品类")
    private List<Long> productCategory;

    /**
     * 产品品类名称
     */
    @ApiModelProperty(value = "产品品类名称")
    private String productCategoryName;

    /**
     * 参考图片
     */
    private String referencePicId;

    /**
     * 参考图片
     */
    @ApiModelProperty(value = "参考图片")
    private List<String> referencePic;

    @ApiModelProperty(value = "产品卖点")
    private String sellingPointProduct;

    @ApiModelProperty(value = "视频风格(0:Amazon,1:tiktok,2:APP/解说类)")
    private Integer videoStyle;
}
