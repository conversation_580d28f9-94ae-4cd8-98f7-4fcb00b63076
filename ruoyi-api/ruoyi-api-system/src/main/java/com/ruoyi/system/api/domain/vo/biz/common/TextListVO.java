package com.ruoyi.system.api.domain.vo.biz.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:25
 */
@Data
public class TextListVO implements Serializable {

    private static final long serialVersionUID = 75386184077628747L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 文本名称
     */
    @ApiModelProperty(value = "文本名称")
    private String name;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否可删除（0:可以,1:不可以）
     */
    @ApiModelProperty(value = "是否可删除（0:可以,1:不可以）")
    private Integer canDelete;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
