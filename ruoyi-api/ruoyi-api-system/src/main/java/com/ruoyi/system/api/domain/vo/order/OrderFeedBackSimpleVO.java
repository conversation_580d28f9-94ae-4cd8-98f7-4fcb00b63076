package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.vo.UserVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 订单反馈表(商家)
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Data
public class OrderFeedBackSimpleVO implements Serializable {
    private static final long serialVersionUID = 2332732541077906788L;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    /**
     * 成品url
     */
    @ApiModelProperty("成品url")
    private String url;

    @ApiModelProperty("反馈类型（1:视频,2:视频和照片,3:照片）")
    private Integer type;

    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    private UserVO createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 是否最新反馈
     */
    @ApiModelProperty(value = "是否最新反馈")
    private Boolean isNew = false;

    /**
     * 关联的任务单
     */
    @ApiModelProperty("关联的任务单")
    private List<OrderVideoTaskDetailSimpleVO> orderVideoTaskDetailSimpleVOS;
}
