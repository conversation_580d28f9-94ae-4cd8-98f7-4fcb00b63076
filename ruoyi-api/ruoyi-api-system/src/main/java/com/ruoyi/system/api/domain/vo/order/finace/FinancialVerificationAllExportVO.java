package com.ruoyi.system.api.domain.vo.order.finace;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.text.StrPool;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 财务对账
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FinancialVerificationAllExportVO implements Serializable {

    private static final long serialVersionUID = -8080412922921954285L;

    @ApiModelProperty(value = "视频订单id")
    @JsonIgnore
    private Long videoId;

    @Excel(name = "会员编码")
    private String merchantCode;

    @Excel(name = "是否代理", readConverterExp = "0=否,1=是", combo = {"是","否"})
    private Integer isProxy;

    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    @Excel(name = "支付时间/需求提交时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @Excel(name = "订单号")
    private String orderNum;

    @Excel(name = "支付号")
    private String payNum;

    @Excel(name = "交易流水号")
    private String mchntOrderNo;

    @Excel(name = "视频编码")
    private String videoCode;

    @Excel(name = "产品中文名")
    private String productChinese;

    @Excel(name = "产品英文名")
    private String productEnglish;

    @ApiModelProperty(value = "拍摄模特id")
    @JsonIgnore
    private Long shootModelId;

    @Excel(name = "拍摄模特")
    private String shootModelName;

    @Excel(name = "订单状态", readConverterExp = "1=待支付,2=待审核,3=待确认,4=待匹配,5=需发货,6=待完成,7=需确认,8=已完成,9=交易关闭")
    private Integer status;

    @Excel(name = "首次反馈商家素材时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN)
    private Date needConfirmTime;

    @Excel(name = "最新提交模特时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN)
    private Date lastModelSubmitTime;

    @Excel(name = "支付方式", readConverterExp = "1=微信,2=支付宝,3=云闪付/银联,4=数字人民币,5=银行,6=对公,7=全币种,10=余额,11=微信+余额,12=支付宝+余额,13=云闪付/银联+余额,14=数字人民币+余额,15=银行+余额,16=对公+余额,17=全币种+余额")
    private Integer payType;

    @Excel(name = "视频费用（单位：$）")
    private BigDecimal videoPrice;

    @Excel(name = "照片数量", readConverterExp = "1=2张,2=5张")
    private Integer picCount;

    @Excel(name = "选配费用（单位：$）")
    private BigDecimal picPrice;

    @Excel(name = "PayPal代付手续费（单位：$）")
    private BigDecimal exchangePrice;

    @Excel(name = "佣金代缴税费（单位：$）")
    private BigDecimal commissionPaysTaxes;

    @Excel(name = "服务费（单位：$）")
    private BigDecimal servicePrice;

    @Excel(name = "百度汇率")
    private BigDecimal currentExchangeRate;

    @Excel(name = "订单总价")
    private BigDecimal amount;

    @Excel(name = "限时满减活动", defaultValue = StrPool.DASHED)
    private BigDecimal fullConcession;

    /**
     * 每月首单立减优惠方案
     */
    @Excel(name = "每月首单立减优惠方案", defaultValue = StrPool.DASHED)
    private String monthFirstOrderDiscount;

    /**
     * 每月首单立减金额
     */
    @Excel(name = "每月首单立减金额", defaultValue = StrPool.DASHED)
    private BigDecimal monthFirstOrderDiscountActivity;

    /**
     * 订单原价 - 差额 - 优惠金额
     */
    @Excel(name = "实付金额")
    private BigDecimal realAmount;

//    @Excel(name = "需支付金额（单位：￥）")
    private BigDecimal payAmount;

//    @Excel(name = "需支付金额（单位：$）")
    private BigDecimal payAmountDollar;

    @Excel(name = "余额支付")
    private BigDecimal useBalance;

//    @Excel(name = "差额")
    private BigDecimal differenceAmount;

    // @Excel(name = "实际支付")
    private BigDecimal realPayAmount;



    @ApiModelProperty(value = "币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

    @Excel(name = "余额退款")
    private BigDecimal balanceRefund;

    @Excel(name = "其他退款金额")
    private BigDecimal otherRefund;

    @Excel(name = "提现金额")
    private BigDecimal withdrawAmount;

    @Excel(name = "发票号")
    private String invoiceNumber;

    @ApiModelProperty(value = "商家id")
    private Long merchantId;

}
