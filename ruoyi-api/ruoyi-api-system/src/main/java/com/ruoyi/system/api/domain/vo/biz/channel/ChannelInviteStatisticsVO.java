package com.ruoyi.system.api.domain.vo.biz.channel;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-29 15:57
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChannelInviteStatisticsVO implements Serializable {
    private static final long serialVersionUID = -726638003545246779L;

    @ApiModelProperty("注册用户数")
    private Integer registerNum;

    @ApiModelProperty("会员数量")
    private Integer memberNum;

    @ApiModelProperty("会员金额")
    private BigDecimal realPayAmount;
}
