package com.ruoyi.system.api.domain.vo.order.finace;

import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :视频订单导出
 * @create :2025-01-07 16:02
 **/
@Data
public class OrderVideoExportVO implements Serializable {
    private static final long serialVersionUID = 1415368739286917617L;
    @Excel(name = "视频费用（单位：$）")
    private BigDecimal videoPrice;

    @Excel(name = "选配费用（单位：$）")
    private BigDecimal picPrice;

    @Excel(name = "照片数量", readConverterExp = "1=2张,2=5张")
    private Integer picCount;

    @Excel(name = "PayPal代付手续费（单位：$）")
    private BigDecimal exchangePrice;

    @Excel(name = "佣金代缴税费（单位：$）")
    private BigDecimal commissionPaysTaxes;

    @Excel(name = "服务费（单位：$）")
    private BigDecimal servicePrice;

    @Excel(name = "百度汇率")
    private BigDecimal currentExchangeRate;

    @Excel(name = "订单总价")
    private BigDecimal amount;
}
