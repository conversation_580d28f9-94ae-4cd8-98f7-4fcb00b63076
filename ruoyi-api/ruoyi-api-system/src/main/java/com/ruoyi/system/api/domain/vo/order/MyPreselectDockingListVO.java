
package com.ruoyi.system.api.domain.vo.order;

import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/18 9:55
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MyPreselectDockingListVO extends OrderPoolListVO {


    private static final long serialVersionUID = 2394036128343692828L;
    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    @Excel(name = "模特佣金")
    private BigDecimal commission;

    /**
     * 携带类型（1:主携带,2:被携带）
     */
    @ApiModelProperty(value = "携带类型（1:主携带,2:被携带）")
    private Integer carryType;

    /**
     * 历史预选模特数量
     */
    @ApiModelProperty(value = "历史预选模特数量")
    private Integer historyPreselectModelCount;

}
