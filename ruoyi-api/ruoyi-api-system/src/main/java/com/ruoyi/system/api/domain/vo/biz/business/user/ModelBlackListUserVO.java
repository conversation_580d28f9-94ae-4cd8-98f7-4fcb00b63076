package com.ruoyi.system.api.domain.vo.biz.business.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :模特被拉黑账号集合
 * @create :2025-01-09 18:13
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ModelBlackListUserVO implements Serializable {

    @ApiModelProperty("模特ID")
    private Long modelId;

    @ApiModelProperty("主键")
    private Long bizUserId;

    @ApiModelProperty("员工名称")
    private String name;

    @ApiModelProperty("微信昵称")
    private String nickName;

    @ApiModelProperty("头像")
    private String pic;



}
