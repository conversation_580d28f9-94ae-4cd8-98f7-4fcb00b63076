package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/1/7 20:54
 */
@Data
public class ToBeRedInvoiceListVO implements Serializable {
    private static final long serialVersionUID = -5045457121804222328L;

    /**
     * 发票红冲ID
     */
    @ApiModelProperty(value = "发票红冲ID")
    private Long id;

    /**
     * 发票ID
     */
    @ApiModelProperty(value = "发票ID")
    private Long invoiceId;

    /**
     * 申票码
     */
    @ApiModelProperty(value = "申票码")
    private String ticketCode;

    @ApiModelProperty(value = "订单类型（0-视频订单，1-会员订单，5-线上钱包充值）")
    private String orderType;

    /**
     * 发票号
     */
    @ApiModelProperty(value = "发票号")
    private String number;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private Set<String> videoCodes;

    /**
     * 商家编码
     */
    @ApiModelProperty(value = "商家编码")
    private String merchantCode;

    /**
     * 发票类型（1：增值税普通发票，2：形式发票）
     */
    @ApiModelProperty(value = "发票类型（1：增值税普通发票，2：形式发票）")
    private Integer invoiceType;

    /**
     * 抬头类型（1：企业单位）
     */
    @ApiModelProperty(value = "抬头类型（1：企业单位）")
    private Integer titleType;

    /**
     * 发票抬头
     */
    @ApiModelProperty(value = "发票抬头")
    private String title;

    /**
     * 税号
     */
    @ApiModelProperty(value = "税号")
    private String dutyParagraph;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 公司地址
     */
    @ApiModelProperty(value = "公司地址")
    private String companyAddress;

    /**
     * 公司联系电话
     */
    @ApiModelProperty(value = "公司联系电话")
    private String companyPhone;

    /**
     * 公司联系人
     */
    @ApiModelProperty(value = "公司联系人")
    private String companyContact;

    /**
     * 附件URI
     */
    @ApiModelProperty(value = "附件URI")
    private String attachmentObjectKey;

    /**
     * 发票内容
     */
    @ApiModelProperty(value = "发票内容")
    private String content;

    /**
     * 发票备注
     */
    @ApiModelProperty(value = "发票备注")
    private String invoiceRemark;

    /**
     * 注意事项
     */
    @ApiModelProperty(value = "注意事项")
    private String cautions;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 红冲原因（1：重开发票，2：商家提现）
     */
    @ApiModelProperty(value = "红冲原因（1：重开发票，2：商家提现）")
    private Integer invoiceRedCause;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date applyTime;

    /**
     * 红冲状态（1：待红冲，2：不红冲，3：已红冲）
     */
    @ApiModelProperty(value = "红冲状态（1：待红冲，2：不红冲，3：已红冲）")
    private Integer invoiceRedStatus;
}
