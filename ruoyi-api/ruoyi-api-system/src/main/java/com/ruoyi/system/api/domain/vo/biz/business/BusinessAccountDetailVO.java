package com.ruoyi.system.api.domain.vo.biz.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家账号表
 * <AUTHOR>
 * @TableName business_account
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessAccountDetailVO implements Serializable {

    private static final long serialVersionUID = 5765176570342930295L;
    @NotNull(message="[主键ID]不能为空")
    @ApiModelProperty("主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @NotNull(message="[账号]不能为空")
    @ApiModelProperty("账号")
    private String account;

    @NotNull(message="[商家id]不能为空")
    @ApiModelProperty("商家id")
    private Long businessId;

    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("名称")
    @Length(max= 20,message="编码长度不能超过20")
    private String name;

    @Size(max = 32, message = "微信昵称不能超过32个字")
    @ApiModelProperty("微信昵称")
    @Length(max = 32, message = "微信昵称不能超过32个字")
    private String nickName;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("种草官ID")
    private String seedId;

    @Size(max= 60,message="编码长度不能超过60")
    @ApiModelProperty("头像")
    @Length(max= 60,message="编码长度不能超过60")
    private String pic;

    @NotNull(message="[账号状态（0正常 1禁用）]不能为空")
    @ApiModelProperty("账号状态（0正常 1禁用）")
    private Integer status;

    @ApiModelProperty("最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    @NotNull(message="[商家主账号（FK：business_account.account）]不能为空")
    @ApiModelProperty("商家主账号（FK：business_account.account）")
    private String ownerAccount;

    @ApiModelProperty("是否主账号：0-否 1-是")
    private Integer isOwnerAccount;

    @NotBlank(message="[商家名称]不能为空")
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("商家名称")
    @Length(max= 20,message="编码长度不能超过20")
    private String businessName;
    /**
     * 是否为代理(0:否,1:是)
     */
    @NotNull(message="[是否为代理(0:否,1:是)]不能为空")
    @ApiModelProperty("是否为代理(0:否,1:是)")
    private Integer isProxy;

    @ApiModelProperty("是否展示手机号(0:否,1:是)")
    private Integer phoneVisible;
    /**
     * 账号状态（0正常 1禁用）
     */
    @NotNull(message="[账号状态（0正常 1禁用）]不能为空")
    @ApiModelProperty("账号状态（0正常 1禁用）")
    private Integer businessStatus;
    /**
     * 客户类型 （0-一般客户 1-重要客户）
     */
    @ApiModelProperty("客户类型 （0-一般客户 1-重要客户）")
    private Integer customerType;
    /**
     * 帐号余额
     */
    @ApiModelProperty("帐号余额")
    private BigDecimal balance;

    @ApiModelProperty("使用余额")
    private BigDecimal useBalance;

    @ApiModelProperty("余额是否锁定（0-不锁定，1-锁定）")
    private Integer isBalanceLock;

    /**
     * 对接客服  FK：sys_user.user_id
     */
    @NotNull(message="[对接客服  FK：sys_user.user_id]不能为空")
    @ApiModelProperty("对接客服  FK：sys_user.user_id")
    private Long waiterId;
    /**
     * 抬头类型  0-企业
     */
    @ApiModelProperty("抬头类型  0-企业")
    private Integer invoiceTitleType;
    /**
     * 发票抬头
     */
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("发票抬头")
    @Length(max= 50,message="编码长度不能超过50")
    private String invoiceTitle;
    /**
     * 税号
     */
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("税号")
    @Length(max= 50,message="编码长度不能超过50")
    private String invoiceDutyParagraph;
    /**
     * 发票内容
     */
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("发票内容")
    @Length(max= 50,message="编码长度不能超过50")
    private String invoiceContent;
    /**
     * 会员编码
     */
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("会员编码")
    @Length(max= 10,message="编码长度不能超过10")
    private String memberCode;
    /**
     * 会员类型: 0-非会员，1-会员
     */
    @NotNull(message="[会员类型: 0-非会员，1-会员]不能为空")
    @ApiModelProperty("会员类型: 0-非会员，1-会员")
    private Integer memberType;
    /**
     * 会员状态：0-非会员1-正常，2-即将过期，3-已过期
     */
    @NotNull(message="[会员状态：0-非会员1-正常，2-即将过期，3-已过期]不能为空")
    @ApiModelProperty("会员状态：0-非会员1-正常，2-即将过期，3-已过期")
    private Integer memberStatus;
    /**
     * 会员套餐名称
     */
    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberPackageType;
    /**
     * 会员首次购买时间
     */
    @ApiModelProperty("会员首次购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date memberFirstTime;

    @ApiModelProperty("首次购买套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberFirstType;
    /**
     * 会员最近购买时间
     */
    @ApiModelProperty("会员最近购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date memberLastTime;
    /**
     * 会员有效期
     */
    @ApiModelProperty("会员有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date memberValidity;

    @ApiModelProperty("商家注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date businessCreateTime;

    @ApiModelProperty("登录账号ID")
    private Long bizUserId;
}