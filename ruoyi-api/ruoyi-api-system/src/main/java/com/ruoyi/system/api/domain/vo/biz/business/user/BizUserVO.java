package com.ruoyi.system.api.domain.vo.biz.business.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-29 09:48
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BizUserVO implements Serializable {
    private static final long serialVersionUID = 4382092324190032542L;

    @NotNull(message="[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotBlank(message="[员工名称]不能为空")
    @ApiModelProperty("员工名称")
    private String name;

//    @NotBlank(message="[微信昵称]不能为空")
    @Size(max = 32, message = "微信昵称不能超过32个字")
    @ApiModelProperty("微信昵称")
    @Length(max = 32, message = "微信昵称不能超过32个字")
    private String nickName;

    
    @Size(max= 500,message="编码长度不能超过500")
    @ApiModelProperty("头像")
    @Length(max= 500,message="编码长度不能超过500")
    private String pic;

    @NotBlank(message="[手机号]不能为空")
    @Size(max= 15,message="编码长度不能超过15")
    @ApiModelProperty("手机号")
    @Length(max= 15,message="编码长度不能超过15")
    private String phone;

    @ApiModelProperty("是否为代理(0:否,1:是)")
    private Integer isProxy;

    @ApiModelProperty("客户类型 （0-一般客户 1-重要客户）")
    private Integer customerType;

    @ApiModelProperty("商家名称")
    private String businessName;

    @ApiModelProperty("对接客服")
    private Long waiterId;

    @NotNull(message="[账号状态（0正常 1禁用 2被删除）]不能为空")
    @ApiModelProperty("账号状态（0正常 1禁用 2被删除）")
    private Integer status;

    @ApiModelProperty("最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    @NotNull(message="[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @NotNull(message="[修改时间]不能为空")
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @NotNull(message="[是否删除（0-未删除，1-已删除）]不能为空")
    @ApiModelProperty("是否删除（0-未删除，1-已删除）")
    private Integer isDel;


}
