package com.ruoyi.system.api.domain.vo.pay;

import cn.hutool.crypto.SecureUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * 支付回调数据
 *
 * <AUTHOR>
 * @date 2024/6/1
 */
@Data
public class PayCallBackResponseVo implements Serializable {
    private static final long serialVersionUID = 5936793585780050285L;
    private String curr_type; // 货币种类
    private String full_sign; // 全签名
    private String mchnt_cd; // 富友分配的商户号
    private String mchnt_order_no; // 商户订单号
    private String order_amt; // 订单金额
    /**
     * 支付平台枚举
     *
     * @see com.ruoyi.common.core.enums.PayPlatformTypeEnum
     */
    private String order_type; // 订单类型：ALIPAY, WECHAT, UNIONPAY(银联二维码)等
    private String random_str; // 随机字符串
    private String reserved_addn_inf;
    private String reserved_bank_type; // 付款方式
    private String reserved_buyer_logon_id; // 买家登录账号
    private String reserved_channel_order_id; // 银行交易号
    private String reserved_coupon_fee; // 优惠金额
    private String reserved_fund_bill_list; // 渠道信息
    private String reserved_fy_settle_dt; // 富友清算日
    private String reserved_fy_trace_no; // 富友追踪号
    private String reserved_is_credit; // 信用卡标识
    private String reserved_promotion_detail; // 微信营销详情
    private String reserved_settlement_amt; // 应结算订单金额
    private String result_code; // 响应代码
    private String result_msg; // 中文描述
    private String settle_order_amt; // 应结订单金额
    private String sign; // 签名
    private String term_id; // 终端号
    private String transaction_id; // 渠道流水号
    private String txn_fin_ts; // 支付完成时间
    private String user_id; // 用户在商户的ID

    public boolean checkSign(String key) {
        String shortSignLocal = SecureUtil.md5(mchnt_cd + "|"
                + mchnt_order_no + "|"
                + settle_order_amt + "|"
                + order_amt + "|"
                + txn_fin_ts + "|"
                + reserved_fy_settle_dt + "|"
                + random_str + "|"
                + key);
        String fullSignLocal = SecureUtil.md5(result_code + "|"
                + result_msg + "|"
                + mchnt_cd + "|"
                + mchnt_order_no + "|"
                + settle_order_amt + "|"
                + order_amt + "|"
                + txn_fin_ts + "|"
                + reserved_fy_settle_dt + "|"
                + random_str + "|"
                + key);
        return fullSignLocal.equals(full_sign) && shortSignLocal.equals(sign);
    }

    @Override
    public String toString() {
        return "PayCallBackResponseVo{" +
                "curr_type='" + curr_type + '\'' +
                ", full_sign='" + full_sign + '\'' +
                ", mchnt_cd='" + mchnt_cd + '\'' +
                ", mchnt_order_no='" + mchnt_order_no + '\'' +
                ", order_amt='" + order_amt + '\'' +
                ", order_type='" + order_type + '\'' +
                ", random_str='" + random_str + '\'' +
                ", reserved_addn_inf='" + reserved_addn_inf + '\'' +
                ", reserved_bank_type='" + reserved_bank_type + '\'' +
                ", reserved_buyer_logon_id='" + reserved_buyer_logon_id + '\'' +
                ", reserved_channel_order_id='" + reserved_channel_order_id + '\'' +
                ", reserved_coupon_fee='" + reserved_coupon_fee + '\'' +
                ", reserved_fund_bill_list='" + reserved_fund_bill_list + '\'' +
                ", reserved_fy_settle_dt='" + reserved_fy_settle_dt + '\'' +
                ", reserved_fy_trace_no='" + reserved_fy_trace_no + '\'' +
                ", reserved_is_credit='" + reserved_is_credit + '\'' +
                ", reserved_promotion_detail='" + reserved_promotion_detail + '\'' +
                ", reserved_settlement_amt='" + reserved_settlement_amt + '\'' +
                ", result_code='" + result_code + '\'' +
                ", result_msg='" + result_msg + '\'' +
                ", settle_order_amt='" + settle_order_amt + '\'' +
                ", sign='" + sign + '\'' +
                ", term_id='" + term_id + '\'' +
                ", transaction_id='" + transaction_id + '\'' +
                ", txn_fin_ts='" + txn_fin_ts + '\'' +
                ", user_id='" + user_id + '\'' +
                '}';
    }
}
