package com.ruoyi.system.api.domain.vo.biz.business;

import com.ruoyi.common.core.enums.WxChatLoginStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PhoneLoginVO implements Serializable {

    private static final long serialVersionUID = 2776624333590213546L;

    @ApiModelProperty("登录状态")
    WxChatLoginStatusEnum loginStatus;

    @ApiModelProperty("账号信息")
    BusinessAccountVO businessAccountVO;

    @ApiModelProperty("二维码链接")
    private String qrcode;

    @ApiModelProperty("查询ticket")
    private String ticket;

    @ApiModelProperty("meg")
    private String msg;

    @ApiModelProperty("token")
    String token;


    @ApiModelProperty("跳转类型 1:跳转至链接,2:展示二维码")
    Integer type;

    @ApiModelProperty("企业微信链接")
    String url;
}
