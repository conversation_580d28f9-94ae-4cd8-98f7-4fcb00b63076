package com.ruoyi.system.api.domain.vo.biz.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :模特家庭VO
 * @create :2024-11-28 10:02
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ModelFamilyVO extends ModelBaseVO implements Serializable {
    private static final long serialVersionUID = -1676339088391075617L;

    @ApiModelProperty(value = "亲属关系(0=发起人,1=母子,2=母女,3=夫妻,4=父子,5=父女,6=兄弟,7=姐妹,8=兄妹,9=姐弟")
    private Integer modelFamilyRelationship;

    @ApiModelProperty(value = "是否家庭模特发起者：0-否,1-是")
    private Integer isInitiator;

    @ApiModelProperty(value = "模特家庭ID")
    private Long familyId;

    @ApiModelProperty(value = "合作深度（0:一般模特,1:优质模特,2:中度模特）", notes = "0:一般模特,1:优质模特,2:中度模特")
    private Integer cooperation;

    /**
     * 模特评分 (0.0-10.0)
     */
    @ApiModelProperty(value = "模特评分 (0.0-10.0)", notes = "模特评分 (0.0-10.0)", required = true)
    private BigDecimal cooperationScore;

    @ApiModelProperty(value = "模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)")
    private Integer status;
}
