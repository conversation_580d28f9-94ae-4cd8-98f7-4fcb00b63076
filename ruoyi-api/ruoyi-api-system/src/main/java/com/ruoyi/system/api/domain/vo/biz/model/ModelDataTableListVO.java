package com.ruoyi.system.api.domain.vo.biz.model;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/7 9:28
 */
@Data
public class ModelDataTableListVO implements Serializable {
    private static final long serialVersionUID = 6501258575694861905L;

    /**
     * 模特ID
     */
    @ApiModelProperty(value = "模特ID")
    private Long id;

    /**
     * 模特账号
     */
    @ApiModelProperty(value = "模特账号")
    private String account;

    /**
     * 模特图片URI
     */
    @ApiModelProperty(value = "模特图片URI")
    private String modelPic;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date createTime;

    /**
     * 模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)
     */
    @ApiModelProperty(value = "模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)")
    private Integer status;

    /**
     * 关联客服ID
     */
    @JsonIgnore
    private Long serviceId;

    /**
     * 关联客服姓名
     */
    @ApiModelProperty(value = "关联客服姓名")
    private String serviceName;

    /**
     * 开发人ID
     */
    @JsonIgnore
    private Long developerId;

    /**
     * 开发人姓名
     */
    @ApiModelProperty(value = "开发人姓名")
    private String developerName;

    /**
     * 性别(1:男,0:女)
     */
    @ApiModelProperty(value = "性别")
    private Integer sex;

    /**
     * 国家"（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）"
     */
    @ApiModelProperty(value = "国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    private Integer nation;

    /**
     * 平台(0:Amazon,1:tiktok,2:其他)
     */
    @ApiModelProperty(value = "平台0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private String platform;

    /**
     * 模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "模特类型0:影响者,1:素人")
    private Integer type;

    /**
     * 年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）
     */
    @ApiModelProperty(value = "年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）")
    private Integer ageGroup;

    /**
     * 合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "合作深度0:一般模特,1:优质模特,2:中度模特")
    private Integer cooperation;

    /**
     * 模特评分 (0.0-10.0)
     */
    @ApiModelProperty(value = "模特评分 (0.0-10.0)")
    private BigDecimal cooperationScore;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    private BigDecimal commission;

    /**
     * 佣金转化为美金单位
     */
    @JsonIgnore
    private BigDecimal commissionSort;

    /**
     * 案例数
     */
    @ApiModelProperty(value = "案例数")
    private Long caseCount;

    /**
     * 案例数最后更新时间
     */
    @ApiModelProperty(value = "案例数最后更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date caseCountUpdateTime;

    /**
     * 案例数预警
     */
    @ApiModelProperty(value = "案例数预警")
    private Integer caseCountEarlyWarn;

    /**
     * 标签数
     */
    @ApiModelProperty(value = "标签数")
    private Long tagCount;

    /**
     * 标签数最后更新时间
     */
    @ApiModelProperty(value = "标签数最后更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date tagCountUpdateTime;

    /**
     * 标签数预警
     */
    @ApiModelProperty(value = "标签数预警")
    private Integer tagCountEarlyWarn;

    /**
     * 品类数
     */
    @ApiModelProperty(value = "品类数")
    private Long categoryCount;

    /**
     * 品类数最后更新时间
     */
    @ApiModelProperty(value = "品类数最后更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date categoryCountUpdateTime;

    /**
     * 品类数预警
     */
    @ApiModelProperty(value = "品类数预警")
    private Integer categoryCountEarlyWarn;

    /**
     * 有蜗牛照（1：有，0：没有）
     */
    @ApiModelProperty(value = "有蜗牛照（1：有，0：没有）")
    private Integer haveSnailPic;

    /**
     * 家庭成员数
     */
    @ApiModelProperty(value = "家庭成员数")
    private Long familyMemberCount;

    /**
     * 收藏数
     */
    @ApiModelProperty(value = "收藏数")
    private Long collectCount;

    /**
     * 被拉黑数
     */
    @ApiModelProperty(value = "被拉黑数")
    private Long blacklistCount;

    /**
     * 提交模特给商家的来源为商家意向的次数
     */
    @ApiModelProperty(value = "提交模特给商家的来源为商家意向的次数")
    private Long submitIntentionCount;

    /**
     * 商家意向次数
     */
    @ApiModelProperty(value = "商家意向次数")
    private Long intentionCount;

    /**
     * 意向接单率
     */
    @ApiModelProperty(value = "意向接单率")
    private BigDecimal intentionOrderRate;

    /**
     * 意向接单率预警
     */
    @ApiModelProperty(value = "意向接单率预警")
    private Integer intentionOrderRateEarlyWarn;

    /**
     * 提交模特给商家的来源为客服添加的次数
     */
    @ApiModelProperty(value = "提交模特给商家的来源为客服添加的次数")
    private Long submitPreSelectCount;

    /**
     * 客服预选次数
     */
    @ApiModelProperty(value = "客服预选次数")
    private Long preSelectCount;

    /**
     * 预选接单率
     */
    @ApiModelProperty(value = "预选接单率")
    private BigDecimal preSelectOrderRate;

    /**
     * 预选接单率预警
     */
    @ApiModelProperty(value = "预选接单率预警")
    private Integer preSelectOrderRateEarlyWarn;

    /**
     * 提交模特给商家的来源为客服分发的次数
     */
    @ApiModelProperty(value = "提交模特给商家的来源为客服分发的次数")
    private Long submitDispatchCount;

    /**
     * 客服分发次数
     */
    @ApiModelProperty(value = "客服分发次数")
    private Long dispatchCount;

    /**
     * 分发接单率
     */
    @ApiModelProperty(value = "分发接单率")
    private BigDecimal dispatchOrderRate;

    /**
     * 提交模特给商家的来源为MT想要的次数
     */
    @ApiModelProperty(value = "提交模特给商家的来源为MT想要的次数")
    private Long submitSelfSelectCount;

    /**
     * 自选订单次数
     */
    @ApiModelProperty(value = "自选订单次数")
    private Long selfSelectCount;

    /**
     * 自选排单率
     */
    @ApiModelProperty(value = "自选排单率")
    private BigDecimal selfSelectOrderRate;

    /**
     * 自选排单率预警
     */
    @ApiModelProperty(value = "自选排单率预警")
    private Integer selfSelectOrderRateEarlyWarn;

    /**
     * 商家发起变更模特的次数
     */
    @ApiModelProperty(value = "商家发起变更模特的次数")
    private Long changeModelCount;

    /**
     * 提交模特给商家的次数
     */
    @ApiModelProperty(value = "提交模特给商家的次数")
    private Long submitModelCount;

    /**
     * 商家拒绝率
     */
    @ApiModelProperty(value = "商家拒绝率")
    private BigDecimal rejectOrderRate;

    /**
     * 商家拒绝率预警
     */
    @ApiModelProperty(value = "商家拒绝率预警")
    private Integer rejectOrderRateEarlyWarn;

    /**
     * 排单数
     */
    @ApiModelProperty(value = "排单数")
    private Long orderScheduledCount;

    /**
     * 排单数预警
     */
    @ApiModelProperty(value = "排单数预警")
    private Integer orderScheduledCountEarlyWarn;

    /**
     * 待拍数
     */
    @ApiModelProperty(value = "待拍数")
    private Long waitPictureCount;

    /**
     * 待拍数预警
     */
    @ApiModelProperty(value = "待拍数预警")
    private Integer waitPictureCountEarlyWarn;

    /**
     * 反馈数
     */
    @ApiModelProperty(value = "反馈数")
    private Long feedbackCount;

    /**
     * 确认签收后超过20天未反馈或才反馈素材的订单数
     */
    @ApiModelProperty(value = "确认签收后超过20天未反馈或才反馈素材的订单数")
    private Long overtimeCount;

    /**
     * 已确认签收的订单数（待完成、需确认、已完成、交易关闭的订单）
     */
    @ApiModelProperty(value = "已确认签收的订单数（待完成、需确认、已完成、交易关闭的订单）")
    private Long confirmReceiptCount;

    /**
     * 超时率
     */
    @ApiModelProperty(value = "超时率")
    private BigDecimal overtimeRate;

    /**
     * 超时率预警
     */
    @ApiModelProperty(value = "超时率预警")
    private Integer overtimeRateEarlyWarn;

    /**
     * 有发起售后单、补偿订单、取消订单或回退订单的订单数（有多个售后的订单仅算1）
     */
    @ApiModelProperty(value = "有发起售后单、补偿订单、取消订单或回退订单的订单数（有多个售后的订单仅算1）")
    private Long afterSaleCount;

    /**
     * 已反馈素材给商家的订单数（需确认、已完成、交易关闭的订单）
     */
    @ApiModelProperty(value = "已反馈素材给商家的订单数（需确认、已完成、交易关闭的订单）")
    private Long feedbackMaterialsCount;

    /**
     * 售后率
     */
    @ApiModelProperty(value = "售后率")
    private BigDecimal afterSaleRate;

    /**
     * 售后率预警
     */
    @ApiModelProperty(value = "售后率预警")
    private Integer afterSaleRateEarlyWarn;

    /**
     * 完成数
     */
    @ApiModelProperty(value = "完成数")
    private Long completeCount;

    /**
     * 丢件数
     */
    @ApiModelProperty(value = "丢件数")
    private Long dropCount;

    /**
     * 丢件数预警
     */
    @ApiModelProperty(value = "丢件数预警")
    private Integer dropCountEarlyWarn;

    /**
     * 被取消数
     */
    @ApiModelProperty(value = "被取消数")
    private Long cancelCount;

    /**
     * 被取消数预警
     */
    @ApiModelProperty(value = "被取消数预警")
    private Integer cancelCountEarlyWarn;

    /**
     * 被退回数
     */
    @ApiModelProperty(value = "被退回数")
    private Long returnCount;

    /**
     * 被退回预警
     */
    @ApiModelProperty(value = "被退回预警")
    private Integer returnCountEarlyWarn;

    /**
     * 备注信息
     */
    @ApiModelProperty(value = "备注信息")
    private List<ModelDataTableRemarkVO> remarks;

    public void echo() {
        intentionOrderRate = intentionOrderRate == null ? BigDecimal.ZERO : intentionOrderRate.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        preSelectOrderRate = preSelectOrderRate == null ? BigDecimal.ZERO : preSelectOrderRate.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        dispatchOrderRate = dispatchOrderRate == null ? BigDecimal.ZERO : dispatchOrderRate.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        selfSelectOrderRate = selfSelectOrderRate == null ? BigDecimal.ZERO : selfSelectOrderRate.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        rejectOrderRate = rejectOrderRate == null ? BigDecimal.ZERO : rejectOrderRate.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        overtimeRate = overtimeRate == null ? BigDecimal.ZERO : overtimeRate.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        afterSaleRate = afterSaleRate == null ? BigDecimal.ZERO : afterSaleRate.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
    }
}
