package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/11 9:58
 */
@Data
public class BackInvoiceStatisticsVO implements Serializable {
    private static final long serialVersionUID = -5182052086766130875L;

    /**
     * 待开票数量
     */
    @ApiModelProperty(value = "待开票数量")
    private Integer quantityToBeInvoiced;

    /**
     * 待红冲数量
     */
    @ApiModelProperty(value = "待红冲数量")
    private Integer quantityToBeFlushed;
}
