package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单详情（基础信息）
 *
 * <AUTHOR>
 * @date 2024/7/2 9:38
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderModelApplyInfoVO extends OrderModelBaseInfoVO {
    private static final long serialVersionUID = -518019996404349824L;

    /**
     * 预选模特ID
     */
    @ApiModelProperty(value = "预选模特ID")
    private Long preselectModelId;

    /**
     * 能否申请
     */
    @ApiModelProperty(value = "能否申请")
    private boolean canApply;

    /**
     * 能否撤销
     */
    @ApiModelProperty(value = "能否撤销")
    private boolean canCancel;

    /**
     * 能否接单或者拒绝
     */
    @ApiModelProperty(value = "能否接单或者拒绝")
    private boolean canYesOrPass;

    /**
     * 模特接单状态（1：等待审核，2：您已拒绝，3：订单已被其他模特选定，4：已确认拍摄）
     */
    @ApiModelProperty(value = "模特接单状态（1：等待审核，2：您已拒绝，3：订单已被其他模特选定，4：已确认拍摄）")
    private Integer operateStatus;
}
