package com.ruoyi.system.api.domain.vo.order.datastatistics;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/4 11:03
 */
@Data
public class OrderVideoBaseBoardVO implements Serializable {
    private static final long serialVersionUID = 4486940888546456888L;

    /**
     * 实时更新时间
     */
    @ApiModelProperty("实时更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date dateTime;

    /**
     * 记录截止时间
     */
    @ApiModelProperty("记录截止时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date writeTimeEnd;

    /**
     * 视频订单总数
     */
    @ApiModelProperty("视频订单总数")
    private Long orderCount;

    /**
     * 昨日新增视频订单数
     */
    @ApiModelProperty("昨日新增视频订单数")
    private Long addedOrderVideoCount;

    /**
     * 视频任务单数
     */
    @ApiModelProperty("视频任务单数")
    private Long taskCount;

    /**
     * 昨日新增视频任务单数
     */
    @ApiModelProperty("昨日新增视频任务单数")
    private Long addedOrderVideoTaskCount;

    public void echo() {
        orderCount = orderCount == null ? 0L : orderCount;
        addedOrderVideoCount = addedOrderVideoCount == null ? 0L : addedOrderVideoCount;
        taskCount = taskCount == null ? 0L : taskCount;
        addedOrderVideoTaskCount = addedOrderVideoTaskCount == null ? 0L : addedOrderVideoTaskCount;
    }
}
