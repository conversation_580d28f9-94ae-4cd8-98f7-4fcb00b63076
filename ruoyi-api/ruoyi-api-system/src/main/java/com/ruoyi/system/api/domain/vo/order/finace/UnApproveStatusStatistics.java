package com.ruoyi.system.api.domain.vo.order.finace;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :未审核数据统计
 * @create :2024-12-17 14:51
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UnApproveStatusStatistics implements Serializable {
    private static final long serialVersionUID = 368241677570931087L;

    @ApiModelProperty(value = "视频订单统计")
    private Integer videoCount;

    @ApiModelProperty(value = "会员订单统计")
    private Integer memberCount;

    @ApiModelProperty(value = "预付款统计")
    private Integer prepayCount;
}
