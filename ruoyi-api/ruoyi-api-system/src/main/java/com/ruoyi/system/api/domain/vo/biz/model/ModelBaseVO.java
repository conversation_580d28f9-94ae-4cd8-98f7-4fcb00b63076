package com.ruoyi.system.api.domain.vo.biz.model;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.enums.ModelTagEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/5
 */
@Data
public class ModelBaseVO implements Serializable {
    private static final long serialVersionUID = -4220339161994388624L;
    /**
     * 模特id
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 模特账号
     */
    @ApiModelProperty(value = "模特账号")
    private String account;

    /**
     * 模特账号
     */
    @ApiModelProperty(value = "模特登录账号")
    private String loginAccount;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 性别(0:男,1:女)
     */
    @ApiModelProperty(value = "性别（1:男,0:女）", notes = "1:男,0:女")
    private Integer sex;
    /**
     * 年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）
     */
    @ApiModelProperty(value = "年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）", notes = "1:婴幼儿,2:儿童,3:成年人,4:老年人")
    private Integer ageGroup;

    /**
     * 模特图片URI
     */
    @ApiModelProperty(value = "模特图片URI")
    private String modelPic;

    /**
     * 平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "平台（0:Amazon,1:tiktok,2:其他,3:APP/解说类）", notes = "0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private String platform;

    /**
     * 模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人")
    private Integer type;

    /**
     * 国家"（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）"
     */
    @ApiModelProperty(value = "国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", notes = "（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    private Integer nation;

    /**
     * 擅长品类
     */
    @ApiModelProperty(value = "擅长品类")
    private List<ModelTagVO> specialtyCategory = new ArrayList<>();
    /**
     * 模特标签
     */
    @ApiModelProperty(value = "模特标签")
    private List<ModelTagVO> tags = new ArrayList<>();

    /**
     * 排序值
     */
    @ApiModelProperty(value = "排序值")
    private Integer sort;

    /**
     * 是否展示（1:展示,0:不展示）
     */
    @ApiModelProperty(value = "是否展示（1:展示,0:不展示）")
    private Integer isShow;

    /**
     * 有蜗牛照（1：有，0：没有）
     */
    @ApiModelProperty(value = "有蜗牛照（1：有，0：没有）")
    private Integer haveSnailPic;

    /**
     * 开发人ID
     */
    @ApiModelProperty(value = "开发人ID")
    @JsonIgnore
    private Long developerId;

    public void echo(List<ModelTagVO> modelTagList) {
        if (CollUtil.isNotEmpty(modelTagList)) {
            setSpecialtyCategory(modelTagList.stream().filter(item -> ModelTagEnum.CATEGORY.getCode().equals(item.getCategoryId())).collect(Collectors.toList()));
            List<ModelTagVO> modelTagVOS = modelTagList.stream().filter(item -> ModelTagEnum.TAG.getCode().equals(item.getCategoryId())).sorted(Comparator.comparing(ModelTagVO::getId, Comparator.nullsLast(Comparator.reverseOrder()))).collect(Collectors.toList());
            setTags(modelTagVOS);
        }
    }
}
