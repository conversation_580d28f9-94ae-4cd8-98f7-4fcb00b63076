package com.ruoyi.system.api.domain.vo.biz.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家表
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "获取商家列表VO（运营端使用）")
public class BusinessDetailVO implements Serializable {

    private static final long serialVersionUID = -1262577170635983267L;
    @NotNull(message="[公司ID]不能为空")
    @ApiModelProperty("公司ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商家主账号（FK：business_account.account）
     */
    @NotNull(message="[商家主账号（FK：business_account.account）]不能为空")
    @ApiModelProperty("商家主账号（FK：business_account.account）")
    private String ownerAccount;
    /**
     * 商家名称
     */
    @NotBlank(message="[商家名称]不能为空")
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("商家名称")
    @Length(max= 20,message="编码长度不能超过20")
    private String name;

    @ApiModelProperty("规模")
    private Integer scale;
    /**
     * 是否为代理(0:否,1:是)
     */
    @NotNull(message="[是否为代理(0:否,1:是)]不能为空")
    @ApiModelProperty("是否为代理(0:否,1:是)")
    private Integer isProxy;
    /**
     * 账号状态（0正常 1禁用）
     */
    @NotNull(message="[账号状态（0正常 1禁用）]不能为空")
    @ApiModelProperty("账号状态（0正常 1禁用）")
    private Integer status;
    /**
     * 客户类型 （0-一般客户 1-重要客户）
     */
    @ApiModelProperty("客户类型 （0-一般客户 1-重要客户）")
    private Integer customerType;

    @ApiModelProperty("商家标识 （0-新客 1-老客 2-'-'）")
    private Integer businessIdentifier;
    /**
     * 帐号余额
     */
    @ApiModelProperty("帐号余额")
    private BigDecimal balance;

    @ApiModelProperty("使用余额")
    private BigDecimal useBalance;

    @ApiModelProperty("余额是否锁定（0-不锁定，1-锁定）")
    private Integer isBalanceLock;
    /**
     * 对接客服  FK：sys_user.user_id
     */
    @NotNull(message="[对接客服  FK：sys_user.user_id]不能为空")
    @ApiModelProperty("对接客服  FK：sys_user.user_id")
    private Long waiterId;

    @ApiModelProperty("对接客服")
    private String waiterName;
    /**
     * 抬头类型  0-企业
     */
    @ApiModelProperty("抬头类型  0-企业")
    private Integer invoiceTitleType;
    /**
     * 发票抬头
     */
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("发票抬头")
    @Length(max= 50,message="编码长度不能超过50")
    private String invoiceTitle;
    /**
     * 税号
     */
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("税号")
    @Length(max= 50,message="编码长度不能超过50")
    private String invoiceDutyParagraph;
    /**
     * 发票内容
     */
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("发票内容")
    @Length(max= 50,message="编码长度不能超过50")
    private String invoiceContent;
    /**
     * 会员编码
     */
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("会员编码")
    @Length(max= 10,message="编码长度不能超过10")
    private String memberCode;
    /**
     * 会员类型: 0-非会员，1-会员
     */
    @NotNull(message="[会员类型: 0-非会员，1-会员]不能为空")
    @ApiModelProperty("会员类型: 0-非会员，1-会员")
    private Integer memberType;
    /**
     * 会员状态：0-非会员1-正常，2-即将过期，3-已过期
     */
    @NotNull(message="[会员状态：0-非会员1-正常，2-即将过期，3-已过期]不能为空")
    @ApiModelProperty("会员状态：0-非会员1-正常，2-即将过期，3-已过期")
    private Integer memberStatus;
    /**
     * 会员套餐名称
     */
    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberPackageType;
    /**
     * 会员首次购买时间
     */
    @ApiModelProperty("会员首次购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date memberFirstTime;

    @ApiModelProperty("首次购买套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberFirstType;
    /**
     * 会员最近购买时间
     */
    @ApiModelProperty("会员最近购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date memberLastTime;
    /**
     * 会员有效期
     */
    @ApiModelProperty("会员有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date memberValidity;

    @ApiModelProperty("备注")
    private String remark;

    @NotNull(message="[创建时间]不能为空")
    @ApiModelProperty("商家创建时间（注册时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("微信名称")
    private String nickName;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("账号名称")
    private String accountName;

    @ApiModelProperty("子账号数量")
    private Integer accountNum;

    @ApiModelProperty("订单数量")
    private Integer orderNum;

    @ApiModelProperty("近期订单数量")
    private Integer recentOrderNum;

    @ApiModelProperty("待完成订单数量")
    private Integer preFinishOrderNum;

    @ApiModelProperty("商家种草码")
    private String seedCode;

    /**
     * 售后率
     */
    @ApiModelProperty(value = "售后率")
    private BigDecimal afterSaleRate;

    @ApiModelProperty("注册渠道类型(0-普通渠道，1-市场，2-分销，4-系统官网)")
    @JsonIgnore
    private Integer registerChannelType;

    @ApiModelProperty("注册渠道Id")
    @JsonIgnore
    private Long registerChannelId;

    @ApiModelProperty("手机可见性(0:否,1:是)")
    private Integer phoneVisible;

    @ApiModelProperty("渠道类型(0-普通渠道，1-市场，2-分销，4-系统官网)")
    private Integer channelType;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty("商务经理")
    private String connectUserName;
}