package com.ruoyi.system.api.config;

import com.ruoyi.common.core.utils.wechat.AesException;
import com.ruoyi.common.core.utils.wechat.WXBizMsgCrypt;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/6/19
 */
@Configuration
public class WxEncryptConfig {
    @Bean
    WXBizMsgCrypt wxBizMsgCrypt(WorkWeChatConfig workWeChatConfig) throws AesException {
        return new WXBizMsgCrypt(workWeChatConfig.getToken(), workWeChatConfig.getEncodingAESKey(), workWeChatConfig.getCorpId());
    }
}
