package com.ruoyi.system.api.domain.vo.biz.model;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/9 14:43
 */
@Data
public class ModelDataTableOrderScheduledRecordVO implements Serializable {
    private static final long serialVersionUID = -7531794998420487580L;

    /**
     * 视频订单ID
     */
    @ApiModelProperty(value = "视频订单ID")
    private Long videoId;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 产品中文名
     */
    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 拍摄模特添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发）
     */
    @ApiModelProperty(value = "拍摄模特添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发）")
    private Integer shootModelAddType;

    /**
     * 提交时间（排单时间）
     */
    @ApiModelProperty(value = "提交时间（排单时间）")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTime;

    /**
     * 排单类型（1:排单,2:携带排单）
     */
    @ApiModelProperty(value = "排单类型（1:排单,2:携带排单）")
    private Integer scheduleType;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    private BigDecimal commission;

    /**
     * 携带类型（1:主携带,2:被携带）
     */
    @ApiModelProperty(value = "携带类型（1:主携带,2:被携带）")
    private Integer carryType;

    /**
     * 素材反馈时长
     */
    @ApiModelProperty(value = "素材反馈时长")
    private Integer feedbackDuration;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）")
    private Integer status;

    /**
     * 订单完成时间
     */
    @ApiModelProperty(value = "订单完成时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date statusTime;

    /**
     * 标签（1：工单、2：售后单、3：补偿订单、4：取消订单、5：回退订单）
     */
    @ApiModelProperty(value = "标签（1：工单、2：售后单、3：补偿订单、4：取消订单、5：回退订单）")
    private String tags;
}
