package com.ruoyi.system.api.domain.vo.order.datastatistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2025/5/19 15:44
 */
@Data
public class EnglishCustomerServiceDataVO implements Serializable {
    private static final long serialVersionUID = 409449841489603703L;

    /**
     * 客服ID
     */
    @ApiModelProperty("客服ID")
    private Long customerServiceId;

    /**
     * 客服姓名
     */
    @ApiModelProperty("客服姓名")
    private String customerServiceName;

    /**
     * 客服状态（0=正常,1=停用）
     */
    @ApiModelProperty("客服状态（0=正常,1=停用）")
    private Integer customerServiceStatus;

    /**
     * 关联模特数
     */
    @ApiModelProperty("关联模特数")
    private Long modelCount;

    /**
     * 上月新增模特数
     */
    @ApiModelProperty("上月新增模特数")
    private Long modelNewCount;

    /**
     * 上月淘汰模特数
     */
    @ApiModelProperty("上月淘汰模特数")
    private Long modelOustCount;

    /**
     * 合作中模特数
     */
    @ApiModelProperty("合作中模特数")
    private Long modelCooperationCount;

    /**
     * 正常合作模特数
     */
    @ApiModelProperty("正常合作模特数")
    private Long modelNormalCount;

    /**
     * 行程中模特数
     */
    @ApiModelProperty("行程中模特数")
    private Long modelTravelCount;

    /**
     * 服务订单数
     */
    @ApiModelProperty("服务订单数")
    private Long orderCount;

    /**
     * 合计服务订单数
     */
    @ApiModelProperty("合计服务订单数")
    private Long orderTotalCount;

    /**
     * 昨日新增订单数
     */
    @ApiModelProperty("昨日新增订单数")
    private Long orderNewCount;

    /**
     * 昨日完成订单数
     */
    @ApiModelProperty("昨日完成订单数")
    private Long orderFinishCount;

    /**
     * 拖单率
     */
    @ApiModelProperty("拖单率")
    private BigDecimal dragOrderRate;

    /**
     * 拖单数
     */
    @ApiModelProperty("拖单数")
    private Long dragOrderCount;

    /**
     * 售后率
     */
    @ApiModelProperty("售后率")
    private BigDecimal afterSaleRate;

    /**
     * 售后数
     */
    @ApiModelProperty("售后数")
    private Long afterSaleCount;

    /**
     * 排单数
     */
    @ApiModelProperty("排单数")
    private Long orderScheduledCount;

    /**
     * 素人排单数
     */
    @ApiModelProperty("素人排单数")
    private Long orderScheduledSoleCount;

    /**
     * 影响者排单数
     */
    @ApiModelProperty("影响者排单数")
    private Long orderScheduledInfluencerCount;

    /**
     * 商家驳回单数
     */
    @ApiModelProperty("商家驳回单数")
    private Long rejectOrderCount;

    /**
     * 平均佣金
     */
    @ApiModelProperty("平均佣金")
    private BigDecimal averageCommission;

    /**
     * 素人平均佣金
     */
    @ApiModelProperty("素人平均佣金")
    private BigDecimal averageCommissionSole;

    /**
     * 影响者平均佣金
     */
    @ApiModelProperty("影响者平均佣金")
    private BigDecimal averageCommissionInfluencer;

    /**
     * 优质模特占比
     */
    @ApiModelProperty("优质模特占比")
    private BigDecimal proportionOfQualityModel;

    /**
     * 中度模特占比
     */
    // @ApiModelProperty("中度模特占比")
    // private BigDecimal proportionOfMidModel;

    /**
     * 一般模特占比
     */
    @ApiModelProperty("一般模特占比")
    private BigDecimal proportionOfGeneralModel;

    /**
     * 右侧拖单数
     */
    @ApiModelProperty("右侧拖单数")
    private Long rightDragOrderCount;

    /**
     * 右侧售后数
     */
    @ApiModelProperty("右侧售后数")
    private Long rightAfterSaleCount;

    public void echo() {
        modelCount = modelCount == null ? 0L : modelCount;
        modelNewCount = modelNewCount == null ? 0L : modelNewCount;
        modelOustCount = modelOustCount == null ? 0L : modelOustCount;
        modelCooperationCount = modelCooperationCount == null ? 0L : modelCooperationCount;
        modelNormalCount = modelNormalCount == null ? 0L : modelNormalCount;
        modelTravelCount = modelTravelCount == null ? 0L : modelTravelCount;
        orderCount = orderCount == null ? 0L : orderCount;
        orderTotalCount = orderTotalCount == null ? 0L : orderTotalCount;
        orderNewCount = orderNewCount == null ? 0L : orderNewCount;
        orderFinishCount = orderFinishCount == null ? 0L : orderFinishCount;
        dragOrderRate = dragOrderRate == null ? BigDecimal.ZERO : dragOrderRate.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        dragOrderCount = dragOrderCount == null ? 0L : dragOrderCount;
        afterSaleRate = afterSaleRate == null ? BigDecimal.ZERO : afterSaleRate.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        afterSaleCount = afterSaleCount == null ? 0L : afterSaleCount;
        orderScheduledCount = orderScheduledCount == null ? 0L : orderScheduledCount;
        orderScheduledSoleCount = orderScheduledSoleCount == null ? 0L : orderScheduledSoleCount;
        orderScheduledInfluencerCount = orderScheduledInfluencerCount == null ? 0L : orderScheduledInfluencerCount;
        rejectOrderCount = rejectOrderCount == null ? 0L : rejectOrderCount;
        averageCommission = averageCommission == null ? BigDecimal.ZERO : averageCommission.setScale(2, RoundingMode.DOWN);
        averageCommissionSole = averageCommissionSole == null ? BigDecimal.ZERO : averageCommissionSole.setScale(2, RoundingMode.DOWN);
        averageCommissionInfluencer = averageCommissionInfluencer == null ? BigDecimal.ZERO : averageCommissionInfluencer.setScale(2, RoundingMode.DOWN);
        proportionOfQualityModel = proportionOfQualityModel == null ? BigDecimal.ZERO : proportionOfQualityModel.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        // proportionOfMidModel = proportionOfMidModel == null ? BigDecimal.ZERO : proportionOfMidModel.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        proportionOfGeneralModel = proportionOfGeneralModel == null ? BigDecimal.ZERO : proportionOfGeneralModel.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        rightDragOrderCount = rightDragOrderCount == null ? 0L : rightDragOrderCount;
        rightAfterSaleCount = rightAfterSaleCount == null ? 0L : rightAfterSaleCount;
    }
}
