package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-12-19 11:09
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessOrderVO implements Serializable {
    private static final long serialVersionUID = 5207602776067300796L;

    @ApiModelProperty(value = "商家Id")
    private Long businessId;

    @ApiModelProperty("订单数量")
    private Integer orderNum;

    @ApiModelProperty("近期订单数量")
    private Integer recentOrderNum;

    @ApiModelProperty("待完成订单数量")
    private Integer preFinishOrderNum;
}
