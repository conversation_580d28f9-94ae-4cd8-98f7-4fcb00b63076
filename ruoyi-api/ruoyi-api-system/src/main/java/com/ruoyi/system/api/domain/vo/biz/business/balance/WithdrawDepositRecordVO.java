package com.ruoyi.system.api.domain.vo.biz.business.balance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/15 9:19
 */
@Data
public class WithdrawDepositRecordVO implements Serializable {
    private static final long serialVersionUID = -7915934884317293053L;

    @ApiModelProperty("订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、4.视频订单支出、5.会员订单支出、6.余额提现、7.线下钱包充值收入、8.线上钱包充值收入)")
    private Integer origin;

    @ApiModelProperty("预付单号")
    private String prepayNum;
    /**
     * 视频编码
     */
    @ApiModelProperty("视频编码")
    private String videoCode;

    /**
     * 状态
     */
    @ApiModelProperty("状态（0:待处理,1:已提现,2.已取消）")
    private Integer status;

    /**
     * 提现金额
     */
    @ApiModelProperty("提现金额")
    private BigDecimal payOutAmount;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 审核时间
     */
    @ApiModelProperty("审核时间")
    private Date auditTime;
}
