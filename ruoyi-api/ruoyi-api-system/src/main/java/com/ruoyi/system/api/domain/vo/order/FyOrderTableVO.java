package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 富友订单表
* <AUTHOR>
 * @TableName fy_order_table
*/
@Data
public class FyOrderTableVO implements Serializable {

    private static final long serialVersionUID = 3323759981196402382L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("商户订单号")
    private String mchntOrderNo;

    @ApiModelProperty("内部订单号")
    private String orderNumber;

    @ApiModelProperty("二维码")
    private String qrcode;

    @ApiModelProperty("平台类型（1-微信，2-支付宝，3-云闪付/银联，4-数字人民币，5-未知支付方式）")
    private Integer platform;

    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty("是否有效（1-有效， 0-无效）")
    private Integer status;

    @ApiModelProperty("创建时间")
    private Date createTime;
}
