package com.ruoyi.system.api.domain.vo.order.datastatistics;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/9 11:27
 */
@Data
public class OrderVideoTrendVO implements Serializable {
    private static final long serialVersionUID = 8484889932211698869L;

    /**
     * 记录截止时间
     */
    @ApiModelProperty("记录截止时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date writeTimeEnd;

    /**
     * 日期数组
     */
    @ApiModelProperty("日期数组")
    private List<String> dateArray;

    /**
     * 新增订单数数组
     */
    @ApiModelProperty("新增订单数数组")
    private List<Long> orderNewCountArray;

    /**
     * 取消订单数数组
     */
    @ApiModelProperty("取消订单数数组")
    private List<Long> orderCancelCountArray;

    /**
     * 排单数数组
     */
    @ApiModelProperty("排单数数组")
    private List<Long> orderScheduledCountArray;
}
