package com.ruoyi.system.api.domain.vo.biz.model;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/20 18:44
 */
@Data
public class ModelTravelVO implements Serializable {
    private static final long serialVersionUID = -2230738541473500731L;
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN,timezone = "GMT+8")
    private Date startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN,timezone = "GMT+8")
    private Date endTime;
    /**
     * 行程原因
     */
    @ApiModelProperty("行程原因")
    @Size(max = 1000, message = "[行程原因]长度不能超过1000位字符")
    private String remark;
}
