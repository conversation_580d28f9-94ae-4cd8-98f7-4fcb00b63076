package com.ruoyi.system.api.domain.vo.biz.business.prepay;

import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :未支付订单详情
 * @create :2024-10-12 16:47
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PrepayUnPayVO implements Serializable {
    private static final long serialVersionUID = -4400386108515838615L;

    @ApiModelProperty(value = "未支付数量")
    private Integer unPayNum;

    @ApiModelProperty(value = "未支付订单列表")
    private List<BusinessBalancePrepay> unPayOnlineList;
}
