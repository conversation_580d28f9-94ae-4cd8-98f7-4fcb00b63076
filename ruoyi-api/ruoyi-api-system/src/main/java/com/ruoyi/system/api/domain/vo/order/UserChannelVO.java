package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :用户渠道信息
 * @create :2025-01-21 16:23
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserChannelVO implements Serializable {
    private static final long serialVersionUID = 4582385068548539882L;
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("登录账号ID")
    private Long bizUserId;

    @ApiModelProperty(value = "商家id")
    private Long businessId;

    @ApiModelProperty(value = "用户注册渠道")
    private Integer registerChannelType;

    @ApiModelProperty(value = "企微渠道类型(0=普通,1=市场，2=分销)")
    private Integer wechatChannelType;

    @ApiModelProperty(value = "添加企微渠道id")
    private Long wechatChannelId;

    @ApiModelProperty(value = "用户注册的渠道")
    private String channelName;
}
