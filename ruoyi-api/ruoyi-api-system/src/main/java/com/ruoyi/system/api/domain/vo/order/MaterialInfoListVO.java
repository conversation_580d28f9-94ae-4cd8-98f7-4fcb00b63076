package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/20 15:43
 */
@Data
public class MaterialInfoListVO implements Serializable {
    private static final long serialVersionUID = -3473526854189568362L;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 视频订单ID
     */
    @ApiModelProperty(value = "视频订单ID")
    private Long videoId;

    /**
     * 回退ID
     */
    @ApiModelProperty(value = "回退ID")
    private Long rollbackId;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 产品中文名
     */
    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    private String productLink;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)", notes = "0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private Integer platform;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人")
    private Integer modelType;

    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", notes = "1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国")
    private Integer shootingCountry;

    /**
     * 视频格式（1:横屏16：9,2:竖屏9：16）
     */
    @ApiModelProperty(value = "视频格式（1:横屏16：9,2:竖屏9：16）", notes = "1:横屏16：9,2:竖屏9：16")
    private Integer videoFormat;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）", notes = "1:2张/$10,2:5张/$20")
    private Integer picCount;

    /**
     * 参考图片（关联资源id）
     */
    @ApiModelProperty(value = "参考图片（关联资源id）")
    @JsonIgnore
    private String referencePicId;

    /**
     * 参考图片
     */
    @ApiModelProperty(value = "参考图片")
    private List<String> referencePic = new ArrayList<>();

    /**
     * 退款照片数量
     */
    @ApiModelProperty(value = "退款照片数量")
    private Integer refundPicCount;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）")
    private Integer status;

    /**
     * 订单退款信息
     */
    @ApiModelProperty(value = "订单退款信息")
    private OrderVideoRefundSimpleVO orderVideoRefund;

    /**
     * 订单退款信息(用于前端判断是否补偿订单或照片退款）
     */
    @ApiModelProperty(value = "订单退款信息(用于前端判断是否补偿订单或照片退款）")
    private List<OrderVideoRefundSimpleVO> orderVideoRefundList;

    /**
     * 拍摄模特id
     */
    @ApiModelProperty(value = "拍摄模特id")
    @JsonIgnore
    private Long shootModelId;

    /**
     * 拍摄模特
     */
    @ApiModelProperty(value = "拍摄模特")
    private ModelOrderSimpleVO shootModel;

    /**
     * 对接人id
     */
    @ApiModelProperty(value = "对接人id")
    @JsonIgnore
    private Long contactId;

    /**
     * 对接人
     */
    @ApiModelProperty(value = "对接人")
    private UserVO contact;

    /**
     * 出单人id
     */
    @ApiModelProperty(value = "出单人id")
    @JsonIgnore
    private Long issueId;

    /**
     * 出单人
     */
    @ApiModelProperty(value = "出单人")
    private UserVO issue;

    /**
     * 历史剪辑记录数量
     */
    @ApiModelProperty(value = "历史剪辑记录数量")
    private Integer historyClipRecord;

    /**
     * 链接
     */
    @ApiModelProperty(value = "素材链接")
    private String link;

    /**
     * 参考视频链接
     */
    @ApiModelProperty(value = "参考视频链接")
    private String referenceVideoLink;

    /**
     * 视频时长
     */
    @ApiModelProperty(value = "剪辑时长")
    private Integer videoDuration;

    /**
     * 创建订单用户id
     */
    @ApiModelProperty(value = "创建订单用户id")
    @JsonIgnore
    private Long createOrderUserId;

    /**
     * 是否存在剪辑要求
     */
    @ApiModelProperty(value = "是否存在剪辑要求 true:有")
    private Boolean isExistClipRequire;

    /**
     * 领取状态（0：待领取，1：已领取）
     */
    @ApiModelProperty(value = "领取状态（0：待领取，1：已领取）")
    private Integer getStatus;

    /**
     * 领取编码
     */
    @ApiModelProperty(value = "领取编码")
    private String getCode;

    /**
     * 领取人ID
     */
    @ApiModelProperty(value = "领取人ID")
    @JsonIgnore
    private Long getById;

    /**
     * 领取人姓名
     */
    @ApiModelProperty(value = "领取人姓名")
    private String getBy;

    /**
     * 领取时间
     */
    @ApiModelProperty(value = "领取时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date getTime;

    /**
     * 剪辑人姓名
     */
    @ApiModelProperty(value = "剪辑人姓名")
    private String editBy;

    /**
     * 剪辑人ID
     */
    @ApiModelProperty(value = "剪辑人ID")
    @JsonIgnore
    private Long editById;

    /**
     * 剪辑时间
     */
    @ApiModelProperty(value = "剪辑时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date editTime;

    /**
     * 反馈人姓名
     */
    @ApiModelProperty(value = "反馈人姓名")
    private String feedbackBy;

    /**
     * 反馈人ID
     */
    @ApiModelProperty(value = "反馈人ID")
    @JsonIgnore
    private Long feedbackById;

    /**
     * 反馈时间
     */
    @ApiModelProperty(value = "反馈时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date feedbackTime;

    /**
     * 售后单状态
     */
    @ApiModelProperty(value = "售后单状态（1：待处理，4：已完成）")
    private Integer afterSaleTaskStatus;

    /**
     * 工单状态
     */
    @ApiModelProperty(value = "工单状态（1：待处理，4：已完成）")
    private Integer workOrderTaskStatus;

    /**
     * 工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）
     */
    @ApiModelProperty(value = "工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）")
    private Integer workOrderType;
}
