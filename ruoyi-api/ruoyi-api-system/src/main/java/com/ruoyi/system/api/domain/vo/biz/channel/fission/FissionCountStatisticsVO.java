package com.ruoyi.system.api.domain.vo.biz.channel.fission;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :种草数量统计
 * @create :2025-05-20 10:17
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FissionCountStatisticsVO implements Serializable {
    private static final long serialVersionUID = 1933010522236047704L;

    @ApiModelProperty("待入账")
    private Integer pendingDepositCount;

    @ApiModelProperty("待提现")
    private Integer pendingWithdrawalCount;

    @ApiModelProperty("待审核")
    private Integer underReviewCount;

    @ApiModelProperty("待打款")
    private Integer pendingTransferCount;

    @ApiModelProperty("已打款")
    private Integer withdrawSuccessCount;

    @ApiModelProperty("审核不通过")
    private Integer reviewRejectedCount;

    @ApiModelProperty("打款异常")
    private Integer transferExceptionCount;

    @ApiModelProperty("总数量")
    private Integer totalCount;

    @ApiModelProperty("提现数量：待审核+待打款+已打款+审核不通过+打款异常")
    private Integer withdrawCount;
}
