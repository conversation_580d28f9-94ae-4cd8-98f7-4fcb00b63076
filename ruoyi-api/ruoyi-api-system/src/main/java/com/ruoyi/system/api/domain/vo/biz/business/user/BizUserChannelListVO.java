package com.ruoyi.system.api.domain.vo.biz.business.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-01-14 19:13
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BizUserChannelListVO extends BizUserListVO implements Serializable {
    private static final long serialVersionUID = -1062077512840821481L;

    @ApiModelProperty("会员编码")
    private String memberCode;

    @ApiModelProperty("商家有效期")
    private Date memberValidity;

    @ApiModelProperty("渠道ID")
    private Long channelId;

    @ApiModelProperty("会员状态：0-非会员1-正常，2-即将过期，3-已过期")
    private Integer memberStatus;
}
