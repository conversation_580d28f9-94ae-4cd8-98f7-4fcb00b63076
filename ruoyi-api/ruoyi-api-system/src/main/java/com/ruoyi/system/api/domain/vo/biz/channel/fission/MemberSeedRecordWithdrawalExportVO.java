package com.ruoyi.system.api.domain.vo.biz.channel.fission;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.enums.WithdrawTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :提现记录导出
 * @create :2025-05-22 09:32
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MemberSeedRecordWithdrawalExportVO implements Serializable {
    private static final long serialVersionUID = -8250366842714547251L;

    @ApiModelProperty("主键")
    private Long id;

    @Excel(name = "提现单号")
    private String withdrawalNum;

    @ApiModelProperty("申请人微信名")
    private String applicantNickName;

    @ApiModelProperty("申请人员工名称")
    private String applicantName;

    @ApiModelProperty("申请人商家名称")
    private String applicantBusinessName;

    @ApiModelProperty("申请人会员编码")
    private String applicantMemberCode;

    @Excel(name = "申请人")
    private String applyInfo;

    @Excel(name = "种草官ID")
    private String channelSeedId;

    private Long channelId;

    @Excel(name = "包含订单")
    private String orderNum;


    @ApiModelProperty("会员种草记录创建时间")
    @Excel(name = "首次开通时间", dateFormat = DatePattern.NORM_DATE_PATTERN, width = 19, height = 144, defaultValue = StrPool.DASHED)
    private Date recordCreateTime;

    @Excel(name = "会员编码")
    private String memberCode;

    @Excel(name = "结算来源", readConverterExp = "2=分销渠道,7=裂变渠道")
    private Integer channelType;

    @Excel(name = "结算方案", readConverterExp = "1=固定金额,2=固定比例")
    private Integer settleType;

    @ApiModelProperty("方案数据")
    private BigDecimal settleRage;

    @Excel(name = "方案数据")
    private String settleRageStr;

    @Excel(name = "申请结算金额")
    private BigDecimal orderSettleAmount;

    @Excel(name = "申请时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN, width = 19, height = 144, defaultValue = StrPool.DASHED)
    private Date createTime;

    @Excel(name = "收款方式", readConverterExp = "2=支付宝,3=银行卡,6=公户收款", defaultValue = StrPool.DASHED)
    private Integer withdrawalAccountType;

    @ApiModelProperty(name = "收款方姓名")
    private String payeeName;

    @ApiModelProperty(name = "收款方身份证号")
    private String payeeIdentityCard;

    @ApiModelProperty("收款方账号")
    private String payeeAccount;

    @Excel(name = "收款方姓名", defaultValue = StrPool.DASHED)
    private String bankPayeeName;

    @Excel(name = "收款方手机号", defaultValue = StrPool.DASHED)
    private String payeePhone;

    @Excel(name = "收款方身份证号", defaultValue = StrPool.DASHED)
    private String bankPayeeIdentityCard;

    @Excel(name = "支付宝账号", defaultValue = StrPool.DASHED)
    private String alipayAccount;

    @Excel(name = "银行卡号", defaultValue = StrPool.DASHED)
    private String bankAccount;

    @Excel(name = "收款公司名称", defaultValue = StrPool.DASHED)
    private String publicPayeeName;

    @Excel(name = "收款银行账号", defaultValue = StrPool.DASHED)
    private String publicAccount;

    @Excel(name = "开户行名称", defaultValue = StrPool.DASHED)
    private String publicBankName;



    @Excel(name = "提现状态", readConverterExp = "2=待审核,3=待打款,4=已打款,5=审核不通过,6=打款异常")
    private Integer status;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    @ApiModelProperty("审核备注")
    private String auditRemark;

    @ApiModelProperty("审核人员id FK sys_user.user_id")
    private Long auditUserId;

    @ApiModelProperty("审核人员名称")
    private String auditUserName;

    @Excel(name = "状态标记时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN, width = 19, height = 144, defaultValue = StrPool.DASHED)
    private Date flowTime;

    @Excel(name = "打款时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN, width = 19, height = 144, defaultValue = StrPool.DASHED)
    private Date payoutTime;

    @ApiModelProperty("提现审核时间")
    private Date withdrawalTime;

    @ApiModelProperty("提现备注")
    private String withdrawalRemark;

    @ApiModelProperty("提现人员id FK sys_user.user_id")
    private Long withdrawalUserId;

    @ApiModelProperty("提现人员名称")
    private String withdrawalUserName;

    @Excel(name = "打款账号", defaultValue = StrPool.DASHED)
    private String payAccount;

    @Excel(name = "备注", defaultValue = StrPool.DASHED)
    private String remark;

}
