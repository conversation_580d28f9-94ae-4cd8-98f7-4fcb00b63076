package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.UserVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单反馈表(商家)
 *
 * <AUTHOR>
 */
@Data
public class OrderFeedBackVO implements Serializable {
    private static final long serialVersionUID = 7373351614776601193L;
    /**
     * id
     */
    private Long id;

    /**
     * 视频订单id
     */
    @ApiModelProperty("视频订单id")
    private Long videoId;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    /**
     * 反馈类型（1:视频,2:视频和照片,3:照片）
     */
    @ApiModelProperty("反馈类型（1:视频,2:视频和照片,3:照片）")
    private Integer type;

    /**
     * 视频地址
     */
    @ApiModelProperty("视频地址")
    private String videoUrl;

    /**
     * 图片地址
     */
    @ApiModelProperty("图片地址")
    private String picUrl;

    /**
     * 视频评分
     */
    @ApiModelProperty("视频评分")
    private Float videoScore;

    /**
     * 评价内容
     */
    @ApiModelProperty("评价内容")
    private String videoScoreContent;

    /**
     * 创建用户id
     */
    @JsonIgnore
    private Long createUserId;

    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    private UserVO createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 到期时间
     */
    @ApiModelProperty("到期时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date overTime;

    /**
     * 是否最新反馈
     */
    @ApiModelProperty(value = "是否最新反馈")
    private Boolean isNew = false;

    /**
     * 修改理由
     */
    @ApiModelProperty("修改理由")
    private String modifyReason;

    /**
     * 能否修改true : 可以修改
     */
    @ApiModelProperty("能否修改true : 可以修改")
    private boolean canModify;
}
