package com.ruoyi.system.api.domain.vo.biz.datastatistics;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/8 9:16
 */
@Data
public class ModelBasicDataVO implements Serializable {
    private static final long serialVersionUID = -6505488524871239258L;

    /**
     * 实时更新时间
     */
    @ApiModelProperty("实时更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date dateTime;

    /**
     * 合作中模特数量
     */
    @ApiModelProperty(value = "合作中模特数量")
    private Long activeModelNumber;

    /**
     * 上月新增模特数量
     */
    @ApiModelProperty(value = "上月新增模特数量")
    private Long newAdditionsLastMonthModelNumber;

    /**
     * 正常合作模特数量
     */
    @ApiModelProperty(value = "正常合作模特数量")
    private Long normalModelNumber;

    /**
     * 行程中模特数量
     */
    @ApiModelProperty(value = "行程中模特数量")
    private Long journeyModelNumber;

    /**
     * 已淘汰模特数量
     */
    @ApiModelProperty(value = "已淘汰模特数量")
    private Long oustModelNumber;

    /**
     * 上月淘汰模特数量
     */
    @ApiModelProperty(value = "上月淘汰模特数量")
    private Long eliminatedLastMonthModelNumber;

    /**
     * 暂停合作模特数量
     */
    @ApiModelProperty(value = "暂停合作模特数量")
    private Long pauseModelNumber;

    /**
     * 取消合作模特数量
     */
    @ApiModelProperty(value = "取消合作模特数量")
    private Long cancelModelNumber;

    /**
     * 首次匹配成功率
     */
    @ApiModelProperty(value = "首次匹配成功率")
    private BigDecimal successRateOfTheFirstMatch;

    /**
     * 模特售后率
     */
    @ApiModelProperty(value = "模特售后率")
    private BigDecimal modelAfterSalesRate;

    /**
     * 模特超时率
     */
    @ApiModelProperty(value = "模特超时率")
    private BigDecimal modelOvertimeRate;

    /**
     * 意向匹配成功率
     */
    @ApiModelProperty(value = "意向匹配成功率")
    private BigDecimal successRateOfIntentionMatching;

    /**
     * 平均匹配时长（天）
     */
    @ApiModelProperty(value = "平均匹配时长（天）")
    private BigDecimal averageMatchingDuration;

    /**
     * 平均反馈时长（天）
     */
    @ApiModelProperty(value = "平均反馈时长（天）")
    private BigDecimal averageFeedbackDuration;

    public ModelBasicDataVO echo() {
        successRateOfTheFirstMatch = successRateOfTheFirstMatch == null ? BigDecimal.ZERO : successRateOfTheFirstMatch.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        modelAfterSalesRate = modelAfterSalesRate == null ? BigDecimal.ZERO : modelAfterSalesRate.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        modelOvertimeRate = modelOvertimeRate == null ? BigDecimal.ZERO : modelOvertimeRate.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        successRateOfIntentionMatching = successRateOfIntentionMatching == null ? BigDecimal.ZERO : successRateOfIntentionMatching.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        activeModelNumber = activeModelNumber == null ? 0L : activeModelNumber;
        newAdditionsLastMonthModelNumber = newAdditionsLastMonthModelNumber == null ? 0L : newAdditionsLastMonthModelNumber;
        normalModelNumber = normalModelNumber == null ? 0L : normalModelNumber;
        journeyModelNumber = journeyModelNumber == null ? 0L : journeyModelNumber;
        oustModelNumber = oustModelNumber == null ? 0L : oustModelNumber;
        eliminatedLastMonthModelNumber = eliminatedLastMonthModelNumber == null ? 0L : eliminatedLastMonthModelNumber;
        pauseModelNumber = pauseModelNumber == null ? 0L : pauseModelNumber;
        cancelModelNumber = cancelModelNumber == null ? 0L : cancelModelNumber;
        averageMatchingDuration = averageMatchingDuration == null ? BigDecimal.ZERO : averageMatchingDuration.setScale(2, RoundingMode.HALF_UP);
        averageFeedbackDuration = averageFeedbackDuration == null ? BigDecimal.ZERO : averageFeedbackDuration.setScale(2, RoundingMode.HALF_UP);
        return this;
    }
}
