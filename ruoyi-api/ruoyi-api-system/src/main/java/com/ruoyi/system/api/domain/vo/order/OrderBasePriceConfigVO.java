package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OrderBasePriceConfigVO implements Serializable {
    private Integer id;

    @ApiModelProperty(value = "原金额(非代理)")
    private BigDecimal originPrice;

    @ApiModelProperty(value = "代理原金额")
    private BigDecimal originPriceProxy;

    @ApiModelProperty(value = "现金额(非代理)")
    private BigDecimal currentPrice;

    @ApiModelProperty(value = "代理现金额")
    private BigDecimal currentPriceProxy;

    @ApiModelProperty(value = "生效状态,1=立即生效")
    private Integer status;

    @ApiModelProperty(value = "生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sinceTime;

    @ApiModelProperty(value = "创建人姓名")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;



}