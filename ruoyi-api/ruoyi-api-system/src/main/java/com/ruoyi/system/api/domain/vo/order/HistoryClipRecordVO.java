package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21 11:54
 */
@Data
public class HistoryClipRecordVO implements Serializable {
    private static final long serialVersionUID = -7450738610125971179L;

    /**
     * 视频订单信息
     */
    @ApiModelProperty(value = "视频订单信息")
    private OrderVideoVO orderVideoVO;

    /**
     * 历史剪辑要求
     */
    @ApiModelProperty(value = "历史剪辑要求")
    private List<HistoryClipRecordListVO> historyClipRecordListVOS;
}
