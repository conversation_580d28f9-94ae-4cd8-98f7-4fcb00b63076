package com.ruoyi.system.api.domain.vo.biz.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家表
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "入驻商家列表（运营端使用）")
public class ResidentBusinessVO implements Serializable {
    private static final long serialVersionUID = 1316203364895435869L;

    @ApiModelProperty("公司ID")
    private Long id;

    @ApiModelProperty("商家主账号（FK：business_account.account）")
    private String ownerAccount;

    @ApiModelProperty("商家名称")
    private String name;

    @ApiModelProperty("商家创建时间（注册时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("是否为代理(0:否,1:是)")
    private Integer isProxy;

    @ApiModelProperty("帐号余额")
    private BigDecimal balance;

    @ApiModelProperty("使用余额")
    private BigDecimal useBalance;

    @ApiModelProperty("对接客服  FK：sys_user.user_id")
    private Long waiterId;

    @ApiModelProperty("对接客服")
    private String waiterName;

    @ApiModelProperty("会员编码")
    private String memberCode;

    @ApiModelProperty("会员类型: 0-非会员，1-会员")
    private Integer memberType;

    @ApiModelProperty("会员状态：0-非会员1-正常，2-即将过期，3-已过期")
    private Integer memberStatus;

    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberPackageType;

    @ApiModelProperty("会员首次购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberFirstTime;

    @ApiModelProperty("首次购买套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberFirstType;

    @ApiModelProperty("会员最近购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberLastTime;

    @ApiModelProperty("会员有效期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberValidity;
}