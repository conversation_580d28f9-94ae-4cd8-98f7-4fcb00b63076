package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/9 14:11
 */
@Data
public class OrderVideoRoastVO implements Serializable {


    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "商家id")
    private Long businessId;

    @ApiModelProperty(value = "商家名称")
    private String businessName;

    @ApiModelProperty(value = "会员编码")
    private String memberCode;

    @ApiModelProperty(value = "吐槽用户名称")
    private String roastUserName;

    @ApiModelProperty(value = "吐槽用户微信名称")
    private String roastUserNickName;

    @ApiModelProperty(value = "视频id")
    private Long videoId;

    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    @ApiModelProperty(value = "拍摄模特ID")
    private Long shootModelId;

    @ApiModelProperty(value = "吐槽类型(1:视频吐槽,2:系统吐槽)")
    private Integer roastType;

    @ApiModelProperty(value = "吐槽对象(1:视频,2:客服,3:其他)")
    private Integer object;

    @ApiModelProperty(value = "吐槽内容")
    private String content;

    @ApiModelProperty(value = "中文部客服id")
    private Long contactId;

    @ApiModelProperty(value = "中文部客服")
    private String waiterName;

    @ApiModelProperty(value = "英文部客服id")
    private Long issueId;

    @ApiModelProperty(value = "英文部客服")
    private String issueName;

    @ApiModelProperty(value = "处理状态（0:待处理,1:已处理）")
    private Integer handleStatus;

    @ApiModelProperty(value = "处理结果")
    private String handleResult;

    @ApiModelProperty(value = "处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    @ApiModelProperty(value = "处理人id")
    private Long handleUserId;

    @ApiModelProperty(value = "处理人")
    private String handleUserName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "拍摄模特数据")
    private ModelInfoVO modelInfoVO;

}
