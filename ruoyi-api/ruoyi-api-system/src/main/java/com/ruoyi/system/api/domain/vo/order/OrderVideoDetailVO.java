package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.enums.RefundStatusEnum;
import com.ruoyi.system.api.domain.vo.OrderLogisticSimpleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/31 15:10
 */
@ApiModel(value = "订单_视频明细对象VO")
@Data
public class OrderVideoDetailVO implements Serializable {

    private static final long serialVersionUID = -8796828027234806829L;
    /**
     * 大订单对象VO
     */
    @ApiModelProperty("大订单对象VO")
    private OrderSimpleVO orderSimpleVO;

    /**
     * 小订单对象VO
     */
    @ApiModelProperty("小订单对象VO")
    private OrderVideoSimpleVO orderVideoSimpleVO;

    /**
     * 物流信息
     */
    @ApiModelProperty(value = "物流信息")
    private List<OrderLogisticSimpleVO> orderLogisticSimpleVOS;

    /**
     * 视频上传信息
     */
    @ApiModelProperty("视频上传信息")
    private OrderVideoUploadLinkSimpleVO orderVideoUploadLinkSimpleVO;

    /**
     * 反馈给商家素材信息
     */
    @ApiModelProperty("反馈给商家素材信息")
    private List<OrderFeedBackSimpleVO> orderFeedBackSimpleVOS;

    /**
     * 模特反馈的素材信息
     */
    @ApiModelProperty("模特反馈的素材信息")
    private List<OrderFeedBackMaterialInfoSimpleVO> orderFeedBackMaterialInfoSimpleVOS;

    /**
     * 流转记录
     */
    @ApiModelProperty("流转记录")
    private List<OrderVideoOperateVO> orderVideoOperateVOS;

    /**
     * 节点图
     */
    @ApiModelProperty("节点图")
    private List<OrderVideoFlowNodeDiagramVO> orderVideoFlowNodeDiagramVOS;

    /**
     * 意向模特变更记录
     */
    @ApiModelProperty("意向模特变更记录")
    private List<OrderVideoModelChangeVO> orderVideoIntentionModelChangeVOS;

    /**
     * 拍摄模特变更记录
     */
    @ApiModelProperty("拍摄模特变更记录")
    private List<OrderVideoModelChangeVO> orderVideoShootModelChangeVOS;

    @ApiModelProperty("退款信息")
    private List<OrderVideoRefundVO> orderVideoRefundVOS;

    @ApiModelProperty("退款总额")
    private BigDecimal refundAmountTotal;

    public void initRefundAmountTotal(){
        BigDecimal total = BigDecimal.ZERO;
        if (CollUtil.isEmpty(orderVideoRefundVOS)){
            this.refundAmountTotal = total;
        }
        for (OrderVideoRefundVO orderVideoRefundVO : orderVideoRefundVOS) {
            if (RefundStatusEnum.AFTER_SALE_FINISHED.getCode().equals(orderVideoRefundVO.getRefundStatus())){
                total = total.add(orderVideoRefundVO.getRefundAmount());
            }
        }
        this.refundAmountTotal = total;
    }
}
