package com.ruoyi.system.api.domain.vo.order.task;

import com.ruoyi.system.api.domain.vo.order.OrderTaskFlowRecordVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :售后单列表详情
 * @create :2024-12-11 09:39
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AfterSaleTaskDetailInfoVO extends AfterSaleTaskDetailListVO implements Serializable {

    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    @ApiModelProperty(value = "产品链接")
    private String productLink;

    @ApiModelProperty(value = "流转记录")
    private List<OrderTaskFlowRecordVO> records;
}
