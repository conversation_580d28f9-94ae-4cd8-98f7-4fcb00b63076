package com.ruoyi.system.api.domain.vo.biz.channel;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 市场渠道列表导出对象VO
 *
 * <AUTHOR>
 * @date 2024/9/26
 */
@Data
public class MarketingChannelListExportVO implements Serializable {
    private static final long serialVersionUID = 3964844168432423882L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @Excel(name = "ID")
    private Long id;

    /**
     * 市场渠道平台 (1=百度,2=小红书,3=穿山甲,4=优量汇,5=今日头条,6=腾讯)
     */
    @ApiModelProperty("市场渠道平台 (1=百度,2=小红书,3=穿山甲,4=优量汇,5=今日头条,6=腾讯)")
    @Excel(name = "投放平台", readConverterExp = "1=百度,2=小红书,3=穿山甲,4=优量汇,5=今日头条,6=腾讯,7=其他")
    private Integer marketingPlatform;

    /**
     * 市场渠道名称
     */
    @ApiModelProperty("市场渠道名称")
    @Excel(name = "渠道名称")
    private String marketingChannelName;

    @ApiModelProperty("落地形式（1:官网首页，2:添加企微客服）")
    private Integer landingForm;

    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    @Excel(name = "创建人名称")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @Excel(name = "创建时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    @ApiModelProperty("独立访客")
    @Excel(name = "独立访客（UV）", defaultValue = "0")
    private Integer uniqueVisitor;

    @ApiModelProperty("新增注册数")
    @Excel(name = "注册用户", defaultValue = "0")
    private Integer newRegistrationCount;

    @ApiModelProperty(value = "会员成交数")
    @Excel(name = "会员成交数", defaultValue = "0")
    private Long memberNum;

    @ApiModelProperty(value = "会员总金额")
    @Excel(name = "会员总金额", defaultValue = "0.0")
    private BigDecimal realPayAmount;

    @Excel(name = "投流链接", defaultValue = "-")
    private String dedicatedLinkCode;
}
