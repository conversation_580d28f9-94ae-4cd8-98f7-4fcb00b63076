package com.ruoyi.system.api.domain.vo.biz.business;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/13 10:17
 */
@Data
public class BusinessCallbackRecordListVO implements Serializable {
    private static final long serialVersionUID = -3158818398450085944L;


    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 回访ID
     */
    @ApiModelProperty("回访ID")
    private Long callbackId;

    /**
     * 回访账号是否是主账号
     */
    @ApiModelProperty("回访账号是否是主账号")
    private Integer accountIsOwnerAccount;

    /**
     * 回访账号员工名称
     */
    @ApiModelProperty("回访账号员工名称")
    private String accountName;

    /**
     * 回访账号员工微信名称
     */
    @ApiModelProperty("回访账号员工微信名称")
    private String accountNickName;

    /**
     * 反馈类型
     */
    @ApiModelProperty("反馈类型")
    private String feedbackType;

    /**
     * 回访内容
     */
    @ApiModelProperty("回访内容")
    private String callbackContent;

    /**
     * 回访图片
     */
    @ApiModelProperty("回访图片")
    @JsonIgnore
    private String resourceId;

    /**
     * 回访图片
     */
    @ApiModelProperty("回访图片")
    private List<String> objectKeys;

    /**
     * 记录时间
     */
    @ApiModelProperty("记录时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date writeTime;

    /**
     * 记录人
     */
    @ApiModelProperty("记录人")
    private String writeBy;

    /**
     * 回访事件
     */
    @ApiModelProperty("回访事件")
    private List<BusinessCallbackEventVO> businessCallbackEventVOS;
}
