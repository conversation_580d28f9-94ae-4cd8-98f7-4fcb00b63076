package com.ruoyi.system.api.domain.vo.biz.datastatistics;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/30 13:53
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DataPieChartVO extends PieChartVO {
    private static final long serialVersionUID = -2625695396443308721L;

    /**
     * 实时更新时间
     */
    @ApiModelProperty("实时更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date dateTime;

    public void echo() {
        if (CollUtil.isNotEmpty(super.getPieChartVOS())) {
            for (PieChartVO pieChartVO : super.getPieChartVOS()) {
                pieChartVO.echo();
            }
        }
    }
}
