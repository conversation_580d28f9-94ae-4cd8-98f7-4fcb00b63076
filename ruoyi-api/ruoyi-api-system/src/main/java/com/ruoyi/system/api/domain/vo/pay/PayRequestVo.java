package com.ruoyi.system.api.domain.vo.pay;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;

import java.io.Serializable;

/**
 * 支付请求数据
 *
 * <AUTHOR>
 * @date 2024/5/31
 */

public class PayRequestVo implements Serializable {
    private static final long serialVersionUID = 1506884510808368035L;
    /**
     * 商户号
     */
    private String mchntCd;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 订单金额
     */
    private String orderAmt;

    /**
     * 商户订单号
     */
    private String mchntOrderNo;

    /**
     * 订单时间
     */
    private String txnBeginTs;

    /**
     * 商品描述 128字
     */
    private String goodsDes;

    /**
     * 终端号
     */
    private String termId = "1";

    /**
     * 终端IP
     */
    private String termIp;

    /**
     * 通知地址
     */
    private String notifyUrl;

    /**
     * 随机字符串
     */
    private String randomStr;

    /**
     * 版本号 v1.0
     */
    private String version = "1.0";

    /**
     * 商户密钥
     */
    private String mchntKey;

    public PayRequestVo() {
    }

    public PayRequestVo(String mchntCd, String orderType, String orderAmt, String mchntOrderNo, String txnBeginTs, String goodsDes, String termIp, String notifyUrl, String mchntKey) {
        this.mchntCd = mchntCd;
        this.orderType = orderType;
        this.orderAmt = orderAmt;
        this.mchntOrderNo = mchntOrderNo;
        this.txnBeginTs = txnBeginTs;
        this.goodsDes = goodsDes;
        this.termIp = termIp;
        this.notifyUrl = notifyUrl;
        this.mchntKey = mchntKey;
        this.randomStr = RandomUtil.randomString(16);
    }

    public PayRequestVo(String mchntCd, String orderType, String mchntOrderNo, String mchntKey) {
        this.mchntCd = mchntCd;
        this.orderType = orderType;
        this.mchntOrderNo = mchntOrderNo;
        this.mchntKey = mchntKey;
        this.randomStr = RandomUtil.randomString(16);
    }

    private String getPreOrderSign() {
        return SecureUtil.md5(mchntCd + "|"
                + orderType + "|"
                + orderAmt + "|"
                + mchntOrderNo + "|"
                + txnBeginTs + "|"
                + goodsDes + "|"
                + termId + "|"
                + termIp + "|"
                + notifyUrl + "|"
                + randomStr + "|"
                + version + "|"
                + mchntKey);
    }

    private String getQuerySign() {
        return SecureUtil.md5(mchntCd + "|"
                + orderType + "|"
                + mchntOrderNo + "|"
                + termId + "|"
                + randomStr + "|"
                + version + "|"
                + mchntKey);
    }

    public String getVersion() {
        return version;
    }

    public String getUnderlinePreOrderJsonStr() {
        return "{" +
                "\"mchnt_cd\":\"" + mchntCd + "\"," +
                "\"order_type\":\"" + orderType + "\"," +
                "\"order_amt\":\"" + orderAmt + "\"," +
                "\"mchnt_order_no\":\"" + mchntOrderNo + "\"," +
                "\"txn_begin_ts\":\"" + txnBeginTs + "\"," +
                "\"goods_des\":\"" + goodsDes + "\"," +
                "\"term_id\":\"" + termId + "\"," +
                "\"term_ip\":\"" + termIp + "\"," +
                "\"notify_url\":\"" + notifyUrl + "\"," +
                "\"random_str\":\"" + randomStr + "\"," +
                "\"version\":\"" + version + "\"," +
                "\"sign\":\"" + getPreOrderSign() + "\"" +
                "}";
    }

    public String getUnderlineQueryJsonStr() {
        return "{" +
                "\"mchnt_cd\":\"" + mchntCd + "\"," +
                "\"order_type\":\"" + orderType + "\"," +
                "\"mchnt_order_no\":\"" + mchntOrderNo + "\"," +
                "\"term_id\":\"" + termId + "\"," +
                "\"random_str\":\"" + randomStr + "\"," +
                "\"version\":\"" + version + "\"," +
                "\"sign\":\"" + getQuerySign() + "\"" +
                "}";
    }

    public void setOrderAmt(String orderAmt) {
        this.orderAmt = orderAmt;
    }
}
