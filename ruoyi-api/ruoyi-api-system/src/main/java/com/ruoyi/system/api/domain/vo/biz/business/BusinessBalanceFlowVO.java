package com.ruoyi.system.api.domain.vo.biz.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 余额流水表
 *
 * <AUTHOR>
 * @TableName business_balance_flow
 */
@Data
public class BusinessBalanceFlowVO implements Serializable {
    private static final long serialVersionUID = -3206162264681086313L;

    @TableId(type = IdType.AUTO)
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("余额流水号")
    private Long flowOrderNo;

    @ApiModelProperty("商家id")
    @NotNull(message = "[商家id]不能为空")
    private Long businessId;

    @NotBlank(message = "[订单号]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("订单号")
    @Length(max = 30, message = "编码长度不能超过30")
    private String orderNum;

    @NotBlank(message = "[退款审批号]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("退款审批号")
    @Length(max = 30, message = "编码长度不能超过30")
    private String refundNum;

    @ApiModelProperty("预付单号")
    private String prepayNum;

    @ApiModelProperty("提现单号")
    private String withdrawNumber;

    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("视频编码")
    @Length(max = 30, message = "编码长度不能超过30")
    private String videoCode;

    @ApiModelProperty("帐号余额")
    private BigDecimal balance;

    @ApiModelProperty("订单金额（单位：￥）")
    private BigDecimal amount;

    @NotNull(message = "[订单类型(0-收入、1-支出)]不能为空")
    @ApiModelProperty("订单类型(0-收入、1-支出)")
    private Integer type;

    @NotNull(message = "[订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、4.视频订单支出、5.会员订单支出、6.余额提现、7.线下钱包充值收入、8.线上钱包充值收入)]不能为空")
    @ApiModelProperty("订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、4.视频订单支出、5.会员订单支出、6.余额提现、7.线下钱包充值收入、8.线上钱包充值收入)")
    private Integer origin;

    @ApiModelProperty("订单交易时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    @ApiModelProperty("创建人id FK: business.id")
    private Long createUserId;

    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("创建人名称： FK: business.name")
    @Length(max = 30, message = "编码长度不能超过30")
    private String createUserName;

    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("修改人id	FK:  business.id")
    private Long updateUserId;

    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("修改人名称  FK: business.name")
    @Length(max = 30, message = "编码长度不能超过30")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String updateUserName;

    @NotNull(message = "[修改时间]不能为空")
    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("退款单号列表")
    private List<String> numberList;


}
