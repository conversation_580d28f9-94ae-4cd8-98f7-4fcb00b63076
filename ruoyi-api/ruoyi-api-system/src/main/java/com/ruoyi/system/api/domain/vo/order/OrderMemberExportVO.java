package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.enums.ChannelTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :订单会员表
 * @create :2024-06-24 16:04
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderMemberExportVO implements Serializable {

    private static final long serialVersionUID = -7105337902246005875L;

    private String wechatChannelStringType;

    @Excel(name = "订单编号")
    private String orderNum;

    @Excel(name = "商家昵称", defaultValue = StrPool.DASHED)
    private String nickName;

    private String businessAccount;

    @Excel(name = "购买标识", readConverterExp = "1=首次购买,2=续费购买", defaultValue = "-")
    private Integer isFirstBuy;

    @Excel(name = "会员编码", defaultValue = StrPool.DASHED)
    private String memberCode;

    @Excel(name = "下单时间", dateFormat = "yyyy-MM-dd HH:mm:ss", defaultValue = StrPool.DASHED)
    private Date orderTime;

    @Excel(name = "套餐类型", readConverterExp = "0=季度会员,1=一年会员,2=三年会员")
    private Integer packageType;

    //    @Excel(name = "优惠信息", defaultValue = StrPool.DASHED)
    private String orderDiscountDetail;

    @Excel(name = "优惠类型", defaultValue = StrPool.DASHED)
    private String orderDiscountType;

    @Excel(name = "渠道/裂变名称", defaultValue = StrPool.DASHED)
    private String orderDiscountChannelName;

    @Excel(name = "会员状态（裂变）", defaultValue = StrPool.DASHED, readConverterExp = "0=非会员裂变,1=会员裂变,2=会员裂变,3=非会员裂变")
    private Integer seedMemberStatus;

    @Excel(name = "结算方案", defaultValue = StrPool.DASHED)
    private String memberDiscount;

    @Excel(name = "会员折扣", defaultValue = StrPool.DASHED)
    private String orderDiscountDiscountRatio;

    @Excel(name = "优惠金额（￥）", defaultValue = StrPool.DASHED)
    private String orderDiscountDiscountAmount;

    private BigDecimal payAmountDollar;

    @Excel(name = "套餐金额（$）")
    private BigDecimal packageAmount;

    @Excel(name = "百度汇率", defaultValue = StrPool.DASHED)
    private BigDecimal currentExchangeRate;

    @Excel(name = "套餐金额（￥）")
    private BigDecimal orderAmount;

    @Excel(name = "添加企微来源", defaultValue = StrPool.DASHED)
    private String wechatChannelType;

    @Excel(name = "售前", defaultValue = StrPool.DASHED)
    private String connectUserName;

    @Excel(name = "支付方式", readConverterExp = "1=微信,2=支付宝,3=云闪付/银联,4=数字人民币,5=银行,6=对公,7=全币种,10=余额,11=微信+余额,12=支付宝+余额,13=云闪付/银联+余额,14=数字人民币+余额,15=银行+余额,16=对公+余额,17=全币种+余额", defaultValue = StrPool.DASHED)
    private Integer payType;

    @Excel(name = "收款账号", defaultValue = StrPool.DASHED)
    private String orderPayeeAccount;

    @Excel(name = "钱包抵扣")
    private BigDecimal useBalance;

    @Excel(name = "剩余支付")
    private BigDecimal surplusAmount;

    @Excel(name = "实付金额", defaultValue = StrPool.DASHED)
    private BigDecimal realPayAmountCurrency;

    @Excel(name = "币种", defaultValue = StrPool.DASHED)
    private String currency;

    @Excel(name = "实付人民币", defaultValue = StrPool.DASHED)
    private BigDecimal realPayAmount;

    @Excel(name = "订单状态", readConverterExp = "1=待支付,2=待审核,3=交易成功,4=交易关闭")
    private Integer status;


    public void setWechatChannelType(Object wechatChannelType) {
        if (wechatChannelType instanceof Integer) {
            Map<Integer, String> channelMap = new HashMap<>();
            String type = StrUtil.isNotBlank(getWechatChannelStringType()) ? getWechatChannelStringType() : StrUtil.DASHED;
            channelMap.put(ChannelTypeEnum.MARKETING.getCode(), ChannelTypeEnum.MARKETING.getTagLabel() + StrUtil.DASHED + type);
            channelMap.put(ChannelTypeEnum.DISTRIBUTION.getCode(), ChannelTypeEnum.DISTRIBUTION.getTagLabel() + StrUtil.DASHED + type);
            channelMap.put(ChannelTypeEnum.FISSION.getCode(), ChannelTypeEnum.FISSION.getTagLabel() + StrUtil.DASHED + type);
            channelMap.put(ChannelTypeEnum.BUSINESS.getCode(), ChannelTypeEnum.BUSINESS.getDesc());
            channelMap.put(ChannelTypeEnum.WEBSITE.getCode(), ChannelTypeEnum.WEBSITE.getDesc());
            this.wechatChannelType = channelMap.getOrDefault(wechatChannelType, StrUtil.DASHED);
        }
    }
}

