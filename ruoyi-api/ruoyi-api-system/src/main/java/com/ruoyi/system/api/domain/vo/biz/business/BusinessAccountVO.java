package com.ruoyi.system.api.domain.vo.biz.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberActivity;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberValidityFlow;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 商家账号表
 * <AUTHOR>
 * @TableName business_account
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessAccountVO implements Serializable {

    private static final long serialVersionUID = -2946851222699476476L;
    @NotNull(message="[主键ID]不能为空")
    @ApiModelProperty("主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("是否mock：1-是， 0-否")
    private Integer isMock;
    /**
     * 账号
     */
    @NotNull(message="[账号]不能为空")
    @ApiModelProperty("账号")
    private String account;

    /**
     * 商家id
     */
    @NotNull(message="[商家id]不能为空")
    @ApiModelProperty("商家id")
    private Long businessId;

    @ApiModelProperty("登录用户ID")
    private Long bizUserId;

    @ApiModelProperty("是否是主账号")
    private Integer isOwnerAccount;

    @ApiModelProperty("主账号")
    private String ownerAccount;
    /**
     * 名称
     */
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("名称")
    @Length(max= 20,message="编码长度不能超过20")
    private String name;
    /**
     * 微信昵称
     */
    @Size(max = 32, message = "微信昵称不能超过32个字")
    @ApiModelProperty("微信昵称")
    @Length(max = 32, message = "微信昵称不能超过32个字")
    private String nickName;
    /**
     * 头像
     */
    @Size(max= 60,message="编码长度不能超过60")
    @ApiModelProperty("头像")
    @Length(max= 60,message="编码长度不能超过60")
    private String pic;

    @Size(max= 16,message="编码长度不能超过16")
    @ApiModelProperty("unionId")
    @Length(max= 16,message="编码长度不能超过16")
    private String unionid;

    @ApiModelProperty("种草官ID")
    private String seedId;

    @Size(max= 32,message="编码长度不能超过32")
    @ApiModelProperty("ExternalUserID企业微信外部联系人id")
    @Length(max= 32,message="编码长度不能超过32")
    private String externalUserId;

    @ApiModelProperty("手机号")
    private String phone;
    /**
     * 账号状态（0正常 1禁用）
     */
    @NotNull(message="[账号状态（0正常 1禁用）]不能为空")
    @ApiModelProperty("账号状态（0正常 1禁用）")
    private Integer status;

    @ApiModelProperty("登录账号状态（0正常 1禁用）")
    private Integer userStatus;

    /**
     * 最后登录时间
     */
    @ApiModelProperty("最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    @ApiModelProperty("登录账号最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date userLastLoginTime;
    /**
     * 商家信息
     */
    @ApiModelProperty("商家信息")
    private BusinessVO businessVO;


    private String connectUserName;
    /**
     * 后端接口基本无需使用此数据
     */
    @ApiModelProperty("子账号列表信息")
    private List<BusinessAccountVO> businessAccountVOS;

    @ApiModelProperty("商家会员活动")
    private BusinessMemberActivity businessMemberActivity;
}