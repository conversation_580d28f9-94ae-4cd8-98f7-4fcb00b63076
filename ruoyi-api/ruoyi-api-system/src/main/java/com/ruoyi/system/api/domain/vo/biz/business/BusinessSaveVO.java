package com.ruoyi.system.api.domain.vo.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-24 09:28
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessSaveVO implements Serializable {
    private static final long serialVersionUID = -8820617542962904714L;

    @NotNull(message="[账号]不能为空")
    @ApiModelProperty("账号")
    private String account;

    @NotNull(message="[密码]不能为空")
    @ApiModelProperty("密码")
    @Size(max= 200, message="编码长度不能超过200")
    @Length(max= 200, message="编码长度不能超过200")
    private String password;
}
