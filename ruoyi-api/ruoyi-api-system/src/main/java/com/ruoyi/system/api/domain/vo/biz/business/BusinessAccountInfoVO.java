package com.ruoyi.system.api.domain.vo.biz.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 商家账号表
 * <AUTHOR>
 * @TableName business_account
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessAccountInfoVO implements Serializable {
    private static final long serialVersionUID = 8295280922203211528L;
    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("是否mock：1-是， 0-否")
    private Integer isMock;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("商家id")
    private Long businessId;

    @ApiModelProperty("登录用户ID")
    private Long bizUserId;

    @ApiModelProperty("种草官ID")
    private String seedId;

    @ApiModelProperty("是否是主账号")
    private Integer isOwnerAccount;

    @ApiModelProperty("主账号")
    private String ownerAccount;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("微信昵称")
    private String nickName;

    @ApiModelProperty("头像")
    private String pic;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("账号状态（0正常 1禁用）")
    private Integer status;

    @ApiModelProperty("登录账号状态（0正常 1禁用）")
    private Integer userStatus;

    @ApiModelProperty("最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    @ApiModelProperty("登录账号最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date userLastLoginTime;

    @ApiModelProperty("商家信息")
    private BusinessVO businessVO;

    @ApiModelProperty("子账号列表信息")
    private List<BusinessAccountInfoVO> businessAccountVOS;
}