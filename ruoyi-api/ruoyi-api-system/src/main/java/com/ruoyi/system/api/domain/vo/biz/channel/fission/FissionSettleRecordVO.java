package com.ruoyi.system.api.domain.vo.biz.channel.fission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :裂变结算记录
 * @create :2025-05-20 10:48
 **/
@Data
public class FissionSettleRecordVO extends MemberSeedRecordWithdrawalVO implements Serializable {
    private static final long serialVersionUID = 5361533069680948795L;

    @ApiModelProperty("邀请商家名称")
    private String inviteBusinessName;

    @ApiModelProperty("邀请商家会员编码")
    private String inviteMemberCode;

    @ApiModelProperty("邀请商家微信昵称")
    private String inviteBizUserNickName;

    @ApiModelProperty("订单结算金额（裂变结算记录使用）")
    private BigDecimal orderSettleAmount;

    @ApiModelProperty("结算类型（1-固定金额，2-固定比例）")
    private Integer settleType;

    @ApiModelProperty("结算比例/固定金额")
    private BigDecimal settleRage;

    @ApiModelProperty("关联id")
    private Long relevanceId;

}
