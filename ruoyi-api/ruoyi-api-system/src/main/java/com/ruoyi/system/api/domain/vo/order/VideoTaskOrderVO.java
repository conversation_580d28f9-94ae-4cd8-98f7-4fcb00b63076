package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/12 9:45
 */
@Data
public class VideoTaskOrderVO {
    /**
     * 主键 order_video_task.id
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 视频订单id FK:order_video.id
     */
    @ApiModelProperty(value = "视频订单id")
    private Long videoId;

    /**
     * 任务单类型（1：售后单，2：工单）
     */
    @ApiModelProperty(value = "任务单类型（1：售后单，2：工单）")
    private Integer taskType;

    /**
     * 状态（1：待处理，2：处理中，3：申请取消中，4：已完成，5：已拒绝，6：已关闭）
     */
    @ApiModelProperty(value = "状态（1：待处理，2：处理中，3：申请取消中，4：已完成，5：已拒绝，6：已关闭）")
    private Integer status;

    @ApiModelProperty(value = "售后视频类型（1：重拍视频，2：补拍视频，3：要高清视频，4：原素材，5：重新上传，6：重新剪辑)")
    private Integer afterSaleVideoType;
}
