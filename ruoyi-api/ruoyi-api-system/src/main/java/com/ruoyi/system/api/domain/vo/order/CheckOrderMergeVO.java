package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/11 11:08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckOrderMergeVO implements Serializable {
    private static final long serialVersionUID = -3011991543927570297L;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    /**
     * 关联订单
     */
    @ApiModelProperty(value = "关联订单")
    private List<OrderVO> orderVOS;
}
