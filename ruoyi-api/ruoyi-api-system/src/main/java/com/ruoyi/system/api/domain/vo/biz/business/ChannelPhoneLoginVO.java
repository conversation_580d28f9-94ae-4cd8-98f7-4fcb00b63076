package com.ruoyi.system.api.domain.vo.biz.business;

import com.ruoyi.common.core.enums.WxChatLoginStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelPhoneLoginVO implements Serializable {

    private static final long serialVersionUID = 2776624333590213546L;

    @ApiModelProperty("登录状态")
    WxChatLoginStatusEnum loginStatus;

    @ApiModelProperty("meg")
    private String msg;

    @ApiModelProperty("账号信息")
    BusinessAccountVO businessAccountVO;

    @ApiModelProperty("token")
    String token;
}
