package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.api.domain.vo.biz.model.AddPreselectModelListVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/5 17:44
 */
@Data
public class MarkOrderVO implements Serializable {
    private static final long serialVersionUID = -8771578335327848712L;
    /**
     * 匹配单ID
     */
    @ApiModelProperty(value = "匹配单ID")
    private Long id;

    /**
     * 模特信息
     */
    @ApiModelProperty(value = "模特信息")
    private AddPreselectModelListVO preselectModelVO;

    /**
     * 排单类型（1:排单,2:携带排单）
     */
    @ApiModelProperty(value = "排单类型（1:排单,2:携带排单）")
    private Integer scheduleType;

    /**
     * 原模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "原模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String originCommissionUnit;

    /**
     * 原模特佣金
     */
    @ApiModelProperty(value = "原模特佣金")
    private BigDecimal originCommission;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    private BigDecimal commission;

    /**
     * 超额说明
     */
    @ApiModelProperty(value = "超额说明")
    private String overstatement;

    /**
     * 携带类型（1:主携带,2:被携带）
     */
    @ApiModelProperty(value = "携带类型（1:主携带,2:被携带）")
    private Integer carryType;

    /**
     * 主携带数量
     */
    @ApiModelProperty(value = "主携带数量")
    private Integer mainCarryCount;

    /**
     * 主携带订单id
     */
    @ApiModelProperty(value = "主携带订单id")
    private Long mainCarryVideoId;

    /**
     * 主携带订单视频编码
     */
    @ApiModelProperty(value = "主携带订单视频编码")
    private String mainCarryVideoCode;

    /**
     * 发货备注
     */
    @ApiModelProperty(value = "发货备注")
    private String shippingRemark;

    /**
     * 发货备注图片（关联资源id，多个用,隔开）
     */
    @JsonIgnore
    private String shippingPic;

    /**
     * 发货图片
     */
    @ApiModelProperty(value = "发货图片")
    private List<String> shippingPics = new ArrayList<>();

    /**
     * 选定时间
     */
    @ApiModelProperty(value = "选定时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date selectedTime;

    /**
     * 添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发）
     */
    @ApiModelProperty(value = "添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发）")
    private Integer addType;

    /**
     * 模特剩余需携带订单数
     */
    @ApiModelProperty(value = "模特剩余需携带订单数")
    private Long leftCarryCount = 0L;

    /**
     * 拍摄模特注意事项
     */
    @ApiModelProperty(value = "拍摄模特注意事项")
    private String shootAttention;

    /**
     * 拍摄注意事项对象存储键值
     */
    @ApiModelProperty("拍摄注意事项对象存储键值")
    private String shootAttentionObjectKey;
}
