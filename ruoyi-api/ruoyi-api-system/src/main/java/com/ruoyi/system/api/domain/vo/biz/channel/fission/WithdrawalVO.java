package com.ruoyi.system.api.domain.vo.biz.channel.fission;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :提现返回
 * @create :2025-05-19 14:20
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawalVO implements Serializable {
    private static final long serialVersionUID = -6851721070387004283L;

    @ApiModelProperty("提现金额")
    private BigDecimal settleAmount;

    @ApiModelProperty("提现账号类型(2-支付宝，3-银行卡，6-公户收款)")
    private Integer withdrawalAccountType;

    @ApiModelProperty("提现单号")
    private String withdrawalNum;

}
