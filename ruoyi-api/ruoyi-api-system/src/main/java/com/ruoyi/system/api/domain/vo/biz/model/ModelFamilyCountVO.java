package com.ruoyi.system.api.domain.vo.biz.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-11-28 11:25
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ModelFamilyCountVO implements Serializable {
    private static final long serialVersionUID = -7793628332172610631L;

    @ApiModelProperty(value = "家庭id")
    private Long familyId;

    @ApiModelProperty(value = "家庭成员数量")
    private Integer count;
}
