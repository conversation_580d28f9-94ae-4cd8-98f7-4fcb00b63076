package com.ruoyi.system.api.model;

import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.system.api.domain.entity.SysUser;
import lombok.Data;

import java.io.Serializable;
import java.util.Set;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@Data
public class LoginUser extends LoginBaseEntity implements Serializable
{

    private static final long serialVersionUID = -4957951569420378095L;
    /**
     * 权限列表
     */
    private Set<String> permissions;

    /**
     * 角色列表
     */
    private Set<String> roles;

    /**
     * 用户信息
     */
    private SysUser sysUser;
}
