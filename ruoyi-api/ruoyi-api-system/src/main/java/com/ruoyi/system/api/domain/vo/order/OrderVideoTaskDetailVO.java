package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-12-10
 */
@Data
public class OrderVideoTaskDetailVO implements Serializable {
    private static final long serialVersionUID = 5188457981262255360L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 任务单类型（1：售后单，2：工单）
     */
    @ApiModelProperty(value = "任务单类型（1：售后单，2：工单）")
    private Integer taskType;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTime;

    /**
     * 售后视频类型（1：重拍视频，2：补拍视频，3：要高清视频，4：原素材，5：重新上传，6：重新剪辑）
     */
    @ApiModelProperty(value = "售后视频类型（1：重拍视频，2：补拍视频，3：要高清视频，4：原素材，5：重新上传，6：重新剪辑）")
    private Integer afterSaleVideoType;

    /**
     * 售后照片类型（1：重拍照片，2：要高清照片，3：补拍照片，4：原素材）
     */
    @ApiModelProperty(value = "售后照片类型（1：重拍照片，2：要高清照片，3：补拍照片，4：原素材）")
    private Integer afterSalePicType;

    /**
     * 工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）
     */
    @ApiModelProperty(value = "工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）")
    private Integer workOrderType;

    /**
     * 问题描述
     */
    @ApiModelProperty(value = "问题描述")
    private String content;
}
