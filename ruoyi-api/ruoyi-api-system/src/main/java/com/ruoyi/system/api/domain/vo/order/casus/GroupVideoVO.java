package com.ruoyi.system.api.domain.vo.order.casus;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
* 案例视频表
* <AUTHOR>
 * @TableName case_video
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GroupVideoVO implements Serializable {

    private static final long serialVersionUID = -2365991170721487911L;
    @ApiModelProperty("分组id")
    private Long groupId;

    @ApiModelProperty("视频id")
    private Long videoId;

    @ApiModelProperty("视频名称")
    private String videoName;

    @ApiModelProperty("封面图片")
    private String videoPic;

    @ApiModelProperty("加入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date joinTime;

    @ApiModelProperty("视频链接")
    private String videoLink;

    @ApiModelProperty("排序")
    private Integer sort;
}
