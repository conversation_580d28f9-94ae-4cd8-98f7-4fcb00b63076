package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/7/24 18:18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateOrderVO implements Serializable {
    private static final long serialVersionUID = 3928068486896555280L;
    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNum;

    /**
     * 不能接单的模特Id
     */
    @ApiModelProperty("不能接单的模特Id")
    private Collection<Long> cannotModel;

    /**
     * 不能接单的购物车Id
     */
    @ApiModelProperty("不能下单的购物车订单Id")
    private Collection<Long> cannotShoppingCartId;

    /**
     * 不能接单的模特姓名
     */
    @ApiModelProperty("不能接单的模特姓名")
    private Collection<String> cannotModelNames;

    /**
     * 不符合订单要求的订单序号
     */
    @ApiModelProperty("不符合订单要求的订单序号")
    private Map<Integer, Collection<Long>> unfulfilledOrderModeSerialNumberMap;
}
