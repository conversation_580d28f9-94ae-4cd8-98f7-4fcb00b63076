package com.ruoyi.system.api.domain.vo.order.datastatistics;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12 11:44
 */
@Data
public class ModelSuccessMatchCountVO implements Serializable {
    private static final long serialVersionUID = 467962775727998382L;


    /**
     * 实时更新时间
     */
    @ApiModelProperty("实时更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date dateTime;

    /**
     * 成功匹配次数
     */
    @ApiModelProperty("成功匹配次数")
    private Long successMatchCount;

    /**
     * 商家意向次数
     */
    @JsonIgnore
    private Long merchantIntentionCount;

    /**
     * 客服自选次数
     */
    @JsonIgnore
    private Long serviceCustomerCount;

    /**
     * 模特自选次数
     */
    @JsonIgnore
    private Long modelCustomerCount;

    /**
     * 客服分发次数
     */
    @JsonIgnore
    private Long serviceDistributionCount;

    /**
     * 匹配拍摄模特来源饼图
     */
    @ApiModelProperty("匹配拍摄模特来源饼图")
    private List<PieChartVO> pieChartVOS;

    public ModelSuccessMatchCountVO echo() {
        if (CollUtil.isNotEmpty(pieChartVOS)) {
            for (PieChartVO pieChartVO : pieChartVOS) {
                pieChartVO.echo();
            }
        } else {
            pieChartVOS = Collections.emptyList();
        }
        successMatchCount = successMatchCount == null ? 0L : successMatchCount;
        return this;
    }
}
