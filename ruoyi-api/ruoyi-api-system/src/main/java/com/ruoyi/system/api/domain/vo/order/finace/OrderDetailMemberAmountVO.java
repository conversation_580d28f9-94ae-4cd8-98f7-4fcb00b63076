package com.ruoyi.system.api.domain.vo.order.finace;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :订单收支明细视频金额详情
 * @create :2024-09-12 11:41
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderDetailMemberAmountVO implements Serializable {
    private static final long serialVersionUID = -6811997525835798710L;

    @ApiModelProperty(value = "会员费用")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "税点费用（单位：￥）")
    private BigDecimal taxPointCost;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "使用余额")
    private BigDecimal useBalance;

    @ApiModelProperty(value = "剩余支付金额")
    private BigDecimal surplusAmount;
    
    @ApiModelProperty(value = "实际支付金额")
    private BigDecimal realPayAmount;

    @ApiModelProperty(value = "套餐类型：0-季度会员，1-年度会员, 2-三年会员")
    private Integer packageType;

    @ApiModelProperty(value = "折扣类型：1-种草折扣")
    private Integer discountType;

    @ApiModelProperty(value = "折扣金额")
    private BigDecimal discountAmount;


    @ApiModelProperty(value = "差额（单位：￥）")
    private BigDecimal differenceAmount;

    @ApiModelProperty(value = "订单原价（单位：￥）")
    private BigDecimal orderAmount;
}
