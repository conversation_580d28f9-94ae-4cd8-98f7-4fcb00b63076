package com.ruoyi.system.api.domain.vo.order.casus;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
* 案例分组添加视频列表
* <AUTHOR>
 * @TableName case_video
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GroupAddVideoVO implements Serializable {

    private static final long serialVersionUID = 714278541621924768L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("视频名称")
    private String name;

    @ApiModelProperty("封面图片")
    private String pic;

}
