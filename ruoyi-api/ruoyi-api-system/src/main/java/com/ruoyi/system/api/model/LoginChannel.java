package com.ruoyi.system.api.model;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.enums.ChanelAccountTypeEnum;
import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.system.api.domain.vo.biz.channel.DistributionChannelVO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-12-02 14:19
 **/
@Data
public class LoginChannel extends LoginBaseEntity implements Serializable {
    private static final long serialVersionUID = 529747250277149148L;

    public LoginChannel() {
    }

    private DistributionChannelVO distributionChannelVo;

    public LoginChannel(DistributionChannelVO distributionChannelVo) {
        if (ObjectUtil.isNull(distributionChannelVo.getChannelAccountType())){
            distributionChannelVo.setChannelAccountType(ChanelAccountTypeEnum.NORMAL.getCode());
        }
        this.distributionChannelVo = distributionChannelVo;
        updateInfo();
    }


    public void updateInfo() {
        this.setUserType(UserTypeConstants.CHANNEL);
        this.setUserid(distributionChannelVo.getId());
        //当登录账号名称为空的时候 使用微信名称代替
        this.setUsername(distributionChannelVo.getChannelName());
    }
}
