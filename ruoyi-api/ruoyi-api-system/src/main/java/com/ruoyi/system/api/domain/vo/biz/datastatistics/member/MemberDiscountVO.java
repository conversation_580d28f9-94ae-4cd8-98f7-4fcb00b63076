package com.ruoyi.system.api.domain.vo.biz.datastatistics.member;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.ChannelDiscountTypeEnum;
import com.ruoyi.common.core.enums.PromotionActivityTypeEnum;
import com.ruoyi.common.core.text.Convert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会员折扣VO
 * @create :2025-06-19 11:30
 **/
@Data
public class MemberDiscountVO implements Serializable {
    private static final long serialVersionUID = 6334792264509062708L;

    @ApiModelProperty("活动ID")
    private Long activityId;

    @ApiModelProperty(value = "优惠扣减类型（1：直减，2：折扣）")
    private Integer discountType;

    @ApiModelProperty(value = "优惠数值")
    private BigDecimal amount;

    @ApiModelProperty(value = "数量")
    private Long count;

    public String getStatisticsLabelName(){
        StringBuilder sb = new StringBuilder();

        if (ObjectUtil.isNotNull(activityId)){
            if (Convert.toLong(PromotionActivityTypeEnum.SEED_CODE_DISTRIBUTION_DISCOUNT.getCode()).compareTo(activityId) == 0){
                sb.append("渠道-");
            }else if (Convert.toLong(PromotionActivityTypeEnum.SEED_CODE_FISSION_DISCOUNT.getCode()).compareTo(activityId) == 0){
                sb.append("裂变-");
            }
        }else {
            sb.append("普通");
        }

        if (ObjectUtil.isNotNull(discountType)){
            if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(discountType)){
                sb.append("立减");
                sb.append(Convert.toStr(amount.longValue()));
            }else if (ChannelDiscountTypeEnum.FIXED_RATIO.getCode().equals(discountType)){
                int amountValue = amount.intValue();
                if (amountValue % 10 == 0){
                    sb.append(amountValue / 10);
                }else {
                    sb.append(amountValue / 10.0);
                }
                sb.append("折");
            }
        }
        return sb.toString();
    }
}
