package com.ruoyi.system.api.domain.vo.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/2/13 18:02
 */
@Data
public class BusinessCallbackStatusCountVO implements Serializable {
    private static final long serialVersionUID = -7530572301962665422L;

    /**
     * 待回访数量
     */
    @ApiModelProperty("待回访数量")
    private Integer waitForReturnVisitCount;

    /**
     * 回访中数量
     */
    @ApiModelProperty("回访中数量")
    private Integer inTheReturnVisitCount;

    /**
     * 已回访数量
     */
    @ApiModelProperty("已回访数量")
    private Integer alreadyVisitedCount;
}
