package com.ruoyi.system.api.domain.vo.order;

import com.ruoyi.system.api.domain.entity.order.OrderVideoContent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/2 14:14
 */
@Data
public class OrderVideoCautionsVO {

    /**
     * 注意事项 图片
     */
    @ApiModelProperty("注意事项 图片")
    private List<String> cautionsPics = new ArrayList<>();

    /**
     * 模特要求（原匹配模特注意事项）
     */
    @ApiModelProperty("模特要求（原匹配模特注意事项）")
    private List<OrderVideoContent> cautions;
}
