package com.ruoyi.system.api.domain.vo.biz.channel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 市场渠道详情返回对象VO
 *
 * <AUTHOR>
 * @date 2024/9/25 9:27
 */
@Data
public class MarketingChannelDetailVO implements Serializable {

    private static final long serialVersionUID = 2468658168563601692L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 市场渠道平台 (1=百度,2=小红书,3=穿山甲,4=优量汇,5=今日头条,6=腾讯)
     */
    @ApiModelProperty("市场渠道平台 (1=百度,2=小红书,3=穿山甲,4=优量汇,5=今日头条,6=腾讯)")
    private Integer marketingPlatform;

    /**
     * 市场渠道名称
     */
    @ApiModelProperty("市场渠道名称")
    private String marketingChannelName;

    /**
     * 落地形式
     */
    @ApiModelProperty("落地形式（1:官网首页，2:添加企微客服）")
    private Integer landingForm;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("专属链接code")
    private String dedicatedLinkCode;
}
