package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Optional;

/**
 * 订单状态VO
 *
 * <AUTHOR>
 * @date 2024/6/6 17:53
 */
@Data
public class OrderStatusVO implements Serializable {
    private static final long serialVersionUID = -2233912356153825561L;
    @ApiModelProperty(value = "待支付订单数量")
    private Integer unPayCount;
    @ApiModelProperty(value = "待确认订单数量")
    private Integer unConfirmCount;
    @ApiModelProperty(value = "待审核订单数量")
    private Integer unCheckCount;
    @ApiModelProperty(value = "待匹配订单数量")
    private Integer unMatchCount;
    @ApiModelProperty(value = "待发货订单数量")
    private Integer unFilledCount;
    @ApiModelProperty(value = "待完成订单数量")
    private Integer unFinishedCount;
    @ApiModelProperty(value = "需确认订单数量")
    private Integer needConfirmCount;
    @ApiModelProperty(value = "已完成订单数量")
    private Integer finishedCount;
    @ApiModelProperty(value = "待上传订单数量")
    private Integer unUploadCount;
    @ApiModelProperty(value = "进行中订单数量")
    private Integer underwayCount;

    public void initUnderwayCount(){
            this.underwayCount = Optional.ofNullable(this.unFinishedCount).orElse(0)
                    + Optional.ofNullable(this.unMatchCount).orElse(0)
                    + Optional.ofNullable(this.unFilledCount).orElse(0)
                    + Optional.ofNullable(this.needConfirmCount).orElse(0);

    }
}
