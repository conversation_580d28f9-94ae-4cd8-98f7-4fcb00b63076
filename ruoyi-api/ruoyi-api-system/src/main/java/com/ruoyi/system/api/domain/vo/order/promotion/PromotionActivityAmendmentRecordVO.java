package com.ruoyi.system.api.domain.vo.order.promotion;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/21 15:54
 */
@Data
public class PromotionActivityAmendmentRecordVO implements Serializable {
    private static final long serialVersionUID = 6722695469800361245L;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    /**
     * 优惠扣减类型（1：直减，2：折扣）
     */
    @ApiModelProperty(value = "优惠扣减类型（1：直减，2：折扣）")
    private Integer discountType;

    /**
     * 优惠数值
     */
    @ApiModelProperty(value = "优惠数值")
    private BigDecimal amount;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;


    @ApiModelProperty("结算佣金类型（1-固定金额，2-固定比例）")
    private Integer settleDiscountType;

    @ApiModelProperty("佣金比例、固定金额")
    private BigDecimal settleDiscount;


    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date endTime;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}
