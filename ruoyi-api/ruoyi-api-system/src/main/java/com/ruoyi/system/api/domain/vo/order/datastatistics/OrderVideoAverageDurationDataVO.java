package com.ruoyi.system.api.domain.vo.order.datastatistics;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/10 10:18
 */
@Data
public class OrderVideoAverageDurationDataVO implements Serializable {
    private static final long serialVersionUID = 5213950791391638943L;

    /**
     * 实时更新时间
     */
    @ApiModelProperty("实时更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date dateTime;

    /**
     * 平均审单时长
     */
    @ApiModelProperty("平均审单时长")
    private BigDecimal averageApprovalDuration;

    /**
     * 平均服务时长
     */
    @ApiModelProperty("平均服务时长")
    private BigDecimal averageServiceDuration;

    /**
     * 任务单数
     */
    @ApiModelProperty("任务单数")
    private Long taskOrderCount;

    /**
     * 任务单率
     */
    @ApiModelProperty("任务单率")
    private BigDecimal taskOrderRate;

    /**
     * 拖单数
     */
    @ApiModelProperty("拖单数")
    private Long dragOrderCount;

    /**
     * 拖单率
     */
    @ApiModelProperty("拖单率")
    private BigDecimal dragOrderRate;

    /**
     * 烂单数
     */
    @ApiModelProperty("烂单数")
    private Long badOrderCount;

    /**
     * 烂单率
     */
    @ApiModelProperty("烂单率")
    private BigDecimal badOrderRate;

    public OrderVideoAverageDurationDataVO echo() {
        dateTime = dateTime == null ? DateUtil.date() : dateTime;
        averageApprovalDuration = averageApprovalDuration == null ? BigDecimal.ZERO : averageApprovalDuration.setScale(2, RoundingMode.HALF_UP);
        averageServiceDuration = averageServiceDuration == null ? BigDecimal.ZERO : averageServiceDuration.setScale(2, RoundingMode.HALF_UP);
        taskOrderCount = taskOrderCount == null ? 0L : taskOrderCount;
        taskOrderRate = taskOrderRate == null ? BigDecimal.ZERO : taskOrderRate.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        dragOrderCount = dragOrderCount == null ? 0L : dragOrderCount;
        dragOrderRate = dragOrderRate == null ? BigDecimal.ZERO : dragOrderRate.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        badOrderCount = badOrderCount == null ? 0L : badOrderCount;
        badOrderRate = badOrderRate == null ? BigDecimal.ZERO : badOrderRate.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        return this;
    }
}
