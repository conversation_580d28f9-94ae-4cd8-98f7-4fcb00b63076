
package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/15
 */
@Data
public class OrderVideoOperateVO implements Serializable {

    private static final long serialVersionUID = 2694029177219256138L;
    /**
     * 事件名称
     */
    @ApiModelProperty("事件名称")
    private String eventName;

    /**
     * 事件内容
     */
    @ApiModelProperty("事件内容")
    private String eventContent;

    /**
     * 事件执行时间
     */
    @ApiModelProperty("事件执行时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date eventExecuteTime;
}
