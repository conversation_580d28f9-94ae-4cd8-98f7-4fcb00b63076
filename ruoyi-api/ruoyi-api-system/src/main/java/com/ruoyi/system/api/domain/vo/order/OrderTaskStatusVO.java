package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
public class OrderTaskStatusVO implements Serializable {
    private static final long serialVersionUID = 3682549956713293870L;

    @ApiModelProperty(value = "待处理数量")
    private Integer unHandleCount;

    @ApiModelProperty(value = "处理中数量")
    private Integer handleIngCount;
    @ApiModelProperty(value = "申请取消中数量")
    private Integer applicationForCancellationCount;

    @ApiModelProperty(value = "已处理数量")
    private Integer handleCount;
    @ApiModelProperty(value = "已拒绝数量")
    private Integer rejectCount;
    @ApiModelProperty(value = "已关闭数量")
    private Integer closeCount;

    @ApiModelProperty(value = "总数量")
    private Integer totalCount;
}
