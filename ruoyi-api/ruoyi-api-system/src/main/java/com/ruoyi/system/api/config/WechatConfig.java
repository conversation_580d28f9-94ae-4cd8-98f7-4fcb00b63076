package com.ruoyi.system.api.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 公众号信息
 *
 * <AUTHOR>
 * @date 2024/6/19
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "tencent.wechat.subscribe")
public class WechatConfig {

    String appId;

    String secret;

    String redirectUrl;

    String redirectPlusUrl;

    Integer type;

    String url;
}
