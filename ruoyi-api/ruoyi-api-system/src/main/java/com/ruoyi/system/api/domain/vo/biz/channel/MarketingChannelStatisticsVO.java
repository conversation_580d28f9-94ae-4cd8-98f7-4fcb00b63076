package com.ruoyi.system.api.domain.vo.biz.channel;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :市场渠道统计
 * @create :2024-12-09 09:48
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MarketingChannelStatisticsVO implements Serializable {
    private static final long serialVersionUID = -689964195294702612L;

    @ApiModelProperty("独立访客")
    private Integer uniqueVisitor;

    @ApiModelProperty("新增注册数")
    private Integer newRegistrationCount;


    @ApiModelProperty("添加企微数")
    private Integer wechatCount;

    @ApiModelProperty(value = "会员成交数")
    private Long memberNum;

    @ApiModelProperty(value = "会员总金额")
    private BigDecimal realPayAmount;
}
