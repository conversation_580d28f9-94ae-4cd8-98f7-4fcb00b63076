package com.ruoyi.system.api.domain.vo.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/11/1 17:53
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RealTimeExchangeRateVO {

    /**
     * 实时汇率
     */
    private BigDecimal realTimeExchangeRate;

    /**
     * 是否是默认数据
     */
    private boolean isDefault;
}
