package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :商家余额锁定记录
 * @create :2024-11-15 16:39
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BalanceLockRecordVO implements Serializable {
    private static final long serialVersionUID = 2857120409027326675L;

    @ApiModelProperty(value = "类型：0-视频,1-会员,2-提现")
    private Integer type;

    @ApiModelProperty(value = "订单号")
    private String orderNum;

    @ApiModelProperty(value = "锁定金额")
    private BigDecimal amount;
}
