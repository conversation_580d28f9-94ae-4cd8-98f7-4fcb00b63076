package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/22 18:37
 */
@Data
public class ModelCarryVO implements Serializable {
    private static final long serialVersionUID = 5079242173742480441L;

    /**
     * 视频订单ID
     */
    @ApiModelProperty(value = "视频订单ID")
    private Long videoId;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 模特ID
     */
    @ApiModelProperty(value = "模特ID")
    private Long modelId;

    /**
     * 模特需携带订单数
     */
    @ApiModelProperty(value = "模特需携带订单数")
    private Long mainCarryCount = 0L;

    /**
     * 模特已携带订单数
     */
    @ApiModelProperty(value = "模特已携带订单数")
    private Long carriedCount = 0L;

    /**
     * 模特剩余需携带订单数
     */
    @ApiModelProperty(value = "模特剩余需携带订单数")
    private Long leftCarryCount = 0L;
}
