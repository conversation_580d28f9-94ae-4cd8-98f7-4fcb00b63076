package com.ruoyi.system.api.domain.vo.biz.business.user;

import com.ruoyi.common.core.enums.WxChatLoginStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-29 09:48
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SaveBizUserVO implements Serializable {
    private static final long serialVersionUID = 4382092324190032542L;

    @ApiModelProperty("扫码状态")
    private WxChatLoginStatusEnum loginStatus;

    @ApiModelProperty("登录账号数据")
    private BizUserVO bizUser;


}
