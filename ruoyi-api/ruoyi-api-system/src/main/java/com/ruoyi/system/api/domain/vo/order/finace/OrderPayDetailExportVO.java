package com.ruoyi.system.api.domain.vo.order.finace;

import cn.hutool.core.text.StrPool;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单收支明细
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@ApiModel(value = "订单对象VO")
@Data
public class OrderPayDetailExportVO implements Serializable {
    private static final long serialVersionUID = 734687070967988311L;

    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @Excel(name = "订单号")
    private String orderNum;

    @Excel(name = "支付号")
    private String payNum;

    @Excel(name = "交易流水号")
    private String mchntOrderNo;

    @Excel(name = "审核", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @Excel(name = "会员编码")
    private String merchantCode;

//    @Excel(name = "商家昵称")
    private String businessNickName;

    @Excel(name = "商家名称")
    private String businessName;

//    @Excel(name = "下单商家")
//    private String  businessInfo;

    @Excel(name = "来源", readConverterExp = "0=视频订单,1=会员订单,3=线下钱包充值订单,5=线上钱包充值订单")
    private Integer orderType;

    @Excel(name = "支付方式", readConverterExp = "1=微信,2=支付宝,3=云闪付/银联,4=数字人民币,5=银行,6=对公,7=全币种,10=余额,11=微信+余额,12=支付宝+余额,13=云闪付/银联+余额,14=数字人民币+余额,15=银行+余额,16=对公+余额,17=全币种+余额,99=其他")
    private Integer payType;

    @Excel(name = "付款账号", width = 45, defaultValue = StrPool.DASHED)
    private String payAccount;

    @Excel(name = "收款账号", width = 45, height = 42, defaultValue = StrPool.DASHED)
    private String bankAccount;

    @Excel(name = "订单总价")
    private BigDecimal payAmount;

    /**
     * 半价续费
     */
    @Excel(name = "半价续费", defaultValue = StrPool.DASHED)
    private BigDecimal renewAtHalfPrice;

    /**
     * 种草码优惠
     */
    @Excel(name = "种草码优惠", defaultValue = StrPool.DASHED)
    private BigDecimal seedCodeDiscount;

    /**
     * 满5单减100
     */
    @Excel(name = "限时满减活动", defaultValue = StrPool.DASHED)
    private BigDecimal fullConcession;

    /**
     * 每月首单立减优惠方案
     */
    @Excel(name = "每月首单立减优惠方案", defaultValue = StrPool.DASHED)
    private String monthFirstOrderDiscount;

    /**
     * 每月首单立减金额
     */
    @Excel(name = "每月首单立减金额", defaultValue = StrPool.DASHED)
    private BigDecimal monthFirstOrderDiscountActivity;

    /**
     * 订单优惠总额
     */
    @Excel(name = "优惠金额", defaultValue = StrPool.DASHED)
    private BigDecimal totalOrderDiscount;

    @Excel(name = "钱包支付")
    private BigDecimal useBalance;

    @Excel(name = "剩余支付")
    private BigDecimal surplusAmount;

    @Excel(name = "实付金额", defaultValue = StrPool.DASHED)
    private BigDecimal realPayAmountCurrency;

    @Excel(name = "币种", defaultValue = StrPool.DASHED)
    private String currency;

    @Excel(name = "实付人民币")
    private BigDecimal realPayAmount;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @Excel(name = "全币种支付类型", readConverterExp = "701=其他平台/银行,702=万里汇", defaultValue = StrPool.DASHED)
    private Integer payTypeDetail;
}
