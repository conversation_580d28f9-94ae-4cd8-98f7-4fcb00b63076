package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.text.StrPool;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/24 10:35
 */
@Data
public class ExportNeedConfirmListVO implements Serializable {
    private static final long serialVersionUID = -4826921913031767903L;

    /**
     * 视频编码
     */
    @Excel(name = "视频编码", defaultValue = StrPool.DASHED)
    private String videoCode;

    /**
     * 产品中文名
     */
    @Excel(name = "中文名称", defaultValue = StrPool.DASHED)
    private String productChinese;

    /**
     * 产品英文名
     */
    @Excel(name = "英文名称", defaultValue = StrPool.DASHED)
    private String productEnglish;

    /**
     * 产品链接
     */
    @Excel(name = "产品链接", defaultValue = StrPool.DASHED)
    private String productLink;

    /**
     * 创建订单用户名称（订单运营）
     */
    @Excel(name = "订单运营", defaultValue = StrPool.DASHED)
    private String createOrderUserName;

    /**
     * 创建订单用户微信名称（订单运营）
     */
    private String createOrderUserNickName;

    /**
     * 视频素材地址链接
     */
    @Excel(name = "视频素材地址链接", defaultValue = StrPool.DASHED)
    private String videoUrl;

    /**
     * 照片素材地址链接
     */
    @Excel(name = "照片素材地址链接", defaultValue = StrPool.DASHED)
    private String picUrl;

    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @Excel(name = "国家", readConverterExp = "1=英国,2=加拿大,3=德国,4=法国,5=意大利,6=西班牙,7=美国", defaultValue = StrPool.DASHED)
    private Integer shootingCountry;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @Excel(name = "模特类型", readConverterExp = "0=亚马逊影响者,1=素人创作者,3=亚马逊影响者/素人创作者", defaultValue = StrPool.DASHED)
    private Integer modelType;

    /**
     * 拍摄模特id
     */
    private Long shootModelId;

    /**
     * 拍摄模特
     */
    @Excel(name = "拍摄模特", defaultValue = StrPool.DASHED)
    private String shootModelName;

    /**
     * 创建时间
     */
    @Excel(name = "反馈时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN, defaultValue = StrPool.DASHED)
    private Date createTime;
}
