package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create :2025-3-4
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderPayInfoDetailVO implements Serializable {
    private static final long serialVersionUID = -284190909733965583L;


    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 订单创建时间
     */
    @ApiModelProperty(value = "订单创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 视频数量
     */
    @ApiModelProperty(value = "视频数量")
    private Integer videoCount;

    /**
     * 小计金额（单位：￥）
     */
    @ApiModelProperty(value = "小计金额（单位：￥）")
    private BigDecimal amount;

    /**
     * 小计金额（单位：$）
     */
    @ApiModelProperty(value = "小计金额（单位：$）")
    private BigDecimal amountDollar;

    @ApiModelProperty(value = "订单活动优惠总额")
    private BigDecimal orderPromotionAmount;

    /**
     * 订单时间标记（每次开启订单、创建订单时间）
     */
    @ApiModelProperty(value = "订单时间标记（每次开启订单、创建订单时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTimeSign;

    /**
     * 视频订单费用明细
     */
    @ApiModelProperty(value = "视频订单费用明细")
    private List<OrderVideoPayInfoVO> orderVideoPayInfos;
}
