package com.ruoyi.system.api.domain.vo.biz.business;

import cn.hutool.core.text.StrPool;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家会员有效期修改流水
 *
 * <AUTHOR>
 * @TableName business_member_validity_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessMemberValidityFlowExportVO implements Serializable {
    private static final long serialVersionUID = -336993560542566327L;

    @Excel(name = "订单号")
    private String orderNum;

    @Excel(name = "交易流水号")
    private String mchntOrderNo;

    @Excel(name = "会员编码")
    private String memberCode;

    @Excel(name = "会员状态", readConverterExp = "0=非会员,1=正常,2=即将过期,3=已过期")
    private Integer memberStatus;

    @Excel(name = "购买/调整时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "套餐类型", readConverterExp = "0=季度会员,1=年度会员,2=三年会员")
    private Integer memberPackageType;

    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd 00:00:00")
    private Date originMemberValidity;

    @Excel(name = "到期时间", width = 30, dateFormat = "yyyy-MM-dd 23:59:59")
    private Date resultMemberValidity;

    @Excel(name = "类型", readConverterExp = "0=系统调整,1=商家购买")
    private Integer type;

    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;


    @Excel(name = "支付方式", readConverterExp = "1=微信,2=支付宝,3=云闪付/银联,4=数字人民币,5=银行,6=对公,7=全币种,10=余额,11=微信+余额,12=支付宝+余额,13=云闪付/银联+余额,14.数字人民币+余额,15.银行+余额,16=对公+余额,17=全币种+余额")
    private Integer payType;

    /**
     * 套餐金额（单位：$）
     */
    @Excel(name = "套餐金额（单位：$）")
    private BigDecimal payAmountDollar;

    /**
     * 百度汇率
     */
    @Excel(name = "百度汇率")
    private BigDecimal currentExchangeRate;

    @Excel(name = "套餐金额（单位：￥）")
    private BigDecimal payAmount;

    @Excel(name = "钱包抵扣")
    private BigDecimal useBalance;

    @Excel(name = "剩余支付")
    private BigDecimal surplusAmount;

    @Excel(name = "实付金额", defaultValue = StrPool.DASHED)
    private BigDecimal realPayAmountCurrency;

    /**
     * 币种
     */
    @Excel(name = "币种", defaultValue = StrPool.DASHED)
    private String currency;

    @Excel(name = "实付人民币", defaultValue = StrPool.DASHED)
    private BigDecimal realPayAmount;
}
