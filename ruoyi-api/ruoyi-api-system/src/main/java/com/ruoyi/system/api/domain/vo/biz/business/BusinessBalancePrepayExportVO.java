package com.ruoyi.system.api.domain.vo.biz.business;

import cn.hutool.core.text.StrPool;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家预付表
 *
 * <AUTHOR>
 * @TableName business_balance_prepay
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalancePrepayExportVO implements Serializable {

    private static final long serialVersionUID = -981735608546630181L;

    @Excel(name = "预付单号")
    private String prepayNum;

    @Excel(name = "商家名称")
    private String businessName;

    @Excel(name = "会员编码")
    private String memberCode;

    @Excel(name = "预付金额")
    private BigDecimal amount;

    @Excel(name = "预付款成功金额")
    private BigDecimal realAmount;

    @Excel(name = "应付金额")
    private BigDecimal payAmount;

    @Excel(name = "实付人民币")
    private BigDecimal realPayAmount;

    @Excel(name = "实付金额", defaultValue = StrPool.DASHED)
    private BigDecimal realPayAmountCurrency;

    private Integer currency;

    @Excel(name = "支付币种", defaultValue = StrPool.DASHED)
    private String currencyString;

    @Excel(name = "备注申请")
    private String applyRemark;

    @Excel(name = "钱包充值类型", readConverterExp = "3=线下钱包充值,5=线上钱包充值")
    private Integer orderType;

    @Excel(name = "申请人")
    private String createBy;

    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date creatTime;

    @Excel(name = "状态", readConverterExp = "0=待处理,1=审核通过,2=审核拒绝")
    private Integer auditStatus;

    @Excel(name = "审核人员名称")
    private String auditUserName;

    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "拒绝原因")
    private String rejectCause;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @Excel(name = "全币种支付类型", readConverterExp = "701=其他平台/银行,702=万里汇", defaultValue = StrPool.DASHED)
    private Integer payTypeDetail;
}
