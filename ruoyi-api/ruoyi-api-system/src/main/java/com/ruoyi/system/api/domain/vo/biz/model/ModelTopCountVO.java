package com.ruoyi.system.api.domain.vo.biz.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/4 16:42
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModelTopCountVO {
    /**
     * 当前置顶数
     */
    @ApiModelProperty("当前置顶数")
    private Integer topCount;

    /**
     * 剩余可置顶数
     */
    @ApiModelProperty("剩余可置顶数")
    private Integer surplusCount;
}
