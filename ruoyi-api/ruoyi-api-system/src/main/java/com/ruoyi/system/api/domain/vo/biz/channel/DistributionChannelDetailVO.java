package com.ruoyi.system.api.domain.vo.biz.channel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 分销渠道详情信息表
 *
 * <AUTHOR>
 * @TableName distribution_channel
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "分销渠道详情信息表")
public class DistributionChannelDetailVO implements Serializable {

    private static final long serialVersionUID = 2030441508344258076L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("商家用户id")
    private Long bizUserId;

    @ApiModelProperty("渠道名称")
    private String channelName;

    /**
     * 海报名称
     */
    @ApiModelProperty("海报名称")
    private String posterName;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("结算佣金类型（1-固定金额，2-固定比例）")
    private Integer settleDiscountType;

    @ApiModelProperty("结算比例")
    private BigDecimal brokeRage;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("种草码")
    private String seedCode;

    @ApiModelProperty("种草官ID")
    private String seedId;

    @ApiModelProperty("分销状态（0=正常,1=禁用）")
    private Integer status;

    @ApiModelProperty("禁用时间/启用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date disableTime;

    @ApiModelProperty("专属链接")
    private String dedicatedLink;

    @ApiModelProperty("专属企微二维码地址")
    private String weChatUrl;

}
