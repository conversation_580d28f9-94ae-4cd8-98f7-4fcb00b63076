package com.ruoyi.system.api.domain.vo.order;

import com.ruoyi.common.core.enums.PayPlatformTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 创建订单
 *
 * <AUTHOR>
 * @date 2024/5/31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreatePayVo implements Serializable {
    private static final long serialVersionUID = 244140821494687099L;
    /**
     * 支付平台
     */
    @ApiModelProperty(value = "支付平台")
    private PayPlatformTypeEnum payPlatformTypeEnum;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    /**
     * 订单描述
     */
    @ApiModelProperty(value = "订单描述")
    private String desc;

    /**
     * 二维码
     */
    @ApiModelProperty(value = "二维码")
    private String qrcode;
    /**
     * 跳转页面数据
     */
    @ApiModelProperty(value = "跳转页面数据")
    private String pageRedirectionData;

    @ApiModelProperty(value = "错误展示")
    private String message;

    @ApiModelProperty(value = "appId")
    private String appId;
}
