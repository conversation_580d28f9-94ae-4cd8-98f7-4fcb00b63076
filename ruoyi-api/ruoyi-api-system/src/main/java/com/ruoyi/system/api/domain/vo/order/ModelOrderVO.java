package com.ruoyi.system.api.domain.vo.order;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/12 18:11
 */
@Data
public class ModelOrderVO implements Serializable {
    private static final long serialVersionUID = -6369339091252965824L;

    /**
     * 模特id
     */
    private Long modelId;

    /**
     * 待确认订单数（即运营提交预选模特给商家后 待商家确认的订单数（即需发货订单））
     */
    private Long unconfirmed = 0L;

    /**
     * 待拍数
     */
    private Long waits = 0L;

    /**
     * 待确认数
     */
    private Long toBeConfirm = 0L;

    /**
     * 已完成数
     */
    private Long finished = 0L;

    /**
     * 是否有超时订单
     */
    private Boolean hasTimeout;

    /**
     * 超时视频订单id
     */
    private List<Long> timeoutVideoIds;

    /**
     * 模特剩余需携带订单数
     */
    private Long carryCount;
}
