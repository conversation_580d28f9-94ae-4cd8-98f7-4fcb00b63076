package com.ruoyi.system.api.domain.vo.order.datastatistics;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/5 13:43
 */
@Data
public class OrderVideoServiceCountVO implements Serializable {
    private static final long serialVersionUID = 667501383095951244L;

    /**
     * 实时更新时间
     */
    @ApiModelProperty("实时更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date dateTime;

    /**
     * 待确认订单数
     */
    @ApiModelProperty("待确认订单数")
    private Long waitConfirmOrderCount;

    /**
     * 待确认订单数中的照顾单数
     */
    @ApiModelProperty("待确认订单数中的照顾单数")
    private Long waitConfirmOrderCareCount;

    /**
     * 待匹配订单数
     */
    @ApiModelProperty("待匹配订单数")
    private Long waitMatchOrderCount;

    /**
     * 待匹配订单数中的照顾单数
     */
    @ApiModelProperty("待匹配订单数中的照顾单数")
    private Long waitMatchOrderCareCount;

    /**
     * 超7天未匹配模特的订单数
     */
    @ApiModelProperty("超7天未匹配模特的订单数")
    private Long overSevenDaysNoMatchOrderCount;

    /**
     * 超7天未匹配模特的订单数中的照顾单数
     */
    @ApiModelProperty("超7天未匹配模特的订单数中的照顾单数")
    private Long overSevenDaysNoMatchOrderCareCount;

    /**
     * 待发货订单数
     */
    @ApiModelProperty("待发货订单数")
    private Long waitDeliveryOrderCount;

    /**
     * 待发货订单数中的照顾单数
     */
    @ApiModelProperty("待发货订单数中的照顾单数")
    private Long waitDeliveryOrderCareCount;

    /**
     * 超7天未发货的订单数
     */
    @ApiModelProperty("超7天未发货的订单数")
    private Long overSevenDaysNoDeliveryOrderCount;

    /**
     * 超7天未发货的订单数中的照顾单数
     */
    @ApiModelProperty("超7天未发货的订单数中的照顾单数")
    private Long overSevenDaysNoDeliveryOrderCareCount;

    /**
     * 待完成订单数
     */
    @ApiModelProperty("待完成订单数")
    private Long waitFinishOrderCount;

    /**
     * 待完成订单数中的照顾单数
     */
    @ApiModelProperty("待完成订单数中的照顾单数")
    private Long waitFinishOrderCareCount;

    /**
     * 签收后超20天未反馈素材的订单数
     */
    @ApiModelProperty("签收后超20天未反馈素材的订单数")
    private Long signOverTwentyDaysNoFeedbackOrderCount;

    /**
     * 签收后超20天未反馈素材的订单数中的照顾单数
     */
    @ApiModelProperty("签收后超20天未反馈素材的订单数中的照顾单数")
    private Long signOverTwentyDaysNoFeedbackOrderCareCount;

    /**
     * 需确认订单数
     */
    @ApiModelProperty("需确认订单数")
    private Long needConfirmOrderCount;

    /**
     * 需确认订单数中的照顾单数
     */
    @ApiModelProperty("需确认订单数中的照顾单数")
    private Long needConfirmOrderCareCount;
}
