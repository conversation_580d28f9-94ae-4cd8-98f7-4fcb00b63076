package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/11 9:58
 */
@Data
public class InvoiceStatisticsVO implements Serializable {
    private static final long serialVersionUID = -5755597534190128089L;
    /**
     * 待开票金额
     */
    @ApiModelProperty(value = "待开票金额")
    private BigDecimal unInvoice;
    /**
     * 已开票金额
     */
    @ApiModelProperty(value = "已开票金额")
    private BigDecimal deliver;
}
