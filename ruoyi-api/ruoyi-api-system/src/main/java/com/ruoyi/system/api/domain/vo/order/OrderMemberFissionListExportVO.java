package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.text.StrPool;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :裂变
 * @create :2025-01-15 09:04
 **/
@Data
public class OrderMemberFissionListExportVO implements Serializable {

    //种草官微信名、种草官姓名、种草官公司名称、邀请商家名称、会员编码、会员类型、购买时间、结算比例、结算金额、状态
    @Excel(name = "种草官微信名")
    private String fissionNickname;

    @Excel(name = "种草官姓名", defaultValue = "-")
    private String fissionAccountName;

    @Excel(name = "种草官公司名称", defaultValue = "-")
    private String fissionBusinessName;

//    @ApiModelProperty("渠道会员编码")
    private String fissionMemberCode;

    @Excel(name = "邀请商家名称", defaultValue = "-")
    private String businessName;

//    @Excel(name = "商家微信名")
    private String bizUserNickName;

    @Excel(name = "会员编码")
    private String memberCode;

    @Excel(name = "会员类型", readConverterExp = "0=季度会员,1=年度会员,2=三年会员")
    private Integer memberPackageType;

    @Excel(name = "购买时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "结算比例")
    private BigDecimal settleRage;

    @Excel(name = "结算金额")
    private BigDecimal settleAmount;

    @Excel(name = "实际结算金额", defaultValue = StrPool.DASHED)
    private BigDecimal realSettleAmount;

    @Excel(name = "状态", readConverterExp = "0=待入账,1=可提现,2=提现审核中,3=待打款,4=已打款,5=审核不通过,6=打款异常,99=入账失败")
    private Integer settleStatus;

    @Excel(name = "打款时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", defaultValue = "-")
    private Date payoutTime;
}
