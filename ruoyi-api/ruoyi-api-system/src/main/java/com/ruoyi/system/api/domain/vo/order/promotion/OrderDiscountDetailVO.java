package com.ruoyi.system.api.domain.vo.order.promotion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/4/18 15:57
 */
@Data
public class OrderDiscountDetailVO implements Serializable {
    private static final long serialVersionUID = 6932307788789133466L;

    /**
     * 优惠活动类型（1-满5单减100，2-会员订单临期续费半价优惠，3-种草码优惠，4-每月首单立减，5-裂变优惠））
     */
    @ApiModelProperty("优惠活动类型（1-满5单减100，2-会员订单临期续费半价优惠，3-渠道优惠，4-每月首单立减，5-裂变优惠））")
    private Integer type;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNum;

    /**
     * 视频ID
     */
    @ApiModelProperty("视频ID")
    private Long videoId;

    /**
     * 视频编码
     */
    @ApiModelProperty("视频编码")
    private String videoCode;

    /**
     * 种草码渠道类型（现只有种草码优惠才有）
     */
    @ApiModelProperty(value = "种草码渠道类型")
    private Integer channelType;

    /**
     * 渠道名称（现只有种草码优惠才有）
     */
    @ApiModelProperty("渠道名称（现只有种草码优惠才有）")
    private String channelName;

    /**
     * 优惠比例
     */
    @ApiModelProperty("优惠比例")
    private BigDecimal discountRatio;

    /**
     * 优惠金额（单位：￥）
     */
    @ApiModelProperty("优惠金额（单位：￥）")
    private BigDecimal discountAmount;

    /**
     * 优惠金额（单位：$）
     */
    @ApiModelProperty("优惠金额（单位：$）")
    private BigDecimal discountAmountDollar;

    /**
     * 优惠扣减类型（1：直减，2：折扣）
     */
    @ApiModelProperty(value = "优惠扣减类型（1：直减，2：折扣）")
    private Integer discountType;

    /**
     * 优惠数值
     */
    @ApiModelProperty(value = "优惠数值")
    private BigDecimal amount;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;
}
