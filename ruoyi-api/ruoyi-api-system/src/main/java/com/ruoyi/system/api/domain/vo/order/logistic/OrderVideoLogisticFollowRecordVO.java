package com.ruoyi.system.api.domain.vo.order.logistic;

import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollowRecord;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 跟进记录表
 *
 * <AUTHOR>
 * @TableName order_video_logistic_follow_record
 */
@Data
public class OrderVideoLogisticFollowRecordVO extends OrderVideoLogisticFollowRecord implements Serializable {

    @ApiModelProperty("图片地址")
    private List<String> resources;
}
