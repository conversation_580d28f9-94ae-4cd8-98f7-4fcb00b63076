package com.ruoyi.system.api.domain.vo.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商家余额详情数据
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessBalanceInfoVO implements Serializable {

    private static final long serialVersionUID = -1262577170635983267L;
    @ApiModelProperty("公司ID")
    private Long id;

    @ApiModelProperty("商家主账号（FK：business_account.account）")
    private String ownerAccount;

    @ApiModelProperty("商家名称")
    private String name;

    @ApiModelProperty("会员编码")
    private String memberCode;

    @ApiModelProperty("钱包余额")
    private BigDecimal balance;

    @ApiModelProperty("已锁定金额")
    private BigDecimal useBalance;

    @ApiModelProperty("可用余额")
    private BigDecimal validBalance;

    @ApiModelProperty("已提现余额")
    private BigDecimal withdrawTotal;

    @ApiModelProperty("已使用余额")
    private BigDecimal useBalanceTotal;

    /***
     * ----------------------------------------------------*
     */
    @ApiModelProperty("订单锁定余额")
    private BigDecimal orderLockBalance;

    @ApiModelProperty("提现锁定余额")
    private BigDecimal payOutLockBalance;

}