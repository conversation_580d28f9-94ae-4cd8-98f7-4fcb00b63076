package com.ruoyi.system.api.domain.vo.order.datastatistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:27
 */
@Data
public class ChineseCustomerServiceDataVO implements Serializable {
    private static final long serialVersionUID = -2655394597655820338L;

    /**
     * 客服ID
     */
    @ApiModelProperty("客服ID")
    private Long customerServiceId;

    /**
     * 客服姓名
     */
    @ApiModelProperty("客服姓名")
    private String customerServiceName;

    /**
     * 客服状态（0=正常,1=停用）
     */
    @ApiModelProperty("客服状态（0=正常,1=停用）")
    private Integer customerServiceStatus;

    /**
     * 服务客户数
     */
    @ApiModelProperty("服务客户数")
    private Long customerCount;

    /**
     * 服务订单数
     */
    @ApiModelProperty("服务订单数")
    private Long orderCount;

    /**
     * 合计服务订单数
     */
    @ApiModelProperty("合计服务订单数")
    private Long orderTotalCount;

    /**
     * 昨日新增订单数
     */
    @ApiModelProperty("昨日新增订单数")
    private Long orderNewCount;

    /**
     * 昨日完成订单数
     */
    @ApiModelProperty("昨日完成订单数")
    private Long orderFinishCount;

    /**
     * 售后率
     */
    @ApiModelProperty("售后率")
    private BigDecimal afterSaleRate;

    /**
     * 售后数
     */
    @ApiModelProperty("售后数")
    private Long afterSaleCount;

    /**
     * 任务单数量
     */
    @ApiModelProperty("任务单数量")
    private Long taskCount;

    /**
     * 昨日新增任务单数量
     */
    @ApiModelProperty("昨日新增任务单数量")
    private Long taskNewCount;

    /**
     * 昨日完成任务单数量
     */
    @ApiModelProperty("昨日完成任务单数量")
    private Long taskFinishCount;

    /**
     * 筛选时间段内新增任务单数量
     */
    @ApiModelProperty("筛选时间段内新增任务单数量")
    private Long taskNewCountInTime;

    /**
     * 筛选时间段内完成任务单数量
     */
    @ApiModelProperty("筛选时间段内完成任务单数量")
    private Long taskFinishCountInTime;

    /**
     * 右侧售后数
     */
    @ApiModelProperty("右侧售后数")
    private Long rightAfterSaleCount;

    public void echo() {
        customerCount = customerCount == null ? 0L : customerCount;
        orderCount = orderCount == null ? 0L : orderCount;
        orderTotalCount = orderTotalCount == null ? 0L : orderTotalCount;
        orderNewCount = orderNewCount == null ? 0L : orderNewCount;
        orderFinishCount = orderFinishCount == null ? 0L : orderFinishCount;
        afterSaleRate = afterSaleRate == null ? BigDecimal.ZERO : afterSaleRate.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        afterSaleCount = afterSaleCount == null ? 0L : afterSaleCount;
        taskCount = taskCount == null ? 0L : taskCount;
        taskNewCount = taskNewCount == null ? 0L : taskNewCount;
        taskFinishCount = taskFinishCount == null ? 0L : taskFinishCount;
        taskNewCountInTime = taskNewCountInTime == null ? 0L : taskNewCountInTime;
        taskFinishCountInTime = taskFinishCountInTime == null ? 0L : taskFinishCountInTime;
        rightAfterSaleCount = rightAfterSaleCount == null ? 0L : rightAfterSaleCount;
    }
}
