package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/26 17:50
 */
@Data
public class OrderVideoUploadLinkVO implements Serializable {
    private static final long serialVersionUID = -5187140238779225329L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 视频id (FK:order_video.id)
     */
    @ApiModelProperty(value = "视频id")
    private Long videoId;
    /**
     * 提交信息对象（1:商家,2:运营）
     */
    @ApiModelProperty(value = "提交信息对象（1:商家,2:运营）")
    private Integer object;
    /**
     * 提交信息用户id
     */
    @ApiModelProperty(value = "提交信息用户id")
    @JsonIgnore
    private Long userId;

    /**
     * 上传的链接
     */
    @ApiModelProperty(value = "上传的链接")
    private String uploadLink;

    /**
     * 上传用户id
     */
    @ApiModelProperty(value = "上传用户id")
    @JsonIgnore
    private Long uploadUserId;

    /**
     * 上传用户
     */
    @ApiModelProperty(value = "上传用户")
    private UserVO uploadUser;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    private Date uploadTime;
    /**
     * 提交信息用户（商家）
     */
    @ApiModelProperty(value = "提交信息用户（商家）")
    private BusinessAccountDetailVO company;
    /**
     * 提交信息用户（运营）
     */
    @ApiModelProperty(value = "提交信息用户（运营）")
    private UserVO back;
    /**
     * 提交信息时间
     */
    @ApiModelProperty(value = "提交信息时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;
    /**
     * 需要上传的链接
     */
    @ApiModelProperty(value = "需要上传的链接")
    private String needUploadLink;

    /**
     * 视频标题
     */
    @ApiModelProperty(value = "视频标题")
    private String videoTitle;
    /**
     * 视频封面图URI
     */
    @ApiModelProperty(value = "视频封面图URI")
    private String videoCover;
    /**
     * 客服备注
     */
    @ApiModelProperty(value = "客服备注")
    private String remark;

    /**
     * 是否已吐槽
     */
    @ApiModelProperty(value = "是否已吐槽（true：是的）")
    private Boolean isRoast;

    /**
     * 上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传，5：无需上传）
     */
    @ApiModelProperty(value = "上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传，5：无需上传）")
    private Integer status;

    /**
     * 上传账号
     */
    @ApiModelProperty(value = "上传账号")
    private String uploadAccount;

    /**
     * 操作备注
     */
    @ApiModelProperty(value = "操作备注")
    private String operateRemark;
}
