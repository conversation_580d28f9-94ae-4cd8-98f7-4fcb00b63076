package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :工作台
 * @create :2025-03-31 09:43
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WorkbenchVO implements Serializable {
    private static final long serialVersionUID = 1159213099481816225L;

    /**
     * 中文部
     */
    @ApiModelProperty(value = "中文部：待审核订单数量")
    private Integer unConfirmCount;

    @ApiModelProperty(value = "中文部：暂停匹配数量")
    private Integer pauseMatchCount;

    @ApiModelProperty(value = "中文部：物流异常订单数量")
    private Integer logisticAnomalyCount;

    /**
     * 英文部
     */
    @ApiModelProperty(value = "英文部：待收货订单")
    private Integer unReceivingCount;

    @ApiModelProperty(value = "英文部：待沟通订单")
    private Integer unContactTotalCount;

    @ApiModelProperty(value = "英文部：未沟通")
    private Integer unContactCount;

    @ApiModelProperty(value = "英文部：沟通中")
    private Integer contactingCount;

    /**
     * 财务部
     */
    @ApiModelProperty(value = "财务部：视频待审核")
    private Integer videoUnAuditCount;

    @ApiModelProperty(value = "财务部：会员待审核")
    private Integer memberUnAuditCount;

    @ApiModelProperty(value = "财务部：待开票")
    private Integer quantityToBeInvoiced;

    @ApiModelProperty(value = "财务部：提现待审核")
    private Long payoutUnAuditCount;

    @ApiModelProperty(value = "财务部：分销未结算")
    private Long distributionUnSettleCount;


    /**
     * 剪辑部
     */
    @ApiModelProperty(value = "剪辑部：待下载")
    private Integer downloadCount;

    @ApiModelProperty(value = "剪辑部：待剪辑")
    private Integer toBeEditedCount;

    @ApiModelProperty(value = "剪辑部：待反馈")
    private Integer waitForFeedbackCount;

    @ApiModelProperty(value = "剪辑部：待上传")
    private Long unUploadLinkCount;



    @ApiModelProperty(value = "工单总数量")
    private Integer workOrderTotalCount;

    @ApiModelProperty(value = "工单待处理数量")
    private Integer handlingWorkOrderCount;

    @ApiModelProperty(value = "工单待处理数量")
    private Integer unHandleWorkOrderCount;

    @ApiModelProperty(value = "售后总数量")
    private Integer afterSaleTotalCount;

    @ApiModelProperty(value = "售后待处理")
    private Integer unHandleAfterSaleCount;

    @ApiModelProperty(value = "售后处理中")
    private Integer handlingAfterSaleCount;
}
