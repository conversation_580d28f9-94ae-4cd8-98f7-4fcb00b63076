package com.ruoyi.system.api.domain.vo.biz.channel;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 市场渠道列表返回对象VO
 *
 * <AUTHOR>
 * @date 2024/9/25 9:27
 */
@Data
public class MarketingChannelListVO implements Serializable {
    private static final long serialVersionUID = 1730159841108195481L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 市场渠道平台 (1=百度,2=小红书,3=穿山甲,4=优量汇,5=今日头条,6=腾讯)
     */
    @ApiModelProperty("市场渠道平台 (1=百度,2=小红书,3=穿山甲,4=优量汇,5=今日头条,6=腾讯)")
    private Integer marketingPlatform;

    /**
     * 市场渠道名称
     */
    @ApiModelProperty("市场渠道名称")
    private String marketingChannelName;

    @ApiModelProperty("落地形式（1:官网首页，2:添加企微客服）")
    private Integer landingForm;

    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    @ApiModelProperty("独立访客")
    private Integer uniqueVisitor;

    @ApiModelProperty("新增注册数")
    private Integer newRegistrationCount;

    @ApiModelProperty(value = "会员成交数")
    private Long memberNum;

    @ApiModelProperty(value = "会员总金额")
    private BigDecimal realPayAmount;

    @ApiModelProperty("专属链接code")
    private String dedicatedLinkCode;

    @ApiModelProperty("微信添加数量")
    private Integer wechatCount;
}
