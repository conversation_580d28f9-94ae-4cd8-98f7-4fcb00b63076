package com.ruoyi.system.api.domain.vo.biz.business;

import com.ruoyi.common.core.enums.PhoneStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-28 14:05
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckPhoneVO implements Serializable {
    private static final long serialVersionUID = 4947890004877776876L;

    @ApiModelProperty("手机号状态")
    private PhoneStatusEnum phoneStatusEnum;

    @ApiModelProperty("二维码链接")
    private String qrcode;

    @ApiModelProperty("查询ticket")
    private String ticket;
}
