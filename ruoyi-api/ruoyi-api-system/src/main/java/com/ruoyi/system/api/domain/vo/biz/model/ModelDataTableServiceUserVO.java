package com.ruoyi.system.api.domain.vo.biz.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/8 18:13
 */
@Data
public class ModelDataTableServiceUserVO implements Serializable {
    private static final long serialVersionUID = 7113930309989257782L;

    /**
     * 客服ID
     */
    @ApiModelProperty(value = "客服ID")
    private Long serviceId;

    /**
     * 客服名称
     */
    @ApiModelProperty(value = "客服名称")
    private String serviceName;

    /**
     * 关联模特数量
     */
    @ApiModelProperty(value = "关联模特数量")
    private Long modelCount;
}
