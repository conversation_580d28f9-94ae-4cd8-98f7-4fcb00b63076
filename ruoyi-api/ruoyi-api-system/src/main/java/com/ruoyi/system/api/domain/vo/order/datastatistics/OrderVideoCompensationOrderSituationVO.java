package com.ruoyi.system.api.domain.vo.order.datastatistics;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 15:13
 */
@Data
public class OrderVideoCompensationOrderSituationVO implements Serializable {
    private static final long serialVersionUID = -5246910001615067578L;

    /**
     * 记录截止时间
     */
    @ApiModelProperty("记录截止时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date writeTimeEnd;

    /**
     * 补偿单数
     */
    @ApiModelProperty("补偿单数")
    private Long compensationOrderCount;

    /**
     * 补偿金额
     */
    @ApiModelProperty("补偿金额")
    private BigDecimal compensationAmount;

    /**
     * 饼图
     */
    @ApiModelProperty("饼图")
    private List<PieChartVO> pieChartVOS;

    public OrderVideoCompensationOrderSituationVO echo() {
        compensationOrderCount = compensationOrderCount == null ? 0L : compensationOrderCount;
        compensationAmount = compensationAmount == null ? BigDecimal.ZERO : compensationAmount;
        if (CollUtil.isNotEmpty(pieChartVOS)) {
            for (PieChartVO pieChartVO : pieChartVOS) {
                pieChartVO.echo();
                if (CollUtil.isNotEmpty(pieChartVO.getPieChartVOS())) {
                    for (PieChartVO chartVO : pieChartVO.getPieChartVOS()) {
                        chartVO.echo();
                    }
                }
            }
        }
        return this;
    }
}
