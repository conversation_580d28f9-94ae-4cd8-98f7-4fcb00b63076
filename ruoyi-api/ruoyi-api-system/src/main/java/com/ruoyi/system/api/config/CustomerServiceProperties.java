package com.ruoyi.system.api.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:51
 */
@Component
@ConfigurationProperties(prefix = "customer-service")
@Data
@RefreshScope
    public class CustomerServiceProperties {

    /**
     * 中文部部门ID
     */
    private Long chineseCustomerServiceDeptId;

    /**
     * 英文部部门ID
     */
    private Long englishCustomerServiceDeptId;
}
