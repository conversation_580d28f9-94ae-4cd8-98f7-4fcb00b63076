package com.ruoyi.system.api.domain.vo.biz.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家会员有效期修改流水
 *
 * <AUTHOR>
 * @TableName business_member_validity_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessMemberValidityFlowVO implements Serializable {
    private static final long serialVersionUID = -336993560542566327L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("商家ID")
    private Long businessId;

    @ApiModelProperty("订单号")
    private String orderNum;

    @ApiModelProperty("会员编码")
    private String memberCode;

    @ApiModelProperty("会员状态：0-非会员1-正常，2-即将过期，3-已过期")
    private Integer memberStatus;

    @ApiModelProperty("原会员有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date originMemberValidity;

    @ApiModelProperty("修改后会员有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date resultMemberValidity;

    @ApiModelProperty("加赠时间")
    private Integer presentedTime;

    @ApiModelProperty("加赠时间类型（1-天,2-月,3-年）")
    private Integer presentedTimeType;

    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberPackageType;

    @ApiModelProperty(value = "实际支付金额")
    private BigDecimal realPayAmount;

    @ApiModelProperty(value = "订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    @ApiModelProperty(value = "币种：1:人民币,2:离岸人民币,3:美元,4:澳元,5:加币,6:英镑,7:港币,8:日元,9:新西兰元,10:新加坡元")
    private Integer currency;

    @ApiModelProperty("处理类型：0-系统调整，1-商家购买")
    private Integer type;

    @ApiModelProperty("修改原因类型（1:老会员入驻,2:七天无理由,3:退会,4:其他）")
    private Integer changeReasonType;

    @ApiModelProperty("修改原因")
    private String remark;

    @ApiModelProperty("处理人（sys_user.user_id）")
    private Long createById;

    @ApiModelProperty("处理人（sys_user.user_id）")
    private String createBy;

    @ApiModelProperty("处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
