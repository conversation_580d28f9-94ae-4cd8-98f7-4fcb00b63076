package com.ruoyi.system.api.domain.vo.biz.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserTextHelpListVO implements Serializable {

    private static final long serialVersionUID = 3952351250807407778L;
    @ApiModelProperty(value = "文本类型：0-协议信息,1-帮助中心-常见问题,2-帮助中心-新手指南")
    private Integer type;

    @ApiModelProperty(value = "帮助中心内容")
    private List<TextHelpVO> list;
}
