package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/13 11:17
 */
@Data
public class OrderModelTimeoutVO implements Serializable {
    private static final long serialVersionUID = 320689378691811602L;
    /**
     * 模特id
     */
    @ApiModelProperty(value = "模特id")
    private Long modelId;
    /**
     * 超时率
     */
    @ApiModelProperty(value = "超时率")
    private BigDecimal overtimeRate;
    /**
     * 售后率
     */
    @ApiModelProperty(value = "售后率")
    private BigDecimal afterSaleRate;
}
