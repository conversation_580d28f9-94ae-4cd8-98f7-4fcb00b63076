package com.ruoyi.system.api.model;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.system.api.domain.vo.biz.model.ModelVO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/20
 */
@Data
public class LoginModel extends LoginBaseEntity implements Serializable {

    private static final long serialVersionUID = -7452471567482093991L;

    public LoginModel() {
    }

    /**
     * 模特信息
     */
    private ModelVO modelVo;

    public LoginModel(ModelVO modelVo) {
        this.modelVo = modelVo;
        updateInfo();
    }


    public void updateInfo() {
        this.setUserType(UserTypeConstants.MODEL);
        this.setUserid(modelVo.getId());
        this.setUsername(modelVo.getName());
    }
}
