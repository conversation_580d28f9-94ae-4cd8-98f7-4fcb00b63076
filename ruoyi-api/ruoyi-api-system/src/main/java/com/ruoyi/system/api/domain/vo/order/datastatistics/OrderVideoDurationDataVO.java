package com.ruoyi.system.api.domain.vo.order.datastatistics;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/9 14:56
 */
@Data
public class OrderVideoDurationDataVO implements Serializable {
    private static final long serialVersionUID = -7398500141133590853L;

    /**
     * 记录截止时间
     */
    @ApiModelProperty("记录截止时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date writeTimeEnd;

    /**
     * 平均时长
     */
    @ApiModelProperty("平均时长")
    private BigDecimal averageDuration;

    /**
     * 饼图
     */
    @ApiModelProperty("饼图")
    private List<PieChartVO> pieChartVOS;

    public OrderVideoDurationDataVO echo() {
        if (CollUtil.isNotEmpty(pieChartVOS)) {
            for (PieChartVO pieChartVO : pieChartVOS) {
                pieChartVO.echo();
            }
        }
        averageDuration = averageDuration == null ? BigDecimal.ZERO : averageDuration.setScale(2, RoundingMode.HALF_UP);
        return this;
    }
}
