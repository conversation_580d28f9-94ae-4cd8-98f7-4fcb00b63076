package com.ruoyi.system.api.domain.vo.order.finace;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单收支明细
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@ApiModel(value = "订单收支明细 - 视频明细")
@Data
public class OrderPayVideoDetailVO implements Serializable {
    private static final long serialVersionUID = 734687070967988311L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date payTime;

    // @ApiModelProperty(value = "入账时间")
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    // private Date recordTime;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date auditTime;

    @ApiModelProperty(value = "订单号")
    private String orderNum;

    @ApiModelProperty(value = "支付单号")
    private String payNum;

    @ApiModelProperty(value = "交易流水号")
    private String mchntOrderNo;

    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    @ApiModelProperty(value = "拍摄模特id")
    @JsonIgnore
    private Long shootModelId;

    @ApiModelProperty(value = "拍摄模特账号")
    private String shootModelAccount;

    @ApiModelProperty(value = "拍摄模特名称")
    private String shootModelName;

    @ApiModelProperty(value = "订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）")
    private Integer status;

    /**
     * 订单进入需确认的时间
     */
    @ApiModelProperty(value = "首次反馈商家素材时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date needConfirmTime;

    /**
     * 最新提交模特时间
     */
    @ApiModelProperty(value = "最新提交模特时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date lastModelSubmitTime;

    @ApiModelProperty(value = "支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额,11:微信+余额,12:支付宝+余额,13:云闪付/银联+余额,14.数字人民币+余额,15.银行+余额,16:对公+余额,17:全币种+余额）")
    private Integer payType;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private Integer payTypeDetail;

    @ApiModelProperty(value = "订单类型（0-视频订单，1-会员订单）")
    private Integer orderType;

    // @ApiModelProperty(value = "是否入账")
    // private Integer isRecord;

    // @ApiModelProperty(value = "支付金额")
    // private BigDecimal payAmount;

    // @ApiModelProperty(value = "实际支付金额")
    // private BigDecimal realPayAmount;

    // @ApiModelProperty(value = "使用余额")
    // private BigDecimal useBalance;

    @ApiModelProperty(value = "当前汇率")
    private BigDecimal currentExchangeRate;

    // @ApiModelProperty(value = "审核状态：")
    // private Integer auditStatus;

    @ApiModelProperty(value = "视频金额（单位：￥）")
    private BigDecimal amount;

    @ApiModelProperty(value = "视频活动优惠金额")
    private BigDecimal videoPromotionAmount;

    @ApiModelProperty(value = "视频价格（单位：$）")
    private BigDecimal videoPrice;

    @ApiModelProperty(value = "需支付金额（单位：￥）")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "需支付金额（单位：￥）")
    private BigDecimal realPayAmount;

    @ApiModelProperty(value = "需支付金额（单位：$）")
    private BigDecimal payAmountDollar;

    @ApiModelProperty(value = "选配费用（单位：$）")
    private BigDecimal picPrice;

    @ApiModelProperty(value = "手续费（单位：$）")
    private BigDecimal exchangePrice;

    @ApiModelProperty(value = "服务费（单位：$）")
    private BigDecimal servicePrice;

    @ApiModelProperty(value = "佣金代缴税费（单位：$）")
    private BigDecimal commissionPaysTaxes;

    @ApiModelProperty(value = "差额（单位：￥）")
    private BigDecimal differenceAmount;

    @ApiModelProperty(value = "照片数量")
    private Integer picCount;

    /**
     * 订单参与的活动以及对应的优惠
     */
    @ApiModelProperty(value = "订单参与的活动以及对应的优惠")
    private List<OrderDiscountDetailVO> orderDiscountDetailVOS;
}
