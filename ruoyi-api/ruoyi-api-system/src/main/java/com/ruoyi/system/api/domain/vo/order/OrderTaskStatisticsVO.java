package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :任务单统计
 * @create :2025-03-31 09:43
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderTaskStatisticsVO implements Serializable {
    private static final long serialVersionUID = 1159213099481816225L;


    @ApiModelProperty(value = "工单总数量")
    private Integer workOrderTotalCount;

    @ApiModelProperty(value = "工单待处理数量")
    private Integer handlingWorkOrderCount;

    @ApiModelProperty(value = "工单待处理数量")
    private Integer unHandleWorkOrderCount;

    @ApiModelProperty(value = "售后总数量")
    private Integer afterSaleTotalCount;

    @ApiModelProperty(value = "售后待处理")
    private Integer unHandleAfterSaleCount;

    @ApiModelProperty(value = "售后处理中")
    private Integer handlingAfterSaleCount;
}
