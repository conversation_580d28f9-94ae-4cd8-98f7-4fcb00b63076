package com.ruoyi.system.api.domain.vo.pay;

import lombok.Data;

import java.io.Serializable;

/**
 * 支付查询回调
 *
 * <AUTHOR>
 * @date 2024/6/1
 */
@Data
public class PayQueryResponseVo implements Serializable {
    private static final long serialVersionUID = -3263072110413085607L;
    private String currType = "CNY";
    private String addnInf;
    private String buyerId;
    private String mchntCd;
    private String mchntOrderNo;
    private String orderAmt;
    private String orderType;
    private String randomStr;
    private String reservedBankType;
    private String reservedBuyerLogonId;
    private String reservedChannelOrderId;
    private String reservedCouponFee;
    private String reservedFundBillList;
    private String reservedFySettleDt;
    private String reservedFyTraceNo;
    private String reservedIsCredit;
    private String reservedPromotionDetail;
    private String reservedTxnFinTs;
    private String resultCode;
    private String resultMsg;
    private String sign;
    private String termId;
    private String transStat;
    private String transactionId;
}
