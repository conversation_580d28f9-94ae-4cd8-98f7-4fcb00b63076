package com.ruoyi.system.api.domain.vo.biz.business;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.api.domain.entity.biz.model.ModelVideoResource;
import com.ruoyi.system.api.domain.vo.biz.model.ModelBaseVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelFamilyVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/21 16:20
 */
@Data
@ApiModel("模特商家端返回对象")
@EqualsAndHashCode(callSuper = true)
public class BusinessAccountCollectModelVO extends ModelBaseVO {
    private static final long serialVersionUID = -6763562684347403512L;
    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    private Integer age;

    /**
     * 生活场景照
     */
    @ApiModelProperty(value = "生活场景照")
    @JsonIgnore
    private String livePicId;

    /**
     * 生活场景照
     */
    @ApiModelProperty(value = "生活场景照")
    private List<String> lifePhoto = new ArrayList<>();
    /**
     * 亚马逊平台案例视频(关联资源id)
     */
    @ApiModelProperty(value = "亚马逊平台案例视频(关联资源id)")
    @JsonIgnore
    private String amazonVideoId ;
    /**
     * 亚马逊平台案例视频(关联资源id)
     */
    @ApiModelProperty(value = "亚马逊平台案例视频(关联资源id)")
    @JsonIgnore
    private String tiktokVideoId;
    /**
     * 亚马逊平台案例视频
     */
    @ApiModelProperty(value = "亚马逊平台案例视频")
    private List<ModelVideoResource> amazonVideo = new ArrayList<>();
    /**
     * tiktok平台案例视频
     */
    @ApiModelProperty(value = "tiktok平台案例视频")
    private List<ModelVideoResource> tiktokVideo = new ArrayList<>();

    /**
     * 视频案例
     */
    @ApiModelProperty(value = "视频案例")
    private List<ModelVideoResource> videoCase = new ArrayList<>();

    @ApiModelProperty(value = "模特家庭信息")
    private List<ModelFamilyVO> modelFamilys = new ArrayList<>();
    /**
     * 是否收藏
     */
    @ApiModelProperty(value = "是否收藏")
    private Boolean collect;
    /**
     * 模特状态
     */
    @ApiModelProperty(value = "模特状态")
    private Integer status;

    @ApiModelProperty(value = "合作深度", notes = "0:一般模特,1:优质模特,2:中度模特")
    private Integer cooperation;

    /**
     * 模特评分 (0.0-10.0)
     */
    @ApiModelProperty(value = "模特评分 (0.0-10.0)", notes = "模特评分 (0.0-10.0)", required = true)
    private BigDecimal cooperationScore;

    @ApiModelProperty(value = "模特状态 行程中/暂停/取消设置暂无档期")
    private Boolean isNoSlot = false;

    @ApiModelProperty(value = "模特id")
    private Long modelId;

    @ApiModelProperty(value = "英文部客服")
    private String issueUserName;
}
