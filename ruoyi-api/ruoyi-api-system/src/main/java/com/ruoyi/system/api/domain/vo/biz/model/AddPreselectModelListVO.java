
package com.ruoyi.system.api.domain.vo.biz.model;

import com.ruoyi.common.core.domain.vo.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/21 16:20
 */
@Data
@ApiModel("添加预选模特列表返回对象")
@EqualsAndHashCode(callSuper = true)
public class AddPreselectModelListVO  extends ModelBaseVO{
    private static final long serialVersionUID = 6376259181098772318L;
    /**
     * 合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "合作深度（0:一般模特,1:优质模特,2:中度模特）", notes = "0:一般模特,1:优质模特,2:中度模特")
    private Integer cooperation;
    /**
     * 模特评分 (0.0-10.0)
     */
    @ApiModelProperty(value = "模特评分 (0.0-10.0)", notes = "模特评分 (0.0-10.0)", required = true)
    private BigDecimal cooperationScore;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    private BigDecimal commission;

    /**
     * 待完成最高接受量
     */
    @ApiModelProperty(value = "待完成最高接受量")
    private Integer acceptability;

    /**
     * 可拍数
     */
    @ApiModelProperty(value = "可拍数")
    private Long can;
    /**
     * 待拍数
     */
    @ApiModelProperty(value = "待拍数")
    private Long waits;

    /**
     * 待确认数
     */
    @ApiModelProperty(value = "待确认数")
    private Long toBeConfirm = 0L;

    /**
     * 模特剩余可携带订单数
     */
    @ApiModelProperty(value = "模特剩余可携带订单数")
    private Long carryCount;

    /**
     * 超时率
     */
    @ApiModelProperty(value = "超时率")
    private BigDecimal overtimeRate;

    /**
     * 售后率
     */
    @ApiModelProperty(value = "售后率")
    private BigDecimal afterSaleRate;
    /**
     * 关联对接人员
     */
    @ApiModelProperty(value = "关联对接人员")
    private List<UserVO> persons = new ArrayList<>();

    /**
     * 是否被拉黑
     */
    @ApiModelProperty(value = "是否被拉黑")
    private Boolean isBlack;

    /**
     * 是否被驳回
     */
    @ApiModelProperty(value = "是否被驳回")
    private Boolean isReject;
}
