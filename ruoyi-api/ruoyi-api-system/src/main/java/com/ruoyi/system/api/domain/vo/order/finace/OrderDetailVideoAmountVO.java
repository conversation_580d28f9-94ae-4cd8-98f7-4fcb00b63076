package com.ruoyi.system.api.domain.vo.order.finace;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :订单收支明细视频金额详情
 * @create :2024-09-12 11:41
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderDetailVideoAmountVO implements Serializable {
    private static final long serialVersionUID = -6811997525835798710L;

    @ApiModelProperty(value = "视频价格（单位：$）")
    private BigDecimal videoPrice;

    @ApiModelProperty(value = "选配费用（单位：$）")
    private BigDecimal picPrice;

    @ApiModelProperty(value = "手续费（单位：$）")
    private BigDecimal exchangePrice;

    @ApiModelProperty(value = "服务费（单位：$）")
    private BigDecimal servicePrice;

    @ApiModelProperty(value = "佣金代缴税费（单位：$）")
    private BigDecimal commissionPaysTaxes;

    @ApiModelProperty(value = "当前汇率")
    private BigDecimal currentExchangeRate;

    @ApiModelProperty(value = "税点费用（单位：￥）")
    private BigDecimal taxPointCost;

    @ApiModelProperty(value = "需支付金额（单位：￥）")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "订单活动优惠总额（单位：￥）")
    private BigDecimal orderPromotionAmount;

    @ApiModelProperty(value = "使用余额")
    private BigDecimal useBalance;

    @ApiModelProperty(value = "剩余支付金额")
    private BigDecimal surplusAmount;

    @ApiModelProperty(value = "实际支付金额")
    private BigDecimal realPayAmount;

    @ApiModelProperty(value = "差额（单位：￥）")
    private BigDecimal differenceAmount;

    @ApiModelProperty(value = "订单原价（单位：￥）")
    private BigDecimal orderAmount;
}
