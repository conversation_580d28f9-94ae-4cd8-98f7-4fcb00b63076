package com.ruoyi.system.api.domain.vo.order.task;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.UserVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :售后单列表详情
 * @create :2024-12-11 09:39
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AfterSaleTaskDetailListVO implements Serializable {
    private static final long serialVersionUID = -2881757357307933835L;

    @ApiModelProperty(value = "详情主键 order_video_task_detail.id")
    private Long id;

    @ApiModelProperty(value = "任务单ID(FK:order_video_task.id)")
    private Long taskId;

    @ApiModelProperty(value = "任务单编号")
    private String taskNum;

    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTime;

    @ApiModelProperty(value = "售后分类（1：视频，2：照片）")
    private Integer afterSaleClass;

    @ApiModelProperty(value = "售后视频类型（1：重拍视频，2：补拍视频，3：要高清视频，4：原素材，5：重新上传，6：重新剪辑）")
    private Integer afterSaleVideoType;

    @ApiModelProperty(value = "售后照片类型（1：重拍照片，2：要高清照片，3：补拍照片，4：原素材）")
    private Integer afterSalePicType;

    @ApiModelProperty(value = "问题描述")
    private String content;

    /**
     * 补充剪辑要求
     */
    @ApiModelProperty(value = "补充剪辑要求")
    private String clipRecord;

    @JsonIgnore
    private String issuePicId;

    @ApiModelProperty(value = "问题图片")
    private List<String> issuePic = new ArrayList<>();

    @ApiModelProperty(value = "优先级（1:紧急,2:一般）", notes = "1:紧急,2:一般")
    private Integer priority;

    @JsonIgnore
    private Long submitById;

    @ApiModelProperty(value = "提交人姓名")
    private String submitBy;

    @ApiModelProperty(value = "处理人")
    private UserVO assignee;

    @JsonIgnore
    private Long assigneeId;

    @ApiModelProperty(value = "处理人")
    private UserVO submit;

    @ApiModelProperty(value = "状态（1：待处理，2：处理中，3：申请取消中，4：已完成，5：已拒绝，6：已关闭）")
    private Integer status;

    @ApiModelProperty(value = "关闭/完成时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date endTime;

    @ApiModelProperty(value = "售后内容")
    private String contentEnglish;
}
