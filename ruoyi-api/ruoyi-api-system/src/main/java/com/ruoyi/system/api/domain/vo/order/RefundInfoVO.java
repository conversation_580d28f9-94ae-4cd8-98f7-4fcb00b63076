package com.ruoyi.system.api.domain.vo.order;

import com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/20 15:25
 */
@Data
public class RefundInfoVO implements Serializable {
    private static final long serialVersionUID = 6900136989467403509L;
    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）", notes = "1:2张/$10,2:5张/$20")
    private Integer picCount;

    @ApiModelProperty(value = "退款照片数量")
    private Integer surplusPicCount;

    /**
     * 图片费用（单位：$）
     */
    @ApiModelProperty(value = "图片费用（单位：$）")
    private BigDecimal picPrice;

    @ApiModelProperty(value = "订单金额（单位：￥）", notes = "单位：￥")
    private BigDecimal amount;

    @ApiModelProperty(value = "需支付金额（单位：￥）")
    private BigDecimal originAmount;

    @ApiModelProperty(value = "视频活动优惠金额")
    private BigDecimal videoPromotionAmount;

    /**
     * 退款金额（单位：￥）
     */
    @ApiModelProperty(value = "退款金额（单位：￥）", notes = "单位：￥")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "已退总额（单位：￥）", notes = "单位：￥")
    private BigDecimal refundTotal;

    @ApiModelProperty(value = "是否可退：0-否，1-是", notes = "单位：￥")
    private Integer canRefund;

    @ApiModelProperty(value = "正在申请退款类型（1:补偿,2:取消订单,3:取消选配）", notes = "1:补偿,2:取消订单,3:取消选配", required = true)
    private Integer refundType;

    @ApiModelProperty(value = "是否已取消选配：0-否，1-是")
    private Integer isCancelOpention;

    /**
     * 订单参与的活动以及对应的优惠
     */
    @ApiModelProperty(value = "订单参与的活动以及对应的优惠")
    private List<OrderDiscountDetailVO> orderDiscountDetailVOS;
}
