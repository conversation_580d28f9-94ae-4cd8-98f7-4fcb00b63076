package com.ruoyi.system.api.domain.vo.order.casus;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
* 案例分组表
* <AUTHOR>
 * @TableName case_group
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CasusGroupVO implements Serializable {

    private static final long serialVersionUID = 1167203794075966757L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("分组名称")
    private String name;

    @ApiModelProperty("平台类型：0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private Integer platform;

    @ApiModelProperty("视频数量")
    private Integer videoCount;
}
