package com.ruoyi.system.api.domain.vo.order.logistic;

import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :视频订单详情Vo
 * @create :2025-06-13 16:34
 **/
@Data
public class LogisticFollowVideoInfoVO extends OrderVideoLogisticFollow implements Serializable {
    private static final long serialVersionUID = -1226009240105812187L;

    @ApiModelProperty(value = "物流主状态简述")
    private String mainStatusSketch;

    @ApiModelProperty(value = "物流主状态")
    private String mainStatus;
}
