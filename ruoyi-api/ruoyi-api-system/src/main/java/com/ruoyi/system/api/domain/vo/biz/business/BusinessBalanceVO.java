package com.ruoyi.system.api.domain.vo.biz.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家表
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessBalanceVO implements Serializable {

    private static final long serialVersionUID = -1262577170635983267L;
    @NotNull(message = "[公司ID]不能为空")
    @ApiModelProperty("公司ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商家主账号（FK：business_account.account）
     */
    @NotNull(message = "[商家主账号（FK：business_account.account）]不能为空")
    @ApiModelProperty("商家主账号（FK：business_account.account）")
    private String ownerAccount;
    /**
     * 商家名称
     */
    @NotBlank(message = "[商家名称]不能为空")
    @Size(max = 20, message = "编码长度不能超过20")
    @ApiModelProperty("商家名称")
    @Length(max = 20, message = "编码长度不能超过20")
    private String name;

    @NotNull(message = "[是否为代理(0:否,1:是)]不能为空")
    @ApiModelProperty("是否为代理(0:否,1:是)")
    private Integer isProxy;

    @NotNull(message = "[账号状态（0正常 1禁用）]不能为空")
    @ApiModelProperty("账号状态（0正常 1禁用）")
    private Integer status;

    @ApiModelProperty("客户类型 （0-一般客户 1-重要客户）")
    private Integer customerType;

    @ApiModelProperty("帐号余额")
    private BigDecimal balance;

    @ApiModelProperty("使用余额")
    private BigDecimal useBalance;

    @ApiModelProperty("有效余额")
    private BigDecimal validBalance;

    @ApiModelProperty("余额是否锁定（0-不锁定，1-锁定）")
    private Integer isBalanceLock;

    @Size(max = 10, message = "编码长度不能超过10")
    @ApiModelProperty("会员编码")
    @Length(max = 10, message = "编码长度不能超过10")
    private String memberCode;

    @NotNull(message = "[会员类型: 0-非会员，1-会员]不能为空")
    @ApiModelProperty("会员类型: 0-非会员，1-会员")
    private Integer memberType;

    @NotNull(message = "[会员状态：0-非会员1-正常，2-即将过期，3-已过期]不能为空")
    @ApiModelProperty("会员状态：0-非会员1-正常，2-即将过期，3-已过期")
    private Integer memberStatus;

    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("商家创建时间（注册时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("微信名称 优先员工姓名")
    private String nickName;

}