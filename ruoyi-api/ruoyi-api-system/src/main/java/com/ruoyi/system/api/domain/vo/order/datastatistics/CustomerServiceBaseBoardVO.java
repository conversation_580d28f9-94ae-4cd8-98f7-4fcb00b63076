package com.ruoyi.system.api.domain.vo.order.datastatistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/19 9:31
 */
@Data
public class CustomerServiceBaseBoardVO implements Serializable {
    private static final long serialVersionUID = 3583128913893249387L;

    /**
     * 服务中订单数
     */
    @ApiModelProperty("服务中订单数")
    private Long serviceOrderCount;

    /**
     * 昨日新增订单数
     */
    @ApiModelProperty("昨日新增订单数")
    private Long orderNewCount;

    /**
     * 昨日完成订单数
     */
    @ApiModelProperty("昨日完成订单数")
    private Long orderFinishCount;

    /**
     * 昨日排单数
     */
    @ApiModelProperty("昨日排单数")
    private Long orderScheduledCount;

    /**
     * 素人昨日排单数
     */
    @ApiModelProperty("素人昨日排单数")
    private Long orderScheduledSoleCount;

    /**
     * 影响者昨日排单数
     */
    @ApiModelProperty("影响者昨日排单数")
    private Long orderScheduledInfluencerCount;

    /**
     * 昨日被驳回模特次数
     */
    @ApiModelProperty("昨日被驳回模特次数")
    private Long modelRejectCount;

    /**
     * 素人昨日被驳回模特次数
     */
    @ApiModelProperty("素人昨日被驳回模特次数")
    private Long modelRejectSoleCount;

    /**
     * 影响者昨日被驳回模特次数
     */
    @ApiModelProperty("影响者昨日被驳回模特次数")
    private Long modelRejectInfluencerCount;

    /**
     * 昨日平均佣金
     */
    @ApiModelProperty("昨日平均佣金")
    private BigDecimal averageCommission = BigDecimal.ZERO;

    /**
     * 素人昨日平均佣金
     */
    @ApiModelProperty("素人昨日平均佣金")
    private BigDecimal averageCommissionSole = BigDecimal.ZERO;

    /**
     * 影响者昨日平均佣金
     */
    @ApiModelProperty("影响者昨日平均佣金")
    private BigDecimal averageCommissionInfluencer = BigDecimal.ZERO;
}
