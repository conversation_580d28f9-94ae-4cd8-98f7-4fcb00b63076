package com.ruoyi.system.api.domain.vo.biz.business.balance;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家余额详情锁定表
 *
 * <AUTHOR>
 * @TableName business_balance_detail_lock
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalanceDetailLockInfoVO implements Serializable {

    private static final long serialVersionUID = 2225406543627474792L;
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("锁定单号（提现单号、视频订单、会员订单）")
    private String number;

    @ApiModelProperty("视频编码")
    private String videoCode;

    @ApiModelProperty("预付单号")
    private String prepayNum;

    @ApiModelProperty("来源单号")
    private String balanceNumber;

    @ApiModelProperty("商家余额详情ID（business_balance_detail.id）")
    private Long balanceDetailId;

    @ApiModelProperty("已用金额")
    private BigDecimal useBalance;

    @ApiModelProperty("提现金额")
    private BigDecimal payOutAmount;

    @ApiModelProperty("状态（0:待处理,1:已提现,2.已取消）")
    private Integer status;

    @ApiModelProperty("订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、7.线下钱包充值收入、8.线上钱包充值收入)")
    private Integer origin;

    @ApiModelProperty("下单运营")
    private String createOrderUserName;

    @ApiModelProperty("下单运营微信名")
    private String createOrderUserNickName;

    @ApiModelProperty("余额增加时间")
    private Date balanceCreateTime;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    @ApiModelProperty("提现备注")
    private String auditRemark;

    @ApiModelProperty("会员编码")
    private String merchantCode;

    @ApiModelProperty("来源单号(小钱包来源单号)")
    private String originNumber;

    @ApiModelProperty("1-退款订单,2-预付款订单")
    private Integer numberType;

    @ApiModelProperty("商家名称")
    private Long businessId;

    @ApiModelProperty("下单商家")
    private String businessName;

    @ApiModelProperty("支付方式")
    private Integer withdrawWay;

    @ApiModelProperty("提现总金额")
    private BigDecimal amount;

    @ApiModelProperty("实际支付")
    private BigDecimal realAmount;

}
