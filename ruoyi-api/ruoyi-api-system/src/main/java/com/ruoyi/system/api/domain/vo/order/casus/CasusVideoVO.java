package com.ruoyi.system.api.domain.vo.order.casus;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
* 案例视频表
* <AUTHOR>
 * @TableName case_video
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CasusVideoVO implements Serializable {

    private static final long serialVersionUID = -2365991170721487911L;
    @NotNull(message="[主键id]不能为空")
    @ApiModelProperty("主键id")
    private Long id;

    @NotBlank(message="[视频名称]不能为空")
    @Size(max= 32,message="编码长度不能超过32")
    @ApiModelProperty("视频名称")
    @Length(max= 32,message="编码长度不能超过32")
    private String name;

    @NotBlank(message="[封面图片]不能为空")
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("封面图片")
    @Length(max= 255,message="编码长度不能超过255")
    private String pic;

    @NotBlank(message="[视频链接]不能为空")
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("视频链接")
    @Length(max= 255,message="编码长度不能超过255")
    private String link;

    @NotNull(message="[平台类型：0:Amazon,1:tiktok,2:其他,3:APP/解说类]不能为空")
    @ApiModelProperty("平台类型：0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private Integer platform;

    @NotBlank(message="[创建人]不能为空")
    @Size(max= 32,message="编码长度不能超过32")
    @ApiModelProperty("创建人")
    @Length(max= 32,message="编码长度不能超过32")
    private String createBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
