package com.ruoyi.system.api.domain.vo.biz.business.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :用户拉黑模特数据
 * @create :2025-01-09 11:25
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserBlackModelVO implements Serializable {
    private static final long serialVersionUID = -5453582960364679991L;

    @ApiModelProperty("登录账号ID")
    private Long bizUserId;

    @ApiModelProperty("模特ID")
    private Long modelId;

    @ApiModelProperty("模特头像")
    private String modelPic;

    @ApiModelProperty("模特名称")
    private String name;

    @ApiModelProperty("国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    private Integer nation;

    @ApiModelProperty("国家_字典值")
    private String nationDict;

    @ApiModelProperty("模特类型(0:影响者,1:素人)'")
    private Integer type;

    @ApiModelProperty("模特类型_字典值")
    private String typeDict;

    @ApiModelProperty("拉黑时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
