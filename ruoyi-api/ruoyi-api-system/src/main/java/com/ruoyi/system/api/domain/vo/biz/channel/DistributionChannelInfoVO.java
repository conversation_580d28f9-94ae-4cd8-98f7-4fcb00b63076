package com.ruoyi.system.api.domain.vo.biz.channel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-30 13:34
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DistributionChannelInfoVO implements Serializable {
    private static final long serialVersionUID = -335594477901953731L;
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty("微信名")
    private String nickName;

    @ApiModelProperty("激活状态（1-有效， 0-无效）")
    private Integer activationStatus;

    @ApiModelProperty("激活时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activationTime;

    /**
     * 海报名称
     */
    @ApiModelProperty("海报名称")
    private String posterName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("结算比例")
    private BigDecimal brokeRage;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    @ApiModelProperty("种草码")
    private String seedCode;

    @ApiModelProperty("渠道类型：2-分销渠道 7-裂变")
    private Integer channelType;

    @ApiModelProperty("渠道账号状态：1-正常 2-已过期 3-已解绑")
    private Integer channelAccountType;

    @ApiModelProperty("会员状态：0-非会员1-正常，2-即将过期，3-已过期")
    private Integer memberStatus;

    @ApiModelProperty("专属链接")
    private String dedicatedLink;

    @ApiModelProperty("专属企微二维码地址")
    private String weChatUrl;

    @ApiModelProperty("结算金额")
    private BigDecimal settleAmount;

    @ApiModelProperty("待结算金额")
    private BigDecimal unSettleAmount;

    @ApiModelProperty("种草收益")
    private BigDecimal totalAmount;
}
