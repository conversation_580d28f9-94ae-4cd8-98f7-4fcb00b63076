package com.ruoyi.system.api.domain.vo.biz.model;

import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-9-5
 */
@Data
public class ModelTagVO implements Serializable {
    private static final long serialVersionUID = -7082828202614413716L;
    /**
     * 模特id
     */
    @ApiModelProperty(value = "模特id")
    private Long modelId;

    /** 分类id */
    @ApiModelProperty(value = "分类id")
    private Long categoryId;

    /**
     * 标签id
     */
    @ApiModelProperty(value = "标签id")
    @Excel(name = "标签id")
    private Long id;

    @ApiModelProperty(value = "标签名称")
    private String name;

    /**
     * 商家端高亮标识
     */
    @ApiModelProperty(value = "商家端高亮标识")
    private boolean highlight;
}
