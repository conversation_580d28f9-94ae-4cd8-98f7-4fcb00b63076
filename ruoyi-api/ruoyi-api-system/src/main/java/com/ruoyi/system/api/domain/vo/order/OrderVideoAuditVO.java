package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :订单合并审核列表
 * @create :2025-03-06 15:53
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderVideoAuditVO extends OrderListVO implements Serializable {
    private static final long serialVersionUID = -5450106877456959172L;
    @ApiModelProperty("合并Id")
    private Long mergeId;

    @ApiModelProperty("是否合并订单")
    private Integer isMergeOrder;

    @ApiModelProperty("合并人姓名")
    private String mergeBy;

    @ApiModelProperty("合并时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date mergeTime;

    @ApiModelProperty("合并后自动关闭时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date autoCloseTime;

    @ApiModelProperty("订单Id列表：',' 隔开")
    private String orderNumStr;

    @ApiModelProperty("订单订单列表")
    private List<String> orderNumGroup;

    @ApiModelProperty(value = "合并订单原价")
    private BigDecimal mergeOrderAmount;

    @ApiModelProperty(value = "合并订单原价（单位：$）")
    private BigDecimal mergeOrderAmountDollar;

    @ApiModelProperty(value = "合并支付金额")
    private BigDecimal mergePayAmount;

    @ApiModelProperty(value = "合并支付金额（单位：$）")
    private BigDecimal mergePayAmountDollar;

    @ApiModelProperty(value = "合并剩余（单位：$）")
    private BigDecimal mergeSurplusAmount;

    @ApiModelProperty(value = "合并使用余额（单位：$）")
    private BigDecimal mergeUseBalance;

    @ApiModelProperty(value = "合并实际支付（单位：$）")
    private BigDecimal mergeRealPayAmount;

    @ApiModelProperty(value = "合并优惠金额（单位：$）")
    private BigDecimal mergeOrderPromotionAmount;

    @ApiModelProperty(value = "合并订单实付金额（对应币种实付）")
    private BigDecimal mergeRealPayAmountCurrency;

}
