package com.ruoyi.system.api.domain.vo.biz.datastatistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/12 14:23
 */
@Data
public class ModelOrderRankingListVO implements Serializable {
    private static final long serialVersionUID = 2144799331309805343L;

    /**
     * 模特名称
     */
    @ApiModelProperty("模特名称")
    private String modelName;

    /**
     * 模特头像
     */
    @ApiModelProperty("模特头像")
    private String modelAvatar;

    /**
     * 模特评分 (0.0-10.0)
     */
    @ApiModelProperty("模特评分")
    private BigDecimal modelCooperationScore;

    /**
     * 模特订单数量
     */
    @ApiModelProperty("模特订单数量")
    private Long orderCount;
}
