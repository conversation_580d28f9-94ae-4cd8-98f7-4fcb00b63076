package com.ruoyi.system.api.domain.vo.biz.model;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.entity.biz.model.ModelVideoResource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/21 16:20
 */
@Data
@ApiModel("模特返回对象")
@EqualsAndHashCode(callSuper = true)
public class ModelVO extends ModelBaseVO{
    private static final long serialVersionUID = -1281041116390996916L;
    /**
     * 最后登录时间
     */
    @ApiModelProperty(value = "最后登录时间")
    private Date lastLoginTime;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;
    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    private Integer age;

    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    private String zipcode;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String detailAddress;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String city;

    /**
     * 州
     */
    @ApiModelProperty(value = "州")
    private String state;

    /**
     * 生活场景照
     */
    @ApiModelProperty(value = "生活场景照")
    @JsonIgnore
    private String livePicId;
    /**
     * 亚马逊案例视频
     */
    @ApiModelProperty(value = "亚马逊案例视频")
    @JsonIgnore
    private String amazonVideoId;
    /**
     * tiktok案例视频
     */
    @ApiModelProperty(value = "tiktok案例视频")
    @JsonIgnore
    private String tiktokVideoId;

    /**
     * 模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)
     */
    @ApiModelProperty(value = "模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)")
    private Integer status;

    /**
     * 状态变更时间
     */
    @ApiModelProperty(value = "状态变更时间")
    private Date statusTime;

    /**
     * 状态说明
     */
    @ApiModelProperty(value = "状态说明")
    private String statusExplain;

    /**
     * 取消合作类型(0-我们取消,1-模特取消)
     */
    @ApiModelProperty(value = "取消合作类型(0-我们取消,1-模特取消)")
    private Integer cancelCooperationType;

    /**
     * 取消合作子类型(关联字典id)
     */
    @ApiModelProperty(value = "状态说明")
    private String cancelCooperationSubType;



    /**
     * 合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "合作深度（0:一般模特,1:优质模特,2:中度模特）", notes = "0:一般模特,1:优质模特,2:中度模特")
    private Integer cooperation;

    /**
     * 模特评分 (0.0-10.0)
     */
    @ApiModelProperty(value = "模特评分 (0.0-10.0)", notes = "模特评分 (0.0-10.0)", required = true)
    private BigDecimal cooperationScore;

    /**
     * 收件人
     */
    @ApiModelProperty(value = "收件人")
    private String recipient;

    /**
     * 生活场景照
     */
    @ApiModelProperty(value = "生活场景照")
    private List<String> lifePhoto = new ArrayList<>();
    /**
     * 行程时间
     */
    @ApiModelProperty(value = "行程时间")
    private ModelTravelVO travel;
    /**
     * 行程结束时间
     */
    @ApiModelProperty(value = "行程结束时间")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN,timezone = "GMT+8")
    private Date travelEndTime;
    /**
     * 关联对接人员
     */
    @ApiModelProperty(value = "关联对接人员")
    private List<UserVO> persons = new ArrayList<>();
    /**
     * 待完成最高接受量
     */
    @ApiModelProperty(value = "待完成最高接受量")
    private Integer acceptability;
    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    private BigDecimal commission;

    @ApiModelProperty(value = "平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private String platform;
    /**
     * 亚马逊平台案例视频
     */
    @ApiModelProperty(value = "亚马逊平台案例视频")
    private List<ModelVideoResource> amazonVideo = new ArrayList<>();
    /**
     * tiktok平台案例视频
     */
    @ApiModelProperty(value = "tiktok平台案例视频")
    private List<ModelVideoResource> tiktokVideo = new ArrayList<>();

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 创建人
     */
    @JsonIgnore
    private Long createBy;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private UserVO createUser;
    /**
     * 置顶时间
     */
    @ApiModelProperty(value = "置顶时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date topTime;

    /**
     * 可拍数
     */
    @ApiModelProperty(value = "可拍数")
    private Long can;
    /**
     * 待拍数
     */
    @ApiModelProperty(value = "待拍数")
    private Long waits;

    /**
     * 待确认数
     */
    @ApiModelProperty(value = "待确认数")
    private Long toBeConfirm = 0L;

    /**
     * 超时率
     */
    @ApiModelProperty(value = "超时率")
    private BigDecimal overtimeRate;
    /**
     * 售后率
     */
    @ApiModelProperty(value = "售后率")
    private BigDecimal afterSaleRate;

    @ApiModelProperty(value = "被拉黑数")
    private Long blacklistCount;
    /**
     * 已完成数
     */
    @ApiModelProperty(value = "已完成数")
    private Long finished;
    /**
     * 被收藏数
     */
    @ApiModelProperty(value = "被收藏数")
    private Long collected;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "家庭id")
    private Long familyId;

    @ApiModelProperty(value = "亲属关系(0=发起人,1=母子,2=母女,3=夫妻,4=父子,5=父女,6=兄弟,7=姐妹,8=兄妹,9=姐弟")
    private Integer modelFamilyRelationship;

    @ApiModelProperty(value = "是否家庭模特发起者：0-否,1-是")
    private Integer isInitiator;

    @ApiModelProperty(value = "是否家庭模特：0-否,1-是")
    private Integer isFamilyModel;

    @ApiModelProperty(value = "家庭成员数量")
    private Integer familyNum;

    /**
     * 模特简介
     */
    @ApiModelProperty(value = "模特简介")
    private String about;

    /**
     * 开发人
     */
    @ApiModelProperty(value = "开发人")
    private UserVO developerUser;
}
