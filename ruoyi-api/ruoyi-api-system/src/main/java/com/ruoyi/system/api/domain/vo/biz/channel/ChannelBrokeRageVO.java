package com.ruoyi.system.api.domain.vo.biz.channel;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-05-15 11:35
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChannelBrokeRageVO implements Serializable {
    private static final long serialVersionUID = -1049008873007642515L;

    @ApiModelProperty("渠道类型：2-分销渠道 7-裂变")
    private Integer channelType;

    @ApiModelProperty("会员折扣类型（1-固定金额，2-固定比例）")
    private Integer memberDiscountType;

    @ApiModelProperty("会员折扣")
    private BigDecimal memberDiscount;

    @ApiModelProperty("结算佣金类型（1-固定金额，2-固定比例）")
    private Integer settleDiscountType;

    @ApiModelProperty("佣金比例、固定金额")
    private BigDecimal settleDiscount;


    @ApiModelProperty("活动开始时间")
    private String startTime;

    @ApiModelProperty("活动结束时间")
    private String endTime;

    @ApiModelProperty("渠道信息")
    private DistributionChannel distributionChannel;


    public void setEntity(ChannelBrokeRageVO vo) {
        if (ObjectUtil.isNull(vo)) {
            return;
        }
        this.channelType = vo.channelType;
        this.memberDiscountType = vo.memberDiscountType;
        this.memberDiscount = vo.memberDiscount;
        this.settleDiscountType = vo.settleDiscountType;
        this.settleDiscount = vo.settleDiscount;
        this.startTime = vo.startTime;
        this.endTime = vo.endTime;
        this.distributionChannel = vo.distributionChannel;

    }
}
