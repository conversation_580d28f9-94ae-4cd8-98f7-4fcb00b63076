package com.ruoyi.system.api.domain.vo.order.logistic;

import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollowRecord;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 物流跟进表
 *
 * <AUTHOR>
 * @TableName order_video_logistic_follow
 */
@Data
public class OrderVideoLogisticFollowDetailVO extends OrderVideoLogisticFollowVO implements Serializable {

    private static final long serialVersionUID = -8431784179667315615L;
    @ApiModelProperty("跟进记录")
    private List<OrderVideoLogisticFollowRecordVO> orderVideoLogisticFollowRecords;

}
