package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-06-20
 */
@Data
public class OrderVideoRefundVO implements Serializable {
    private static final long serialVersionUID = 7651757489469996768L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /**
     * 视频订单id
     */
    @ApiModelProperty(value = "视频订单id")
    private Long videoId;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 退款审批号
     */
    @ApiModelProperty(value = "退款审批号")
    private String refundNum;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 产品中文名
     */
    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    private String productLink;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private Integer platform;

    /** 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国） */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    private Integer shootingCountry;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）", notes = "1:2张/$10,2:5张/$20")
    private Integer picCount;

    @ApiModelProperty(value = "退款照片数量")
    private Integer refundPicCount;

    @ApiModelProperty(value = "是否全额退款照片（0:是,1:不是）")
    private Integer isFullRefundPic;

    /**
     * 申请时订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成）
     */
    @ApiModelProperty(value = "申请时订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成）")
    private Integer status;

    /**
     * 拍摄模特id
     */
    @ApiModelProperty(value = "拍摄模特id")
    @JsonIgnore
    private Long shootModelId;

    /**
     * 拍摄模特
     */
    @ApiModelProperty(value = "拍摄模特")
    private ModelOrderSimpleVO shootModel;

    /**
     * 对接人id
     */
    @ApiModelProperty(value = "对接人id")
    @JsonIgnore
    private Long contactId;

    /**
     * 对接人
     */
    @ApiModelProperty(value = "对接人")
    private UserVO contactUser;

    /**
     * 出单人id
     */
    @ApiModelProperty(value = "出单人id")
    @JsonIgnore
    private Long issueId;

    /**
     * 出单人
     */
    @ApiModelProperty(value = "出单人")
    private UserVO issueUser;

    /**
     * 视频金额（单位：￥）
     */
    @ApiModelProperty(value = "视频金额（单位：￥）", notes = "单位：￥")
    private BigDecimal amount;

    @ApiModelProperty(value = "视频已退金额", notes = "单位：￥")
    private BigDecimal refundAmountTotal;

    /**
     * 视频金额 - 视频差额
     */
    @ApiModelProperty(value = "实际视频金额（单位：￥）", notes = "单位：￥")
    private BigDecimal realAmount;

    /**
     * 退款金额（单位：￥）
     */
    @ApiModelProperty(value = "退款金额（单位：￥）", notes = "单位：￥")
    private BigDecimal refundAmount;

    /**
     * 退款类型（1:补偿,2:取消订单,3:取消选配）
     */
    @ApiModelProperty(value = "退款类型（1:补偿,2:取消订单,3:取消选配）", notes = "1:补偿,2:取消订单,3:取消选配")
    private Integer refundType;

    /**
     * 发起方（1:商家,2:平台）
     */
    @ApiModelProperty(value = "发起方（1:商家,2:平台）", notes = "1:商家,2:平台")
    private Integer initiatorSource;

    /**
     * 发起人名称
     */
    @ApiModelProperty(value = "发起人名称")
    private String initiatorName;

    /**
     * 退款原因
     */
    @ApiModelProperty(value = "退款原因")
    private String refundCause;

    /**
     * 退款状态（0:退款待审核,1:退款中,2:已拒绝,3:已取消,4:退款成功）
     */
    @ApiModelProperty(value = "退款状态（0:退款待审核,1:退款中,2:已拒绝,3:已取消,4:退款成功）", notes = "0:退款待审核,1:退款中,2:已拒绝,3:已取消,4:退款成功")
    private Integer refundStatus;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateTime;

    @ApiModelProperty(value = "操作人")
    private String operateBy;

    /**
     * 拒绝理由
     */
    @ApiModelProperty(value = "拒绝理由")
    private String rejectCause;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建订单用户账号")
    private String createOrderUserAccount;

    @ApiModelProperty(value = "视频订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭")
    private Integer videoStatus;

    @ApiModelProperty(value = "是否是取消订单:0-否 1-是")
    private Integer isCancelOrder;
}
