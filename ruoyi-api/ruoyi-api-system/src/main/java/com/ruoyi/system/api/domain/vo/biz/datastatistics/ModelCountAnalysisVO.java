package com.ruoyi.system.api.domain.vo.biz.datastatistics;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.enums.ModelCooperationEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/9 17:45
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModelCountAnalysisVO implements Serializable {
    private static final long serialVersionUID = 585168428642016233L;

    /**
     * 记录截止时间
     */
    @ApiModelProperty("记录截止时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date writeTimeEnd;

    /**
     * 该月新增/淘汰模特（人）
     */
    @ApiModelProperty("该月新增/淘汰模特（人）")
    private Long modelNumber;

    /**
     * 新增/淘汰模特平均佣金（$）
     */
    @ApiModelProperty("新增/淘汰模特平均佣金（$）")
    private BigDecimal modelAverageCommission;

    /**
     * 模特合作深度分析饼图
     */
    @ApiModelProperty("模特合作深度分析饼图")
    private List<PieChartVO> pieChartVOS;

    public ModelCountAnalysisVO echo() {
        if (CollUtil.isEmpty(pieChartVOS)) {
            pieChartVOS = List.of(
                    PieChartVO.init(ModelCooperationEnum.ORDINARY.getLabel()),
                    PieChartVO.init(ModelCooperationEnum.MODERATE.getLabel()),
                    PieChartVO.init(ModelCooperationEnum.QUALITY.getLabel())
            );
        } else {
            pieChartVOS.forEach(PieChartVO::echo);
        }
        modelNumber = modelNumber == null ? 0 : modelNumber;
        modelAverageCommission = modelAverageCommission == null ? BigDecimal.ZERO : modelAverageCommission;

        return this;
    }
}
