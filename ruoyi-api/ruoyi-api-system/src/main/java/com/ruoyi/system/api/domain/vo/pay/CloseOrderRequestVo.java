package com.ruoyi.system.api.domain.vo.pay;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;

import java.io.Serializable;

/**
 * 支付请求数据
 *
 * <AUTHOR>
 * @date 2024/5/31
 */

public class CloseOrderRequestVo implements Serializable {

    private static final long serialVersionUID = -2462949846494925582L;
    /**
     * 版本号 v1.0
     */
    private String version = "1.0";

    /**
     * 商户号
     */
    private String mchntCd;

    /**
     * 随机字符串
     */
    private String randomStr;

    /**
     * 终端号
     */
    private String termId = "1";

    /**
     * 商户订单号
     */
    private String mchntOrderNo;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 商户密钥
     */
    private String mchntKey;

    public CloseOrderRequestVo() {
    }

    public CloseOrderRequestVo(String mchntCd, String orderType, String mchntOrderNo, String mchntKey) {
        this.mchntCd = mchntCd;
        this.orderType = orderType;
        this.mchntOrderNo = mchntOrderNo;
        this.mchntKey = mchntKey;
        this.randomStr = RandomUtil.randomString(16);
    }

    private String getPreOrderSign() {
        return SecureUtil.md5(mchntCd + "|"
                + orderType + "|"
                + mchntOrderNo + "|"
                + termId + "|"
                + randomStr + "|"
                + version + "|"
                + mchntKey);
    }

    private String getQuerySign() {
        return SecureUtil.md5(mchntCd + "|"
                + orderType + "|"
                + mchntOrderNo + "|"
                + termId + "|"
                + randomStr + "|"
                + version + "|"
                + mchntKey);
    }

    public String getVersion() {
        return version;
    }

    public String getUnderlinePreOrderJsonStr() {
        return "{" +
                "\"mchnt_cd\":\"" + mchntCd + "\"," +
                "\"order_type\":\"" + orderType + "\"," +
                "\"mchnt_order_no\":\"" + mchntOrderNo + "\"," +
                "\"term_id\":\"" + termId + "\"," +
                "\"random_str\":\"" + randomStr + "\"," +
                "\"version\":\"" + version + "\"," +
                "\"sign\":\"" + getPreOrderSign() + "\"" +
                "}";
    }

    public String getUnderlineQueryJsonStr() {
        return "{" +
                "\"mchnt_cd\":\"" + mchntCd + "\"," +
                "\"order_type\":\"" + orderType + "\"," +
                "\"mchnt_order_no\":\"" + mchntOrderNo + "\"," +
                "\"term_id\":\"" + termId + "\"," +
                "\"random_str\":\"" + randomStr + "\"," +
                "\"version\":\"" + version + "\"," +
                "\"sign\":\"" + getQuerySign() + "\"" +
                "}";
    }
}
