package com.ruoyi.system.api.domain.vo.biz.datastatistics.member;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会员不同类型订单数量
 * @create :2025-06-19 14:26
 **/
@Data
public class MemberTrendVO implements Serializable {
    private static final long serialVersionUID = -4085525443370438343L;

    @ApiModelProperty(value = "记录时间")
    private String recordTime;

    @ApiModelProperty(value = "新会员数")
    private Long memberCount;

    @ApiModelProperty(value = "续费会员数")
    private Long renewMemberCount;

    @ApiModelProperty(value = "到期会员数量")
    private Long expireMemberCount;

    @ApiModelProperty(value = "退出会员数量")
    private Long exitMemberCount;

    @ApiModelProperty(value = "续费/新会员数量")
    private Long rechargeMemberCount;

    public static MemberTrendVO init(String recordTime) {
        MemberTrendVO memberTrendVO = new MemberTrendVO();
        memberTrendVO.setRecordTime(recordTime);
        memberTrendVO.setMemberCount(0L);
        memberTrendVO.setRenewMemberCount(0L);
        memberTrendVO.setExpireMemberCount(0L);
        memberTrendVO.setExitMemberCount(0L);
        memberTrendVO.setRechargeMemberCount(0L);
        return memberTrendVO;
    }
}
