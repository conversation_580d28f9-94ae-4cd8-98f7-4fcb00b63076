package com.ruoyi.system.api.domain.vo.biz.business;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家表
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessVO implements Serializable {

    private static final long serialVersionUID = -1262577170635983267L;
    @NotNull(message="[公司ID]不能为空")
    @ApiModelProperty("公司ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商家主账号（FK：business_account.account）
     */
    @NotNull(message="[商家主账号（FK：business_account.account）]不能为空")
    @ApiModelProperty("商家主账号（FK：business_account.account）")
    private String ownerAccount;
    /**
     * 商家名称
     */
    @NotBlank(message="[商家名称]不能为空")
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("商家名称")
    @Length(max= 20,message="编码长度不能超过20")
    private String name;

    @ApiModelProperty("商家规模")
    private Integer scale;

    @ApiModelProperty("种草码")
    private String seedCode;
    /**
     * 是否为代理(0:否,1:是)
     */
    @NotNull(message="[是否为代理(0:否,1:是)]不能为空")
    @ApiModelProperty("是否为代理(0:否,1:是)")
    private Integer isProxy;
    /**
     * 账号状态（0正常 1禁用）
     */
    @NotNull(message="[账号状态（0正常 1禁用）]不能为空")
    @ApiModelProperty("账号状态（0正常 1禁用）")
    private Integer status;
    /**
     * 客户类型 （0-一般客户 1-重要客户）
     */
    @ApiModelProperty("客户类型 （0-一般客户 1-重要客户）")
    private Integer customerType;

    @ApiModelProperty("商家标识 （0-新客 1-老客 2-'-'）")
    private Integer businessIdentifier;
    /**
     * 帐号余额
     */
    @ApiModelProperty("帐号余额")
    private BigDecimal balance;

    @ApiModelProperty("使用余额")
    private BigDecimal useBalance;

    @ApiModelProperty("有效余额")
    private BigDecimal validBalance;

    @ApiModelProperty("余额是否锁定（0-不锁定，1-锁定）")
    private Integer isBalanceLock;

    /**
     * 对接客服  FK：sys_user.user_id
     */
    @NotNull(message="[对接客服  FK：sys_user.user_id]不能为空")
    @ApiModelProperty("对接客服  FK：sys_user.user_id")
    private Long waiterId;
    /**
     * 抬头类型  0-企业
     */
    @ApiModelProperty("抬头类型  0-企业")
    private Integer invoiceTitleType;
    /**
     * 发票抬头
     */
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("发票抬头")
    @Length(max= 50,message="编码长度不能超过50")
    private String invoiceTitle;
    /**
     * 税号
     */
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("税号")
    @Length(max= 50,message="编码长度不能超过50")
    private String invoiceDutyParagraph;
    /**
     * 发票内容
     */
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("发票内容")
    @Length(max= 50,message="编码长度不能超过50")
    private String invoiceContent;
    /**
     * 会员编码
     */
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("会员编码")
    @Length(max= 10,message="编码长度不能超过10")
    private String memberCode;
    /**
     * 会员类型: 0-非会员，1-会员
     */
    @NotNull(message="[会员类型: 0-非会员，1-会员]不能为空")
    @ApiModelProperty("会员类型: 0-非会员，1-会员")
    private Integer memberType;
    /**
     * 会员状态：0-非会员1-正常，2-即将过期，3-已过期
     */
    @NotNull(message="[会员状态：0-非会员1-正常，2-即将过期，3-已过期]不能为空")
    @ApiModelProperty("会员状态：0-非会员1-正常，2-即将过期，3-已过期")
    private Integer memberStatus;
    /**
     * 会员套餐名称
     */
    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberPackageType;
    /**
     * 会员首次购买时间
     */
    @ApiModelProperty("会员首次购买时间")
    private Date memberFirstTime;

    @ApiModelProperty("首次购买套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberFirstType;
    /**
     * 会员最近购买时间
     */
    @ApiModelProperty("会员最近购买时间")
    private Date memberLastTime;

    @ApiModelProperty("会员有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date memberValidity;

    @ApiModelProperty("会员有效期 年月日 时分秒 格式")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date memberValidityTime;

    /**
     * 是否展示手机号(0:否,1:是)
     */
    @ApiModelProperty("手机可见性")
    private Integer phoneVisible;
}