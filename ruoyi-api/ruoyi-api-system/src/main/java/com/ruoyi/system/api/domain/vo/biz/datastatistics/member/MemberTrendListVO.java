package com.ruoyi.system.api.domain.vo.biz.datastatistics.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.compress.utils.Lists;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会员趋势列表VO
 * @create :2025-06-20 10:50
 **/
@Data
public class MemberTrendListVO implements Serializable {
    private static final long serialVersionUID = -7313029992507736146L;

    @ApiModelProperty("日期数组")
    private List<String> dateArray;

    @ApiModelProperty("新会员数数组")
    private List<Long> memberCountArray;

    @ApiModelProperty("续费会员数数组")
    private List<Long> renewMemberCountArray;

    @ApiModelProperty("到期会员数量数组")
    private List<Long> expireMemberCountArray;

    @ApiModelProperty("退出会员数量数组")
    private List<Long> exitMemberCountArray;

    @ApiModelProperty("续费/新会员数量数组")
    private List<Long> rechargeMemberCountArray;


    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public static MemberTrendListVO init(){
        MemberTrendListVO memberTrendListVO = new MemberTrendListVO();
        memberTrendListVO.setDateArray(Lists.newArrayList());
        memberTrendListVO.setMemberCountArray(Lists.newArrayList());
        memberTrendListVO.setRenewMemberCountArray(Lists.newArrayList());
        memberTrendListVO.setExpireMemberCountArray(Lists.newArrayList());
        memberTrendListVO.setExitMemberCountArray(Lists.newArrayList());
        memberTrendListVO.setRechargeMemberCountArray(Lists.newArrayList());
        memberTrendListVO.setUpdateTime(new Date());
        return memberTrendListVO;
    }
}
