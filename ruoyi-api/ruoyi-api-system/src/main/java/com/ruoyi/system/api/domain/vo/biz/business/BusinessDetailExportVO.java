package com.ruoyi.system.api.domain.vo.biz.business;

import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 商家表
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessDetailExportVO implements Serializable {

    @Excel(name = "会员编码")
    private String memberCode;

    @Excel(name = "公司名称")
    private String name;

    @Excel(name = "主账号微信")
    private String nickName;

    @Excel(name = "最新会员开通时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date memberLastTime;

    @Excel(name = "会员到期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date memberValidity;

    @Excel(name = "账号状态", readConverterExp = "0=正常,1=禁用")
    private Integer status;

    @Excel(name = "售前客服")
    private String connectUserName;

    @Excel(name = "对接客服")
    private String waiterName;

    @Excel(name = "会员状态", readConverterExp = "0=非会员,1=正常,2=即将过期,3=已过期")
    private Integer memberStatus;

    @Excel(name = "子账号数量")
    private Integer accountNum;

    @Excel(name = "订单总数")
    private Integer orderNum;

    @Excel(name = "近30天排单量")
    private Integer recentOrderNum;

    @Excel(name = "待完成量")
    private Integer preFinishOrderNum;

    @Excel(name = "是否代理", readConverterExp = "0=否,1=是")
    private Integer isProxy;

    @Excel(name = "重要程度", readConverterExp = "0=一般客户,1=重要客户")
    private Integer customerType;

    @Excel(name = "修改记录", height = 14)
    private String updateRemark;
}