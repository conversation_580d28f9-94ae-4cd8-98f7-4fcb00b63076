package com.ruoyi.system.api.domain.vo.biz.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/9 14:43
 */
@Data
public class ModelDataTableOrderScheduledTagCountVO implements Serializable {
    private static final long serialVersionUID = -5338509875621000110L;

    /**
     * 全部数量
     */
    @ApiModelProperty(value = "全部数量")
    private Long allCount;

    /**
     * 无异常数量
     */
    @ApiModelProperty(value = "无异常数量")
    private Long noExceptionCount;

    /**
     * 工单数量
     */
    @ApiModelProperty(value = "工单数量")
    private Long workOrderCount;

    /**
     * 售后单数量
     */
    @ApiModelProperty(value = "售后单数量")
    private Long afterSaleOrderCount;

    /**
     * 补偿订单数量
     */
    @ApiModelProperty(value = "补偿订单数量")
    private Long compensationOrderCount;

    /**
     * 取消订单数量
     */
    @ApiModelProperty(value = "取消订单数量")
    private Long cancelOrderCount;

    /**
     * 回退订单数量
     */
    @ApiModelProperty(value = "回退订单数量")
    private Long rollbackOrderCount;
}
