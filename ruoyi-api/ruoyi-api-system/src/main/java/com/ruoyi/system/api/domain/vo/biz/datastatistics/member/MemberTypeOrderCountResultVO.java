package com.ruoyi.system.api.domain.vo.biz.datastatistics.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会员不同类型订单数量Vo
 * @create :2025-06-20 11:08
 **/
@Data
public class MemberTypeOrderCountResultVO implements Serializable {
    private static final long serialVersionUID = -3627236139447557326L;

    @ApiModelProperty("会员排单数统计")
    private List<MemberTypeOrderCountVO> memberTypeOrderCountVOS;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
