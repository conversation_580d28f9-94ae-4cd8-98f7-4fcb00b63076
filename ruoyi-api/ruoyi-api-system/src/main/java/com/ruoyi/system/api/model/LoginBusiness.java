package com.ruoyi.system.api.model;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/6/20
 */
@Data
public class LoginBusiness extends LoginBaseEntity implements Serializable {

    private static final long serialVersionUID = 8166540425431756613L;

    public LoginBusiness() {
    }

    /**
     * 商家信息
     */
    private BusinessAccountVO businessAccountVO;

    public LoginBusiness(BusinessAccountVO businessAccountVO) {
        this.businessAccountVO = businessAccountVO;
        updateInfo();
    }


    public void updateInfo() {
        this.setUserType(UserTypeConstants.USER);
        this.setUserid(businessAccountVO.getId());
        //当登录账号名称为空的时候 使用微信名称代替
        this.setUsername(Optional.ofNullable(businessAccountVO.getName()).orElse(businessAccountVO.getNickName()));
        this.setBizUserId(businessAccountVO.getBizUserId());
    }
    public void setBaseEntity(LoginBaseEntity loginBaseEntity){
        this.setToken(loginBaseEntity.getToken());
        this.setLoginTime(loginBaseEntity.getLoginTime());
        this.setExpireTime(loginBaseEntity.getExpireTime());
        this.setIpaddr(loginBaseEntity.getIpaddr());
        this.setUserAgent(loginBaseEntity.getUserAgent());
    }
}
