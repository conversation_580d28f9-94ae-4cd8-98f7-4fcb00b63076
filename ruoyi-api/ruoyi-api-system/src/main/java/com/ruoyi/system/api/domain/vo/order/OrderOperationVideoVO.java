package com.ruoyi.system.api.domain.vo.order;

import com.ruoyi.common.core.domain.vo.TagVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel(value = "运营订单_视频对象VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderOperationVideoVO extends OrderVideoVO {
    private static final long serialVersionUID = 6387695186442748461L;
    /**
     * 上一单视频订单id
     */
    @ApiModelProperty(value = "上一单视频订单id")
    private Long prevVideoId;
    /**
     * 下一单视频订单id
     */
    @ApiModelProperty(value = "下一单视频订单id")
    private Long nextVideoId;

    /**
     * 产品品类
     */
    @ApiModelProperty(value = "产品品类")
    private List<TagVO> productCategory;
}
