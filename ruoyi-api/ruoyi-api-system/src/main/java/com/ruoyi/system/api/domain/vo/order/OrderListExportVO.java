package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderListExportVO implements Serializable {
    private static final long serialVersionUID = -1672850214581324127L;

    /**
     * 订单号
     */
    @Excel(name = "订单号", width = 31)
    private String orderNum;

    /**
     * 支付时间
     */
    @Excel(name = "支付时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN, width = 20)
    private Date payTime;

    /**
     * 视频合计费用
     */
    //@Excel(name = "视频费用合计(CNY)", defaultValue = "-")
    private BigDecimal videoAmountCount;

    @Excel(name = "视频费用合计(USD)", defaultValue = "-")
    private BigDecimal videoAmountCountUsd;

    /**
     * 税点费用（单位：￥）
     */
    //@Excel(name = "开票费用")
    private BigDecimal taxPointCost;

    /**
     * 佣金代缴税费
     */
    @Excel(name = "佣金代缴税费(USD)")
    private BigDecimal commissionPaysTaxes;

    /**
     * 支付金额
     */
    @Excel(name = "订单总价(CNY)")
    private BigDecimal payAmount;

    /**
     * 支付金额
     */
    @Excel(name = "优惠金额(CNY)", defaultValue = "-")
    private BigDecimal orderPromotionAmount;

    /**
     * 支付金额
     *//*
    @Excel(name = "订单总价(USD)")
    private BigDecimal payAmountUsd;*/

    private boolean isDefaultExchangeRate;

    /**
     * 子订单列表
     */
    private List<OrderVideoVO> orderVideoVOS;

    /**
     * 当前汇率
     */
    private BigDecimal currentExchangeRate;
}
