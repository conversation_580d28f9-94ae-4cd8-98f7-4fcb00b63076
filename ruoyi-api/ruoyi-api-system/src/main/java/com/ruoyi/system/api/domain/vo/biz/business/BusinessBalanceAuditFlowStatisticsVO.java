package com.ruoyi.system.api.domain.vo.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
* 余额提现审核表
* <AUTHOR>
 * @TableName business_balance_audit_flow
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessBalanceAuditFlowStatisticsVO implements Serializable {

    private static final long serialVersionUID = 5635342728504999981L;

    @ApiModelProperty("待处理提现数量")
    private Integer preApproveNum;

    @ApiModelProperty("已提现提现数量")
    private Integer approveNum;

    @ApiModelProperty("已取消提现数量")
    private Integer cancelNum;
}
