package com.ruoyi.system.api.domain.vo.order.finace;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.text.StrPool;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单收支明细
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@ApiModel(value = "订单对象VO")
@Data
public class OrderPayVideoDetailExportVO implements Serializable {
    private static final long serialVersionUID = 734687070967988311L;

    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @Excel(name = "订单号")
    private String orderNum;

    @Excel(name = "支付号")
    private String payNum;

    @Excel(name = "交易流水号")
    private String mchntOrderNo;

    // @Excel(name = "入账时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    // private Date recordTime;

    @Excel(name = "视频编码")
    private String videoCode;

    @Excel(name = "产品中文名")
    private String productChinese;

    @Excel(name = "产品英文名")
    private String productEnglish;

    @Excel(name = "照片数量", readConverterExp = "1=2张,2=5张", defaultValue = "-")
    private Integer picCount;


    @Excel(name = "拍摄模特")
    private String shootModelName;

    @Excel(name = "订单状态", readConverterExp = "1=待支付,2=待审核,3=待确认,4=待匹配,5=需发货,6=待完成,7=需确认,8=已完成,9=交易关闭")
    private Integer status;

    /**
     * 订单进入需确认的时间
     */
    @Excel(name = "首次反馈商家素材时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN)
    private Date needConfirmTime;

    /**
     * 最新提交模特时间
     */
    @Excel(name = "最新提交模特时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN)
    private Date lastModelSubmitTime;

    @Excel(name = "支付方式", readConverterExp = "1=微信,2=支付宝,3=云闪付/银联,4=数字人民币,5=银行,6=对公,7=全币种,10=余额,11=微信+余额,12=支付宝+余额,13=云闪付/银联+余额,14=数字人民币+余额,15=银行+余额,16=对公+余额,17=全币种+余额")
    private Integer payType;

    @Excel(name = "视频佣金（单位：$）")
    private BigDecimal videoPrice;

    @Excel(name = "照片佣金（单位：$）")
    private BigDecimal picPrice;

    @Excel(name = "佣金代缴税费（单位：$）")
    private BigDecimal commissionPaysTaxes;

    @Excel(name = "PayPal代付手续费（单位：$）")
    private BigDecimal exchangePrice;

    @Excel(name = "蜗牛服务费（单位：$）")
    private BigDecimal servicePrice;

    @Excel(name = "百度汇率")
    private BigDecimal currentExchangeRate;

    @Excel(name = "订单总价")
    private BigDecimal amount;

    // @Excel(name = "状态", readConverterExp = "0=未入账,1=已入账")
    // private Integer isRecord;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @Excel(name = "全币种支付类型", readConverterExp = "701=其他平台/银行,702=万里汇", defaultValue = StrPool.DASHED)
    private Integer payTypeDetail;
}
