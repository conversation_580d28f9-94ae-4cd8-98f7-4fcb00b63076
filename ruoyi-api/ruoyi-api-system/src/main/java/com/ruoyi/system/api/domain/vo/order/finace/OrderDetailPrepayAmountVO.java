package com.ruoyi.system.api.domain.vo.order.finace;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :订单收支明细视频金额详情
 * @create :2024-09-12 11:41
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderDetailPrepayAmountVO implements Serializable {
    private static final long serialVersionUID = -6811997525835798710L;

    @ApiModelProperty("预付金额（单位：￥）")
    private BigDecimal amount;

    @ApiModelProperty("预付款成功金额")
    private BigDecimal realAmount;

    @ApiModelProperty("应付金额")
    private BigDecimal payAmount;

    @ApiModelProperty("实际支付金额")
    private BigDecimal realPayAmount;

    @ApiModelProperty("订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    @ApiModelProperty("币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

    @ApiModelProperty(value = "当前汇率")
    private BigDecimal currentExchangeRate;
}
