package com.ruoyi.system.api.domain.vo.biz.business;

import com.ruoyi.system.api.domain.entity.biz.business.BusinessOwnerFlow;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :商家主账号换绑记录VO
 * @create :2025-01-16 14:27
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessOwnerFlowVO extends BusinessOwnerFlow implements Serializable {
    private static final long serialVersionUID = 1154392314715696659L;

    @ApiModelProperty("商家名称")
    private String businessName;

    @ApiModelProperty("会员编码")
    private String memberCode;
}
