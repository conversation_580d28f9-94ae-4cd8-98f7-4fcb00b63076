package com.ruoyi.system.api.domain.vo.order.finace;

import cn.hutool.core.text.StrPool;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 财务对账
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FinancialVerificationExportVO implements Serializable {

    private static final long serialVersionUID = -8080412922921954285L;

    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @Excel(name = "会员编码")
    private String merchantCode;

    @Excel(name = "订单号")
    private String orderNum;

    @Excel(name = "支付号")
    private String payNum;

    @Excel(name = "关联单号")
    private String relevanceNum;

    @Excel(name = "订单类型", readConverterExp = "0=视频订单,1=会员订单,3=线下钱包充值订单,5=线上钱包充值订单")
    private Integer orderType;

    private Long videoId;

    @Excel(name = "视频编码")
    private String videoCode;

    @Excel(name = "下单商家")
    private String businessName;

    @Excel(name = "付款账号", width = 45)
    private String payAccount;

    @Excel(name = "支付方式", readConverterExp = "1=微信,2=支付宝,3=云闪付/银联,4=数字人民币,5=银行,6=对公,7=全币种,10=余额,11=微信+余额,12=支付宝+余额,13=云闪付/银联+余额,14=数字人民币+余额,15=银行+余额,16=对公+余额,17=全币种+余额,998=境外汇款,999=其他,99=其他")
    private Integer payType;

    @Excel(name = "收款账号", width = 45, height = 42, defaultValue = StrPool.DASHED)
    private String bankAccount;

    @Excel(name = "视频费用（单位：$）")
    private BigDecimal videoPrice;

    @Excel(name = "选配费用（单位：$）")
    private BigDecimal picPrice;

    @Excel(name = "照片数量", readConverterExp = "1=2张,2=5张")
    private Integer picCount;

    @Excel(name = "PayPal代付手续费（单位：$）")
    private BigDecimal exchangePrice;

    @Excel(name = "佣金代缴税费（单位：$）")
    private BigDecimal commissionPaysTaxes;

    @Excel(name = "服务费（单位：$）")
    private BigDecimal servicePrice;

    @Excel(name = "百度汇率")
    private BigDecimal currentExchangeRate;

    @Excel(name = "订单总价")
    private BigDecimal amount;

    private BigDecimal orderAmount;

    @Excel(name = "订单合计")
    private BigDecimal payAmount;

    @Excel(name = "限时满减活动", defaultValue = StrPool.DASHED)
    private BigDecimal fullConcession;

    /**
     * 每月首单立减优惠方案
     */
    @Excel(name = "每月首单立减优惠方案", defaultValue = StrPool.DASHED)
    private String monthFirstOrderDiscount;

    /**
     * 每月首单立减金额
     */
    @Excel(name = "每月首单立减金额", defaultValue = StrPool.DASHED)
    private BigDecimal monthFirstOrderDiscountActivity;
//
//    @Excel(name = "优惠金额", defaultValue = StrPool.DASHED)
//    private String orderPromotionAmountString;

    @Excel(name = "余额支付")
    private BigDecimal useBalance;

    @Excel(name = "剩余应支付")
    private BigDecimal surplusAmount;

    // @Excel(name = "实际支付")
    private BigDecimal realPayAmount;

    @Excel(name = "实付金额", defaultValue = StrPool.DASHED)
    private BigDecimal realPayAmountCurrency;

    private Integer currency;

    @Excel(name = "支付币种", defaultValue = StrPool.DASHED)
    private String currencyString;

//    @Excel(name = "赠送/差额")
    private BigDecimal differenceAmount;

    @Excel(name = "赠送/差额", defaultValue = StrPool.DASHED)
    private String differenceAmountString;
    /**
     * 处理
     */
    @Excel(name = "审核备注")
    private String auditRemark;

    @ApiModelProperty(value = "商家id")
    private Long merchantId;



}
