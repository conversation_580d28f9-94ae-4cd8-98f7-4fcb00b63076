package com.ruoyi.system.api.domain.vo.biz.channel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :裂变渠道数据
 * @create :2025-01-14 17:30
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FissionChannelVO extends DistributionChannelVO implements Serializable {
    private static final long serialVersionUID = 9081057205871920912L;

    @ApiModelProperty("登录账号id")
    private Long bizUserId;

    @ApiModelProperty("商家名称")
    private String businessName;

    @ApiModelProperty("账号类型：0-普通账号，1-主账号，2-子账号")
    private Integer accountType;

    @ApiModelProperty("微信昵称")
    private String nickName;

    @ApiModelProperty("账号名称")
    private String accountName;

    @ApiModelProperty("会员编码")
    private String memberCode;

    @ApiModelProperty("种草码状态: 0=正常,1=禁用")
    private Integer seedCodeStatus;

    @ApiModelProperty("失效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date failureTime;

    @ApiModelProperty("会员状态：0-非会员1-正常，2-即将过期，3-已过期")
    private Integer memberStatus;

    @ApiModelProperty("专属链接")
    private String dedicatedLink;

    @ApiModelProperty("专属企微二维码地址")
    private String weChatUrl;
}
