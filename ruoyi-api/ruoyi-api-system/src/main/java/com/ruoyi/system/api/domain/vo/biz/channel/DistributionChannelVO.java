package com.ruoyi.system.api.domain.vo.biz.channel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 分销渠道信息表
 *
 * <AUTHOR>
 * @TableName distribution_channel
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "分销渠道信息列表")
public class DistributionChannelVO implements Serializable {

    private static final long serialVersionUID = 2030441508344258076L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("渠道名称")
    private String channelName;

    /**
     * 海报名称
     */
    @ApiModelProperty("海报名称")
    private String posterName;

    @ApiModelProperty("种草码")
    private String seedCode;

    @ApiModelProperty("种草官ID")
    private String seedId;

    @ApiModelProperty("独立访客")
    private Long uniqueVisitor;

    @ApiModelProperty("渠道关联bizUserId")
    private Long bizUserId;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("渠道类型：2-分销渠道 7-裂变")
    private Integer channelType;

    @ApiModelProperty("渠道账号状态：1-正常 2-已过期 3-已解绑")
    private Integer channelAccountType;

    @ApiModelProperty("邀请企微数")
    private Long addWeChatNum;

    @ApiModelProperty("邀请注册数")
    private Long registerNum;

    @ApiModelProperty("单个渠道的会员订单成交金额")
    private BigDecimal memberOrderAmount;

    @ApiModelProperty("会员成交数")
    private Integer memberNum;

    @ApiModelProperty("结算佣金类型（1-固定金额，2-固定比例）")
    private Integer settleDiscountType;

    @ApiModelProperty("结算比例")
    private BigDecimal brokeRage;

    @ApiModelProperty("订单成交额")
    private BigDecimal realPayAmount;

    @ApiModelProperty("待结算金额")
    private BigDecimal unSettleAmount;

    @ApiModelProperty("已结算金额")
    private BigDecimal settleAmount;

    @ApiModelProperty("实际已结算金额")
    private BigDecimal realSettleAmount;

    @ApiModelProperty("分销状态（0=正常,1=禁用）")
    private Integer status;

    @ApiModelProperty("禁用时间/启用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date disableTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("参与时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


}
