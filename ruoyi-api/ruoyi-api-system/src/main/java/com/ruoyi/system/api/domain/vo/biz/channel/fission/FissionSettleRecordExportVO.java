package com.ruoyi.system.api.domain.vo.biz.channel.fission;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.enums.WithdrawTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :财务管理-裂变明细导出
 * @create :2025-05-22 11:35
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FissionSettleRecordExportVO implements Serializable {
    private static final long serialVersionUID = 7299916924599622129L;
    @ApiModelProperty("主键")
    private Long id;

    @Excel(name = "提现单号")
    private String withdrawalNum;

    @ApiModelProperty("申请人微信名")
    private String applicantNickName;

    @ApiModelProperty("申请人员工名称")
    private String applicantName;

    @ApiModelProperty("申请人商家名称")
    private String applicantBusinessName;

    @ApiModelProperty("申请人会员编码")
    private String applicantMemberCode;

    @Excel(name = "种草官信息")
    private String applyInfo;

    @Excel(name = "种草官ID")
    private String channelSeedId;

    private Long channelId;

    @Excel(name = "包含订单")
    private String orderNum;

    @Excel(name = "结算来源", readConverterExp = "2=分销渠道,7=裂变渠道")
    private Integer channelType;

    @Excel(name = "结算方案", readConverterExp = "1=固定金额,2=固定比例")
    private Integer settleType;

    @ApiModelProperty("方案数据")
    private BigDecimal settleRage;

    @Excel(name = "方案数据")
    private String settleRageStr;

    @Excel(name = "结算金额")
    private BigDecimal orderSettleAmount;

    @Excel(name = "申请时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN, width = 19, height = 144, defaultValue = StrPool.DASHED)
    private Date createTime;

    @Excel(name = "收款方式", readConverterExp = "2=支付宝,3=银行卡,6=公户收款")
    private Integer withdrawalAccountType;


    @ApiModelProperty(name = "收款方姓名")
    private String payeeName;

    @ApiModelProperty(name = "收款方身份证号")
    private String payeeIdentityCard;

    @ApiModelProperty("收款方账号")
    private String payeeAccount;

    @Excel(name = "收款方姓名", defaultValue = StrPool.DASHED)
    private String bankPayeeName;

    @Excel(name = "收款方手机号", defaultValue = StrPool.DASHED)
    private String payeePhone;

    @Excel(name = "收款方身份证号", defaultValue = StrPool.DASHED)
    private String bankPayeeIdentityCard;

    @Excel(name = "支付宝账号", defaultValue = StrPool.DASHED)
    private String alipayAccount;

    @Excel(name = "银行卡号", defaultValue = StrPool.DASHED)
    private String bankAccount;

    @Excel(name = "收款公司名称", defaultValue = StrPool.DASHED)
    private String publicPayeeName;

    @Excel(name = "收款银行账号", defaultValue = StrPool.DASHED)
    private String publicAccount;

    @Excel(name = "开户行名称", defaultValue = StrPool.DASHED)
    private String publicBankName;

    @ApiModelProperty("提现状态")
    private Integer status;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    @ApiModelProperty("审核备注")
    private String auditRemark;

    @ApiModelProperty("审核人员id FK sys_user.user_id")
    private Long auditUserId;

    @ApiModelProperty("审核人员名称")
    private String auditUserName;

    @Excel(name = "打款账号", defaultValue = StrPool.DASHED)
    private String payAccount;

    @Excel(name = "打款时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN, width = 19, height = 144, defaultValue = StrPool.DASHED)
    private Date payoutTime;

    @Excel(name = "结算时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN, width = 19, height = 144, defaultValue = StrPool.DASHED)
    private Date withdrawalTime;

    @ApiModelProperty("提现备注")
    private String withdrawalRemark;

    @ApiModelProperty("提现人员id FK sys_user.user_id")
    private Long withdrawalUserId;

    @ApiModelProperty("提现人员名称")
    private String withdrawalUserName;

    public void setPayeeName(String payeeName) {
        if (StrUtil.isBlank(payeeName)) {
            return;
        }
        this.payeeName = payeeName;
    }

    public void setPayeePhone(String payeePhone) {
        if (StrUtil.isBlank(payeePhone)) {
            return;
        }
        this.payeePhone = payeePhone;
    }

    public void setPayeeIdentityCard(String payeeIdentityCard) {
        if (StrUtil.isBlank(payeeIdentityCard)) {
            return;
        }
        this.payeeIdentityCard = payeeIdentityCard;
    }

    public void setPayAccount(String payAccount) {
        if (StrUtil.isBlank(payAccount)) {
            return;
        }
        this.payAccount = payAccount;
    }

    public void setWithdrawalAccountType(Integer withdrawalAccountType) {
        if (ObjectUtil.isNull(withdrawalAccountType) || !List.of(WithdrawTypeEnum.ALIPAY.getCode(), WithdrawTypeEnum.BANK.getCode()).contains(withdrawalAccountType)) {
            return;
        }
        this.withdrawalAccountType = withdrawalAccountType;
    }
}
