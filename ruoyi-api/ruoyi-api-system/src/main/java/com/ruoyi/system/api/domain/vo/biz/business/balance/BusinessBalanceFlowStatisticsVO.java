package com.ruoyi.system.api.domain.vo.biz.business.balance;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :余额流水统计
 * @create :2024-12-30 15:39
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalanceFlowStatisticsVO implements Serializable {
    private static final long serialVersionUID = -1201938973269973694L;

    @ApiModelProperty("商家ID")
    private Long businessId;

    @ApiModelProperty("钱包充值合计")
    private BigDecimal prepayAmountTotal;

    @ApiModelProperty("-余额支付")
    private BigDecimal balancePayTotal;

    @ApiModelProperty("-提现合计")
    private BigDecimal withdrawTotal;

    @ApiModelProperty("退款统计")
    private BigDecimal payoutTotal;
}
