package com.ruoyi.system.api.domain.vo.order.logistic;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.vo.LogisticInfoVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 物流跟进表
 *
 * <AUTHOR>
 * @TableName order_video_logistic_follow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderVideoLogisticFollowVO implements Serializable {

    private static final long serialVersionUID = 6778532497701832488L;

    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[物流关联表id]不能为空")
    @ApiModelProperty("物流关联表id order_video_logistic.id")
    private Long orderVideoLogisticId;

    @NotNull(message = "[商家id（business.id）]不能为空")
    @ApiModelProperty("商家id（business.id）")
    private Long businessId;

    @NotBlank(message = "[会员编码（business.member_code）]不能为空")
    @Size(max = 10, message = "编码长度不能超过10")
    @ApiModelProperty("会员编码（business.member_code）")
    @Length(max = 10, message = "编码长度不能超过10")
    private String memberCode;

    @NotBlank(message = "[视频编码]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("视频编码")
    @Length(max = 30, message = "编码长度不能超过30")
    private String videoCode;

    @NotNull(message = "[视频id FK:order_video.id]不能为空")
    @ApiModelProperty("视频id FK:order_video.id")
    private Long videoId;

    @Size(max = 100, message = "编码长度不能超过100")
    @ApiModelProperty("物流单号")
    @Length(max = 100, message = "编码长度不能超过100")
    private String number;

    @NotNull(message = "[处理状态枚举]不能为空")
    @ApiModelProperty("处理状态枚举(0-未通知、1-已通知、2-延迟发货、3-延迟发货提醒、4-延迟发货已提醒、5-催发货提醒、6-催发货已提醒、7-地址变更通知、8-变更已通知、9-补充说明、10-标记发货、11-标记发货提醒、12-标记发货已提醒、13-通知确认模特、14-已通知确认模特、15-催确认模特提醒、16-已通知催确认模特)")
    private Integer handleStatus;

    @NotNull(message = "[物流状态(0-未发货、1-已发货)]不能为空")
    @ApiModelProperty("物流状态(0-未发货、1-已发货)")
    private Integer logisticStatus;

    @ApiModelProperty("跟进状态码(1-需处理、2-暂不处理、10-已发货、11-需跟进、12-模特待确认、13-无需跟进、14-已结束)")
    private Integer followStatus;

    @ApiModelProperty("通知时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date notifyTime;

    @ApiModelProperty("发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date logisticStartTime;

    @ApiModelProperty("是否默认发货时间：0-否，1-是")
    private Integer isDefaultLogisticStartTime;

    @ApiModelProperty("最新物流主状态")
    private String latestMainStatus;

    @ApiModelProperty("最新物流主状态")
    private String latestMainStatusSketch;

    @ApiModelProperty("物流系统同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date logisticUpdateTime;

    @ApiModelProperty("是否回调（0-手动 1-系统）")
    private Integer isCallBack;

    @ApiModelProperty("模特结果枚举(0-待处理、1-已询问、2-已收货、3-丢件、4-订单回退、5-待通知拍摄、6-已通知拍摄)")
    private Integer modelResult;

    @ApiModelProperty("实际签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;

    @Size(max = 300, message = "[最新处理说明]不能超过300")
    @ApiModelProperty("最新处理说明")
    @Length(max = 300, message = "[最新处理说明]不能超过300")
    private String latestRemark;

    @ApiModelProperty("最终处理图片")
    private String latestResourceId;

    @ApiModelProperty("最终处理图片")
    private List<String> resources;

    @ApiModelProperty("产品中文名")
    private String productChinese;

    @ApiModelProperty("产品英文名")
    private String productEnglish;

    @ApiModelProperty("产品图")
    private String productPic;

    @ApiModelProperty(value = "产品链接")
    private String productLink;

    @ApiModelProperty("拍摄模特Id")
    private Long shootModelId;

    @ApiModelProperty(value = "拍摄模特")
    private ModelOrderSimpleVO shootModel;

    @ApiModelProperty(value = "物流信息详情")
    private List<LogisticInfoVO> logisticInfo;

    @ApiModelProperty("中文部客服Id")
    private Long contactId;

    @ApiModelProperty(value = "中文部客服")
    private UserVO contact;


    @ApiModelProperty(value = "英文部id")
    private Long issueId;

    @ApiModelProperty(value = "英文部")
    private UserVO issue;

    @ApiModelProperty(value = "订单运营用户名")
    private String createOrderUserName;

    @ApiModelProperty("订单运营微信名")
    private String createOrderUserNickName;

    @ApiModelProperty("下单运营用户名")
    private String createOrderOperationUserName;

    @ApiModelProperty("下单运营微信名")
    private String createOrderOperationUserNickName;

    @ApiModelProperty(value = "平台类型：0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private Integer platform;

    @ApiModelProperty("订单状态")
    private Integer videoStatus;


    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("创建人姓名")
    @Length(max = 32, message = "编码长度不能超过32")
    private String createBy;

    @ApiModelProperty("创建人ID")
    private Long createById;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("更新人姓名")
    @Length(max = 32, message = "编码长度不能超过32")
    private String updateBy;

    @ApiModelProperty("更新人ID")
    private Long updateById;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    private String closeProductChinese;
    private String closeProductEnglish;
    private String closeProductPic;
    private Long closeShootModelId;
    private Long closeContactId;
    private Long closeIssueId;
    private String closeProductLink;
    private String closeOrderCreator;
    private String closeOrderCreatorNick;
    private String closeOrderOperator;
    private String closeOrderOperatorNick;
    private Integer closePlatform;

    public void init() {
        if (ObjectUtil.isNotNull(this.closeShootModelId)) {
            this.shootModelId = this.closeShootModelId;
        }
        if (ObjectUtil.isNotNull(this.closeContactId)) {
            this.contactId = this.closeContactId;
        }
        if (ObjectUtil.isNotNull(this.closeIssueId)) {
            this.issueId = this.closeIssueId;
        }
        if (ObjectUtil.isNotNull(this.closePlatform)) {
            this.platform = this.closePlatform;
        }
        if (StrUtil.isNotBlank(this.closeProductChinese)) {
            this.productChinese = this.closeProductChinese;
        }
        if (StrUtil.isNotBlank(this.closeProductEnglish)) {
            this.productEnglish = this.closeProductEnglish;
        }
        if (StrUtil.isNotBlank(this.closeProductPic)) {
            this.productPic = this.closeProductPic;
        }
        if (StrUtil.isNotBlank(this.closeProductLink)) {
            this.productLink = this.closeProductLink;
        }
        if (StrUtil.isNotBlank(this.closeOrderCreator)) {
            this.createOrderUserName = this.closeOrderCreator;
        }
        if (StrUtil.isNotBlank(this.closeOrderCreatorNick)) {
            this.createOrderUserNickName = this.closeOrderCreatorNick;
        }
        if (StrUtil.isNotBlank(this.closeOrderOperator)) {
            this.createOrderOperationUserName = this.closeOrderOperator;
        }
        if (StrUtil.isNotBlank(this.closeOrderOperatorNick)) {
            this.createOrderOperationUserNickName = this.closeOrderOperatorNick;
        }
    }


}
