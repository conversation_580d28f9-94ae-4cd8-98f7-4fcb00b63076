package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/2 9:38
 */
@Data
public class OrderModelListVO implements Serializable {
    private static final long serialVersionUID = -2818396138154373204L;


    /**
     * 视频订单id
     */
    @ApiModelProperty(value = "视频订单id")
    private Long id;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private Integer platform;

    /**
     * 视频时长
     */
    @ApiModelProperty(value = "视频时长")
    private Integer videoDuration;

    /**
     * 视频风格
     */
    @ApiModelProperty(value = "视频风格(0:Amazon,1:tiktok,2:APP/解说类)")
    private Integer videoStyle;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）")
    @JsonIgnore
    private Integer picCount;

    /**
     * 退款照片数量
     */
    @ApiModelProperty(value = "退款照片数量")
    @JsonIgnore
    private Integer refundPicCount;

    /**
     * 剩余退款照片数量
     */
    @ApiModelProperty(value = "剩余退款照片数量")
    private Integer surplusPicCount;

    @ApiModelProperty(value = "订单创建人ID")
    private Long createOrderBizUserId;
}
