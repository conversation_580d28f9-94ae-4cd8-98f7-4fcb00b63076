package com.ruoyi.system.api.domain.vo.biz.business.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-29 09:48
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BizUserDetailVO implements Serializable {
    private static final long serialVersionUID = 4382092324190032542L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("商家id")
    private Long businessId;

    @ApiModelProperty("员工名称")
    private String name;

//    @NotBlank(message="[微信昵称]不能为空")
    private String nickName;

    
    private String pic;

    @NotBlank(message="[手机号]不能为空")
    private String phone;

    @ApiModelProperty("是否为代理(0:否,1:是)")
    private Integer isProxy;

    @ApiModelProperty("客户类型 （0-一般客户 1-重要客户）")
    private Integer customerType;

    @ApiModelProperty("商家名称")
    private String businessName;

    @ApiModelProperty("对接客服")
    private Long waiterId;

    @ApiModelProperty("账号状态（0正常 1禁用 2被删除）")
    private Integer status;

    @ApiModelProperty("当前账号")
    private String account;

    @ApiModelProperty("当前账号名称")
    private String accountName;

    @ApiModelProperty("主账号")
    private String ownerAccount;

    @ApiModelProperty("当前账号商家名称")
    private String accountBusinessName;

    @ApiModelProperty("会员编码")
    private String memberCode;

    @ApiModelProperty(value = "用户注册渠道")
    private Integer registerChannelType;

    @ApiModelProperty(value = "企微渠道类型(0=普通,1=市场，2=分销)")
    private Integer wechatChannelType;

    @ApiModelProperty(value = "添加企微渠道id")
    private Long wechatChannelId;

    @ApiModelProperty(value = "商务经理名称")
    private String connectUserName;


    @ApiModelProperty(value = "用户注册的渠道")
    private String channelName;

    @ApiModelProperty(value = "添加企微时间")
    private Date addWechatTime;
}
