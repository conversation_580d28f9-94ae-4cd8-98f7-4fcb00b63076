package com.ruoyi.system.api.domain.vo.biz.datastatistics.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会员充值基础数据
 * @create :2025-06-17 11:56
 **/
@Data
public class MemberRechargeCountVO implements Serializable {
    private static final long serialVersionUID = -2158442784301959311L;

    @ApiModelProperty(value = "服务中会员数")
    private Long memberCount;

    @ApiModelProperty(value = "昨日新会员数")
    private Long yesterdayMemberCount;

    @ApiModelProperty(value = "昨日续费会员数")
    private Long yesterdayRenewMemberCount;

    @ApiModelProperty(value = "昨日退会数")
    private Long yesterdayExitMemberCount;


    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public static MemberRechargeCountVO init(){
        MemberRechargeCountVO rechargeCountVO = new MemberRechargeCountVO();
        rechargeCountVO.setMemberCount(0L);
        rechargeCountVO.setYesterdayMemberCount(0L);
        rechargeCountVO.setYesterdayRenewMemberCount(0L);
        rechargeCountVO.setYesterdayExitMemberCount(0L);
        rechargeCountVO.setUpdateTime(new Date());
        return rechargeCountVO;
    }
}
