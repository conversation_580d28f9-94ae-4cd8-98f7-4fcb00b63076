package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.UserVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
public class OrderTaskFlowRecordVO implements Serializable {
    private static final long serialVersionUID = 8969811499841526592L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String taskNum;

    /**
     * 记录时间
     */
    @ApiModelProperty(value = "记录时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date time;

    /**
     * 操作人ID
     */
    @JsonIgnore
    private Long operateById;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private UserVO operate;

    /**
     * 操作人类型（1：处理人，2：剪辑人，3：系统）
     */
    @ApiModelProperty(value = "操作人类型（1：处理人，2：剪辑人，3：系统）")
    private Integer operateByType;

    /**
     * 操作类型 详见OrderTaskDetailFlowOperateTypeEnum
     *
     * @see com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum
     */
    @ApiModelProperty(value = "操作类型")
    private Integer operateType;

    /**
     * 被指派人ID
     */
    @JsonIgnore
    private Long appointeeId;

    /**
     * 被指派人
     */
    @ApiModelProperty(value = "被指派人")
    private UserVO appointee;

    /**
     * 完结方式（1：主动完结，2：补发，3：补偿，4：反馈素材给商家，5：模特反馈素材）
     */
    @ApiModelProperty(value = "完结方式（1：主动完结，2：补发，3：补偿，4：反馈素材给商家，5：模特反馈素材）")
    private Integer completionMode;

    /**
     * 问题图片（FK:order_resource.id）
     */
    @ApiModelProperty(value = "问题图片")
    @JsonIgnore
    private String issuePicId;

    /**
     * 问题图片
     */
    @ApiModelProperty(value = "问题图片")
    private List<String> issuePic = new ArrayList<>();

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
