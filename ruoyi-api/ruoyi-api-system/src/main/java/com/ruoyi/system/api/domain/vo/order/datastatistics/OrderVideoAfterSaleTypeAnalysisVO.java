package com.ruoyi.system.api.domain.vo.order.datastatistics;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 14:02
 */
@Data
public class OrderVideoAfterSaleTypeAnalysisVO implements Serializable {
    private static final long serialVersionUID = -4966927458005169113L;

    /**
     * 记录截止时间
     */
    @ApiModelProperty("记录截止时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date writeTimeEnd;

    /**
     * 售后类型分析VOS
     */
    @ApiModelProperty("售后类型分析VOS")
    private List<OrderVideoAfterSaleTypeAnalysisDetailVO> orderVideoAfterSaleTypeAnalysisDetailVOS;

    public OrderVideoAfterSaleTypeAnalysisVO echo() {
        for (OrderVideoAfterSaleTypeAnalysisDetailVO orderVideoAfterSaleTypeAnalysisDetailVO : orderVideoAfterSaleTypeAnalysisDetailVOS) {
            orderVideoAfterSaleTypeAnalysisDetailVO.echo();
        }
        return this;
    }
}
