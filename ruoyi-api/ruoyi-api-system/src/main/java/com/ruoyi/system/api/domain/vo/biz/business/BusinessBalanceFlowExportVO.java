package com.ruoyi.system.api.domain.vo.biz.business;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 余额流水表
 *
 * <AUTHOR>
 * @TableName business_balance_flow
 */
@Data
public class BusinessBalanceFlowExportVO implements Serializable {
    private static final long serialVersionUID = -3206162264681086313L;

    @Excel(name = "交易时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    @Excel(name ="收支类型", readConverterExp = "0=收入,1=支出")
    private Integer type;

    @Excel(name ="金额（元）")
    private BigDecimal amount;

    @Excel(name ="收支来源", readConverterExp = "1=补偿订单收入,2=取消订单收入,3=取消选配收入,4=视频订单支出,5=会员订单支出,6=线下余额提现,7=线下钱包充值收入,8=线上钱包充值收入")
    private Integer origin;

    @Excel(name ="余额（元）")
    private BigDecimal balance;

    @Excel(name ="视频编号")
    private String videoCode;

    @Excel(name ="订单编号")
    private String orderNum;

    @Excel(name ="退款审批号")
    private String refundNum;

    @ApiModelProperty("预付单号")
    private String prepayNum;

    public void setPrepayNum(String prepayNum) {
        this.prepayNum = prepayNum;
        if (StrUtil.isBlank(orderNum)){
            this.orderNum = prepayNum;
        }
    }
}
