package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.prepay.OrderPayeeAccountVO;
import com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单对象VO
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Data
public class OrderVO implements Serializable {
    private static final long serialVersionUID = 734687070967988311L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    @ApiModelProperty(value = "支付单号")
    private String payNum;

    /**
     * 视频数量
     */
    @ApiModelProperty(value = "视频数量")
    private Integer videoCount;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "订单金额（单位：$）")
    private BigDecimal orderAmountDollar;
    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "支付金额（单位：$）")
    private BigDecimal payAmountDollar;

    @ApiModelProperty(value = "当前汇率")
    private BigDecimal currentExchangeRate;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal realPayAmount;

    @ApiModelProperty(value = "订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    @ApiModelProperty(value = "差额")
    private BigDecimal differenceAmount;

    @ApiModelProperty(value = "订单活动优惠总额")
    private BigDecimal orderPromotionAmount;

    @ApiModelProperty("套餐价格")
    private BigDecimal packageAmount;

    @ApiModelProperty("加赠时间")
    private Integer presentedTime;

    @ApiModelProperty("加赠时间类型（1-天,2-月,3-年）")
    private Integer presentedTimeType;

    /**
     * 使用余额
     */
    @ApiModelProperty(value = "使用余额")
    private BigDecimal useBalance;

    @ApiModelProperty(value = "剩余支付金额")
    private BigDecimal surplusAmount;

    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    /** 订单超时时间 */
    @ApiModelProperty(value = "订单超时时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date overTime;

    /**
     * 支付用户id
     */
    @ApiModelProperty(value = "支付用户id")
    private Long payUserId;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 支付账户（对公）
     */
    @ApiModelProperty(value = "支付账户（对公）")
    @Excel(name = "支付账户（对公）")
    private String payAccount;

    /**
     * 财务审核状态（0:待审核,1:审核通过,2.审核异常,3.已关闭
     */
    @ApiModelProperty(value = "财务审核状态（0:待审核,1:审核通过,2.审核异常,3.已关闭")
    @Excel(name = "财务审核状态")
    private Integer auditStatus;

    /**
     * 支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额,11:微信+余额,12:支付宝+余额,13:云闪付/银联+余额,14.数字人民币+余额,15.银行+余额,16:对公+余额,17:全币种+余额）
     */
    @ApiModelProperty(value = "支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额,11:微信+余额,12:支付宝+余额,13:云闪付/银联+余额,14.数字人民币+余额,15.银行+余额,16:对公+余额,17:全币种+余额）")
    @Excel(name = "支付方式")
    private Integer payType;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private Integer payTypeDetail;

    @ApiModelProperty(value = "币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

    @ApiModelProperty(value = "订单备注")
    @Excel(name = "订单备注")
    private String orderRemark;

    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @ApiModelProperty(value = "渠道类型")
    private Integer channelType;

    @ApiModelProperty(value = "种草码")
    private String seedCode;

    @ApiModelProperty(value = "种草官ID")
    private String seedId;

    @ApiModelProperty(value = "会员结算类型（1-固定金额，2-固定比例）")
    private Integer memberDiscountType;

    @ApiModelProperty(value = "结算比例")
    private BigDecimal settleRage;

    private List<OrderDocumentResourceVO>  resourceVos;

    @ApiModelProperty(value = "商家凭证")
    private List<OrderDocumentResourceVO> businessResourceVos;

    @ApiModelProperty("收款账号")
    private OrderPayeeAccountVO orderPayeeAccountVO;

    @ApiModelProperty("视频订单列表")
    private List<OrderVideoVO> orderVideoReceivableAuditDetails;

    @ApiModelProperty("审核信息流水")
    private List<OrderAuditFlowVO> orderAuditFlows;

    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer memberPackageType;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @ApiModelProperty(value = "种草码优惠金额（单位：￥）")
    private BigDecimal seedCodeDiscount;

    /**
     * 下单用户id
     */
    @ApiModelProperty(value = "下单用户id")
    @JsonIgnore
    private Long orderUserId;

    /**
     * 商家信息
     */
    @ApiModelProperty(value = "下单用户")
    private BusinessAccountDetailVO orderUser;


    @ApiModelProperty(value = "提交审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitCredentialTime;

    @ApiModelProperty(value = "合并订单信息")
    private List<Order> mergeOrderList;

    @ApiModelProperty(value = "合并支付金额")
    private BigDecimal mergePayAmount;

    @ApiModelProperty(value = "合并支付金额（单位：$）")
    private BigDecimal mergePayAmountDollar;

    @ApiModelProperty(value = "合并剩余（单位：$）")
    private BigDecimal mergeSurplusAmount;

    @ApiModelProperty(value = "合并使用余额（单位：$）")
    private BigDecimal mergeUseBalance;

    @ApiModelProperty(value = "合并实际支付（单位：$）")
    private BigDecimal mergeRealPayAmount;

    @ApiModelProperty(value = "合并订单实付金额（对应币种实付）")
    private BigDecimal mergeRealPayAmountCurrency;

    @ApiModelProperty(value = "合并优惠金额（单位：$）")
    private BigDecimal mergeOrderPromotionAmount;

    @ApiModelProperty(value = "是否合并订单(0-否，1-是)")
    private Integer isMergeOrder;

    /**
     * 订单参与的活动以及对应的优惠
     */
    @ApiModelProperty(value = "订单参与的活动以及对应的优惠")
    private List<OrderDiscountDetailVO> orderDiscountDetailVOS;

}
