package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/24 11:47
 */
@Data
public class HistoryUploadRecordVO implements Serializable {
    private static final long serialVersionUID = 8224146578429046015L;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 提交信息对象（1:商家,2:运营）
     */
    @ApiModelProperty(value = "提交信息对象（1:商家,2:运营）")
    private Integer object;

    /**
     * 提交信息用户id
     */
    @ApiModelProperty(value = "提交信息用户id")
    @JsonIgnore
    private Long userId;

    /**
     * 提交信息用户名称
     */
    @ApiModelProperty(value = "提交信息用户名称")
    private String userName;

    /**
     * 提交信息时间
     */
    @ApiModelProperty(value = "提交信息时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date time;

    /**
     * 上传次数
     */
    @ApiModelProperty(value = "上传次数")
    private Integer count;

    /**
     * 需要上传的链接
     */
    @ApiModelProperty(value = "需要上传的链接")
    private String needUploadLink;

    /**
     * 视频标题
     */
    @ApiModelProperty(value = "视频标题")
    private String videoTitle;

    /**
     * 上传账号
     */
    @ApiModelProperty(value = "上传账号")
    private String uploadAccount;

    /**
     * 视频封面图URI
     */
    @ApiModelProperty(value = "视频封面图URI")
    private String videoCover;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 上传的链接
     */
    @ApiModelProperty(value = "最终上传的链接")
    private String uploadLink;

    /**
     * 上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传，4：上传失败）
     */
    @ApiModelProperty(value = "上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传，4：上传失败）")
    private Integer status;

    /**
     * 上传用户id
     */
    @ApiModelProperty(value = "上传用户id")
    @JsonIgnore
    private Long uploadUserId;

    /**
     * 上传用户名称
     */
    @ApiModelProperty(value = "上传用户名称")
    private String uploadUserName;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date uploadTime;

    /**
     * 操作备注
     */
    @ApiModelProperty(value = "操作备注")
    private String operateRemark;
}
