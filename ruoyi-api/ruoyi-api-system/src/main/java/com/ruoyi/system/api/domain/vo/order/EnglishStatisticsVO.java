package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-04-01 09:51
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EnglishStatisticsVO implements Serializable {

    private static final long serialVersionUID = -7544187893810807755L;

    @ApiModelProperty(value = "英文部：待沟通订单")
    private Integer unContactTotalCount;

    @ApiModelProperty(value = "英文部：未沟通")
    private Integer unContactCount;

    @ApiModelProperty(value = "英文部：沟通中")
    private Integer contactingCount;
}
