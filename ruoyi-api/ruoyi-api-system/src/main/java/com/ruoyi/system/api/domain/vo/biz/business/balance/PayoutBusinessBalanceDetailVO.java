package com.ruoyi.system.api.domain.vo.biz.business.balance;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家余额详情表
 *
 * <AUTHOR>
 * @TableName business_balance_detail
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PayoutBusinessBalanceDetailVO implements Serializable {

    private static final long serialVersionUID = -4387545116525571523L;
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("商家id（business.id）")
    private Long businessId;

    @ApiModelProperty("单号（自动生成：TK、YF前缀）")
    private String number;

    @ApiModelProperty("视频编码")
    private String videoCode;

    @ApiModelProperty("下单运营")
    private String createOrderUserName;

    @ApiModelProperty("类型(1-补偿订单收入、2-取消订单收入、3-取消选配收入、7.线下钱包充值收入、8.线上钱包充值收入)")
    private Integer origin;

    @ApiModelProperty("余额增加时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("已用金额")
    private BigDecimal useBalance;

    @ApiModelProperty("可提现金额")
    private BigDecimal validBalance;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "包含赠送金额")
    private BigDecimal containPresentedAmount;

}
