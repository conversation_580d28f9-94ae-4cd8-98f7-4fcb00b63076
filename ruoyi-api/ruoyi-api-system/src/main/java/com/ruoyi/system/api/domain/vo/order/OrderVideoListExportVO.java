package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.text.StrPool;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class OrderVideoListExportVO implements Serializable {
    private static final long serialVersionUID = -3645725038811516315L;

    /**
     * 订单号
     */
    @Excel(name = "订单号", width = 31)
    private String orderNum;

    /**
     * 视频编码
     */
    @Excel(name = "视频编码")
    private String videoCode;

    /**
     * 产品中文名
     */
    @Excel(name = "中文名称")
    private String productChinese;

    /**
     * 产品英文名
     */
    @Excel(name = "英文名称")
    private String productEnglish;

    /**
     * 产品链接
     */
    @Excel(name = "产品链接")
    private String productLink;

    /**
     * 创建订单用户名称
     */
    @Excel(name = "订单运营")
    private String createOrderUserName;

    /**
     * 拍摄模特
     */
    @Excel(name = "拍摄模特")
    private String shootModelName;

    /**
     * 订单状态
     */
    @Excel(name = "订单状态", readConverterExp = "1=待支付,2=待审核,3=待匹配,4=待匹配,5=需发货,6=待完成,7=需确认,8=已完成,9=交易关闭")
    private Integer status;

    /**
     * 视频价格（单位：$）
     */
    @Excel(name = "视频佣金")
    private BigDecimal videoPrice;

    /**
     * 图片费用（单位：$）
     */
    @Excel(name = "照片佣金", defaultValue = StrPool.DASHED)
    private BigDecimal picPrice;

    /**
     * 佣金代缴税费（单位：$）
     */
    @Excel(name = "佣金代缴税费")
    private BigDecimal commissionPaysTaxes;

    /**
     * 手续费（单位：$）
     */
    @Excel(name = "PayPal代付手续费")
    private BigDecimal exchangePrice;

    /**
     * 服务费用（单位：$）
     */
    @Excel(name = "蜗牛服务费")
    private BigDecimal servicePrice;

    /**
     * 当前汇率
     */
    @Excel(name = "实时百度汇率", defaultValue = StrPool.DASHED)
    private BigDecimal currentExchangeRate;

    /**
     * 视频金额（单位：$）
     */
    @Excel(name = "小计（USD）", defaultValue = StrPool.DASHED)
    private BigDecimal amountDollar;

    /**
     * 视频金额（单位：￥）
     */
    @Excel(name = "小计（CNY）", defaultValue = StrPool.DASHED)
    private BigDecimal amount;
}
