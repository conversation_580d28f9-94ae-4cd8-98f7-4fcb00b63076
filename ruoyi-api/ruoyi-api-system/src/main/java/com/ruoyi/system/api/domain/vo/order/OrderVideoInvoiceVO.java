package com.ruoyi.system.api.domain.vo.order;

import com.ruoyi.system.api.domain.entity.order.OrderInvoice;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-01-10 15:19
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderVideoInvoiceVO extends OrderInvoice implements Serializable {
    private static final long serialVersionUID = 1586781775921866030L;

    @ApiModelProperty("发票ID")
    private Long invoiceId;

    @ApiModelProperty("订单号")
    private String orderNum;

    @ApiModelProperty("视频订单ID")
    private Long videoId;

    @ApiModelProperty("视频编码")
    private String videoCode;

    @ApiModelProperty("开票金额")
    private BigDecimal invoiceAmount;
}
