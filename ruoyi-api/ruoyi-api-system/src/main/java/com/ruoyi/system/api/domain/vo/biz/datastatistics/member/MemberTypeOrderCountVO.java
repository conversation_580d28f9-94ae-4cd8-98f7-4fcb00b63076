package com.ruoyi.system.api.domain.vo.biz.datastatistics.member;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会员不同类型订单数量
 * @create :2025-06-19 14:26
 **/
@Data
public class MemberTypeOrderCountVO implements Serializable {
    private static final long serialVersionUID = -4085525443370438343L;

    @ApiModelProperty(value = "记录时间")
    private String recordTime;

    @ApiModelProperty(value = "新会员订单数量")
    private Long newMemberOrderCount;

    @ApiModelProperty(value = "老会员订单数量")
    private Long oldMemberOrderCount;

    @ApiModelProperty(value = "所有订单数量")
    private Long totalOrderCount;

    public static MemberTypeOrderCountVO init(String recordTime) {
        MemberTypeOrderCountVO memberTypeOrderCountVO = new MemberTypeOrderCountVO();
        memberTypeOrderCountVO.setRecordTime(recordTime);
        memberTypeOrderCountVO.setNewMemberOrderCount(0L);
        memberTypeOrderCountVO.setOldMemberOrderCount(0L);
        memberTypeOrderCountVO.setTotalOrderCount(0L);
        return memberTypeOrderCountVO;
    }
}
