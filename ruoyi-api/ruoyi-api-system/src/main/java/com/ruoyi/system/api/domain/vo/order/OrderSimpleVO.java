package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单简单对象VO
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@ApiModel(value = "订单对象VO")
@Data
public class OrderSimpleVO implements Serializable {
    private static final long serialVersionUID = -5843156176761089808L;
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 下单用户账号
     */
    @ApiModelProperty(value = "下单用户账号")
    private String orderUserAccount;

    /**
     * 下单用户名称
     */
    @ApiModelProperty(value = "下单用户名称")
    private String orderUserName;

    /**
     * 下单用户微信名称
     */
    @ApiModelProperty(value = "下单用户微信名称")
    private String orderUserNickName;

    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    /**
     * 支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额,11:微信+余额,12:支付宝+余额,13:云闪付/银联+余额,14.数字人民币+余额,15.银行+余额,16:对公+余额,17:全币种+余额）
     */
    @ApiModelProperty(value = "支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额,11:微信+余额,12:支付宝+余额,13:云闪付/银联+余额,14.数字人民币+余额,15.银行+余额,16:对公+余额,17:全币种+余额）")
    private Integer payType;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private Integer payTypeDetail;

    /**
     * 支付用户名称
     */
    @ApiModelProperty(value = "支付用户名称")
    private String payUserName;

    /**
     * 支付用户微信名称
     */
    @ApiModelProperty(value = "支付用户微信名称")
    private String payUserNickName;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 当前汇率
     */
    @ApiModelProperty(value = "当前汇率")
    @Excel(name = "当前汇率")
    private BigDecimal currentExchangeRate;

    /**
     * 是否使用的默认汇率（true 默认）
     */
    @ApiModelProperty(value = "是否使用的默认汇率（true 默认）")
    private boolean isDefaultExchangeRate;

    @ApiModelProperty(value = "视频优惠金额")
    private BigDecimal orderPromotionAmount;

    @ApiModelProperty(value = "是否优惠订单：0-否，1-是")
    private Integer isPromotion;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @ApiModelProperty(value = "财务审核状态（0:待审核,1:审核通过,2.审核异常,3.已关闭")
    private Integer auditStatus;

    /**
     * 支付单号
     */
    @ApiModelProperty(value = "支付单号")
    private String payNum;

    public void setOrderPromotionAmount(BigDecimal orderPromotionAmount) {
        this.orderPromotionAmount = orderPromotionAmount;
        this.isPromotion = StatusTypeEnum.NO.getCode();
        if (orderPromotionAmount != null && orderPromotionAmount.compareTo(BigDecimal.ZERO) > 0) {
            this.isPromotion = StatusTypeEnum.YES.getCode();
        }
    }
}
