package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-05-16 11:12
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CheckSeedCodeVO implements Serializable {
    private static final long serialVersionUID = 550986578847844432L;

    @ApiModelProperty(value = "结果")
    private Boolean result;

    @ApiModelProperty(value = "错误信息")
    private String errorMessage;
}
