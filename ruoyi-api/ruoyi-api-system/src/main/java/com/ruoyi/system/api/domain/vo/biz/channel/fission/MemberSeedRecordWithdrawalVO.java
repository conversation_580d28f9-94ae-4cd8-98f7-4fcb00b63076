package com.ruoyi.system.api.domain.vo.biz.channel.fission;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :提现记录
 * @create :2025-05-19 13:53
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MemberSeedRecordWithdrawalVO implements Serializable {
    private static final long serialVersionUID = 2657416614503630004L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("提现单号")
    private String withdrawalNum;

    @ApiModelProperty("种草官ID（distribution_channel.seed_id）")
    private String channelSeedId;

    @ApiModelProperty("渠道Id")
    private Long channelId;

    @ApiModelProperty("渠道类型：2-分销渠道 7-裂变")
    private Integer channelType;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty("海报名称")
    private String posterName;

    @ApiModelProperty("种草码")
    private String seedCode;

    @ApiModelProperty("申请结算金额（所有种草记录和）")
    private BigDecimal settleAmount;

    @ApiModelProperty("提现账号类型(2-支付宝，3-银行卡，6-公户收款)")
    private Integer withdrawalAccountType;

    @ApiModelProperty("收款方姓名")
    private String payeeName;

    @ApiModelProperty("收款方手机号")
    private String payeePhone;

    @ApiModelProperty("收款方身份证号")
    private String payeeIdentityCard;

    @ApiModelProperty("收款方账号")
    private String payeeAccount;

    @ApiModelProperty("开户行名称")
    private String bankName;

    @ApiModelProperty("状态（2-待审核，3-待打款，4-已打款，5-审核不通过，6-打款异常）")
    private Integer status;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date auditTime;

    @ApiModelProperty("审核备注")
    private String auditRemark;

    @ApiModelProperty("审核人员id FK sys_user.user_id")
    private Long auditUserId;

    @ApiModelProperty("审核人员名称")
    private String auditUserName;

    @ApiModelProperty("打款时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date payoutTime;

    @ApiModelProperty("提现审核时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date withdrawalTime;

    @ApiModelProperty("提现备注")
    private String withdrawalRemark;

    @ApiModelProperty("提现人员id FK sys_user.user_id")
    private Long withdrawalUserId;

    @ApiModelProperty("提现人员名称")
    private String withdrawalUserName;

    @ApiModelProperty("打款账号")
    private String payAccount;

    @ApiModelProperty("打款凭证url")
    private String resourceUrl;

    @ApiModelProperty("打款凭证url")
    private List<String> resourceUrlList;

    @ApiModelProperty("申请时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;

    @ApiModelProperty("微信名")
    private String applicantNickName;

    @ApiModelProperty("员工名称")
    private String applicantName;

    @ApiModelProperty("商家名称")
    private String applicantBusinessName;

    @ApiModelProperty("会员编码")
    private String applicantMemberCode;

    @ApiModelProperty("订单列表")
    @JsonIgnore
    private String orderNumStr;

    @ApiModelProperty("订单列表")
    private List<String> orderNumList;

    @ApiModelProperty("分组结算金额")
    private BigDecimal settleAmountGroup;

    @ApiModelProperty("包含订单")
    private String orderNum;

    @ApiModelProperty("结算方案")
    private Integer settleType;

    @ApiModelProperty("方案数据")
    private BigDecimal settleRage;

    @ApiModelProperty("申请结算金额")
    private BigDecimal orderSettleAmount;

    @ApiModelProperty("邀请商家会员编码")
    private String memberCode;

    @ApiModelProperty("会员种草记录创建时间")
    private Date recordCreateTime;

    public void setOrderNumStr(String orderNumStr) {
        this.orderNumStr = orderNumStr;
        if (StrUtil.isNotBlank(orderNumStr)) {
            this.orderNumList = StrUtil.split(orderNumStr, StrUtil.COMMA);
        }
    }

    public void setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl;
        if (StrUtil.isNotBlank(resourceUrl)) {
            this.resourceUrlList = StrUtil.split(resourceUrl, StrUtil.COMMA);
        }
    }
}
