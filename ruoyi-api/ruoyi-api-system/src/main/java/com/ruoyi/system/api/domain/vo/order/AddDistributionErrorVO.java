package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/20 11:48
 */
@Data
public class AddDistributionErrorVO implements Serializable {
    private static final long serialVersionUID = -3831924676430908472L;

    /**
     * 添加成功的预选模特主键
     */
    private List<Long> preselectModelIds;

    /**
     * 已被客服添加的模特ID
     */
    @ApiModelProperty("已被客服添加的模特ID")
    private List<Long> addModelIds;

    /**
     * 已被添加为意向模特的模特ID
     */
    @ApiModelProperty("已被添加为意向模特的模特ID")
    private List<Long> intentionModelIds;

    /**
     * 匹配条件变更，不满足的模特ID
     */
    @ApiModelProperty("匹配条件变更，不满足的模特ID")
    private List<Long> changeModelIds;
    /**
     * 模特通品信息变更，不满足的模特ID
     */
    @ApiModelProperty("模特通品信息变更，不满足的模特ID")
    private List<Long> generalProductLimitModelIds;
}
