package com.ruoyi.system.api.domain.vo.order.workbench;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :工作台暂停匹配VO
 * @create :2025-04-01 14:30
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PauseMatchVO implements Serializable {
    private static final long serialVersionUID = 4801782160660930886L;

    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    @ApiModelProperty(value = "暂停匹配原因")
    private String pauseReason;
}
