package com.ruoyi.system.api.domain.vo.biz.datastatistics.member;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会员排单数占比统计
 * @create :2025-06-18 11:18
 **/
@Data
public class BusinessAnalysisVO implements Serializable {
    private static final long serialVersionUID = 5684539263093256099L;

    @ApiModelProperty("排单总数")
    private Long businessOrderTotal;

    @ApiModelProperty("最后更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;

    @ApiModelProperty("会员排单数占比统计饼图")
    private List<PieChartVO> businessOrderPieChartVOS;

    public BusinessAnalysisVO init(List<PieChartVO> list, List<String> labels) {
        if (CollUtil.isEmpty(labels)){
            return this;
        }
        Long total = 0L;
        List<PieChartVO> result = new ArrayList<>();
        //初始化数据
        for (String item : labels) {
            PieChartVO pieChartVO = PieChartVO.init(item);
            result.add(pieChartVO);
        }

        //根据传参设置数据
        if (CollUtil.isNotEmpty(list)) {
            Map<String, Long> pieChartMap = new HashMap<>();
            for (PieChartVO pieChartVO : list) {
                pieChartMap.put(pieChartVO.getLabel(), pieChartVO.getCount());
                total += pieChartVO.getCount();
            }
            for (PieChartVO item : result){
                item.setCount(pieChartMap.getOrDefault(item.getLabel(), 0L));
                item.echoRatio(total);
            }
        }
        this.businessOrderPieChartVOS = result;
        this.businessOrderTotal = total;
        return this;
    }
}
