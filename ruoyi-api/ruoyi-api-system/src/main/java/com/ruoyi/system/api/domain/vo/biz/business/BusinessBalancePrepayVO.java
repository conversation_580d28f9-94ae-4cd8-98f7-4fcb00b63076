package com.ruoyi.system.api.domain.vo.biz.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.api.domain.vo.biz.business.prepay.OrderPayeeAccountVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家预付表
 *
 * <AUTHOR>
 * @TableName business_balance_prepay
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalancePrepayVO implements Serializable {

    private static final long serialVersionUID = -981735608546630181L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("预付单号")
    private String prepayNum;

    @ApiModelProperty("商家ID")
    private Long businessId;

    @ApiModelProperty("预付金额（单位：￥）")
    private BigDecimal amount;

    @ApiModelProperty("包含赠送金额（单位：￥）")
    private BigDecimal containPresentedAmount;

    @ApiModelProperty("预付款成功金额")
    private BigDecimal realAmount;

    @ApiModelProperty("应付金额")
    private BigDecimal payAmount;

    @ApiModelProperty("实际支付金额")
    private BigDecimal realPayAmount;

    @ApiModelProperty("订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    @ApiModelProperty("币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

    @ApiModelProperty(value = "当前汇率")
    private BigDecimal currentExchangeRate;

    @ApiModelProperty("备注申请")
    private String applyRemark;

    @ApiModelProperty(value = "支付账户（对公）")
    private String payAccount;

    @ApiModelProperty("支付方式(1:微信,2:支付宝支付,5:银行卡转账,6:对公转账，7-全币种,99-其他)")
    private Integer payType;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private Integer payTypeDetail;

    @ApiModelProperty("支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @ApiModelProperty("审核状态（0:待处理,1:审核通过,2.审核拒绝）")
    private Integer auditStatus;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @ApiModelProperty("审核人员id FK sys_user.user_id")
    private Long auditUserId;

    @ApiModelProperty("审核人员名称")
    private String auditUserName;

    @ApiModelProperty("拒绝原因")
    private String rejectCause;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("申请人id（sys_user.user_id）")
    private Long createById;

    @ApiModelProperty("申请人（sys_user.user_name）")
    private String createBy;

    @ApiModelProperty("申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date creatTime;

    @ApiModelProperty("商家主账号")
    private String ownerAccount;

    @ApiModelProperty("商家名称")
    private String businessName;

    @ApiModelProperty("是否为代理(0:否,1:是)")
    private Integer isProxy;

    @ApiModelProperty("会员编码")
    private String memberCode;

    @ApiModelProperty("商家注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date businessRegisterTime;

    @ApiModelProperty("微信昵称")
    private String nickName;

    @ApiModelProperty("收款账号")
    private OrderPayeeAccountVO orderPayeeAccountVO;

    @ApiModelProperty(value = "支付单号")
    private String payNum;

    @ApiModelProperty(value = "订单类型（3-线下钱包充值，5-线上钱包充值）")
    private Integer orderType;

    @ApiModelProperty(value = "支付用户id")
    private Long payUserId;

    @ApiModelProperty(value = "支付宝支付商户号")
    private String alipayPayAppId;

    @ApiModelProperty(value = "微信支付商户号")
    private String wechatPayAppId;

    @ApiModelProperty("关闭订单时间")
    private Date closeOrderTime;

    @ApiModelProperty(value = "提交凭证时间")
    private Date submitCredentialTime;

    @ApiModelProperty("订单状态（1:待支付,2:待审核,8:已完成,9:交易关闭）")
    private Integer status;

    @ApiModelProperty("收款账号ID")
    private Long accountId;

}
