package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.prepay.OrderPayeeAccountVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 订单列表返回VO
 *
 * <AUTHOR>
 * @date 2024/6/1 13:54
 */
@Data
public class OrderListVO implements Serializable {

    private static final long serialVersionUID = 3696451145651188442L;
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    @ApiModelProperty("支付单号")
    private String payNum;

    @ApiModelProperty(value = "原价（单位：￥）")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "原价（单位：$）")
    private BigDecimal orderAmountDollar;

    @ApiModelProperty(value = "支付金额（单位：￥）")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "订单实付金额（单位：￥）")
    private BigDecimal realPayAmount;

    @ApiModelProperty(value = "订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    @ApiModelProperty(value = "订单活动优惠总额（单位：￥）")
    private BigDecimal orderPromotionAmount;

    @ApiModelProperty(value = "差额")
    private BigDecimal differenceAmount;

    @ApiModelProperty(value = "币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

    @ApiModelProperty(value = "支付金额（单位：$）")
    private BigDecimal payAmountDollar;

    @ApiModelProperty(value = "使用余额（单位：￥）")
    private BigDecimal useBalance;

    @ApiModelProperty(value = "剩余支付金额")
    private BigDecimal surplusAmount;

    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderTime;

    @ApiModelProperty(value = "订单时间标记（每次开启订单、创建订单时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderTimeSign;

    @ApiModelProperty(value = "开启订单次数")
    private Integer reopenCount;

    @ApiModelProperty(value = "付款账户")
    private String payAccount;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;

    /**
     * 下单用户id
     */
    @ApiModelProperty(value = "下单用户id")
    @JsonIgnore
    private Long orderUserId;

    /**
     * 商家信息
     */
    @ApiModelProperty(value = "下单用户")
    private BusinessAccountDetailVO orderUser;

    /**
     * 下单用户名称
     */
    @ApiModelProperty(value = "下单用户名称")
    private String orderUserName;

    /**
     * 下单用户微信名称
     */
    @ApiModelProperty(value = "下单用户微信名称")
    private String orderUserNickName;

    /**
     * 商家id
     */
    @ApiModelProperty(value = "商家id")
    @JsonIgnore
    private Long merchantId;

    /**
     * 商家信息
     */
    @ApiModelProperty(value = "商家信息")
    private BusinessAccountDetailVO merchantInfo;

    /**
     * 支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额,11:微信+余额,12:支付宝+余额,13:云闪付/银联+余额,14.数字人民币+余额,15.银行+余额,16:对公+余额,17:全币种+余额）
     */
    @ApiModelProperty(value = "支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额,11:微信+余额,12:支付宝+余额,13:云闪付/银联+余额,14.数字人民币+余额,15.银行+余额,16:对公+余额,17:全币种+余额）")
    private Integer payType;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private Integer payTypeDetail;

    @ApiModelProperty(value = "提交凭证时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitCredentialTime;

    @ApiModelProperty(value = "财务审核状态（0:待审核,1:审核通过,2.审核异常,3.已关闭）")
    private Integer auditStatus;

    @ApiModelProperty("审核人员名称")
    private String auditUserName;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @ApiModelProperty("关闭订单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date closeOrderTime;

    @ApiModelProperty(value = "订单备注")
    private String  orderRemark;

    @ApiModelProperty(value = "用于校验的商家用户ID")
    private Set<Long> checkCompanyUserIds;

    /**
     * 税点费用（单位：￥）
     */
    @ApiModelProperty(value = "税点费用（单位：￥）")
    private BigDecimal taxPointCost;

    /**
     * 当前汇率
     */
    @ApiModelProperty(value = "当前汇率")
    private BigDecimal currentExchangeRate;

    @ApiModelProperty(value = "是否使用的默认汇率（0:不是,1:默认）")
    private boolean isDefaultExchangeRate;

    /**
     * 子订单列表
     */
    @ApiModelProperty(value = "子订单列表")
    private List<OrderVideoVO> orderVideoVOS;

    @ApiModelProperty(value = "发票信息")
    private OrderInvoiceVO orderInvoice;

    @ApiModelProperty("收款账号")
    private OrderPayeeAccountVO orderPayeeAccountVO;

    @ApiModelProperty("佣金代缴税费")
    private BigDecimal commissionPaysTaxes;

    private Date statusTime;

    private Integer status;

    @ApiModelProperty("是否展示开启订单的按钮")
    private Integer isShowReopenOrder;

    @ApiModelProperty("视频数量")
    private Integer videoCount;

    /**
     * 合并单ID
     */
    @ApiModelProperty(value = "合并单ID")
    private Long mergeId;

    @ApiModelProperty(value = "下单人姓名")
    private String createOrderUserName;
}
