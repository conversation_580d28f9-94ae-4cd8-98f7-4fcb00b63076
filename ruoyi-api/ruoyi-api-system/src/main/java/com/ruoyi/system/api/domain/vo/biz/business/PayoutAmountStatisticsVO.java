package com.ruoyi.system.api.domain.vo.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
* 余额提现审核表
* <AUTHOR>
 * @TableName business_balance_audit_flow
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PayoutAmountStatisticsVO implements Serializable {

    private static final long serialVersionUID = 5635342728504999981L;

    @ApiModelProperty("待处理提现金额")
    private BigDecimal preApproveAmount;

    @ApiModelProperty("已提现提现金额")
    private BigDecimal approveAmount;

    @ApiModelProperty("已取消提现金额")
    private BigDecimal cancelAmount;
}
