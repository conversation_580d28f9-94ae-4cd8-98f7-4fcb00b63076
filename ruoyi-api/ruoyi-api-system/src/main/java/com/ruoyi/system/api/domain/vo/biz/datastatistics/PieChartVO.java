package com.ruoyi.system.api.domain.vo.biz.datastatistics;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/9 17:57
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PieChartVO implements Serializable {
    private static final long serialVersionUID = -2205622238631564379L;

    /**
     * 标签
     */
    @ApiModelProperty("标签")
    private String label;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private Long count;

    /**
     * 比例
     */
    @ApiModelProperty("比例")
    private BigDecimal ratio;

    /**
     * 饼图对象
     */
    @ApiModelProperty("饼图对象")
    private List<PieChartVO> pieChartVOS;

    public void echo() {
        ratio = ratio.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
    }

    public void echoRatio(Long totalCount) {
        if (ObjectUtil.isNull(totalCount) || totalCount.compareTo(0L) == 0) {
            ratio = BigDecimal.ZERO;
        }
        ratio = BigDecimal.valueOf(Optional.ofNullable(count).orElse(0L))
                .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP)
                .divide(BigDecimal.valueOf(totalCount), 2, RoundingMode.HALF_UP);
    }

    public static PieChartVO init(String label) {
        return PieChartVO.builder().label(label).count(0L).ratio(BigDecimal.ZERO).build();
    }
}
