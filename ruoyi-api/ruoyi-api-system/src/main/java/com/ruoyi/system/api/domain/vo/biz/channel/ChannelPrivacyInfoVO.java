package com.ruoyi.system.api.domain.vo.biz.channel;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :渠道私密信息
 * @create :2024-12-03 09:22
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChannelPrivacyInfoVO implements Serializable {
    private static final long serialVersionUID = -6002732403098728491L;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty("种草码")
    private String seedCode;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("渠道地址")
    private String channelUrl;

}
