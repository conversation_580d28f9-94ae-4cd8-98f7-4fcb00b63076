package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 合并单待支付列表VO
 *
 * <AUTHOR>
 * @date 2025/3/7
 */
@Data
public class OrderMergeListVO implements Serializable {
    private static final long serialVersionUID = 8567403811841817460L;

    /**
     * 合并单ID
     */
    @ApiModelProperty(value = "合并单ID")
    private Long mergeId;

    /**
     * 合并人微信名
     */
    @ApiModelProperty(value = "合并人微信名")
    private String mergeNickBy;

    /**
     * 合并人
     */
    @ApiModelProperty(value = "合并人")
    private String mergeBy;

    /**
     * 合并人ID
     */
    @ApiModelProperty(value = "合并人ID")
    private Long mergeById;

    /**
     * 合并时间
     */
    @ApiModelProperty(value = "合并时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date mergeTime;

    /**
     * 合并总价
     */
    @ApiModelProperty(value = "合并总价")
    private BigDecimal mergePayAmount;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    @JsonIgnore
    private String orderNums;

    /**
     * 大订单信息
     */
    @ApiModelProperty(value = "大订单信息")
    private List<OrderListVO> orderListVOS = new ArrayList<>();
}
