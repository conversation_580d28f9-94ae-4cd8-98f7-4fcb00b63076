package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.system.api.domain.vo.biz.business.prepay.OrderPayeeAccountVO;
import com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :订单会员表
 * @create :2024-06-24 16:04
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderMemberVO implements Serializable {

    private static final long serialVersionUID = -7105337902246005875L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "订单主键ID")
    private Long orderId;

    @ApiModelProperty(value = "订单号")
    private String orderNum;

    @ApiModelProperty(value = "订单金额（单位：￥）")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "支付金额（单位：￥）")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "支付金额（单位：$）")
    private BigDecimal payAmountDollar;

    @ApiModelProperty(value = "当前汇率")
    private BigDecimal currentExchangeRate;

    @ApiModelProperty(value = "使用余额")
    private BigDecimal useBalance;

    @ApiModelProperty(value = "剩余支付金额（单位：￥）")
    private BigDecimal surplusAmount;

    @ApiModelProperty(value = "剩余支付金额（单位：$）")
    private BigDecimal surplusAmountDollar;

    @ApiModelProperty(value = "实际支付金额（单位：￥）")
    private BigDecimal realPayAmount;

    @ApiModelProperty(value = "订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    @ApiModelProperty(value = "实际支付金额（单位：$）")
    private BigDecimal realPayAmountDollar;

    @ApiModelProperty(value = "差额")
    private BigDecimal differenceAmount;

    @ApiModelProperty(value = "订单活动优惠总额")
    private BigDecimal orderPromotionAmount;

    @ApiModelProperty(value = "币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    @ApiModelProperty(value = "下单用户id")
    private Long orderUserId;

    @ApiModelProperty(value = "支付用户id")
    private Long payUserId;

    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @ApiModelProperty(value = "支付账户（对公）")
    @Excel(name = "支付账户（对公）")
    private String payAccount;

    @ApiModelProperty(value = "财务审核状态（0:待审核,1:审核通过,2.审核异常,3.已关闭")
    @Excel(name = "财务审核状态")
    private Integer auditStatus;

    @ApiModelProperty("审核人员名称")
    private String auditUserName;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @ApiModelProperty(value = "支付方式（1:微信,2:支付宝,3:云闪付/银联,4=数字人民币,5=银行,6:对公,7=全币种,10:余额,11:微信+余额,12:支付宝+余额,13:云闪付/银联+余额,14=数字人民币+余额,15=银行+余额,16:对公+余额,17=全币种+余额）")
    @Excel(name = "支付方式")
    private Integer payType;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private Integer payTypeDetail;

    @ApiModelProperty(value = "提交凭证时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitCredentialTime;

    @ApiModelProperty(value = "订单备注")
    @Excel(name = "订单备注")
    private String orderRemark;

    @NotNull(message="[订单状态(1待支付、2待审核、3交易成功、4交易关闭)]不能为空")
    @ApiModelProperty("订单状态(1待支付、2待审核、3交易成功、4交易关闭)")
    private Integer status;

    @NotNull(message="[套餐类型：0-季度套餐，1-一年会员，2-三年会员]不能为空")
    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer packageType;

    @ApiModelProperty("套餐价格")
    private BigDecimal packageAmount;

    @ApiModelProperty(value = "税点（单位：%）")
    private BigDecimal taxPoint;

    @ApiModelProperty("服务费（单位：￥）")
    private BigDecimal taxPointCost;

    @ApiModelProperty("会员开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date memberStartTime;

    @ApiModelProperty("会员结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date memberEndTime;

    @ApiModelProperty("加赠时间")
    private Integer presentedTime;

    @ApiModelProperty("加赠时间类型（1-天,2-月,3-年）")
    private Integer presentedTimeType;

    /**
     * 商家id
     */
    @NotNull(message = "[商家id]不能为空")
    @ApiModelProperty(value = "商家id", required = true)
    @Excel(name = "商家id")
    private Long merchantId;

    @ApiModelProperty(value = "登录用户ID", required = true)
    private Long bizUserId;


    @ApiModelProperty(value = "商家账号", required = true)
    private String businessAccount;

    /**
     * 商家编码
     */
    @NotNull(message = "[商家编码]不能为空")
    @ApiModelProperty(value = "商家编码", required = true)
    @Excel(name = "商家编码")
    private String merchantCode;

    @ApiModelProperty("商家名称")
    private String businessName;

    @ApiModelProperty("微信名称 优先员工名称")
    private String nickName;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("会员编码")
    private String memberCode;

    @ApiModelProperty("发票信息")
    private OrderInvoiceVO orderInvoice;

    /**
     * 种草码优惠金额（单位：￥）
     */
    @ApiModelProperty(value = "种草码优惠金额（单位：￥）")
    private BigDecimal seedCodeDiscount;

    @ApiModelProperty(value = "是否默认汇率")
    private Boolean isDefaultExchangeRate;

    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @ApiModelProperty(value = "种草码")
    private String seedCode;

    @ApiModelProperty(value = "种草官ID")
    private String seedId;

    @ApiModelProperty(value = "种草官会员状态")
    private Integer seedMemberStatus;

    @ApiModelProperty(value = "会员结算类型（1-固定金额，2-固定比例）")
    private Integer memberDiscountType;

    @ApiModelProperty(value = "结算比例")
    private BigDecimal settleRage;

    @ApiModelProperty("收款账号")
    private OrderPayeeAccountVO orderPayeeAccountVO;

    @ApiModelProperty(value = "是否首次购买1:首次2:续费")
    private Integer isFirstBuy;

    @ApiModelProperty(value = "添加企业微信来源渠道")
    private Integer registerChannelType;

    @ApiModelProperty(value = "企微渠道类型(0=普通渠道,1=营销市场渠道,2=分销渠道,3=子账号,4=系统官网,5=系统SEO,6=VIP,7=裂变)")
    private Integer wechatChannelType;

    @ApiModelProperty(value = "转义后渠道数据-前端可直接调用")
    private String wechatChannelStringType;

    @ApiModelProperty(value = "实际支付人民币总和")
    private BigDecimal totalRealPayAmount;

    @ApiModelProperty(value = "种草码渠道类型")
    private Integer channelType;

    @ApiModelProperty(value = "添加企微时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addWechatTime;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 下单用户账号
     */
    @ApiModelProperty(value = "下单用户账号")
    private String orderUserAccount;

    /**
     * 下单用户名称
     */
    @ApiModelProperty(value = "下单用户名称")
    private String orderUserName;

    /**
     * 下单用户微信名称
     */
    @ApiModelProperty(value = "下单用户微信名称")
    private String orderUserNickName;

    /**
     * 订单参与的活动以及对应的优惠
     */
    @ApiModelProperty(value = "订单参与的活动以及对应的优惠")
    private List<OrderDiscountDetailVO> orderDiscountDetailVOS;

    @ApiModelProperty(value = "商务经理名称")
    private String connectUserName;
}

