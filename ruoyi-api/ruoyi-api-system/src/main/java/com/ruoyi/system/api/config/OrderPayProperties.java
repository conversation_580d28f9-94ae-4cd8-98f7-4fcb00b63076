package com.ruoyi.system.api.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/26
 */
@Configuration
@ConfigurationProperties(prefix = "order.pay")
@Data
@RefreshScope
public class OrderPayProperties {
    /**
     * 开票税点 单位（%）
     */
    private BigDecimal taxPoint;
    /**
     * 种草码折扣 单位（%）
     */
    private BigDecimal grassCodeDiscount;
    /**
     * 全币种支付美金上限
     */
    private BigDecimal fullCurrencyUpperLimit;
}
