package com.ruoyi.system.api.domain.vo.biz.model;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/8 19:22
 */
@Data
public class ModelDataTableDetailVO implements Serializable {
    private static final long serialVersionUID = -3812059669267020768L;

    /**
     * 模特ID
     */
    @ApiModelProperty(value = "模特ID")
    private Long id;

    /**
     * 模特图片URI
     */
    @ApiModelProperty(value = "模特图片URI")
    private String modelPic;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 模特账号
     */
    @ApiModelProperty(value = "模特账号")
    private String account;

    /**
     * 性别(1:男,0:女)
     */
    @ApiModelProperty(value = "性别(1:男,0:女)")
    private Integer sex;

    /**
     * 年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）
     */
    @ApiModelProperty(value = "年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）")
    private Integer ageGroup;

    /**
     * 国家"（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）"
     */
    @ApiModelProperty(value = "国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    private Integer nation;

    /**
     * 模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "模特类型0:影响者,1:素人")
    private Integer type;

    /**
     * 模特评分 (0.0-10.0)
     */
    @ApiModelProperty(value = "模特评分 (0.0-10.0)")
    private BigDecimal cooperationScore;

    /**
     * 平台(0:Amazon,1:tiktok,2:其他)
     */
    @ApiModelProperty(value = "平台(0:Amazon,1:tiktok,2:其他)")
    private String platform;

    /**
     * 模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)
     */
    @ApiModelProperty(value = "模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)")
    private Integer status;

    /**
     * 模特行程恢复时间
     */
    @ApiModelProperty(value = "模特行程恢复时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date tripRecoveryTime;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    private BigDecimal commission;

    /**
     * 收藏数
     */
    @ApiModelProperty(value = "收藏数")
    private Long collectCount;

    /**
     * 被拉黑数
     */
    @ApiModelProperty(value = "被拉黑数")
    private Long blacklistCount;

    /**
     * 家庭成员IDS
     */
    @JsonIgnore
    private String familyMemberIds;

    /**
     * 家庭成员
     */
    @ApiModelProperty(value = "家庭成员")
    private List<ModelBaseVO> familyMember;

    /**
     * 客服ID
     */
    @JsonIgnore
    private Long serviceId;

    /**
     * 客服名称
     */
    @ApiModelProperty(value = "客服名称")
    private String serviceName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}
