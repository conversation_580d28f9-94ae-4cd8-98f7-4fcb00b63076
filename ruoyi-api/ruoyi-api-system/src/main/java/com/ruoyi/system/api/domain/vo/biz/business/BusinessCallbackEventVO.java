package com.ruoyi.system.api.domain.vo.biz.business;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:17
 */
@Data
public class BusinessCallbackEventVO implements Serializable {
    private static final long serialVersionUID = -4719963461396293492L;


    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 回访ID (FK:business_callback.id)
     */
    @ApiModelProperty("回访ID")
    private Long callbackId;

    /**
     * 回访事件
     */
    @ApiModelProperty("回访事件(1:会员5天0排单,2:会员1个月0排单,3:会员已购1个月,4:会员已购3个月,5:会员已购6个月,6:会员过期前1个月,7:会员过期前1周,8:会员过期前1天,9:会员已过期1周,10:会员已过期1个月,11:近30天无排单)")
    private Integer callbackEvent;

    /**
     * 最晚回访时间
     */
    @ApiModelProperty("最晚回访时间")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date callbackTime;
}
