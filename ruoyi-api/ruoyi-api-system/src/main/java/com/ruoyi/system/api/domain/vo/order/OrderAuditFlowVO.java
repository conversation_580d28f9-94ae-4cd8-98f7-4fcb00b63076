package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单财务审核流水
 *
 * <AUTHOR>
 * @TableName order_audit_flow
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrderAuditFlowVO implements Serializable {
    private static final long serialVersionUID = 7912248323365452510L;

    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotBlank(message = "[订单号]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("订单号")
    @Length(max = 32, message = "编码长度不能超过32")
    private String orderNum;

    @ApiModelProperty("财务审核状态（0:待审核,1:审核通过,2.审核异常,3.已关闭）")
    private Integer auditStatus;

    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("审核人员名称")
    @Length(max = 32, message = "编码长度不能超过32")
    private String auditUserName;

    @ApiModelProperty("审核人员id FK sys_user.user_id")
    private Long auditUserId;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @Size(max = 500, message = "编码长度不能超过500")
    @ApiModelProperty("订单支付凭证")
    @Length(max = 500, message = "编码长度不能超过500")
    private String orderDocumentResource;

    @Size(max = 300, message = "编码长度不能超过300")
    @ApiModelProperty("备注")
    @Length(max = 300, message = "编码长度不能超过300")
    private String remark;

    @ApiModelProperty("订单支付凭证")
    private List<OrderDocumentResourceVO> resourceVos;

    @ApiModelProperty("实际支付金额（单位：￥）")
    private BigDecimal realPayAmount;

    @ApiModelProperty(value = "订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    @ApiModelProperty(value = "差额")
    private BigDecimal differenceAmount;

    @ApiModelProperty(value = "订单活动优惠总额")
    private BigDecimal orderPromotionAmount;

    @ApiModelProperty("支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @ApiModelProperty("支付方式(1:微信,2:支付宝支付,5:银行卡转账,6:对公转账,7:全币种)")
    private Integer payType;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private Integer payTypeDetail;

    @ApiModelProperty(value = "币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

}
