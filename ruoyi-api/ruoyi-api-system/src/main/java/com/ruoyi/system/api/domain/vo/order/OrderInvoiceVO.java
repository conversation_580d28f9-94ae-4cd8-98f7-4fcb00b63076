package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-06-18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderInvoiceVO implements Serializable {
    private static final long serialVersionUID = 5535464587145656892L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    @ApiModelProperty(value = "订单类型（0:视频订单,1:会员订单）")
    @Excel(name = "订单类型")
    private Integer type;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 商家id
     */
    @JsonIgnore
    private Long merchantId;

    /**
     * 商家信息
     */
    @ApiModelProperty(value = "商家信息")
    private BusinessAccountDetailVO businessInfo;

    /**
     * 商家对接客服
     */
    @ApiModelProperty(value = "商家对接客服")
    private UserVO waiterUser;

    /**
     * 发票号
     */
    @ApiModelProperty(value = "发票号")
    private String number;

    /**
     * 发票文件URI
     */
    @ApiModelProperty(value = "发票文件URI")
    private String objectKey;

    /**
     * 发票类型（1：增值税普通发票，2：形式发票）
     */
    @ApiModelProperty(value = "发票类型（1：增值税普通发票，2：形式发票）")
    private Integer invoiceType;

    /**
     * 抬头类型（1：企业单位）
     */
    @ApiModelProperty(value = "抬头类型（1：企业单位）")
    private Integer titleType;

    /**
     * 发票抬头
     */
    @ApiModelProperty(value = "发票抬头")
    private String title;

    /**
     * 税号
     */
    @ApiModelProperty(value = "税号")
    private String dutyParagraph;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 公司地址
     */
    @ApiModelProperty(value = "公司地址")
    private String companyAddress;

    /**
     * 公司联系电话
     */
    @ApiModelProperty(value = "公司联系电话")
    private String companyPhone;

    /**
     * 公司联系人
     */
    @ApiModelProperty(value = "公司联系人")
    private String companyContact;

    /**
     * 附件URI
     */
    @ApiModelProperty(value = "附件URI")
    private String attachmentObjectKey;

    /**
     * 发票内容
     */
    @ApiModelProperty(value = "发票内容")
    private String content;

    /**
     * 发票备注
     */
    @ApiModelProperty(value = "发票备注")
    private String invoiceRemark;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 开票状态（1:待开票,2:待确认,3:已投递,4:已作废）
     */
    @ApiModelProperty(value = "开票状态（1:待开票,2:待确认,3:已投递,4:已作废）", notes = "1:待开票,2:待确认,3:已投递,4:已作废")
    private Integer status;

    /**
     * 开票时间
     */
    @ApiModelProperty(value = "开票时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date invoicingTime;

    /**
     * 审核人
     */
    @JsonIgnore
    private Long auditBy;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人")
    private UserVO auditUser;


    /**
     * 操作人
     */
    @JsonIgnore
    private Long operatorBy;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private UserVO operator;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date operatorTime;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}
