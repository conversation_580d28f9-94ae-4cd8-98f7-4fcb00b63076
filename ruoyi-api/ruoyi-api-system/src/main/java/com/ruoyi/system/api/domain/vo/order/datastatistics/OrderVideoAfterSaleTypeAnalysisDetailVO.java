package com.ruoyi.system.api.domain.vo.order.datastatistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/10 14:09
 */
@Data
public class OrderVideoAfterSaleTypeAnalysisDetailVO implements Serializable {
    private static final long serialVersionUID = -6228642393968135387L;

    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private String date;

    /**
     * 总计
     */
    @ApiModelProperty("总计")
    private Long totalCount;

    /**
     * 售后单数量
     */
    @ApiModelProperty("售后单数量")
    private Long afterSaleCount;

    /**
     * 售后单占比
     */
    @ApiModelProperty("售后单占比")
    private BigDecimal afterSaleRate;

    /**
     * 工单数量
     */
    @ApiModelProperty("工单数量")
    private Long workOrderCount;

    /**
     * 工单占比
     */
    @ApiModelProperty("工单占比")
    private BigDecimal workOrderRate;

    /**
     * 补偿单数量
     */
    @ApiModelProperty("补偿单数量")
    private Long reparationCount;

    /**
     * 补偿单占比
     */
    @ApiModelProperty("补偿单占比")
    private BigDecimal reparationRate;

    /**
     * 回退单数量
     */
    @ApiModelProperty("回退单数量")
    private Long returnCount;

    /**
     * 回退单占比
     */
    @ApiModelProperty("回退单占比")
    private BigDecimal returnRate;

    public void echo() {
        totalCount = totalCount == null ? 0 : totalCount;
        afterSaleCount = afterSaleCount == null ? 0 : afterSaleCount;
        afterSaleRate = afterSaleRate == null ? BigDecimal.ZERO : afterSaleRate.multiply(new BigDecimal("100"));
        workOrderCount = workOrderCount == null ? 0 : workOrderCount;
        workOrderRate = workOrderRate == null ? BigDecimal.ZERO : workOrderRate.multiply(new BigDecimal("100"));
        reparationCount = reparationCount == null ? 0 : reparationCount;
        reparationRate = reparationRate == null ? BigDecimal.ZERO : reparationRate.multiply(new BigDecimal("100"));
        returnCount = returnCount == null ? 0 : returnCount;
        returnRate = returnRate == null ? BigDecimal.ZERO : returnRate.multiply(new BigDecimal("100"));
    }
}
