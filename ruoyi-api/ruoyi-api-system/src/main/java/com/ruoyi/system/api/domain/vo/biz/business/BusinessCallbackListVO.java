package com.ruoyi.system.api.domain.vo.biz.business;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BusinessCallbackListVO extends BusinessCallbackBaseVO {
    private static final long serialVersionUID = -4622323969611782794L;

    /**
     * 回访状态（1:待回访,2:回访中,3:已回访）
     */
    @ApiModelProperty("回访状态（1:待回访,2:回访中,3:已回访）")
    private Integer status;

    /**
     * 标记时间
     */
    @ApiModelProperty("标记时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date markTime;

    /**
     * 记录时间
     */
    @ApiModelProperty("记录时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date writeTime;

    /**
     * 回访事件
     */
    @ApiModelProperty("回访事件")
    private List<BusinessCallbackEventVO> businessCallbackEventVOS;
}
