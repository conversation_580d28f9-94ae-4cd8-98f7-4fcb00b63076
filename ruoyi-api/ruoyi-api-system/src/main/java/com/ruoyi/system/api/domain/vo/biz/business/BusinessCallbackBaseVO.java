package com.ruoyi.system.api.domain.vo.biz.business;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/13 10:17
 */
@Data
public class BusinessCallbackBaseVO implements Serializable {
    private static final long serialVersionUID = -7369865638103793284L;


    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 商家ID (FK:business.id)
     */
    @ApiModelProperty("商家ID")
    private Long businessId;

    /**
     * 商家名称
     */
    @ApiModelProperty("商家名称")
    private String name;

    /**
     * 会员编码
     */
    @ApiModelProperty("会员编码")
    private String memberCode;

    /**
     * 规模
     */
    @ApiModelProperty("规模(1:1-20人,2:21-100人,3:101-200人,4:201-1000人,5:超过1000人)")
    private Integer scale;

    /**
     * 对接客服  FK：sys_user.user_id
     */
    @ApiModelProperty("对接客服  FK：sys_user.user_id")
    @JsonIgnore
    private Long waiterId;

    /**
     * 对接客服名称
     */
    @ApiModelProperty("对接客服名称")
    private String waiterName;

    /**
     * 会员首次购买时间
     */
    @ApiModelProperty("会员首次购买时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date memberFirstTime;

    /**
     * 会员有效期
     */
    @ApiModelProperty("会员有效期")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date memberValidity;

    /**
     * 子账号数量
     */
    @ApiModelProperty("子账号数量")
    private Integer accountNum;

    /**
     * 订单数量
     */
    @ApiModelProperty("订单数量")
    private Integer orderNum;

    /**
     * 近30天排单量
     */
    @ApiModelProperty("近30天排单量")
    private Integer recentOrderNum;

    /**
     * 待完成订单数量
     */
    @ApiModelProperty("待完成订单数量")
    private Integer preFinishOrderNum;
}
