
package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-16
 */
@Data
public class VideoMatchOrderVO implements Serializable {

    private static final long serialVersionUID = -4073663314836085066L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 视频id
     */
    @ApiModelProperty(value = "视频id")
    private Long videoId;

    /**
     * 状态（1:正常,2:暂停）
     */
    @ApiModelProperty(value = "状态（1:正常,2:暂停）")
    private Integer status;

    /**
     * 正常的预选模特列表
     */
    @ApiModelProperty(value = "正常的预选模特列表")
    private List<VideoMatchPreselectModelOrderVO> normalVideoMatchPreselectModelOrderVOS;

    /**
     * 携带类型（1:主携带,2:被携带）
     */
    @ApiModelProperty(value = "携带类型（1:主携带,2:被携带）")
    private Integer carryType;

    @ApiModelProperty(value = "暂停原因")
    private String pauseReason;

    @ApiModelProperty(value = "提交时间")
    private Date submitDate;

    @ApiModelProperty(value = "拍摄模特id")
    private Long shootModelId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "平台")
    private String shootModelPlatform;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    private BigDecimal commission;


}
