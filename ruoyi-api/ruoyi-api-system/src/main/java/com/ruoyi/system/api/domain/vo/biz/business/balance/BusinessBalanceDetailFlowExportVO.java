package com.ruoyi.system.api.domain.vo.biz.business.balance;

import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :余额全部明细
 * @create :2024-12-28 18:05
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessBalanceDetailFlowExportVO implements Serializable {

    private static final long serialVersionUID = -5537761170745965747L;

    @Excel(name = "交易时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "会员编码")
    private String memberCode;

    @Excel(name = "单号")
    private String number;

    @Excel(name = "来源单号")
    private String originNumber;

    @Excel(name = "来源编码")
    private String originCode;

    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    @Excel(name = "支付方式", readConverterExp = "1=微信,2=支付宝,3=云闪付/银联,4=数字人民币,5=银行,6=对公,7=全币种,10=余额,11=微信+余额,12=支付宝+余额,13=云闪付/银联+余额,14=数字人民币+余额,15=银行+余额,16=对公+余额,17=全币种+余额")
    private Integer payType;

    @ApiModelProperty("1-退款订单,2-预付款订单")
    private Integer numberType;

    @ApiModelProperty("订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、4.视频订单支出、5.会员订单支出、6.提现支出、7.线下钱包充值、8.线上钱包充值收入)")
    private Integer origin;

    @ApiModelProperty("订单类型(0-收入、1-支出)")
    private Integer detailFlowType;

    /**
     * 如果是支出     originType = origin + numberType * 10
     */
    @Excel(name ="类型", readConverterExp = "1=补偿订单收入,2=取消订单收入,3=取消选配收入,7=线下钱包充值收入,8=线上钱包充值收入,14=退款抵扣,15=退款抵扣,16=退款提现,24=钱包余额抵扣,25=钱包余额抵扣,26=钱包提现")
    private Integer originType;

    @Excel(name = "去向订单")
    private String useOrderNum;

    @ApiModelProperty("去向视频编码")
    private String useVideoCode;

    /**
     *  支出填充
     */
    @Excel(name = "订单类型", readConverterExp = "4=视频订单,5=会员订单,6=提现订单")
    private Integer useOrigin;

    @Excel(name = "金额")
    private BigDecimal useBalance;
}
