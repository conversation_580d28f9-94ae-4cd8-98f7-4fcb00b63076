package com.ruoyi.system.api.domain.vo.biz.channel.fission;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecord;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会员种草记录
 * @create :2025-05-19 10:17
 **/
@Data
public class MemberSeedRecordVO extends MemberSeedRecord implements Serializable {
    private static final long serialVersionUID = -2094921068718213559L;
    @ApiModelProperty("提现单号")
    private String withdrawalNum;

    @ApiModelProperty("打款时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date payoutTime;

    @ApiModelProperty("提现审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date withdrawalTime;

    @ApiModelProperty("打款备注")
    private String withdrawalRemark;

    @ApiModelProperty("打款人员名称")
    private String withdrawalUserName;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @ApiModelProperty("审核备注")
    private String auditRemark;

    @ApiModelProperty("审核人员名称")
    private String auditUserName;

    @Override
    public void setChannelPhone(String channelPhone) {
        super.setChannelPhone(channelPhone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));
    }
    @Override
    public void setBizUserPhone(String bizUserPhone) {
        super.setBizUserPhone(bizUserPhone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));
    }
}
