package com.ruoyi.system.api.domain.vo.order.logistic;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 物流跟进表
 *
 * <AUTHOR>
 * @TableName order_video_logistic_follow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderVideoLogisticFollowStatisticsVO implements Serializable {

    private static final long serialVersionUID = 8400412509458045950L;

    @ApiModelProperty("需处理数量")
    private Long needHandleCount;

    @ApiModelProperty("暂不处理数量")
    private Long tempHoldCount;

    @ApiModelProperty("需跟进数量")
    private Long needFollowCount;

    @ApiModelProperty("模特待确认数量")
    private Long modelConfirmPendCount;

    @ApiModelProperty("无需跟进数量")
    private Long noFollowNeedCount;

    @ApiModelProperty("已结束数量")
    private Long closeCount;
}
