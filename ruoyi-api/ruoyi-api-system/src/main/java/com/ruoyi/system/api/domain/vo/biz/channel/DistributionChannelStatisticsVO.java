package com.ruoyi.system.api.domain.vo.biz.channel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :分销渠道统计
 * @create :2024-06-25 09:57
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "分销渠道统计vo")
@Builder
public class DistributionChannelStatisticsVO implements Serializable {
    private static final long serialVersionUID = 3832096627784909821L;

    @ApiModelProperty(value = "渠道数量")
    private Long channelNum;

    @ApiModelProperty(value = "邀请注册数量")
    private Long registerNum;

    @ApiModelProperty(value = "独立访客")
    private Long uniqueVisitor;

    @ApiModelProperty(value = "会员成交数")
    private Long memberNum;

    @ApiModelProperty(value = "添加企微数量")
    private Long addWeChatNum;

    @ApiModelProperty("待结算金额")
    private BigDecimal unSettleAmount;

    @ApiModelProperty("会员成交总金额")
    private BigDecimal memberAmount;
}
