package com.ruoyi.system.api.domain.vo.biz.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.api.domain.vo.biz.business.balance.PayoutBusinessBalanceDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 余额提现审核表
 *
 * <AUTHOR>
 * @TableName business_balance_audit_flow
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessBalanceAuditFlowDetailVO implements Serializable {


    private static final long serialVersionUID = 9040072343255580692L;
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("商家ID")
    private Long businessId;

    @ApiModelProperty("提现金额")
    private BigDecimal amount;

    @ApiModelProperty("实付金额（单位：￥）")
    private BigDecimal realAmount;

    @ApiModelProperty("提现类型（1-微信，2-支付宝，3-银行卡，4-境外汇款，5-其他）")
    private Integer withdrawWay;


    @ApiModelProperty("支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @ApiModelProperty("提现申请图片")
    private List<String> payoutResourceUrlList;

    @ApiModelProperty("图片资源地址url")
    private String resourceUrl;

    @ApiModelProperty("审核状态（0:待处理,1:已提现,2.已取消）")
    private Integer auditStatus;

    @ApiModelProperty("审核人员id FK sys_user.user_id")
    private Long auditUserId;

    @ApiModelProperty("审核人员名称")
    private String auditUserName;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("提现备注")
    private String applyRemark;

    @ApiModelProperty("发起人id FK sys_user.user_id")
    private Long createUserId;

    @ApiModelProperty("发起人名称")
    private String createUserName;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("提现余额详情列表")
    private List<PayoutBusinessBalanceDetailVO> businessBalanceDetails;

}
