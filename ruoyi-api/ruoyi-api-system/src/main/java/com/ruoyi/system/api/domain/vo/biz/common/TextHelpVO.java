package com.ruoyi.system.api.domain.vo.biz.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:25
 */
@Data
public class TextHelpVO implements Serializable {

    private static final long serialVersionUID = -6616576129250742916L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "文本名称")
    private String name;

    @ApiModelProperty(value = "文本内容")
    private String content;

    @ApiModelProperty(value = "文本类型：0-协议信息,1-帮助中心-常见问题,2-帮助中心-新手指南")
    private Integer type;

    @ApiModelProperty(value = "排序值")
    private Integer sort;

    @ApiModelProperty(value = "状态：0启用，1-禁用")
    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
