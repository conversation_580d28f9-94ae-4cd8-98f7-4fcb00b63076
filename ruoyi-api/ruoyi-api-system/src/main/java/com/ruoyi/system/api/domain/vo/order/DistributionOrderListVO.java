package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.api.domain.entity.order.OrderVideoContent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/16 18:20
 */
@Data
public class DistributionOrderListVO implements Serializable {
    private static final long serialVersionUID = 6085866501546703341L;

    /**
     * 视频订单ID
     */
    @ApiModelProperty(value = "视频订单ID")
    private Long videoId;

    /**
     * 匹配单ID
     */
    @ApiModelProperty(value = "匹配单ID")
    private Long matchId;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 产品中文名
     */
    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    private String productLink;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private Integer platform;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）")
    private Integer modelType;

    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    private Integer shootingCountry;

    /**
     * 视频格式（1:横屏16：9,2:竖屏9：16）
     */
    @ApiModelProperty(value = "视频格式（1:横屏16：9,2:竖屏9：16）")
    private Integer videoFormat;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）")
    private Integer picCount;

    /**
     * 退款照片数量
     */
    @ApiModelProperty(value = "退款照片数量")
    private Integer refundPicCount;

    /**
     * 剩余退款照片数量
     */
    @ApiModelProperty(value = "剩余退款照片数量")
    private Integer surplusPicCount;

    /**
     * 参考图片（关联资源id）
     */
    @ApiModelProperty(value = "参考图片（关联资源id）")
    @JsonIgnore
    private String referencePicId;

    /**
     * 参考图片
     */
    @ApiModelProperty(value = "参考图片")
    private List<String> referencePic = new ArrayList<>();

    /**
     * 创建订单用户名称
     */
    @ApiModelProperty(value = "创建订单用户名称")
    private String createOrderUserName;

    /**
     * 创建订单用户微信名称
     */
    @ApiModelProperty(value = "创建订单用户微信名称")
    private String createOrderUserNickName;

    /**
     * 视频时长
     */
    @ApiModelProperty(value = "视频时长")
    private Integer videoDuration;

    /**
     * 拍摄建议（原拍摄要求）
     */
    @ApiModelProperty("拍摄建议（原拍摄要求）")
    private List<OrderVideoContent> shootRequired;

    /**
     * 产品卖点
     */
    @ApiModelProperty(value = "产品卖点")
    private String sellingPointProduct;

    /**
     * 模特要求（原匹配模特注意事项）
     */
    @ApiModelProperty("模特要求（原匹配模特注意事项）")
    private OrderVideoCautionsVO orderVideoCautionsVO;

    /**
     * 商品规格要求
     */
    @ApiModelProperty(value = "商品规格要求")
    private String orderSpecificationRequire;

    /**
     * 特别强调
     */
    @ApiModelProperty(value = "特别强调")
    private String particularEmphasis;

    /**
     * 特别强调图片
     */
    @ApiModelProperty(value = "特别强调图片")
    private List<String> particularEmphasisPic;

    /**
     * 对接人ID
     */
    @JsonIgnore
    private Long contactId;

    /**
     * 中文部客服名称
     */
    @ApiModelProperty(value = "中文部客服名称")
    private String chineseServiceName;

    /**
     * 历史预选模特数量
     */
    @ApiModelProperty(value = "历史预选模特数量")
    private Integer historyPreselectModelCount;

    /**
     * 添加时间
     */
    @ApiModelProperty(value = "添加时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date addTime;

    /**
     * 状态（0:未对接,1:已对接,2:已选定,3:已淘汰）
     */
    @ApiModelProperty(value = "状态（0:未对接,1:已对接,2:已选定,3:已淘汰）", notes = "0:未对接,1:已对接,2:已选定,3:已淘汰")
    private Integer status;

    /**
     * 模特ID
     */
    @JsonIgnore
    private Long modelId;

    /**
     * 预选模特ID
     */
    @ApiModelProperty(value = "预选模特ID")
    private Long preselectModelId;

    /**
     * 视频风格
     */
    @ApiModelProperty(value = "视频风格(0:Amazon,1:tiktok,2:APP/解说类)")
    private Integer videoStyle;

    /**
     * 参考视频链接
     */
    @ApiModelProperty(value = "参考视频链接")
    private String referenceVideoLink;

    /**
     * 是通品（1：是，0：不是）
     */
    @ApiModelProperty(value = "是通品（1：是，0：不是）")
    private Integer isGund;

}
