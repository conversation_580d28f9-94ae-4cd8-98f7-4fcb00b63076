package com.ruoyi.system.api.domain.vo.biz.datastatistics;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/9 15:03
 */
@Data
public class ModelNumberTrendAnalysisVO implements Serializable {
    private static final long serialVersionUID = -679374308780982331L;

    /**
     * 记录截止时间
     */
    @ApiModelProperty("记录截止时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date writeTimeEnd;

    /**
     * 日期数组
     */
    @ApiModelProperty("日期数组")
    private List<String> dateArray;

    /**
     * 淘汰数数组
     */
    @ApiModelProperty("淘汰数数组")
    private List<Long> eliminatedNumberArray;

    /**
     * 新增数数组
     */
    @ApiModelProperty("新增数数组")
    private List<Long> newAdditionsNumberArray;
}
