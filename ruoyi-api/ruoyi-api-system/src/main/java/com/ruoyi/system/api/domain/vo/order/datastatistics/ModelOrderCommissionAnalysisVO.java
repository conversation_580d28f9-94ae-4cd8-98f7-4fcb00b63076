package com.ruoyi.system.api.domain.vo.order.datastatistics;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/9 10:27
 */
@Data
public class ModelOrderCommissionAnalysisVO implements Serializable {
    private static final long serialVersionUID = -1096827549948210125L;

    /**
     * 实时更新时间
     */
    @ApiModelProperty("实时更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date dateTime;

    /**
     * 订单总数
     */
    @ApiModelProperty("订单总数")
    private Long orderCount;

    /**
     * 订单平均佣金（$）
     */
    @ApiModelProperty("订单平均佣金（$）")
    private BigDecimal averageOrderCommission;

    /**
     * 订单佣金区间数组
     */
    @ApiModelProperty("订单佣金区间数组")
    private List<String> orderCommissionSectionArray;

    /**
     * 亚马逊影响者订单数数组
     */
    @ApiModelProperty("亚马逊影响者订单数数组")
    private List<Long> amazonInfluencerOrderCountArray;

    /**
     * 素人订单数数组
     */
    @ApiModelProperty("素人订单数数组")
    private List<Long> averagePeopleOrderCountArray;

    public ModelOrderCommissionAnalysisVO echo() {
        averageOrderCommission = averageOrderCommission == null ? BigDecimal.ZERO : averageOrderCommission;
        return this;
    }
}
