package com.ruoyi.system.api.domain.vo.order.task;

import com.ruoyi.system.api.domain.vo.order.OrderVideoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :售后单列表(带视频订单信息)
 * @create :2024-12-11 09:15
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AfterSaleListVO extends OrderVideoVO implements Serializable {
    private static final long serialVersionUID = -5379743835724105327L;
    @ApiModelProperty(value = "视频订单ID")
    private Long videoId;

    @ApiModelProperty(value = "售后单列表详情列表")
    private List<AfterSaleTaskDetailListVO> orderVideoTaskDetailList;

    @ApiModelProperty(value = "售后类型（1：重拍视频，2：补拍视频，3：要高清视频，4：原素材，5：重新上传，6：重新剪辑,11：重拍照片，12：要高清照片，13：补拍照片）")
    private List<Integer> afterSaleTypes;
}
