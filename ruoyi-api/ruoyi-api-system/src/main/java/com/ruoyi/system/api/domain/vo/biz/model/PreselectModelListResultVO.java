package com.ruoyi.system.api.domain.vo.biz.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 预选模特列表Service层返回结果VO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PreselectModelListResultVO {

    /**
     * 模特列表数据
     */
    private List<AddPreselectModelListVO> list;

    /**
     * 是否限制为优质模特
     */
    private Boolean isLimitedToQualityModel;
}
