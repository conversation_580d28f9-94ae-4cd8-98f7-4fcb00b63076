package com.ruoyi.system.api.domain.vo.biz.channel;

import com.ruoyi.common.core.enums.ChannelTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 渠道信息
 * @create :2024-09-27 14:40
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChannelVO implements Serializable {
    private static final long serialVersionUID = -5201595563518419386L;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty("渠道id")
    private Long id;

    @ApiModelProperty("渠道类型")
    private ChannelTypeEnum channelType;

    @ApiModelProperty("渠道专属链接Code")
    private String dedicatedLinkCode;

    @ApiModelProperty("专属企微二维码地址")
    private String weChatUrl;

    @ApiModelProperty("标签id")
    private String tagId;
}
