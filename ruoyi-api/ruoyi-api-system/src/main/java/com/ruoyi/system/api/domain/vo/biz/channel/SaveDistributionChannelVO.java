package com.ruoyi.system.api.domain.vo.biz.channel;

import com.ruoyi.common.core.enums.WxChatLoginStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-25 14:27
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SaveDistributionChannelVO implements Serializable {
    private static final long serialVersionUID = -1371426474696747892L;

    @ApiModelProperty("扫码状态")
    private WxChatLoginStatusEnum loginStatus;
}
