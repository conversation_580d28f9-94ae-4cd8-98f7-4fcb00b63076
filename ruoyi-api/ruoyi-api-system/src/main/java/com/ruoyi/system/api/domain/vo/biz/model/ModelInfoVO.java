package com.ruoyi.system.api.domain.vo.biz.model;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.domain.vo.UserVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/1 15:49
 */
@Data
public class ModelInfoVO implements Serializable {
    private static final long serialVersionUID = 197272994281452512L;
    /**
     * 模特id
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 模特账号
     */
    @ApiModelProperty(value = "模特账号")
    private String account;
    /**
     * 最后登录时间
     */
    @ApiModelProperty(value = "最后登录时间")
    private Date lastLoginTime;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 国家"（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）"
     */
    @ApiModelProperty(value = "国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    private Integer nation;

    /**
     * 模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)
     */
    @ApiModelProperty(value = "模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)")
    private Integer status;

    /**
     * 模特图片URI
     */
    @ApiModelProperty(value = "模特图片URI")
    private String modelPic;

    /**
     * 模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "模特类型", notes = "0:影响者,1:素人")
    private Integer type;

    /**
     * 收件人
     */
    @ApiModelProperty(value = "收件人")
    private String recipient;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String city;

    /**
     * 州
     */
    @ApiModelProperty(value = "州")
    private String state;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String detailAddress;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    private String zipcode;

    @ApiModelProperty(value = "手机号")
    private String phone;

    /** 待完成最高接受量 */
    @ApiModelProperty(value = "待完成最高接受量")
    @Excel(name = "待完成最高接受量")
    private Integer acceptability;
    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    private BigDecimal commission;
    /**
     * 关联对接人员
     */
    @ApiModelProperty(value = "关联对接人员")
    private List<UserVO> persons = new ArrayList<>();

    @ApiModelProperty(value = "平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private String platform;

    /**
     * 合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "合作深度（0:一般模特,1:优质模特,2:中度模特）", notes = "0:一般模特,1:优质模特,2:中度模特")
    private Integer cooperation;

    /**
     * 模特评分 (0.0-10.0)
     */
    @ApiModelProperty(value = "模特评分 (0.0-10.0)", notes = "模特评分 (0.0-10.0)", required = true)
    private BigDecimal cooperationScore;

    @ApiModelProperty(value = "拉黑人员列表")
    private List<Long> blacklistUserIds;

    /**
     * 是否展示（1:展示,0:不展示）
     */
    @ApiModelProperty(value = "是否展示（1:展示,0:不展示）")
    private Integer isShow;
}
