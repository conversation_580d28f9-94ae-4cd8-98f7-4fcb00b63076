version: "3.7"
services:
  ruoyi-gateway-daily:
    container_name: ruoyi-gateway-daily
    image: $IMAGE_TAG/ruoyi-gateway:latest-daily
    restart: always
    healthcheck:
      test: [ "C<PERSON>", "wget","--spider","-q", "http://127.0.0.1:8080/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-daily:
        ipv4_address: ***********
  ruoyi-auth-daily:
    container_name: ruoyi-auth-daily
    image: $IMAGE_TAG/ruoyi-auth:latest-daily
    restart: always
    healthcheck:
      test: [ "C<PERSON>", "wget","--spider","-q", "http://127.0.0.1:9200/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-daily:
        ipv4_address: ***********
  ruoyi-system-daily:
    container_name: ruoyi-modules-system-daily
    image: $IMAGE_TAG/ruoyi-system:latest-daily
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:9201/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-daily:
        ipv4_address: ***********
  biz-center-daily:
    container_name: biz-center-daily
    image: $IMAGE_TAG/biz-center:latest-daily
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7010/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-daily:
        ipv4_address: ***********
  order-service-daily:
    container_name: order-service-daily
    image: $IMAGE_TAG/order-service:latest-daily
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7011/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-daily:
        ipv4_address: ***********
  biz-job-daily:
    container_name: biz-job-daily
    image: $IMAGE_TAG/biz-job:latest-daily
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7211/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-daily:
        ipv4_address: ***********1
  order-job-daily:
    container_name: order-job-daily
    image: $IMAGE_TAG/order-job:latest-daily
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7210/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-daily:
        ipv4_address: ***********2
  job-admin-daily:
    container_name: job-admin-daily
    image: $IMAGE_TAG/job-admin:latest-daily
    restart: always
    ports:
      - "25789:8080"
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:8080/xxl-job-admin/actuator/health" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      XXL_ARGS: $XXL_ARGS
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-daily:
        ipv4_address: ************
  wnkx-message-daily:
    container_name: wnkx-message-daily
    image: $IMAGE_TAG/wnkx-message:latest-daily
    restart: always
    ports:
      - "19039:19019"
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:19019/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    environment:
      XXL_ARGS: $XXL_ARGS
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-daily:
        ipv4_address: ***********3
networks:
  wnkx-network-daily:
    external: true

