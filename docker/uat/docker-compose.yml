version: "3.7"
services:
  ruoyi-gateway-uat:
    container_name: ruoyi-gateway-uat
    image: $IMAGE_TAG/ruoyi-gateway:latest-uat
    restart: always
    healthcheck:
      test: [ "C<PERSON>", "wget","--spider","-q", "http://127.0.0.1:8080/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-test:
        ipv4_address: ***********
  ruoyi-auth-uat:
    container_name: ruoyi-auth-uat
    image: $IMAGE_TAG/ruoyi-auth:latest-uat
    restart: always
    healthcheck:
      test: [ "<PERSON><PERSON>", "wget","--spider","-q", "http://127.0.0.1:9200/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-test:
        ipv4_address: ***********
  ruoyi-system-uat:
    container_name: ruoyi-modules-system-uat
    image: $IMAGE_TAG/ruoyi-system:latest-uat
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:9201/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-test:
        ipv4_address: ***********
  biz-center-uat:
    container_name: biz-center-uat
    image: $IMAGE_TAG/biz-center:latest-uat
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7010/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-test:
        ipv4_address: ***********
  order-service-uat:
    container_name: order-service-uat
    image: $IMAGE_TAG/order-service:latest-uat
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7011/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
      EXTRA_ARGS: $ODS_ARGS
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-test:
        ipv4_address: ***********
  biz-job-uat:
    container_name: biz-job-uat
    image: $IMAGE_TAG/biz-job:latest-uat
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7211/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-test:
        ipv4_address: ***********1
  order-job-uat:
    container_name: order-job-uat
    image: $IMAGE_TAG/order-job:latest-uat
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7210/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-test:
        ipv4_address: ***********2
  job-admin-uat:
    container_name: job-admin-uat
    image: $IMAGE_TAG/job-admin:latest-uat
    restart: always
    ports:
      - "25589:8080"
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:8080/xxl-job-admin/acttestor/health" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      XXL_ARGS: $XXL_ARGS
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-test:
        ipv4_address: ************
  wnkx-message-uat:
    container_name: wnkx-message-uat
    image: $IMAGE_TAG/wnkx-message:latest-uat
    restart: always
    ports:
      - "19049:19019"
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:19019/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    environment:
      XXL_ARGS: $XXL_ARGS
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-test:
        ipv4_address: ***********3
networks:
  wnkx-network-test:
    external: true

