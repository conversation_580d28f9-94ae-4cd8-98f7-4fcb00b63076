version: "3.7"
services:
  ruoyi-gateway:
    image: $IMAGE_TAG/ruoyi-gateway:latest-prod
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:8080/hello" ]
      interval: 10s
      timeout: 2s
      retries: 3
      start_period: 60s
    ports:
      - "28080:8080"
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 1280M
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      wnkx.nacos.server-addr: $NACOS_PROD_ADDRESS
      APM_TOKEN: $APM_TOKEN
    logging:
      driver: loki
      options:
        loki-url: "http://logos.woniu.video/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      - wnkx-network
  ruoyi-auth:
    image: $IMAGE_TAG/ruoyi-auth:latest-prod
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:9200/hello" ]
      interval: 10s
      timeout: 2s
      retries: 3
      start_period: 60s
    deploy:
      replicas: 2
      placement:
        constraints: [ node.role == manager ]
      resources:
        limits:
          cpus: '2.0'
          memory: 2408M
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      wnkx.nacos.server-addr: $NACOS_PROD_ADDRESS
      APM_TOKEN: $APM_TOKEN
    logging:
      driver: loki
      options:
        loki-url: "http://logos.woniu.video/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      - wnkx-network
  ruoyi-system:
    image: $IMAGE_TAG/ruoyi-system:latest-prod
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:9201/hello" ]
      interval: 10s
      timeout: 2s
      retries: 3
      start_period: 60s
    deploy:
      replicas: 2
      placement:
        constraints: [ node.role == manager ]
      resources:
        limits:
          cpus: '2.0'
          memory: 2408M
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      wnkx.nacos.server-addr: $NACOS_PROD_ADDRESS
      APM_TOKEN: $APM_TOKEN
    logging:
      driver: loki
      options:
        loki-url: "http://logos.woniu.video/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      - wnkx-network
  biz-center:
    image: $IMAGE_TAG/biz-center:latest-prod
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7010/hello" ]
      interval: 10s
      timeout: 2s
      retries: 3
      start_period: 60s
    deploy:
      replicas: 2
      placement:
        constraints: [ node.role == manager ]
      resources:
        limits:
          cpus: '2.0'
          memory: 2408M
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      wnkx.nacos.server-addr: $NACOS_PROD_ADDRESS
      APM_TOKEN: $APM_TOKEN
    logging:
      driver: loki
      options:
        loki-url: "http://logos.woniu.video/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      - wnkx-network
  order-service:
    image: $IMAGE_TAG/order-service:latest-prod
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7011/hello" ]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 60s
    deploy:
      replicas: 3
      placement:
        constraints: [ node.role == manager ]
      resources:
        limits:
          cpus: '2.0'
          memory: 2408M
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      EXTRA_ARGS: $ODS_ARGS
      wnkx.nacos.server-addr: $NACOS_PROD_ADDRESS
      APM_TOKEN: $APM_TOKEN
    logging:
      driver: loki
      options:
        loki-url: "http://logos.woniu.video/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      - wnkx-network
  biz-job:
    image: $IMAGE_TAG/biz-job:latest-prod
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7211/hello" ]
      interval: 10s
      timeout: 2s
      retries: 3
      start_period: 60s
    deploy:
      replicas: 1
      placement:
        constraints: [ node.role == manager ]
      resources:
        limits:
          cpus: '1.0'
          memory: 1280M
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      wnkx.nacos.server-addr: $NACOS_PROD_ADDRESS
    logging:
      driver: loki
      options:
        loki-url: "http://logos.woniu.video/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      - wnkx-network
  order-job:
    image: $IMAGE_TAG/order-job:latest-prod
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7210/hello" ]
      interval: 10s
      timeout: 2s
      retries: 3
      start_period: 60s
    deploy:
      replicas: 1
      placement:
        constraints: [ node.role == manager ]
      resources:
        limits:
          cpus: '1.0'
          memory: 1280M
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      wnkx.nacos.server-addr: $NACOS_PROD_ADDRESS
    logging:
      driver: loki
      options:
        loki-url: "http://logos.woniu.video/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      - wnkx-network
  job-admin:
    image: $IMAGE_TAG/job-admin:latest-prod
    ports:
      - "25688:8080"
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:8080/xxl-job-admin/actuator/health" ]
      interval: 10s
      timeout: 2s
      retries: 3
      start_period: 60s
    deploy:
      replicas: 1
      placement:
        constraints: [ node.role == manager ]
      resources:
        limits:
          cpus: '2.0'
          memory: 768M
    environment:
      XXL_ARGS: $XXL_ARGS
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      wnkx.nacos.server-addr: $NACOS_PROD_ADDRESS
    logging:
      driver: loki
      options:
        loki-url: "http://logos.woniu.video/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      - wnkx-network
  wnkx-message-test:
    image: $IMAGE_TAG/wnkx-message:latest-prod
    ports:
      - "19019:19019"
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:19019/hello" ]
      interval: 10s
      timeout: 2s
      retries: 3
      start_period: 60s
    deploy:
      replicas: 1
      placement:
        constraints: [ node.role == manager ]
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    environment:
      XXL_ARGS: $XXL_ARGS
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    logging:
      driver: loki
      options:
        loki-url: "http://logos.woniu.video/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      - wnkx-network
networks:
  wnkx-network:
    external: true

