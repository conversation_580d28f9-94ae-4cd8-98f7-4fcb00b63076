version: "3.7"
services:
  ruoyi-gateway-test:
    container_name: ruoyi-gateway-test
    image: $IMAGE_TAG/ruoyi-gateway:latest-test
    restart: always
    healthcheck:
      test: [ "C<PERSON>", "wget","--spider","-q", "http://127.0.0.1:8080/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-uat:
        ipv4_address: ***********
  ruoyi-auth-test:
    container_name: ruoyi-auth-test
    image: $IMAGE_TAG/ruoyi-auth:latest-test
    restart: always
    healthcheck:
      test: [ "<PERSON><PERSON>", "wget","--spider","-q", "http://127.0.0.1:9200/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-uat:
        ipv4_address: ***********
  ruoyi-system-test:
    container_name: ruoyi-modules-system-test
    image: $IMAGE_TAG/ruoyi-system:latest-test
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:9201/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-uat:
        ipv4_address: ***********
  biz-center-test:
    container_name: biz-center-test
    image: $IMAGE_TAG/biz-center:latest-test
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7010/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-uat:
        ipv4_address: ***********
  order-service-test:
    container_name: order-service-test
    image: $IMAGE_TAG/order-service:latest-test
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7011/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-uat:
        ipv4_address: ***********
  biz-job-test:
    container_name: biz-job-test
    image: $IMAGE_TAG/biz-job:latest-test
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7211/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-uat:
        ipv4_address: ***********1
  order-job-test:
    container_name: order-job-test
    image: $IMAGE_TAG/order-job:latest-test
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7210/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-uat:
        ipv4_address: ***********2
  job-admin-test:
    container_name: job-admin-test
    image: $IMAGE_TAG/job-admin:latest-test
    restart: always
    ports:
      - "25389:8080"
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:8080/xxl-job-admin/actuator/health" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      XXL_ARGS: $XXL_ARGS
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-uat:
        ipv4_address: ************
  wnkx-message-test:
    container_name: wnkx-message-test
    image: $IMAGE_TAG/wnkx-message:latest-test
    restart: always
    ports:
      - "19029:19019"
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:19019/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    environment:
      XXL_ARGS: $XXL_ARGS
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-uat:
        ipv4_address: ************
networks:
  wnkx-network-uat:
    external: true

