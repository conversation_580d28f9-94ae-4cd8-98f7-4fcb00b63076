version: "3.7"
services:
  ruoyi-gateway:
    container_name: ruoyi-gateway
    image: $IMAGE_TAG/ruoyi-gateway:latest
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:8080/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 768M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network:
        ipv4_address: ***********
  ruoyi-auth:
    container_name: ruoyi-auth
    image: $IMAGE_TAG/ruoyi-auth:latest
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:9200/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 768M
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network:
        ipv4_address: ***********
  ruoyi-system:
    container_name: ruoyi-modules-system
    image: $IMAGE_TAG/ruoyi-system:latest
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:9201/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 768M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network:
        ipv4_address: ***********
  biz-center:
    container_name: biz-center
    image: $IMAGE_TAG/biz-center:latest
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7010/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 768M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network:
        ipv4_address: ***********
  order-service:
    container_name: order-service
    image: $IMAGE_TAG/order-service:latest
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7011/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 768M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network:
        ipv4_address: ***********
  biz-job:
    container_name: biz-job
    image: $IMAGE_TAG/biz-job:latest
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7211/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 768M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network:
        ipv4_address: ***********1
  order-job:
    container_name: order-job
    image: $IMAGE_TAG/order-job:latest
    restart: always
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:7210/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 768M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network:
        ipv4_address: ***********2
  job-admin:
    container_name: job-admin
    image: $IMAGE_TAG/job-admin:latest
    restart: always
    ports:
      - "25388:8080"
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:8080/xxl-job-admin/actuator/health" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    environment:
      XXL_ARGS: $XXL_ARGS
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 768M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network:
        ipv4_address: ************
  wnkx-message:
    container_name: wnkx-message
    image: $IMAGE_TAG/wnkx-message:latest
    restart: always
    ports:
      - "19019:19019"
    healthcheck:
      test: [ "CMD", "wget","--spider","-q", "http://127.0.0.1:19019/hello" ]
      interval: 10s
      timeout: 1s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1024M
    environment:
      XXL_ARGS: $XXL_ARGS
      DEPLOY_ARGS: $DEPLOY_ARGS
      JVM_ARGS: $JVM_ARGS
      APM_TOKEN: $APM_TOKEN
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network:
        ipv4_address: ************
networks:
  wnkx-network:
    external: true

