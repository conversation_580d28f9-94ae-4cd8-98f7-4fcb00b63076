<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ruoyi</groupId>
    <artifactId>ruoyi</artifactId>
    <version>3.5.0</version>

    <name>ruoyi</name>
    <url>http://www.ruoyi.vip</url>
    <description>若依微服务系统</description>

    <properties>
        <ruoyi.version>3.5.0</ruoyi.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>11</java.version>
        <spring-boot.version>2.6.13</spring-boot.version>
        <spring-cloud.version>2021.0.1</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.1</spring-cloud-alibaba.version>
        <alibaba.nacos.version>2.0.4</alibaba.nacos.version>
        <spring-boot-admin.version>2.6.6</spring-boot-admin.version>
        <spring-boot.mybatis>2.2.2</spring-boot.mybatis>
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <swagger.core.version>1.6.2</swagger.core.version>
        <knife4j.version>3.0.3</knife4j.version>
        <tobato.version>1.27.2</tobato.version>
        <kaptcha.version>2.3.2</kaptcha.version>
        <pagehelper.boot.version>1.4.1</pagehelper.boot.version>
        <druid.version>1.2.8</druid.version>
        <dynamic-ds.version>3.5.0</dynamic-ds.version>
        <commons.io.version>2.11.0</commons.io.version>
        <commons.fileupload.version>1.4</commons.fileupload.version>
        <velocity.version>2.3</velocity.version>
        <fastjson.version>1.2.80</fastjson.version>
        <jjwt.version>0.9.1</jjwt.version>
        <minio.version>8.2.2</minio.version>
        <poi.version>4.1.2</poi.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <transmittable-thread-local.version>2.12.2</transmittable-thread-local.version>
        <lombok.version>1.18.20</lombok.version>
        <mybatis-plus.version>3.5.2</mybatis-plus.version>
        <hutool.version>5.8.26</hutool.version>
        <amazon.awssdk.s3.version>2.17.57</amazon.awssdk.s3.version>
        <cos-sts_api.version>3.1.0</cos-sts_api.version>
        <cos_api.version>5.6.155</cos_api.version>
        <sonar.projectKey>woniu-world</sonar.projectKey>
        <sonar.projectName>woniu-world</sonar.projectName>
        <sonar.qualitygate.wait>true</sonar.qualitygate.wait>
        <okhttp.version>4.9.2</okhttp.version>
        <selenium.version>4.20.0</selenium.version>
        <tencent.tmt.version>3.1.1008</tencent.tmt.version>
        <tencent.sms.version>3.1.1163</tencent.sms.version>
        <html2pdf.version>4.0.3</html2pdf.version>
        <freemarker.version>2.6.13</freemarker.version>
        <redisson.version>3.16.0</redisson.version>
        <wnkx.module.version>0.0.1</wnkx.module.version>
        <alipay.version>4.39.234.ALL</alipay.version>
        <aliyun.version>1.2.3</aliyun.version>
        <apache.pdfbox.version>2.0.27</apache.pdfbox.version>
        <itext7.version>7.1.9</itext7.version>
        <jsonp.version>1.16.1</jsonp.version>
        <ip2region.version>2.7.0</ip2region.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Alibaba Nacos 配置 -->
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${alibaba.nacos.version}</version>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Mybatis 依赖配置 -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${spring-boot.mybatis}</version>
            </dependency>

            <!-- Mybatis-Plus 依赖配置 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- Swagger 依赖配置 -->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>

            <!-- knife4j -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- 文件上传工具类 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>

            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- Collection 增强Java集合框架 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>

            <!-- JSON 解析器和生成器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <!-- 核心模块 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-common-core</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-common-swagger</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-common-security</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 权限范围 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-common-datascope</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-common-log</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-common-redis</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- SpringBoot Boot Redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>


            <!-- 系统接口 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-api-system</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <!-- hutool -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- amazon awssdk s3 -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3</artifactId>
                <version>${amazon.awssdk.s3.version}</version>
            </dependency>

            <!-- cos-sts_api -->
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos-sts_api</artifactId>
                <version>${cos-sts_api.version}</version>
            </dependency>

            <!-- cos_api -->
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${cos_api.version}</version>
            </dependency>
            <!--spock-->
            <dependency>
                <groupId>org.spockframework</groupId>
                <artifactId>spock-core</artifactId>
                <version>2.3-groovy-3.0</version>
            </dependency>

            <!-- okhttp -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wnkx</groupId>
                <artifactId>wnkx-common-loadbalancer-spring-boot-starter</artifactId>
                <version>${wnkx.module.version}</version>
            </dependency>

            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-java</artifactId>
                <version>${selenium.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-tmt</artifactId>
                <version>${tencent.tmt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-sms</artifactId>
                <version>${tencent.sms.version}</version>
            </dependency>

            <!-- FreeMarker -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>
            <!-- html2pdf -->
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>html2pdf</artifactId>
                <version>${html2pdf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wnkx</groupId>
                <artifactId>wnkx-common-db</artifactId>
                <version>${wnkx.module.version}</version>
            </dependency>
            <!--        alipay-->
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>${alipay.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dypnsapi20170525</artifactId>
                <version>${aliyun.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${apache.pdfbox.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsonp.version}</version>
            </dependency>
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>ruoyi-auth</module>
        <module>ruoyi-gateway</module>
        <module>ruoyi-modules</module>
        <module>ruoyi-api</module>
        <module>ruoyi-common</module>
        <module>wnkx-business</module>
        <module>order-business</module>
        <module>wnkx-job</module>
        <module>wnkx-message</module>
    </modules>
    <packaging>pom</packaging>

    <dependencies>
        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <testSources>
                        <testSource>
                            <directory>${project.basedir}/src/test/java</directory>
                            <includes>
                                <include>**/*.groovy</include>
                            </includes>
                        </testSource>
                    </testSources>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.10</version>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <formats>
                                <format>XML</format>
                            </formats>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>
    <profiles>
        <profile>
            <id>liquibase</id>
            <build>
                <resources>
                    <resource>
                        <directory>db</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
                <plugins>
                    <plugin>
                        <groupId>org.liquibase</groupId>
                        <artifactId>liquibase-maven-plugin</artifactId>
                        <version>4.17.0</version>
                        <inherited>false</inherited>
                        <configuration>
                            <propertyFile>liquibase.properties</propertyFile>
                            <promptOnNonLocalDatabase>false</promptOnNonLocalDatabase>
                        </configuration>
                        <dependencies>
                            <dependency>
                                <groupId>mysql</groupId>
                                <artifactId>mysql-connector-java</artifactId>
                                <version>8.0.29</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
