package com.ruoyi.common.redis.configure;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/7/11 16:42
 */
@Configuration
@ConfigurationProperties(prefix = "redisson")
@Data
public class RedissonProperties {
    /**
     * uri
     */
    @Value("${spring.redis.host}")
    private String host;
    /**
     * 端口
     */
    @Value("${spring.redis.port}")
    private Integer port;
    /**
     * 密码
     */
    @Value("${spring.redis.password}")
    private String password;

    /**
     * 数据库
     */
    @Value("${spring.redis.database:0}")
    private Integer database;

    /**
     * 连接池大小
     */
    private Integer connectionPoolSize;
    /**
     * 最小空闲连接数
     */
    private Integer connectionMinimumIdleSize;
    /**
     * Redisson的线程数
     */
    private Integer threads;
    /**
     * Netty的线程数
     */
    private Integer nettyThreads;
}
