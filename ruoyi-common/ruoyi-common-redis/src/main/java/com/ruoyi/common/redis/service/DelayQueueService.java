package com.ruoyi.common.redis.service;


import com.ruoyi.common.core.constant.CacheConstants;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.TimeUnit;

@Slf4j
public abstract class DelayQueueService {

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private RedisService redisService;

    /**
     * 添加任务到延迟队列
     *
     * @param queueName      队列名称
     * @param value          对象信息json
     * @param delayInSeconds 过期时间秒
     * @param timeUnit       时间单位
     */
    public void addTask(String queueName, String value, long delayInSeconds, TimeUnit timeUnit) {
        RQueue<String> queue = redissonClient.getQueue(queueName);
        RDelayedQueue<String> delayedQueue = redissonClient.getDelayedQueue(queue);
        delayedQueue.offer(value, delayInSeconds, timeUnit);
        log.info("添加任务成功，队列名称：{}，对象信息：{}，过期时间：{}，过期时间单位，{}", queueName, value, delayInSeconds, timeUnit);
    }

    /**
     * 添加任务到延迟队列（如果当前值存在 则刷新过期时间）
     *
     * @param queueName      队列名称
     * @param value          对象信息json
     * @param delayInSeconds 过期时间
     * @param timeUnit       时间单位
     */
    public void addTaskIfAbsent(String queueName, String value, long delayInSeconds, TimeUnit timeUnit) {
        RQueue<String> queue = redissonClient.getQueue(queueName);
        RDelayedQueue<String> delayedQueue = redissonClient.getDelayedQueue(queue);

        // 检查队列中是否已经存在该值
        if (queue.contains(value) || delayedQueue.contains(value)) {
            // 从队列中移除该值
            delayedQueue.remove(value);
            log.info("队列{}已存在值，删除旧值：{}", queueName, value);
        }

        // 添加新的值到延迟队列
        delayedQueue.offer(value, delayInSeconds, timeUnit);
        log.info("添加任务成功，队列名称：{}，对象信息：{}，过期时间：{}，过期时间单位，{}", queueName, value, delayInSeconds, timeUnit);
    }

    /**
     * 删除延迟队列中的某个值
     *
     * @param queueName 队列名称
     * @param value     需要删除的对象信息json
     * @return 是否删除成功
     */
    public void removeTask(String queueName, String value) {
        // 获取延迟队列
        RQueue<String> queue = redissonClient.getQueue(queueName);
        RDelayedQueue<String> delayedQueue = redissonClient.getDelayedQueue(queue);

        // 尝试删除指定的元素
        boolean remove = delayedQueue.remove(value);

        log.info("队列名称：{}，删除任务：{}，删除状态：{}", queueName, value, remove ? "成功" : "失败");
    }


    /**
     * 开始执行延迟队列
     *
     * @param queueName 队列名称
     */
    public abstract void startProcessing(String queueName);

    /**
     * 停止执行延迟队列
     *
     * @param queueName 队列名称
     */
    public void stopProcessing(String queueName) {
        redisService.releaseLock(CacheConstants.DELAY_QUEUE_PROCESS_QUEUE_LOCK_KEY + queueName);
        log.warn("队列已关闭：{}", queueName);
    }

    // @Bean
    // public void monitor() {
    //     redisService.releaseLocksByPrefix(CacheConstants.DELAY_QUEUE_PROCESS_QUEUE_LOCK_KEY);
    //     log.info("重启时释放所有锁");
    //
    //     Set<String> cacheSet = redisService.getCacheSet(CacheConstants.DELAY_QUEUE_RUNNING_KEY);
    //     for (String queueName : cacheSet) {
    //         startProcessing(queueName);
    //     }
    //     log.info("重启时监听所有队列{}", cacheSet);
    // }
}

