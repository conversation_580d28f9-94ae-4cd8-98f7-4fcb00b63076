package com.ruoyi.common.redis.configure;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class RedissonConfig {
    private final RedissonProperties redissonProperties;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        config.useSingleServer()
                .setAddress("redis://" + redissonProperties.getHost() + StrUtil.COLON + redissonProperties.getPort())
                .setPassword(redissonProperties.getPassword())
                .setDatabase(redissonProperties.getDatabase())
                .setConnectionPoolSize(redissonProperties.getConnectionPoolSize())  // 连接池大小
                .setConnectionMinimumIdleSize(redissonProperties.getConnectionMinimumIdleSize());  // 最小空闲连接数


        // 设置Redisson线程池配置
        config.setThreads(redissonProperties.getThreads());  // Redisson的线程数
        config.setNettyThreads(redissonProperties.getNettyThreads());  // Netty的线程数

        return Redisson.create(config);
    }
}
