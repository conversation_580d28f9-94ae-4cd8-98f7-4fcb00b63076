package com.ruoyi.common.redis.service;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.constant.CacheConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * spring redis 工具类
 *
 * <AUTHOR>
 **/
@SuppressWarnings(value = {"unchecked", "rawtypes"})
@Component
public class RedisService {
    @Autowired
    public RedisTemplate redisTemplate;

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key   缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key      缓存的键值
     * @param value    缓存的值
     * @param timeout  时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Long timeout, final TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等,默认单位为秒
     *
     * @param key     缓存的键值
     * @param value   缓存的值
     * @param timeout 时间
     */
    public <T> void setCacheObject(final String key, final T value, final Long timeout) {
        redisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout) {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit) {
        return redisTemplate.expire(key, timeout, unit);
    }

    /**
     * 获取有效时间
     *
     * @param key Redis键
     * @return 有效时间
     */
    public long getExpire(final String key) {
        return redisTemplate.getExpire(key);
    }

    /**
     * 判断 key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public <T> T getCacheObject(final String key) {
        ValueOperations<String, T> operation = redisTemplate.opsForValue();
        return operation.get(key);
    }

    /**
     * 删除单个对象
     *
     * @param key
     */
    public boolean deleteObject(final String key) {
        return redisTemplate.delete(key);
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     * @return
     */
    public long deleteObject(final Collection collection) {
        return redisTemplate.delete(collection);
    }

    /**
     * 删除指定前缀的数据
     *
     * @param prefix 前缀
     */
    public void deleteKeysByPrefix(String prefix) {
        Set<String> keys = scanKeysByPrefix(prefix);
        if (!keys.isEmpty()) {
            deleteObject(keys);
        }
    }

    private Set<String> scanKeysByPrefix(String prefix) {
        Set<String> keys = new HashSet<>();
        ScanOptions scanOptions = ScanOptions.scanOptions().match(prefix + "*").count(1000).build();

        Cursor<byte[]> cursor = (Cursor<byte[]>) redisTemplate.execute((RedisCallback<Cursor<byte[]>>) connection -> connection.scan(scanOptions));

        while (cursor != null && cursor.hasNext()) {
            keys.add(new String(cursor.next()));
        }

        return keys;
    }


    /**
     * 缓存List数据
     *
     * @param key      缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> long setCacheList(final String key, final List<T> dataList) {
        Long count = redisTemplate.opsForList().rightPushAll(key, dataList);
        return count == null ? 0 : count;
    }

    /**
     * 获得缓存的list对象
     *
     * @param key 缓存的键值
     * @return 缓存键值对应的数据
     */
    public <T> List<T> getCacheList(final String key) {
        return redisTemplate.opsForList().range(key, 0, -1);
    }

    /**
     * 缓存Set
     *
     * @param key     缓存键值
     * @param dataSet 缓存的数据
     * @return 缓存数据的对象
     */
    public <T> BoundSetOperations<String, T> setCacheSet(final String key, final Set<T> dataSet) {
        BoundSetOperations<String, T> setOperation = redisTemplate.boundSetOps(key);
        Iterator<T> it = dataSet.iterator();
        while (it.hasNext()) {
            setOperation.add(it.next());
        }
        return setOperation;
    }

    /**
     * 获得缓存的set
     *
     * @param key
     * @return
     */
    public <T> Set<T> getCacheSet(final String key) {
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 缓存Map
     *
     * @param key
     * @param dataMap
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap) {
        if (dataMap != null) {
            redisTemplate.opsForHash().putAll(key, dataMap);
        }
    }

    /**
     * 获得缓存的Map
     *
     * @param key
     * @return
     */
    public <T> Map<String, T> getCacheMap(final String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 获得缓存的hash的字段数量
     *
     * @param key
     * @return
     */
    public Long getCacheHashCount(final String key) {
        return redisTemplate.opsForHash().size(key);
    }

    /**
     * 往Hash中存入数据
     *
     * @param key   Redis键
     * @param hKey  Hash键
     * @param value 值
     */
    public <T> void setCacheMapValue(final String key, final String hKey, final T value) {
        redisTemplate.opsForHash().put(key, hKey, value);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    public <T> T getCacheMapValue(final String key, final String hKey) {
        HashOperations<String, String, T> opsForHash = redisTemplate.opsForHash();
        return opsForHash.get(key, hKey);
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key   Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public <T> List<T> getMultiCacheMapValue(final String key, final Collection<Object> hKeys) {
        return redisTemplate.opsForHash().multiGet(key, hKeys);
    }

    /**
     * 删除多个Hash中的数据
     *
     * @param key   Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public void deleteMultiCacheMapValue(final String key, final Object[] hKeys) {
        redisTemplate.opsForHash().delete(key, hKeys);
    }

    /**
     * 获得缓存的基本对象列表
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public Collection<String> keys(final String pattern) {
        return redisTemplate.keys(pattern);
    }

    public Boolean setNx(String key, Object value, Long second) {
        return this.redisTemplate.opsForValue().setIfAbsent(key, value, Duration.ofSeconds(second));
    }

    public Boolean setNx(String key, Object value) {
        return this.redisTemplate.opsForValue().setIfAbsent(key, value);
    }

    /**
     * 加锁
     **/
    public Boolean getLock(String key, Long second) {
        return setNx(key, 1, second);
    }

    /**
     * 批量加锁
     **/
    public void getLocks(String lockPrefix, List<Long> keys, Long second) {
        List<Long> lockKeys = new ArrayList<>();
        try {
            for (Long key : keys) {
                Assert.isTrue(setNx(lockPrefix + key, 1, second), "数据处理中，请稍后重试~");
                lockKeys.add(key);
            }
        } catch (Exception e) {
            for (Long lockKey : lockKeys) {
                releaseLock(lockPrefix + lockKey);
            }
        }
    }

    /**
     * 加锁
     **/
    public Boolean getLock(String key) {
        return setNx(key, 1);
    }

    /**
     * 释放锁
     **/
    public Long releaseLock(String key) {
        String luaScript = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        RedisScript<Long> redisScript = new DefaultRedisScript<>(luaScript, Long.class);
        return (Long) this.redisTemplate.execute(redisScript, Collections.singletonList(key), 1);
    }

    public Long releaseLock(String key, String value) {
        String luaScript = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        RedisScript<Long> redisScript = new DefaultRedisScript<>(luaScript, Long.class);
        return (Long) this.redisTemplate.execute(redisScript, Collections.singletonList(key), value);
    }

    /**
     * 释放指定前缀的所有锁
     *
     * @param prefix 锁的前缀
     * @return 被删除的锁的数量
     */
    public Long releaseLocksByPrefix(String prefix) {
        String luaScript = "local keys = redis.call('keys', KEYS[1]) " +
                "for i=1,#keys,5000 do " +
                "redis.call('del', unpack(keys, i, math.min(i+4999, #keys))) " +
                "end " +
                "return #keys";
        RedisScript<Long> redisScript = new DefaultRedisScript<>(luaScript, Long.class);
        return (Long) redisTemplate.execute(redisScript, Collections.singletonList(prefix + "*"));
    }

    /**
     * 计数器+1
     *
     * @param key 字符串前缀
     * @return 对象列表
     */
    public Long setIncr(final String key) {
        //加1
        return redisTemplate.getConnectionFactory().getConnection().incr(
                redisTemplate.getKeySerializer().serialize(key)
        );
    }

    /**
     * 计数器+1
     *
     * @param key 字符串前缀
     * @return 对象列表
     */
    public Long setIncr(final String key, final long timeout, final TimeUnit unit) {
        // 加1
        Long incr = redisTemplate.getConnectionFactory().getConnection().incr(
                redisTemplate.getKeySerializer().serialize(key)
        );
        redisTemplate.expire(key, timeout, unit);
        return incr;
    }

    /**
     * 计数器减一
     * @param key 缓存key
     * @param minus 是否允许减为负数
     */
    public void decrementCounter(final String key, final boolean minus) {
        if (!minus) {
            Integer cacheIncr = getCacheIncr(key);
            if (cacheIncr <= 0) {
                return;
            }
        }
        // 减1操作
        redisTemplate.getConnectionFactory().getConnection().decr(
                redisTemplate.getKeySerializer().serialize(key)
        );
    }

    /**
     * 获取计数器的值
     */
    public Integer getCacheIncr(final String key) {
        // 获取 ValueOperations 实例
        ValueOperations<String, Integer> valueOperations = redisTemplate.opsForValue();

        // 获取当前计数器的值
        return Optional.ofNullable(valueOperations.get(key)).orElse(0);
    }


    public Boolean persist(final String key) {
        return redisTemplate.persist(key);
    }

    public <K, V> Boolean setZSet(final K key, final V value, double score) {
        return redisTemplate.opsForZSet().add(key, value, score);
    }

    public <K, V> Set<V> getZSet(final K key, double min, double max, long offset, long count) {
        return redisTemplate.opsForZSet().rangeByScore(key, min, max, offset, count);
    }

    public <K, T> Long getZSetRank(final K key, T object) {
        return redisTemplate.opsForZSet().rank(key, object);
    }

    public <K, T> Set<T> getZSetRange(final K key, long start, long end) {
        return redisTemplate.opsForZSet().range(key, start, end);
    }

    public <K> Long removeZSet(K key, Object... values) {
        return redisTemplate.opsForZSet().remove(key, values);
    }

    public <K> Long removeRangeZSet(K key, long start, long end) {
        return redisTemplate.opsForZSet().removeRange(key, start, end);
    }

    /**
     * 获取集合数量
     *
     * @param key
     * @return
     */
    public <K> Long zCard(K key) {
        return redisTemplate.opsForZSet().zCard(key);
    }

    /**
     * 向频道发布消息
     *
     * @param channel 频道
     * @param message 消息
     * @return true成功 false失败
     */
    public boolean publish(String channel, Object message) {
        if (StrUtil.isBlank(channel)) {
            return false;
        }
        try {
            redisTemplate.convertAndSend(channel, message);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取计数最小的项并将其计数加1
     */
    public String getChannelContactUserWithMinimumCount() {
        List<String> keys = (List<String>) redisTemplate.keys(CacheConstants.CURRENT_CHANNEL_ACTIVE_KEY_PREFIX + "*")
                .stream()
                .collect(Collectors.toList());

        if (keys.isEmpty()) {
            return null;
        }

        String minItem = null;
        int minCount = Integer.MAX_VALUE;

        // 找出计数最小的项
        for (String key : keys) {
            int count = getCacheObject(key);
            if (count < minCount) {
                minCount = count;
                minItem = key.substring(CacheConstants.CURRENT_CHANNEL_ACTIVE_KEY_PREFIX.length());
            }
        }

        // 增加计数
        if (minItem != null) {
            String key = CacheConstants.CURRENT_CHANNEL_ACTIVE_KEY_PREFIX + minItem;
            redisTemplate.opsForValue().increment(key, 1);
        }

        return minItem;
    }
}
