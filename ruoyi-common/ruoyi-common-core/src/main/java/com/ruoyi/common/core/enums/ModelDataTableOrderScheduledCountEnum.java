package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/9 10:56
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ModelDataTableOrderScheduledCountEnum {

    SELECT1(1, "0单", BigDecimal.ZERO, BigDecimal.ZERO),
    SELECT2(2, "1-5单", BigDecimal.valueOf(1), BigDecimal.valueOf(5)),
    SELECT3(3, "6-10单", BigDecimal.valueOf(6), BigDecimal.valueOf(10)),
    SELECT4(4, "11-15单", BigDecimal.valueOf(11), BigDecimal.valueOf(15)),
    SELECT5(5, "16-20单", BigDecimal.valueOf(16), BigDecimal.valueOf(20)),
    SELECT6(6, "21单以上", BigDecimal.valueOf(21), BigDecimal.valueOf(99999)),
    ;

    private Integer code;
    private String label;
    private BigDecimal begin;
    private BigDecimal end;

    public static ModelDataTableOrderScheduledCountEnum getByCode(Integer code) {
        for (ModelDataTableOrderScheduledCountEnum value : ModelDataTableOrderScheduledCountEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return SELECT1;
    }
}
