package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * 开票状态枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderInvoiceStatusEnum {
    UN_PERFECT(0, "开票信息待完善"),
    UN_INVOICE(1, "待开票"),
    UN_CONFIRM(2, "待确认"),
    DELIVER(3, "已投递"),
    CANCELLATION(4, "已作废"),
    TO_BE_REVIEWED(5, "待审核"),
    CANCELED(6, "已取消"),
    REOPENED(7, "已重开"),
    ;

    private Integer code;
    private String label;

    public static Map<String, Integer> getCodeMap() {
        Map<String, Integer> statusMap = new HashMap<>();
        for (OrderInvoiceStatusEnum status : OrderInvoiceStatusEnum.values()) {
            statusMap.put(status.name(), status.getCode());
        }
        return statusMap;
    }
}
