package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 模特年龄层枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ModelAgeGroupEnum {

    INFANT(1, "婴幼儿"),
    CHILD(2, "儿童"),
    ADULT(3, "成年人"),
    AGED(4, "老年人"),
    ;
    private Integer code;
    private String label;

    /**
     * 根据code找label
     */
    public static String getLabel(Integer code) {
        for (ModelAgeGroupEnum e : ModelAgeGroupEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getLabel();
            }
        }
        return null;
    }
}
