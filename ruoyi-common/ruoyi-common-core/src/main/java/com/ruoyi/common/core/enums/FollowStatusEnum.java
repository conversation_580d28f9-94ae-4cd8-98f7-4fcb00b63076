package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;


/**
 * 跟进状态枚举
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum FollowStatusEnum {

    NEED_HANDLE(1, "需处理", 0),
    TEMP_HOLD(2, "暂不处理", 0),
    SHIP(10, "已发货", 1),
    NEED_FOLLOW_UP(11, "需跟进", 1),
    MODEL_CONFIRM_PEND(12, "模特待确认", 1),
    NO_FOLLOW_NEED(13, "无需跟进", 1),
    CLOSE(14, "已结束", 1),
    DELETE(99, "已删除", 0),
    ;

    private Integer code;
    private String label;
    private Integer logisticStatus;

    public static String getLabelByCode(Integer code) {
        for (FollowStatusEnum e : FollowStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getLabel();
            }
        }
        return null;
    }
}

