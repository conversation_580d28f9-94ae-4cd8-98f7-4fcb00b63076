package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/18
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderPoolMatchCountEnum {
    ONE(1, "一", "=", 1),

    TWO(2, "二", "=", 2),

    THREE(3, "三", "=", 3),

    FOUR(4, "四", "=", 4),
    FIVE(5, "五", "=", 5),
    FIVE_OR_MORE(6, "五次以上", ">", 5),
    ;

    private Integer code;
    private String label;
    private String logic;
    private Integer count;

    public static OrderPoolMatchCountEnum getByCode(Integer code) {
        for (OrderPoolMatchCountEnum value : OrderPoolMatchCountEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return ONE;
    }
}
