package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderStatusEnum {

    UN_PAY(1, "待支付"),
    UN_CHECK(2, "待审核"),
    UN_CONFIRM(3, "待确认"),
    UN_MATCH(4, "待匹配"),
    NEED_FILLED(5, "需发货"),
    UN_FINISHED(6, "待完成"),
    NEED_CONFIRM(7, "需确认"),
    FINISHED(8, "已完成"),
    TRADE_CLOSE(9, "交易关闭"),
    ;
    private Integer code;
    private String label;

    /**
     * 根据code找label
     */
    public static String getLabel(Integer code) {
        for (OrderStatusEnum e : OrderStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getLabel();
            }
        }
        return null;
    }

    public static Map<String, Integer> getCodeMap() {
        Map<String, Integer> statusMap = new HashMap<>();
        for (OrderStatusEnum status : OrderStatusEnum.values()) {
            statusMap.put(status.name(), status.getCode());
        }
        return statusMap;
    }
    public static List<Integer> getOrderUnFinishStatusList() {
        return List.of(
                UN_CONFIRM.code,
                UN_MATCH.code,
                NEED_FILLED.code,
                UN_FINISHED.code,
                NEED_CONFIRM.code,
                FINISHED.code);
    }
}
