package com.ruoyi.common.core.web.page;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
@Data
@ApiModel(value="表格分页数据对象", description="表格分页数据对象")
@AllArgsConstructor
@NoArgsConstructor
public class PageInfo<T> implements Serializable {

    private static final long serialVersionUID = -8561167185399371731L;
    /** 总记录数 */
    @ApiModelProperty(value = "总记录数")
    private long total;

    /** 列表数据 */
    @ApiModelProperty(value = "列表数据")
    private List<T> rows;

    @ApiModelProperty(value = "扩展参数")
    private String totalAmount;
}
