package com.ruoyi.common.core.enums;

import com.ruoyi.common.core.domain.dto.OrderByDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 模特数据表列表列枚举
 *
 * <AUTHOR>
 * @date 2025/7/8 9:40
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ModelDataTableOrderScheduledRecordListColumnEnum {
    COLUMN1(1, "来源", "shoot_model_add_type", true),
    COLUMN2(2, "排单时间", "submit_time", false),
    COLUMN3(3, "排单方式", "CASE WHEN schedule_type = 1 THEN commissionSort WHEN schedule_type = 2 AND carry_type = 1 THEN -1 WHEN schedule_type = 2 AND carry_type = 2 THEN -2  END", false),
    COLUMN4(4, "素材反馈时长", "feedbackDuration", false),
    COLUMN5(5, "订单状态", "status", false),
    COLUMN6(6, "订单完成时间", "statusTime", false),
    ;

    private Integer code;
    private String columnName;
    private String columnSortSqlName;
    private Boolean reversal;

    public static String getColumnSortSqlNameByCode(Integer code) {
        for (ModelDataTableOrderScheduledRecordListColumnEnum value : ModelDataTableOrderScheduledRecordListColumnEnum.values()) {
            if (value.code.equals(code)) {
                return value.columnSortSqlName;
            }
        }
        return "";
    }

    public static String getColumnSortWayByCode(Integer code, String sortWay) {
        for (ModelDataTableOrderScheduledRecordListColumnEnum value : ModelDataTableOrderScheduledRecordListColumnEnum.values()) {
            if (value.code.equals(code)) {
                if (value.reversal) {
                    return OrderByDto.DIRECTION.DESC.value().equals(sortWay) ? OrderByDto.DIRECTION.ASC.value() : OrderByDto.DIRECTION.DESC.value();
                }
                return sortWay;
            }
        }
        return OrderByDto.DIRECTION.DESC.value();
    }
}
