package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-27 18:42
 **/
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BalanceSourceTypeEnum {
    //退款类型（1:补偿、2:取消订单、3:取消选配）
    CANCEL_ORDER_INCOME(2, "取消订单收入", 0 ),
    CANCEL_ORDER_PART_INCOME(1, "补偿订单收入", 0),
    CANCEL_CHOOSE_INCOME(3, "取消选配收入", 0),
    ORDER_SPEND(4, "视频订单支出", 1),
    MEMBER_SPEND(5, "会员订单支出", 1),
    PAYOUT_SPEND(6, "线下余额提现", 1),
    PREPAY_INCOME(7, "线下钱包充值收入", 0),
    ONLINE_RECHARGE(8, "线上钱包充值收入",0),
    ;
    private Integer code;
    private String label;
    //0-收入 1-支出
    private Integer type;

    // 根据code获取枚举
    public static BalanceSourceTypeEnum getByCode(Integer code) {
        for (BalanceSourceTypeEnum sourceType : values()) {
            if (sourceType.code.equals(code)) {
                return sourceType;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }
}
