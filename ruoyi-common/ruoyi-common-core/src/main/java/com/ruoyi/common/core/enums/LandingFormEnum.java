package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 市场渠道落地形式枚举
 *
 * <AUTHOR>
 * @date 2024/9/25 10:52
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum LandingFormEnum {

    OFFICIAL_WEBSITE(1, "官网首页"),
    ENTERPRISE_MICRO_CUSTOMER_SERVICE(2, "添加企微客服"),
    ;

    private Integer code;
    private String label;
}
