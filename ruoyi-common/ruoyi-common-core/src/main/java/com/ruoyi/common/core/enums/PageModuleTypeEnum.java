package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-22 09:23
 **/
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PageModuleTypeEnum {
    BANNER(1, "海报"),
    QUICK_LINK_LESS(2, "金刚区"),
    QUICK_LINK_MORE(3, "瓷片区"),
    ;
    private Integer code;
    private String label;

    public static PageModuleTypeEnum getPageModuleTypeEnum(Integer code){
        for (PageModuleTypeEnum e : PageModuleTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }

        return null;
    }
}
