package com.ruoyi.common.core.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 模特类型枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ModelTypeEnum {

    INFLUENT(0, "亚马逊影响者"),
    AVERAGE_PEOPLE(1, "素人创作者"),
    ALL(3, "都可以"),
    ;
    private Integer code;
    private String label;

    /**
     * 根据code找label
     */
    public static String getLabel(Integer code) {
        for (ModelTypeEnum e : ModelTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getLabel();
            }
        }
        return StrUtil.EMPTY;
    }

}
