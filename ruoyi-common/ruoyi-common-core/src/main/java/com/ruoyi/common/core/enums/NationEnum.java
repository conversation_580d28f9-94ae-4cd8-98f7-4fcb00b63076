package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 国家枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum NationEnum {
    UK(1, "英国", "局部区域"),
    CANADA(2, "加拿大", "省"),
    GERMANY(3, "德国", "省份/地区"),
    FRANCE(4, "法国", "省"),
    IT(5, "意大利", "省份/地区"),
    ES(6, "西班牙", "省份/地区"),
    USA(7, "美国", "州"),
    ;
    private Integer code;
    private String label;
    private String stateName;
    /**
     * 根据code找label
     */
    public static String getLabel(Integer code) {
        for (NationEnum e : NationEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getLabel();
            }
        }
        return null;
    }    /**
     * 根据code找label
     */
    public static NationEnum getNationEnum(Integer code) {
        for (NationEnum e : NationEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
