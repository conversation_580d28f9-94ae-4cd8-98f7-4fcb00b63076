package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 翻译枚举值
 *
 * <AUTHOR>
 * @date 2024/6/11
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum TranslateEnum {
    EN(0, "en"),
    ZH(1, "zh");
    private Integer code;
    private String label;

    /**
     * 根据code找label
     */
    public static String getLabel(Integer code) {
        for (TranslateEnum e : TranslateEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getLabel();
            }
        }
        return null;
    }
}
