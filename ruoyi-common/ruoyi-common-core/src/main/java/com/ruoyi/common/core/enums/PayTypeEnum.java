package com.ruoyi.common.core.enums;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * 支付方式枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PayTypeEnum {
    WECHAT(1, "微信"),
    ALIPAY(2, "支付宝"),
    UNIONPAY(3, "云闪付/银联"),
    DCEP(4, "数字人民币"),
    BANK(5, "银行"),
    PUBLIC(6, "对公"),
    FULL_CURRENCY(7, "全币种"),
    BALANCE(10, "余额"),
    WECHAT_BALANCE(11, "微信+余额"),
    ALIPAY_BALANCE(12, "支付宝+余额"),
    UNIONPAY_BALANCE(13, "云闪付/银联+余额"),
    DCEP_BALANCE(14, "数字人民币+余额"),
    BANK_BALANCE(15, "银行卡+余额"),
    PUBLIC_BALANCE(16, "对公+余额"),
    FULL_CURRENCY_BALANCE(17, "全币种+余额"),
    ;
    private Integer code;
    private String label;

    public static void assemblePayType(Set<Integer> payTypes) {
        if (CollUtil.isEmpty(payTypes)) {
            return;
        }

        if (payTypes.contains(PayTypeEnum.WECHAT.getCode())) payTypes.add(PayTypeEnum.WECHAT_BALANCE.getCode());
        if (payTypes.contains(PayTypeEnum.ALIPAY.getCode())) payTypes.add(PayTypeEnum.ALIPAY_BALANCE.getCode());
        if (payTypes.contains(PayTypeEnum.UNIONPAY.getCode())) payTypes.add(PayTypeEnum.UNIONPAY_BALANCE.getCode());
        if (payTypes.contains(PayTypeEnum.DCEP.getCode())) payTypes.add(PayTypeEnum.DCEP_BALANCE.getCode());
        if (payTypes.contains(PayTypeEnum.BANK.getCode())) payTypes.add(PayTypeEnum.BANK_BALANCE.getCode());
        if (payTypes.contains(PayTypeEnum.PUBLIC.getCode())) payTypes.add(PayTypeEnum.PUBLIC_BALANCE.getCode());
        if (payTypes.contains(PayTypeEnum.BALANCE.getCode())) {
            payTypes.add(PayTypeEnum.WECHAT_BALANCE.getCode());
            payTypes.add(PayTypeEnum.ALIPAY_BALANCE.getCode());
            payTypes.add(PayTypeEnum.UNIONPAY_BALANCE.getCode());
            payTypes.add(PayTypeEnum.DCEP_BALANCE.getCode());
            payTypes.add(PayTypeEnum.BANK_BALANCE.getCode());
            payTypes.add(PayTypeEnum.PUBLIC_BALANCE.getCode());
        }
    }
    public static void assemblePayType(List<Integer> payTypes) {
        if (CollUtil.isEmpty(payTypes)) {
            return;
        }

        if (payTypes.contains(PayTypeEnum.WECHAT.getCode())) {
            payTypes.add(PayTypeEnum.WECHAT_BALANCE.getCode());
        }

        if (payTypes.contains(PayTypeEnum.ALIPAY.getCode())) {
            payTypes.add(PayTypeEnum.ALIPAY_BALANCE.getCode());
        }

        if (payTypes.contains(PayTypeEnum.UNIONPAY.getCode())) {
            payTypes.add(PayTypeEnum.UNIONPAY_BALANCE.getCode());
        }

        if (payTypes.contains(PayTypeEnum.DCEP.getCode())) {
            payTypes.add(PayTypeEnum.DCEP_BALANCE.getCode());
        }

        if (payTypes.contains(PayTypeEnum.BANK.getCode())) {
            payTypes.add(PayTypeEnum.BANK_BALANCE.getCode());
        }

        if (payTypes.contains(PayTypeEnum.PUBLIC.getCode())) {
            payTypes.add(PayTypeEnum.PUBLIC_BALANCE.getCode());
        }
        if (payTypes.contains(PayTypeEnum.FULL_CURRENCY.getCode())) {
            payTypes.add(PayTypeEnum.FULL_CURRENCY_BALANCE.getCode());
        }

        if (payTypes.contains(PayTypeEnum.BALANCE.getCode())) {
            payTypes.add(PayTypeEnum.WECHAT_BALANCE.getCode());
            payTypes.add(PayTypeEnum.ALIPAY_BALANCE.getCode());
            payTypes.add(PayTypeEnum.UNIONPAY_BALANCE.getCode());
            payTypes.add(PayTypeEnum.DCEP_BALANCE.getCode());
            payTypes.add(PayTypeEnum.BANK_BALANCE.getCode());
            payTypes.add(PayTypeEnum.PUBLIC_BALANCE.getCode());
            payTypes.add(PayTypeEnum.FULL_CURRENCY_BALANCE.getCode());
        }
    }

    public static PayTypeEnum getPayTypeEnumByCode(Integer code) {
        for (PayTypeEnum payTypeEnum : PayTypeEnum.values()) {
            if (payTypeEnum.getCode().equals(code)) {
                return payTypeEnum;
            }
        }
        return PayTypeEnum.WECHAT;
    }

    /**
     * 支付方式明细枚举
     */
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum PayTypeDetailEnum {
        FULL_CURRENCY_OTHER(7, 701, "其他平台/银行"),
        FULL_CURRENCY_WORLD_FIRST(7, 702, "万里汇"),
        ;

        private Integer parentCode;
        private Integer code;
        private String label;

        public static PayTypeDetailEnum getPayTypeDetailEnumByCode(Integer code) {
            for (PayTypeDetailEnum payTypeDetailEnum : PayTypeDetailEnum.values()) {
                if (payTypeDetailEnum.getCode().equals(code)) {
                    return payTypeDetailEnum;
                }
            }
            return PayTypeDetailEnum.FULL_CURRENCY_OTHER;
        }
    }
}
