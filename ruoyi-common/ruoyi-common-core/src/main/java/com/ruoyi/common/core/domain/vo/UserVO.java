package com.ruoyi.common.core.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/21 16:28
 */
@Data
@ApiModel("用户返回对象")
public class UserVO implements Serializable {
    private static final long serialVersionUID = 3500362594207002434L;
    @ApiModelProperty(value = "用户名")
    private String name;
    @ApiModelProperty(value = "用户id")
    private Long id;
    @ApiModelProperty(value = "手机号")
    private String phonenumber;
}
