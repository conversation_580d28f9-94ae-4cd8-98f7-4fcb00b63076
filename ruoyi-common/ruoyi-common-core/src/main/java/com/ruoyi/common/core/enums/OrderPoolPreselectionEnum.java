package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/16 18:06
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderPoolPreselectionEnum {
    ZERO(1, "0人", 0, 0),

    ONE_TO_THREE(2, "1~3人", 1, 3),

    FOUR_TO_NINE(3, "4~9人", 4, 9),

    MORE_THAN_TEN(4, "10人以上", 10, 999),
    ;

    private Integer code;
    private String label;
    private Integer begin;
    private Integer end;

    public static OrderPoolPreselectionEnum getByCode(Integer code) {
        for (OrderPoolPreselectionEnum value : OrderPoolPreselectionEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return ZERO;
    }
}
