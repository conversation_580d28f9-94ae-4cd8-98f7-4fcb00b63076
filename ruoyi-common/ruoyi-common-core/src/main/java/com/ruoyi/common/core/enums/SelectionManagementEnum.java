package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 预选管理权限枚举
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SelectionManagementEnum {
    
    /**
     * 查看自己 - 只能查看自己关联的模特数据
     */
    VIEW_OWN(0, "查看自己"),
    
    /**
     * 查看全部 - 可以查看所有模特数据（等同于admin权限）
     */
    VIEW_ALL(1, "查看全部");

    private Integer code;
    private String info;
}
