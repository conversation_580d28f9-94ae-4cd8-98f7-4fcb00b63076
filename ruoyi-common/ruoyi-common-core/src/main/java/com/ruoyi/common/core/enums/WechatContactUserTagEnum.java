package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 审核状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum WechatContactUserTagEnum {

    SYS_WEBSITE_1(1, "w", "Sys-Website-1", "官网标签"),
    SYS_VIP_1(2, "vip", "Sys-Vip-1", "vip页面标签"),
    ACQUISITION(3, "Acquisition", "Acquisition", "获客链接");
    private Integer code;
    private String shortTag;
    private String tag;
    private String label;


    public static List<WechatContactUserTagEnum> getAllTag() {
        return List.of(WechatContactUserTagEnum.values());
    }

    public static WechatContactUserTagEnum getTagByShortTag(String shortTag) {
        for (WechatContactUserTagEnum tagEnum : WechatContactUserTagEnum.values()) {
            if (tagEnum.shortTag.equals(shortTag)) {
                return tagEnum;
            }
        }
        return SYS_WEBSITE_1;
    }

    public static String getShortTagByCode(Integer urlType) {
        for (WechatContactUserTagEnum tagEnum : WechatContactUserTagEnum.values()) {
            if (tagEnum.code.equals(urlType)) {
                return tagEnum.shortTag;
            }
        }
        return null;
    }
}
