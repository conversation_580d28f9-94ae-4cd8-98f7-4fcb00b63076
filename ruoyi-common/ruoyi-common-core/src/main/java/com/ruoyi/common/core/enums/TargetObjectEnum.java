package com.ruoyi.common.core.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 视频关联内容目标对象枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum TargetObjectEnum {
    MERCHANT(1, "商家"),

    OPERATION(2, "运营"),
    MODEL(3, "模特"),
    ;


    private Integer code;
    private String label;

    public static String merchantAndOperation() {
        return MERCHANT.getCode() + StrUtil.COMMA + OPERATION.getCode();
    }

    public static String operationAndModel() {
        return OPERATION.getCode() + StrUtil.COMMA + MODEL.getCode();
    }

    public static String all() {
        return MERCHANT.getCode() + StrUtil.COMMA + OPERATION.getCode() + StrUtil.COMMA + MODEL.getCode();
    }
}
