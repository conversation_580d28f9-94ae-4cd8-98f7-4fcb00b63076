package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 审核状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BusinessPrepayAuditStatusEnum {

    PRE_APPROVE(0, "待处理"),
    APPROVE(1, "审核同意"),
    CANCEL(2, "审核拒绝"),
    CLOSE(3, "交易关闭"),
    ;
    private Integer code;
    private String label;

}
