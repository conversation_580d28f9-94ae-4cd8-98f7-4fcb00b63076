package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/9 14:36
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderTaskTypeEnum {
    AFTER_SALE(1, "售后单"),
    WORK_ORDER(2, "工单"),
    ;
    private Integer code;
    private String label;

    public static OrderTaskTypeEnum getByCode(Integer code) {
        for (OrderTaskTypeEnum status : OrderTaskTypeEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return AFTER_SALE;
    }
}
