package com.ruoyi.common.core.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 照片数量类型枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PicCountEnum {
    TWO(1, "2张/$10", 2),
    FIVE(2, "5张/$20", 5),
    ;

    private Integer code;
    private String label;
    private Integer value;

    public static String getLabel(Integer code) {
        for (PicCountEnum e : PicCountEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getLabel();
            }
        }
        return StrUtil.EMPTY;
    }
    public static Integer getValue(Integer code) {
        for (PicCountEnum e : PicCountEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getValue();
            }
        }
        return 0;
    }
}
