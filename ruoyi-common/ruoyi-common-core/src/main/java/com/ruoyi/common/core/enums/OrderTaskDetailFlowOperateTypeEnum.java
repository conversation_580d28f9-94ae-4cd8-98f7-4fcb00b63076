package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单工单流转记录操作类型枚举
 *
 * <AUTHOR>
 * @date 2024/6/24 11:06
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderTaskDetailFlowOperateTypeEnum {

    CREATE_AFTER_SALES(OrderTaskTypeEnum.AFTER_SALE, OrderTaskStatusEnum.UN_HANDLE, 101, "创建售后"),
    REFUSE_AFTER_SALE(OrderTaskTypeEnum.AFTER_SALE, OrderTaskStatusEnum.REJECT, 102, "拒绝售后"),
    CONFIRM_AFTER_SALE(OrderTaskTypeEnum.AFTER_SALE, OrderTaskStatusEnum.HANDLE_ING, 103, "确认售后"),
    AFTER_SALE_FEEDBACK_MATERIAL_TO_THE_BUSINESS(OrderTaskTypeEnum.AFTER_SALE, OrderTaskStatusEnum.HANDLE, 104, "反馈素材给商家"),
    APPLY_FOR_CANCELLATION(OrderTaskTypeEnum.AFTER_SALE, OrderTaskStatusEnum.APPLICATION_FOR_CANCELLATION, 105, "申请取消"),
    WITHDRAW_THE_APPLICATION(OrderTaskTypeEnum.AFTER_SALE, OrderTaskStatusEnum.HANDLE_ING, 106, "撤销申请"),
    CANCEL_AFTER_SALE(OrderTaskTypeEnum.AFTER_SALE, OrderTaskStatusEnum.CLOSE, 107, "取消售后"),
    REFUSE_TO_CANCEL(OrderTaskTypeEnum.AFTER_SALE, OrderTaskStatusEnum.HANDLE_ING, 108, "拒绝取消"),
    AFTER_SALE_REOPEN(OrderTaskTypeEnum.AFTER_SALE, OrderTaskStatusEnum.UN_HANDLE, 109, "重新打开"),
//    CANCEL_WORK_ORDER(OrderTaskTypeEnum.AFTER_SALE, null, 110, "取消工单"),
    AFTER_SALE_FINISHED(OrderTaskTypeEnum.AFTER_SALE, OrderTaskStatusEnum.HANDLE, 111, "完成售后单"),
    AGREE_SALE_FINISHED(OrderTaskTypeEnum.AFTER_SALE, OrderTaskStatusEnum.CLOSE, 112, "同意取消"),
    CLOSE_AFTER_SALE(OrderTaskTypeEnum.AFTER_SALE, OrderTaskStatusEnum.CLOSE, 113, "关闭售后"),

    CREATE_WORK_ORDER(OrderTaskTypeEnum.WORK_ORDER, OrderTaskStatusEnum.UN_HANDLE, 201, "创建工单"),
    CLOSE_WORK_ORDER(OrderTaskTypeEnum.WORK_ORDER, OrderTaskStatusEnum.CLOSE, 202, "关闭工单"),
    TRANSFER_TO_A_HANDLER(OrderTaskTypeEnum.WORK_ORDER, null, 203, "转处理人处理"),
    WORK_ORDER_REOPEN(OrderTaskTypeEnum.WORK_ORDER, OrderTaskStatusEnum.UN_HANDLE, 204, "重新打开"),
    REISSUE(OrderTaskTypeEnum.WORK_ORDER, null, 205, "补发"),
    COMPENSATION(OrderTaskTypeEnum.WORK_ORDER, null, 206, "补偿"),
    //    FORWARD_TO_THE_SUBMITTER(OrderTaskTypeEnum.WORK_ORDER, null, 207, "转提交人处理"),
    REJECT_WORK_ORDER(OrderTaskTypeEnum.WORK_ORDER, OrderTaskStatusEnum.REJECT, 208, "拒绝工单"),
    //    REFUSAL_OF_A_REDUCTION(OrderTaskTypeEnum.WORK_ORDER, null, 209, "拒绝降要求"),
//    GRANT_A_REDUCTION(OrderTaskTypeEnum.WORK_ORDER, null, 210, "同意降要求"),
    FINISHED_WORK_ORDER(OrderTaskTypeEnum.WORK_ORDER, null, 211, "完结工单"),
    WORK_ORDER_FEEDBACK_MATERIAL_TO_THE_BUSINESS(OrderTaskTypeEnum.WORK_ORDER, null, 212, "反馈素材给商家"),

    ROLLBACK_ORDER(null, null, 601, "订单回退"),
    ;
    private OrderTaskTypeEnum taskType;
    private OrderTaskStatusEnum orderTaskStatus;
    private Integer code;
    private String label;

    public static OrderTaskDetailFlowOperateTypeEnum getByCode(Integer code) {
        for (OrderTaskDetailFlowOperateTypeEnum operateTypeEnum : values()) {
            if (operateTypeEnum.getCode().equals(code)) {
                return operateTypeEnum;
            }
        }
        return null; // 如果未找到匹配的枚举值，则返回null
    }

    /**
     * 提交人权限
     */
    public static final List<Integer> SUBMIT_PERMISSIONS = new ArrayList<>();

    /**
     * 处理人权限
     */
    public static final List<Integer> ASSIGNEE_PERMISSIONS = new ArrayList<>();

    /**
     * 状态按钮权限
     */
    public static final Map<Integer, List<Integer>> ORDER_TASK_STATUS_OPERATE_PERMISSIONS;
    static {
        //售后单提交人权限：
        SUBMIT_PERMISSIONS.add(CANCEL_AFTER_SALE.code);
        SUBMIT_PERMISSIONS.add(REFUSE_TO_CANCEL.code);
        SUBMIT_PERMISSIONS.add(AFTER_SALE_REOPEN.code);
        SUBMIT_PERMISSIONS.add(AGREE_SALE_FINISHED.code);
        //工单提交人权限：
        SUBMIT_PERMISSIONS.add(CLOSE_WORK_ORDER.code);
        SUBMIT_PERMISSIONS.add(WORK_ORDER_REOPEN.code);

        //售后单处理人权限：
        ASSIGNEE_PERMISSIONS.add(REFUSE_AFTER_SALE.code);
        ASSIGNEE_PERMISSIONS.add(CONFIRM_AFTER_SALE.code);
        ASSIGNEE_PERMISSIONS.add(AFTER_SALE_FEEDBACK_MATERIAL_TO_THE_BUSINESS.code);
        ASSIGNEE_PERMISSIONS.add(APPLY_FOR_CANCELLATION.code);
        ASSIGNEE_PERMISSIONS.add(WITHDRAW_THE_APPLICATION.code);

        //工单处理人权限:
        ASSIGNEE_PERMISSIONS.add(REISSUE.code);
        ASSIGNEE_PERMISSIONS.add(COMPENSATION.code);
        ASSIGNEE_PERMISSIONS.add(TRANSFER_TO_A_HANDLER.code);
        ASSIGNEE_PERMISSIONS.add(REJECT_WORK_ORDER.code);
        ASSIGNEE_PERMISSIONS.add(FINISHED_WORK_ORDER.code);
        ASSIGNEE_PERMISSIONS.add(WORK_ORDER_FEEDBACK_MATERIAL_TO_THE_BUSINESS.code);

        ORDER_TASK_STATUS_OPERATE_PERMISSIONS = new HashMap<>();
        //待处理 按钮权限:
        List<Integer> unHandelList = new ArrayList<>();

        //已拒绝
        List<Integer> rejectList = new ArrayList<>();

        //处理中
        List<Integer> handleIngList = new ArrayList<>();

        //申请取消中
        List<Integer> applicationForCancellationList = new ArrayList<>();

        //售后单

        //待处理
        unHandelList.add(CANCEL_AFTER_SALE.code);
        unHandelList.add(REFUSE_AFTER_SALE.code);
        unHandelList.add(CONFIRM_AFTER_SALE.code);
        //已拒绝
        rejectList.add(AFTER_SALE_REOPEN.code);
        //处理中
        handleIngList.add(CANCEL_AFTER_SALE.code);
        handleIngList.add(AFTER_SALE_FEEDBACK_MATERIAL_TO_THE_BUSINESS.code);
        handleIngList.add(AFTER_SALE_FINISHED.code);
        handleIngList.add(APPLY_FOR_CANCELLATION.code);
        //申请取消中
        applicationForCancellationList.add(WITHDRAW_THE_APPLICATION.code);
        applicationForCancellationList.add(REFUSE_TO_CANCEL.code);
        applicationForCancellationList.add(AGREE_SALE_FINISHED.code);


        //工单
        unHandelList.add(CLOSE_WORK_ORDER.code);
        unHandelList.add(TRANSFER_TO_A_HANDLER.code);
        unHandelList.add(REISSUE.code);
        unHandelList.add(COMPENSATION.code);
        unHandelList.add(REJECT_WORK_ORDER.code);
        unHandelList.add(FINISHED_WORK_ORDER.code);
        unHandelList.add(WORK_ORDER_FEEDBACK_MATERIAL_TO_THE_BUSINESS.code);

        rejectList.add(WORK_ORDER_REOPEN.code);

        ORDER_TASK_STATUS_OPERATE_PERMISSIONS.put(OrderTaskStatusEnum.UN_HANDLE.getCode(), unHandelList);
        ORDER_TASK_STATUS_OPERATE_PERMISSIONS.put(OrderTaskStatusEnum.REJECT.getCode(), rejectList);
        ORDER_TASK_STATUS_OPERATE_PERMISSIONS.put(OrderTaskStatusEnum.HANDLE_ING.getCode(), handleIngList);
        ORDER_TASK_STATUS_OPERATE_PERMISSIONS.put(OrderTaskStatusEnum.APPLICATION_FOR_CANCELLATION.getCode(), applicationForCancellationList);

    }

}
