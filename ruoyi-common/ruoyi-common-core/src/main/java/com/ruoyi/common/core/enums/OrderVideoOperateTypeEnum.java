package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 视频订单操作记录类型
 *
 * <AUTHOR>
 * @date 2024/10/14 11:16
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderVideoOperateTypeEnum {
    ADD_CART("加入购物车", "#操作人#加入购物车", 0),
    CREATE_ORDER("生成订单", "#操作人#创建订单", 0),
    MODIFY_THE_EXCHANGE_RATE("修改汇率", "蜗牛运营#操作人#修改了汇率", 0),
    ALTERATION_COST("修改订单费用", "#操作人#修改{}", 0),
    ALTERATION_COST_REMARK("修改订单费用", "备注：{}", 1),
    PAYMENT_SUCCESS("支付成功", "通过{}完成支付", 0),
    SUBMIT_PAYMENT("提交支付", "#操作人#提交{}{}支付凭证", 0),
    PAYMENT_APPROVED("支付审核通过", "财务#操作人#通过{}{}审核", 0),
    PAYMENT_ANOMALY("支付异常", "财务#操作人#标记{}{}支付异常", 1),
    REVIEW_ORDER("审核订单", "客服#操作人#完成订单审核", 1),
    ORDER_MARK("订单标记", "客服#操作人#完成订单标记", 1),
    NON_PAYMENT_CANCEL_ORDER("订单取消", "{}#操作人#申请取消订单", 0),
    REOPEN_ORDER("开启订单", "{}#操作人#重新开启订单", 0),
    PAYMENT_CANCEL_ORDER("订单取消", "{}#操作人#申请取消订单，退款金额￥{}", 0),
    SUBMIT_MODEL("提交模特", "客服#操作人#提交拍摄{}", 0),
    CHANGE_MODEL("变更模特", "#操作人#变更意向模特{}", 0),
    CHANGE_MODEL_NO_CHOICE("变更模特", "#操作人#变更意向模特时，没有选择模特", 0),
    MATCHING_FEEDBACK("匹配情况反馈", "客服#操作人#提交匹配情况反馈", 0),
    CONSENT_MATCHING_FEEDBACK("同意匹配反馈", "#操作人#同意匹配调整", 0),
    REJECT_MATCH_FEEDBACK("拒绝匹配反馈", "#操作人#拒绝匹配调整", 0),
    MODIFY_ORDER_INFORMATION("修改订单", "客服#操作人#修改了订单", 1),
    THE_MATCHING_CONDITION_IS_MODIFIED("匹配情况反馈修改", "客服#操作人#商家同意后修改订单信息", 0),
    CONFIRM_MODEL("确认模特", "#操作人#确认匹配模特", 0),
    MERCHANT_FLAG_DELIVERY("标记发货", "#操作人#已标记发货", 0),
    MERCHANT_DELIVERY("商家发货", "{}#操作人#已填写发货信息", 0),
    PAUSE_MATCH("暂停匹配", "客服#操作人#暂停匹配模特，原因是{}", 1),
    REDELIVERY("补发快递", "客服#操作人#补发快递", 0),
    MODEL_FEEDBACK_MATERIAL("模特反馈素材", "模特#操作人#提交素材链接", 1),
    CUSTOMER_SERVICE_FEEDBACK_MATERIAL("客服反馈素材", "客服#操作人#提交素材链接", 1),
    ORDER_REFUND_REPARATION("订单退款", "#操作人#{}，补偿￥{}（退款成功后在钱包管理中可查看），补偿理由：{}", 1),
    ORDER_REFUND_REPARATION_AND_CANCEL("订单退款且取消", "#操作人#{}，补偿￥{}（退款成功后在钱包管理中可查看），补偿理由：{}", 1),
    ORDER_REFUND_CANCEL_ORDER("订单退款", "#操作人#{}，退款￥{}（退款成功后在钱包管理中可查看），退款原因：{}", 1),
    ORDER_REFUND_CANCEL_OPTION("订单退款", "#操作人#{}退{}张，退款￥{}（退款成功后在钱包管理中可查看），退款理由：{}", 1),
    REFUND_THROUGH("退款通过", "{}申请的{}已由财务#操作人#审批通过，退款￥{}", 1),
    REFUND_THROUGH_REPARATION("退款通过", "{}申请的{}已由财务#操作人#审批通过，补偿￥{}", 1),
    REFUND_NOT_APPROVED("退款不通过", "{}申请的{}已由财务#操作人#审批不通过", 1),
    MATERIAL_SUBMISSION_MERCHANT("素材提交商家", "剪辑#操作人#提交素材给商家", 0),
    MODIFY_MATERIAL_SUBMISSION_MERCHANT("修改素材提交给商家", "剪辑#操作人#修改素材提交给商家", 0),
    BACK_CONFIRMATION_MATERIAL("确认素材", "客服#操作人#确认素材内容", 0),
    MERCHANT_CONFIRMATION_MATERIAL("商家确认素材", "#操作人#确认素材内容", 0),
    INITIATING_AFTER_SALE("发起售后单", "客服#操作人#发起售后单", 1),
    INITIATING_WORK_ORDER("发起工单", "客服#操作人#发起工单", 1),
    UPLOAD_LINK("上传链接", "剪辑#操作人#填写上传链接", 0),
    ORDER_COMPLETION("订单完成", "订单已完成", 0),
    ORDER_AUTO_COMPLETION("订单完成", "订单已自动完成", 0),
    ROLLBACK_ORDER("订单回退", "订单由#操作人#将{}回退至待匹配", 0),
    ROLLBACK_ORDER_REMARK("订单回退", "回退原因：{}", 1),
    UNCARRY("取消携带", "受主携带订单回退影响，取消被携带关系，变更为排单类型，佣金为0{}", 1),
    CONTINUE_MODEL_MATCHING("继续匹配模特", "客服#操作人#重新匹配模特", 1),
    CONFIRM_RECEIPT("手动确认收货", "客服#操作人#确认收货", 1),
    CONFIRM_RECEIPT_SYSTEM("系统更新更新签收时间", "17track返回新签收时间", 1),
    CANCEL_LOGISTIC("物流作废", "#操作人# 作废物流（单号：{}），作废原因：{}", 1),
    UPDATE_LOGISTIC("修改物流单号", "#操作人# 修改物流单号（原单号：{}），修改原因：{}", 1),
    UPLOAD_PICTURES("上传图片", "客服#操作人#上传图片", 1)
    ;


    private String eventName;
    private String eventContent;
    private Integer isPublic;
    public static Integer NO_PUBLIC = 0;
    public static Integer PUBLIC = 1;

    public static OrderVideoOperateTypeEnum getByEventName(String eventName) {
        for (OrderVideoOperateTypeEnum value : values()) {
            if (value.eventName.equals(eventName)) {
                return value;
            }
        }
        return null;
    }
}
