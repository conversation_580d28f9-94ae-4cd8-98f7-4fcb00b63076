package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 意向模特淘汰类型
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PreselectModelOustTypeEnum {

    MERCHANT_REJECTION(1, "商家驳回"),
    CUSTOMER_SERVICE_ELIMINATION(2, "客服淘汰"),
    WANT_NOT(3, "模特不想要"),
    NOT_SELECTED(4, "未被选中"),
    TIMEOUT_INTENTION_NOT_SELECTED(5, "超时未选择意向"),
    ROLLBACK_ORDER(6, "订单回退"),
    ;
    private Integer code;
    private String label;
}
