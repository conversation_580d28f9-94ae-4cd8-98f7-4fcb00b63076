package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * 会员订单订单状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderMemberStatusEnum {

    UN_PAY(1, "待支付"),
    UN_CHECK(2, "待审核"),
    UN_CONFIRM(3, "交易成功"),
    UN_MATCH(4, "交易关闭");
    private Integer code;
    private String label;

    /**
     * 根据code找label
     */
    public static String getLabel(Integer code) {
        for (OrderMemberStatusEnum e : OrderMemberStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getLabel();
            }
        }
        return null;
    }

    public static Map<String, Integer> getCodeMap() {
        Map<String, Integer> statusMap = new HashMap<>();
        for (OrderMemberStatusEnum status : OrderMemberStatusEnum.values()) {
            statusMap.put(status.name(), status.getCode());
        }
        return statusMap;
    }
}
