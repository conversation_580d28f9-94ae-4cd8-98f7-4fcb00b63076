package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单佣金区间枚举
 *
 * <AUTHOR>
 * @date 2025/5/9 11:04
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderCommissionSectionEnum {
    FIRST_GEAR(1, "0-5", new BigDecimal("0"), new BigDecimal("5")),
    SECOND_GEAR(2, "6-10", new BigDecimal("6"), new BigDecimal("10")),
    THIRD_GEAR(3, "11-15", new BigDecimal("11"), new BigDecimal("15")),
    FOURTH_GEAR(4, "16-20", new BigDecimal("16"), new BigDecimal("20")),
    FIFTH_GEAR(5, "20以上", new BigDecimal("21"), new BigDecimal("9999999")),
    ;

    private Integer code;
    private String label;
    private BigDecimal begin;
    private BigDecimal end;

    public static OrderCommissionSectionEnum getBySection(BigDecimal section) {
        for (OrderCommissionSectionEnum value : values()) {
            if (section.compareTo(value.begin) >= 0 && section.compareTo(value.end) <= 0) {
                return value;
            }
        }
        return FIRST_GEAR;
    }

    public static List<String> getLabels() {
        List<String> labels = new ArrayList<>();

        for (OrderCommissionSectionEnum value : values()) {
            labels.add(value.label);
        }
        return labels;
    }
}
