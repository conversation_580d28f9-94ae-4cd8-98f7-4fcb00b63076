package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Title: LayoutTypeEnum
 * <AUTHOR>
 * @Package com.ruoyi.common.core.enums
 * @Date 2024/8/20 14:50
 * @description: 布局类型
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum LayoutTypeEnum {
    VIDEO_SHOP_WINDOW_LAYOUT_DOUBLE(1, "一行2个", 2),
    VIDEO_SHOP_WINDOW_LAYOUT_ONE(2, "一行1个", 1),

    QUICK_LINK_MORE_LAYOUT_THREE(1, "左1右2", 3),
    QUICK_LINK_MORE_LAYOUT_DOUBLE(2, "左1右1", 2),
    ;
    private Integer code;
    private String label;
    private Integer number;

    public static Integer getQuickLinkMoreLayout(Integer code){
        List<LayoutTypeEnum> list = List.of(QUICK_LINK_MORE_LAYOUT_THREE, QUICK_LINK_MORE_LAYOUT_DOUBLE);

        for (LayoutTypeEnum item : list){
            if (item.getCode().equals(code)){
                return item.getNumber();
            }
        }
        return 0;
    }

}
