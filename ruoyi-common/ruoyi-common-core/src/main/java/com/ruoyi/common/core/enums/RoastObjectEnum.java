package com.ruoyi.common.core.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 吐槽对象枚举
 *
 * <AUTHOR>
 * @date 2024/8/9 14:12
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum RoastObjectEnum {
    PIC(1, "视频"),
    VIDEO(2, "客服"),
    ELSE(3, "其他");

    private Integer code;
    private String label;

    public static  RoastObjectEnum findByCode(Integer code) {
        for (RoastObjectEnum value : RoastObjectEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
