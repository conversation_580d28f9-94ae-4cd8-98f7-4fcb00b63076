package com.ruoyi.common.core.enums.statistics;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会员退款单数占比
 * @create :2025-06-19 10:32
 **/
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BusinessExitEnum {
    SEVEN_DAYS_NO_REASON(0, "七天无理由退会"),
    NON_SEVEN_DAYS(1, "非七天无理由退会");

    private Integer code;
    private String label;
}
