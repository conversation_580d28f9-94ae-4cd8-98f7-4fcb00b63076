package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/10 9:55
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum LogicalSymbolEnum {
    GE(1,">=", "大于等于"),
    GT(2,">", "大于"),
    EQ(3,"=", "等于"),
    LT(4,"<", "小于"),
    LE(5,"<=", "小于等于"),
    ;
    private Integer code;
    private String logicalSymbol;
    private String label;

    public static String getLogicalSymbol(Integer code) {
        for (LogicalSymbolEnum logicalSymbolEnum : LogicalSymbolEnum.values()) {
            if (logicalSymbolEnum.getCode().equals(code)) {
                return logicalSymbolEnum.getLogicalSymbol();
            }
        }
        return null;
    }
}
