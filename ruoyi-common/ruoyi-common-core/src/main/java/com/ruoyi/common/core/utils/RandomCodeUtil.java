package com.ruoyi.common.core.utils;

import java.security.SecureRandom;

/**
 * <AUTHOR>
 * @date 2024/9/25 11:30
 */
public class RandomCodeUtil {

    // 定义字符集合，排除o, c, h, a, n, e, l（不区分大小写）
    private static final String CHARACTERS = "23456789" +
            "BDFGJKMPQRSTUVWXYZ" +  // 排除O, C, H, A, N, E, L, I
            "bdfgjkmpqrstuvwxyz";    // 排除o, c, h, a, n, e, l, i

    public static final String ALL_CHARACTERS = "0123456789" +
            "ABCDEFGHIJKLMNOPQRSTUVWXYZ" +  // 排除O, C, H, A, N, E, L
            "abcdefghijklmnopqrstuvwxyz";    // 排除o, c, h, a, n, e, l

    public static final String ALL_NUMBER = "0123456789";

    public static final String SIMPLE_NUMBER = "23456789";

    public static final String ALL_LETTER = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

    public static final String ALL_CAPITAL_LETTER = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    public static final String CAPITAL_LETTER = "BDFGJKMPQRSTUVWXYZ";

    public static final String ALL_SMALL_LETTER = "abcdefghijklmnopqrstuvwxyz";

    // SecureRandom 更加安全
    private static final SecureRandom random = new SecureRandom();

    // 生成随机指定位数字符
    public static String generateRandomCode(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(CHARACTERS.charAt(random.nextInt(CHARACTERS.length())));
        }
        return sb.toString();
    }
    // 生成随机指定位数字符
    public static String generateRandomCode(String choose, int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(choose.charAt(random.nextInt(choose.length())));
        }
        return sb.toString();
    }

    public static void main(String[] args) {

    }
}
