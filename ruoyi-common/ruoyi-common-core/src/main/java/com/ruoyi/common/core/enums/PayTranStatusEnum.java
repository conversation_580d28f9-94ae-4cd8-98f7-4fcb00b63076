package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 支付平台枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PayTranStatusEnum {

    CLOSED(0, "CLOSED","TRADE_CLOSED", "交易关闭"),
    SUCCESS(1, "SUCCESS", "TRADE_SUCCESS", "交易成功"),
    REFUND(2, "REFUND", "-", "退款中"),
    NOTPAY(3, "NOTPAY", "-", "未支付"),
    REVOKED(4, "REVOKED", "-", "已撤销"),
    USERPAYING(5, "USERPAYING", "WAIT_BUYER_PAY", "用户支付中"),
    PAYERROR(6, "PAYERROR", "-", "支付失败"),
    UNKNOWN(7, "UNKNOWN", "-", "未知状态"),
    FINISH(8, "UNKNOWN", "TRADE_FINISHED", "交易结束"),
    AUDIT(9, "AUDIT", "-", "提交审核中"),
    EXCHANGE_RATE(10, "EXCHANGE_RATE", "-", "汇率异常"),
    ANOTHER_PAY(11, "ANOTHER_PAY", "-", "代付中"),
    ANOTHER_PAY_FAILURE(12, "ANOTHER_PAY_FAILURE", "-", "代付失效"),
    ORDER_MERGE(13, "ORDER_MERGE", "-", "订单已合并"),
    ;
    private Integer code;
    private String value;
    private String aliPayStatus;
    private String desc;


    public static String getDesc(Integer code) {
        for (PayTranStatusEnum e : PayTranStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getDesc();
            }
        }
        return UNKNOWN.getDesc();
    }

    public static String getValue(Integer code) {
        for (PayTranStatusEnum e : PayTranStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getValue();
            }
        }
        return UNKNOWN.getValue();
    }

    public static PayTranStatusEnum getEnum(String value) {
        for (PayTranStatusEnum e : PayTranStatusEnum.values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return UNKNOWN;
    }
    public static PayTranStatusEnum getEnumByAliPayStatus(String aliPayStatus) {
        for (PayTranStatusEnum e : PayTranStatusEnum.values()) {
            if (e.getAliPayStatus().equals(aliPayStatus)) {
                return e;
            }
        }
        return UNKNOWN;
    }
}
