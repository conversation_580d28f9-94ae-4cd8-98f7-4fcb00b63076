package com.ruoyi.common.core.enums;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 模特佣金单位枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CommissionUnitEnum {
    USD("USD", "美金", new BigDecimal("1")),
    CAD("CAD", "加币", new BigDecimal("0.73")),
    GBP("GBP", "英镑", new BigDecimal("1.35")),
    EUR("EUR", "欧元", new BigDecimal("1.15")),
    ;

    private String unit;
    private String label;
    private BigDecimal toUSDRate;

    public static String getLabelByUnit(String unit) {
        for (CommissionUnitEnum value : values()) {
            if (value.unit.equals(unit)) {
                return value.getLabel();
            }
        }
        return CharSequenceUtil.EMPTY;
    }
    public static CommissionUnitEnum getByUnit(String unit) {
        for (CommissionUnitEnum value : values()) {
            if (value.unit.equals(unit)) {
                return value;
            }
        }
        return USD;
    }
}
