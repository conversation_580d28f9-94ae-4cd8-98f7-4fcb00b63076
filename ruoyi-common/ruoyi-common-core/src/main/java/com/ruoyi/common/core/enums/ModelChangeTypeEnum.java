package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 模特变更记录类型枚举
 *
 * <AUTHOR>
 * @date 2024/9/11 17:12
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ModelChangeTypeEnum {

    INSERT(1, "新增模特"),
    UPDATE(2, "修改模特信息"),
    CHANGE_STATUS(3, "变更状态"),
    CHANGE_TOP(4, "变更置顶"),
    CHANGE_SORT(5, "修改模特排序"),
    UPDATE_FAMILY_MODEL(6, "修改家庭成员"),
    ;

    private Integer code;
    private String label;
}
