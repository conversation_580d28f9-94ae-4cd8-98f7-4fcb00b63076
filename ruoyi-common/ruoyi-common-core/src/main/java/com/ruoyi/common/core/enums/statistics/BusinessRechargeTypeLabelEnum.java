package com.ruoyi.common.core.enums.statistics;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会员退款单数占比
 * @create :2025-06-19 10:32
 **/
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BusinessRechargeTypeLabelEnum {
    NORMAL(0, "普通充值"),
    CHANNEL(1, "渠道"),
    FISSION(2, "裂变");

    private Integer code;
    private String label;
}
