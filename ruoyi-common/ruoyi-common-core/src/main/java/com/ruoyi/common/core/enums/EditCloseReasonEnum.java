package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 剪辑管理关闭原因枚举
 * <AUTHOR>
 * @date 2025/4/1 17:49
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum EditCloseReasonEnum {
    CANCEL_UPLOAD(1, "取消上传"),
    NO_FEEDBACK_TO_THE_MERCHANT(2, "不反馈给商家"),
    ORDER_ROLLBACK(3, "订单回退"),
    LINKAGE_OFF(4, "联动关闭"),
    ;
    private Integer code;
    private String label;
}
