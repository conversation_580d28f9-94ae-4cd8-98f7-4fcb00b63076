package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/11 10:58
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderTaskDetailFlowCompletionModeEnum {
    ACTIVE_COMPLETION(1, "主动完结"),
    REISSUE(2, "补发"),
    COMPENSATION(3, "补偿"),
    FEEDBACK_MATERIAL_TO_THE_BUSINESS(4, "反馈素材给商家"),
    MODEL_FEEDBACK_MATERIAL(5, "模特反馈素材"),
    ;

    private Integer code;
    private String label;
}
