package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :亲属关系类型
 * @create :2024-11-27 15:40
 **/
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ModelFamilyRelationshipTypeEnum {
    INITIATOR(0, "发起人"),
    MOTHER_SON(1, "母子"),
    MOTHER_DAUGHTER(2, "母女"),
    HUSBAND_WIFE(3, "夫妻"),
    FATHER_SON(4, "父子"),
    FATHER_DAUGHTER(5, "父女"),
    BROTHERS(6, "兄弟"),
    SISTERS(7, "姐妹"),
    BROTHER_SISTER(8, "兄妹"),
    SISTER_BROTHER(9, "姐弟");

    ;
    private Integer code;
    private String label;
}
