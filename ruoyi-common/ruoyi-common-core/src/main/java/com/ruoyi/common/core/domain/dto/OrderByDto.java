package com.ruoyi.common.core.domain.dto;

import com.ruoyi.common.core.utils.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description :
 * @create :2024-03-21 14:42
 **/
public class OrderByDto implements Serializable {
    private static final long serialVersionUID = 3906185475076214947L;
    // 排序方向
    public enum DIRECTION {
        DESC("DESC"), ASC("ASC");

        public static final DIRECTION defaultDirection = DESC;

        private final String value;

        DIRECTION(String value) {this.value = value;}

        public String value() {return this.value;}

        public static DIRECTION value(String value) {
            DIRECTION direction = null;
            for(DIRECTION findDirection: DIRECTION.values()) {
                if(findDirection.value.equals(value)) {
                    direction = findDirection;
                    break;
                }
            }

            if(null == direction) {
                direction = defaultDirection;
            }

            return direction;
        }
    }
    private LinkedHashMap<String, DIRECTION> fields = new LinkedHashMap<>();

    public void setField(String field, DIRECTION direction) {
        this.fields.put(field, direction);
    }

    public LinkedHashMap<String, DIRECTION> getFields() {
        return fields;
    }
    public boolean isEmpty() {
        return fields.isEmpty();
    }
    public String getSql() {
        String sql = "";
        if(!isEmpty()) {
            List<String> orderSql = new ArrayList<>();
            for (Map.Entry<String, DIRECTION> entry : getFields().entrySet()) {
                orderSql.add(StringUtils.toUnderScoreCase(entry.getKey()) + " " + entry.getValue());
            }
            sql = StringUtils.join(orderSql, ',');
        }

        return sql;
    }
}
