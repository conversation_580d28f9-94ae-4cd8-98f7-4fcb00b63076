package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 退款类型枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum RefundTypeEnum {

    REPARATION(1, "补偿订单"),
    CANCEL_ORDER(2, "取消订单"),
    CANCEL_OPENTION(3, "取消选配"),
    ;
    private Integer code;
    private String label;

    public static boolean checkRefundType(Integer refundType) {
        for (RefundTypeEnum refundTypeEnum : RefundTypeEnum.values()) {
            if (refundTypeEnum.getCode().equals(refundType)) {
                return true;
            }
        }
        return false;
    }

    public static RefundTypeEnum getByCode(Integer refundType) {
        for (RefundTypeEnum refundTypeEnum : RefundTypeEnum.values()) {
            if (refundTypeEnum.getCode().equals(refundType)) {
                return refundTypeEnum;
            }
        }
        return REPARATION;
    }
}
