package com.ruoyi.common.core.enums.statistics;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 商家会员类型枚举
 *
 * <AUTHOR>
 * @date 2025/06/19
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BusinessMemberTypeLabelEnum {
    NEW_MEMBER(1, "首次充值会员"),
    FIRST_RENEWAL(2, "首次续费会员"),
    MULTIPLE_RENEWAL(3, "多次续费会员");

    private Integer code;
    private String label;
}
