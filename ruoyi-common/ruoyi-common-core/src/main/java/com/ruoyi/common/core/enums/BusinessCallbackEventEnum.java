package com.ruoyi.common.core.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 回访事件枚举
 *
 * <AUTHOR>
 * @date 2025/2/12 10:04
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BusinessCallbackEventEnum {

    MEMBERS_5_DAYS_0_BOOKING(1, "会员5天0排单", 1,1),
    MEMBERS_1_MONTH_0_LIST(2, "会员1个月0排单", 1,2),
    MEMBERS_HAVE_PURCHASED_IT_FOR_1_MONTH(3, "会员已购1个月", 2,1),
    MEMBERS_HAVE_BEEN_BUYING_FOR_3_MONTHS(4, "会员已购3个月", 2,2),
    MEMBERS_HAVE_BEEN_BUYING_FOR_6_MONTHS(5, "会员已购6个月", 2,3),
    ONE_MONTH_BEFORE_MEMBERSHIP_EXPIRES(6, "会员过期前1个月", 3,1),
    ONE_WEEK_BEFORE_MEMBERSHIP_EXPIRES(7, "会员过期前1周", 3,2),
    ONE_DAY_BEFORE_MEMBERSHIP_EXPIRES(8, "会员过期前1天", 3,3),
    MEMBERSHIP_HAS_EXPIRED_FOR_1_WEEK(9, "会员已过期1周", 4,1),
    MEMBERSHIP_HAS_EXPIRED_FOR_1_MONTH(10, "会员已过期1个月", 4,2),
    NO_LIST_FOR_NEARLY_30_DAYS(11, "近30天无排单", 5,1),
    ;
    private Integer code;
    private String label;
    //  同类型事件same值相同
    private Integer same;
    //  同类型事件rank值越高级别越高
    private Integer rank;

    // 根据 codeList 获取多个枚举项
    public static BusinessCallbackEventEnum getByCode(Integer code) {
        for (BusinessCallbackEventEnum event : values()) {
            if (event.getCode().equals(code)) {
                return event;
            }
        }
        return null;
    }
}
