package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/10/14 17:22
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderVideoFlowNodeEnum {
    ORDER_PAYMENT(1, "下单支付", 0),
    MATCHING_MODEL(2, "匹配模特", 1),
    MERCHANT_DELIVERY(3, "商家发货", 2),
    FINISH_SHOOTING(4, "完成拍摄", 3),
    MERCHANT_CONFIRMATION(5, "商家确认", 4),
    ORDER_COMPLETION(6, "订单完成", 5),
    CANCEL_ORDER(10, "取消订单", 10),
    ;
    private Integer code;
    private String label;
    private Integer sort;
}
