package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 订单工单状态类型枚举
 *
 * <AUTHOR>
 * @date 2024/6/24 11:06
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderTaskStatusEnum {

    UN_HANDLE(1, "待处理"),
    HANDLE_ING(2, "处理中"),
    APPLICATION_FOR_CANCELLATION(3, "申请取消中"),
    HANDLE(4, "已处理"),
    REJECT(5, "已拒绝"),
    CLOSE(6, "已关闭"),
    ;
    private Integer code;
    private String label;
}
