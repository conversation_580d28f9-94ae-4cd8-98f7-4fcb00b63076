package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/12 14:37
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BusinessCallbackStatusEnum {

    WAIT_FOR_RETURN_VISIT(1, "待回访"),
    IN_THE_RETURN_VISIT(2, "回访中"),
    ALREADY_VISITED(3, "已回访"),
    ;
    private Integer code;
    private String label;
}
