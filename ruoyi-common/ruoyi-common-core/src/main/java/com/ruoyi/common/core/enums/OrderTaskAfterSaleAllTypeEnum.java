package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/9 10:31
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderTaskAfterSaleAllTypeEnum {
    RESHOOT_VIDEO(1, 1, 1, "重拍视频"),
    RESHOT_VIDEO(2, 2, 1, "补拍视频"),
    NEED_HD_VIDEO(3, 3, 1, "要高清视频"),
    SOURCE_MATERIAL(4, 4, 1, "原素材"),
    RE_UPLOAD(5, 5, 1, "重新上传"),
    RECUT(6, 6, 1, "重新剪辑"),


    RESHOOT_PIC(11, 1, 2, "重拍照片"),
    NEED_HD_PIC(12, 2, 2, "要高清照片"),
    RESHOT_PIC(13, 3, 2, "补拍照片"),
    PIC_SOURCE_MATERIAL(14, 4, 2, "原素材"),
    ;
    private Integer code;
    private Integer value;
    private Integer type;
    private String label;

    public static OrderTaskAfterSaleAllTypeEnum getTaskAfterSaleAllTypeEnumByCode(Integer code) {
        for (OrderTaskAfterSaleAllTypeEnum item : OrderTaskAfterSaleAllTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    public static Integer getTaskAfterSaleVideoByCode(Integer value) {
        for (OrderTaskAfterSaleAllTypeEnum item : OrderTaskAfterSaleAllTypeEnum.values()) {
            if (item.getValue().equals(value) && item.getType().equals(1)) {
                return item.getCode();
            }
        }
        return SOURCE_MATERIAL.getCode();
    }
    public static Integer getTaskAfterSalePicByCode(Integer value) {
        for (OrderTaskAfterSaleAllTypeEnum item : OrderTaskAfterSaleAllTypeEnum.values()) {
            if (item.getValue().equals(value) && item.getType().equals(2)) {
                return item.getCode();
            }
        }
        return SOURCE_MATERIAL.getCode();
    }
}
