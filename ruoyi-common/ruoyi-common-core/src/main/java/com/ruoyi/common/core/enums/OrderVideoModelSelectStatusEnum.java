package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 模特选择记录状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderVideoModelSelectStatusEnum {

    UN_HANDLE(0, "待处理"),
    IN_REVIEW(1, "正在审核中"),
    EXPIRE(2, "过期未确认"),
    CANCEL(3, "卖家取消"),
    REJECT(4, "您已拒绝"),
    CONFIRM(5, "已确认拍摄"),
    CANCEL_APPLY(6, "您已取消申请"),
    ;
    private Integer code;
    private String label;
}
