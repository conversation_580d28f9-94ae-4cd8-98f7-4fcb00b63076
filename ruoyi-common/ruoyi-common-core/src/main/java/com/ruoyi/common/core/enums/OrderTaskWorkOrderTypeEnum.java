package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/9 10:31
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderTaskWorkOrderTypeEnum {
    MODEL_DIDNT_GET_IT(1, "模特没收到"),
    ACCELERATOR_MATERIAL(2, "催素材"),
    TAKE_DOWN_VIDEO(3, "下架视频"),
    NEED_CLIPS(4, "需剪辑"),
    ELSE(5, "其他"),
    UPLOAD_EXCEPTION(6, "上传异常"),
    PROBLEMS_RELATED_TO_MATERIAL_LINKS(7, "素材链接问题"),
    ;
    private Integer code;
    private String label;
}
