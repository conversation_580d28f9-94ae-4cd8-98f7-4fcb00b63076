package com.ruoyi.common.core.web.domain;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * LoginEntity基类
 *
 * <AUTHOR>
 */
@ApiModel(value = "LoginEntity基类", description = "LoginEntity基类")
@Data
public class LoginBaseEntity implements Serializable {
    private static final long serialVersionUID = -5855229874616669770L;
    /**
     * 用户类型
     *
     * @see com.ruoyi.common.core.constant.UserTypeConstants
     */
    private Integer userType;


    /**
     * 用户唯一标识
     */
    private String token;

    /**
     * 用户名id
     */
    private Long userid;

    /**
     * 用户名
     */
    private String username;

    /**
     * 登录账号ID*
     */
    private Long bizUserId;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 浏览器UA
     */
    private String userAgent;
}
