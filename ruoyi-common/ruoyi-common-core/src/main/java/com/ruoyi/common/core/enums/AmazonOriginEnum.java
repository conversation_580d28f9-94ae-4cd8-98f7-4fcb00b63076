package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * Amazon区域枚举
 * <AUTHOR>
 * @date 2025/4/18 11:39
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum AmazonOriginEnum {

    AUSTRALIA("Australia", "https://www.amazon.com.au"),
    BELGIUM("Belgium", "https://www.amazon.com.be"),
    BRAZIL("Brazil", "https://www.amazon.com.br"),
    CANADA("Canada", "https://www.amazon.ca"),
    CHINA("China", "https://www.amazon.cn"),
    EGYPT("Egypt", "https://www.amazon.eg"),
    FRANCE("France", "https://www.amazon.fr"),
    GERMANY("Germany", "https://www.amazon.de"),
    INDIA("India", "https://www.amazon.in"),
    IRELAND("Ireland", "https://www.amazon.ie"),
    ITALY("Italy", "https://www.amazon.it"),
    JAPAN("Japan", "https://www.amazon.co.jp"),
    MEXICO("Mexico", "https://www.amazon.com.mx"),
    NETHERLANDS("Netherlands", "https://www.amazon.nl"),
    POLAND("Poland", "https://www.amazon.pl"),
    SAUDI_ARABIA("Saudi Arabia", "https://www.amazon.sa"),
    SINGAPORE("Singapore", "https://www.amazon.sg"),
    SOUTH_AFRICA("South Africa", "https://www.amazon.co.za"),
    SPAIN("Spain", "https://www.amazon.es"),
    SWEDEN("Sweden", "https://www.amazon.se"),
    TURKEY("Turkey", "https://www.amazon.com.tr"),
    UAE("United Arab Emirates", "https://www.amazon.ae"),
    UK("United Kingdom", "https://www.amazon.co.uk"),
    US("United States", "https://www.amazon.com");
    ;
    private String region;
    private String url;

    public static AmazonOriginEnum getLabelByUrl(String url) {
        for (AmazonOriginEnum amazonOriginEnum : AmazonOriginEnum.values()) {
            if (url.startsWith(amazonOriginEnum.getUrl())) {
                return amazonOriginEnum;
            }
        }
        return US;
    }

}
