package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 支付状态码枚举
 *
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PayStatusCodeEnum {
    MESSAGE_ERROR("020001", "报文错误"),
    OTHER_ERROR("030001", "其他错误"),
    AUTHORIZATION_CODE_INCORRECT("030002", "授权码不正确"),
    ORDER_REPEATED("030003", "订单已重复"),
    ORDER_PAID("030004", "订单已支付"),
    MERCHANT_DOES_NOT_EXIST("030005", "商户不存在"),
    ORDER_REVOKED("030006", "订单已撤销"),
    ORDER_CLOSED("030007", "订单已关闭"),
    ORDER_DOES_NOT_EXIST("030008", "订单不存在"),
    MERCHANT_NO_PERMISSION("030009", "商户无权限"),
    ORDER_REFUNDED("030011", "订单已退款"),
    REFUND_ORDER_DOES_NOT_EXIST("030012", "退款订单不存在"),
    INSUFFICIENT_POSITION("030013", "头寸不足"),
    REFUND_AMOUNT_EXCEEDS_TOTAL_ORDER_AMOUNT("030014", "退款金额超过原订单总金额"),
    INSUFFICIENT_BALANCE("030015", "余额不足"),
    ORIGINAL_ORDER_DOES_NOT_EXIST("030016", "原订单不存在"),
    ORDER_NOT_REFUNDABLE("030017", "订单不可退款"),
    MERCHANT_HAS_JOINED("030018", "商户已入驻"),
    MERCHANT_BUSINESS_CATEGORY_DOES_NOT_EXIST("030020", "商户经营类目不存在"),
    NON_EMPTY_FIELD_APPEARS_EMPTY("1001", "非空字段出现空值"),
    SIGNATURE_ERROR("1002", "验签错误"),
    FIELD_CONTENT_ERROR("1003", "字段内容错误"),
    MERCHANT_NUMBER_DOES_NOT_EXIST("1009", "商户号不存在/XX交易未开通"),
    CANNOT_FIND_TRANSACTION("1010", "找不到交易"),
    ORIGINAL_TRANSACTION_STATUS_IS_NOT_SUCCESSFUL("1011", "原交易状态为非成功"),
    INSUFFICIENT_BALANCE_NOT_ALLOWED_REFUND("1012", "余额不足不允许退款/银联主扫不允许退款"),
    MERCHANT_ORDER_NUMBER_REPEATED("1013", "商户订单号重复"),
    MESSAGE_FORMAT_ERROR("1014", "报文格式错"),
    REQUEST_FUNCTION_NOT_SUPPORTED("1015", "请求功能尚不支持"),
    TARGET_TIMEOUT("2001", "目标方超时"),
    TARGET_CONNECTION_FAILED("2002", "目标方连接失败"),
    ORDER_FULL_REFUND("1031", "订单已全额退款"),
    ORDER_INSUFFICIENT_REFUND_AMOUNT("1032", "订单可退金额不足"),
    SUCCESS("000000", "成功"),
    PAYING("010001", "支付中"),
    PAYING_2("010002", "支付中"),
    PAYING_3("030010", "支付中"),
    PAYING_4("9999", "支付中");

    private String code;
    private String desc;

    public static String getDesc(String code) {
        for (PayStatusCodeEnum e : PayStatusCodeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getDesc();
            }
        }
        return null;
    }

    public static PayPlatformTypeEnum getEnum(String transStat) {
        return null;
    }
}
