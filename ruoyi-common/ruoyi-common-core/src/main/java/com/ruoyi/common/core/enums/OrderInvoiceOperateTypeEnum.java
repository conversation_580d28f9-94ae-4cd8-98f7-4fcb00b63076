package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 发票操作类型枚举
 *
 * <AUTHOR>
 * @date 2025/1/6 18:40
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderInvoiceOperateTypeEnum {

    APPLY_FOR_BILLING(1,"申请开票", "#操作人#发起开票申请"),
    COMPANY_CANCEL_BILLING(2,"取消开票", "#操作人#取消开票申请"),
    CONFIRMATION_BILLING(3,"确认开票", "#操作人#确认开票内容"),
    MODIFY_BILLING_INFORMATION(4,"修改开票信息", "#操作人#修改发票信息"),
    UPLOAD_INVOICE(5,"上传发票", "#操作人#上传发票"),
    RE_UPLOAD_INVOICE(6,"重新上传发票", "#操作人#重新上传发票"),
    AUDIT_INVOICE(7,"审核发票", "#操作人#已完成发票核对并投递给商家"),
    RED_ALERT(8,"红冲提醒", "商家提现{}元"),
    MARK_RED_PUNCH_A_NEW_TICKET_IS_REQUIRED(9,"标记红冲", "#操作人#标记该发票需红冲，红冲金额{}元，需重新开票，开票金额{}元。备注：{}"),
    MARK_RED_PUNCH_NO_NEED_TO_RE_INVOICE(10,"标记红冲", "#操作人#标记该发票需红冲，红冲金额{}元，无需重新开票。备注：{}"),
    MARK_RED_PUNCH_NO_RED_PUNCH(11,"标记红冲", "#操作人#标记该发票无需红冲。备注：{}"),
    RE_BILLING(12,"重开发票", "#操作人#发起重新开票申请"),
    RED_FLUSH_REOPENS(13,"红冲重开", "#操作人#确认申票码{}发票红冲并重开"),
    ;


    private Integer code;
    private String eventName;
    private String eventContent;
}
