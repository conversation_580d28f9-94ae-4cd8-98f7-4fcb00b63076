package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 种草记录枚举
 *
 * <AUTHOR>
 * @date 2025/5/16 14:57
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum MemberSeedRecordStatusEnum {
    PENDING_DEPOSIT(0, "待入账"),
    PENDING_WITHDRAWAL(1, "待提现"),
    UNDER_REVIEW(2, "待审核"),
    PENDING_TRANSFER(3, "待打款"),
    TRANSFERRED(4, "已打款"),
    REVIEW_REJECTED(5, "审核不通过"),
    TRANSFER_EXCEPTION(6, "打款异常"),
    UN_NEED_TRANSFERRED(7, "无需提现"),
    DEPOSIT_FAILED(99, "入账失败"),
    CANNOT_WITHDRAWAL_TEMPORARY(999, "暂不可提现"),
    ;
    private Integer code;
    private String label;
}
