package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 审核状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BusinessBalanceAuditStatusEnum {

    PRE_APPROVE(0, "待处理"),
    APPROVE(1, "已提现"),
    CANCEL(2, "已取消"),
    ;
    private Integer code;
    private String label;

    public static List<Integer> getAudit(){
        return Arrays.asList(APPROVE.code, CANCEL.code);
    }

}
