package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单素材反馈时长数据分析枚举
 *
 * <AUTHOR>
 * @date 2025/6/9 17:13
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderVideoFeedbackDurationEnum {
    FIRST_GEAR(1, "7天内", new BigDecimal("0"), new BigDecimal("7")),
    SECOND_GEAR(2, "7-14天", new BigDecimal("7"), new BigDecimal("14")),
    THIRD_GEAR(3, "14-20天", new BigDecimal("14"), new BigDecimal("20")),
    FOURTH_GEAR(4, "20-30天", new BigDecimal("20"), new BigDecimal("30")),
    FIFTH_GEAR(5, "30-60天", new BigDecimal("30"), new BigDecimal("60")),
    SIXTH_GEAR(6, "超60天", new BigDecimal("60"), new BigDecimal("9999999")),
    ;

    private Integer code;
    private String label;
    private BigDecimal begin;
    private BigDecimal end;

    public static boolean isInSection(String label, BigDecimal section) {
        for (OrderVideoFeedbackDurationEnum value : values()) {
            if (value.getLabel().equals(label) && section.compareTo(value.begin) > 0 && section.compareTo(value.end) <= 0) {
                return true;
            }
        }
        return false;
    }

    public static List<String> getLabels() {
        List<String> labels = new ArrayList<>();

        for (OrderVideoFeedbackDurationEnum value : values()) {
            labels.add(value.label);
        }
        return labels;
    }
}
