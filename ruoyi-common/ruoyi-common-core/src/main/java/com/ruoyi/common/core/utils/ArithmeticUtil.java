package com.ruoyi.common.core.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2025/5/20 11:28
 */
public class ArithmeticUtil {

    public static BigDecimal safeDivide(BigDecimal numerator, long denominator, int scale, RoundingMode roundingMode) {
        return denominator == 0 ? BigDecimal.ZERO :
                numerator.divide(BigDecimal.valueOf(denominator), scale, roundingMode);
    }
}
