package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-27 18:50
 **/
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BusinessScaleTypeEnum {
    TINY_SCALE(1, "1-20人"),
    SMALL_FIRST_SCALE(2, "21-100人"),
    SMALL_SECOND_SCALE(3, "101-200人"),
    MEDIUM_SCALE(4, "201-1000人"),
    LARGE_SCALE(5, "超过1000人"),
    ;
    private Integer code;
    private String label;
}
