package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 标记枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CustomerTypeEnum {

    NORMAL(0, "一般客户", 5),
    VIP(1, "重要客户", 10),
    COMMON(2, "普通客户", 0),
    ;
    private Integer code;
    private String label;
    private Integer num;

    public static CustomerTypeEnum getByCode(Integer code){
        for (CustomerTypeEnum e : CustomerTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
