package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 支付平台枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PayPlatformTypeEnum {

    WECHAT(1, "WECHAT", "微信"),
    ALIPAY(2, "ALIPAY", "支付宝"),
    UNIONPAY(3, "UNIONPAY", "云闪付/银联"),
    DIGICCY(4, "DIGICCY", "数字人民币"),
    UNKNOWN(5, "UNKNOWN", "未知支付方式");
    private Integer code;
    private String value;
    private String desc;


    public static String getDesc(Integer code) {
        for (PayPlatformTypeEnum e : PayPlatformTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getDesc();
            }
        }
        return UNKNOWN.getDesc();
    }

    public static String getValue(Integer code) {
        for (PayPlatformTypeEnum e : PayPlatformTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getValue();
            }
        }
        return UNKNOWN.getValue();
    }

    public static PayPlatformTypeEnum getByCode(Integer code) {
        for (PayPlatformTypeEnum e : PayPlatformTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return UNKNOWN;
    }
}
