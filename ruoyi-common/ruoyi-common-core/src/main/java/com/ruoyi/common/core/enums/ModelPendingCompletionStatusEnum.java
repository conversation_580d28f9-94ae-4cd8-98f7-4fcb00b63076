package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 模特待完成订单状态枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ModelPendingCompletionStatusEnum {

    PENDING_COMPLETION(0, "待完成", "订单进入待完成阶段，支持模特查阅订单信息和上传视频素材链接"),
    COMPLETED(1, "订单完结", "订单商家已确认，表示订单完结"),
    SELLER_CANCELLATION(2, "退款成功", "运营发起了取消订单（整笔退）时，退款成功，模特端任务同步结束"),
    AFTER_SALES(3, "发生售后", "订单发生了售后需求，模特需要根据售后内容，重新上传视频链接"),
    ;

    private Integer code;
    private String label;
    private String description;
}
