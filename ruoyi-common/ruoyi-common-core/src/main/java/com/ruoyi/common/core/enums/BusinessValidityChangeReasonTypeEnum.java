package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 修改会员有效期类型
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BusinessValidityChangeReasonTypeEnum {
    OLD_MEMBER(1, "老会员入驻"),
    SEVEN_DAYS_WITHOUT_REASON(2, "七天无理由"),
    EXIT_MEMBER(3, "退会"),
    OTHER(4, "其他"),
    ;
    private Integer code;
    private String label;
}
