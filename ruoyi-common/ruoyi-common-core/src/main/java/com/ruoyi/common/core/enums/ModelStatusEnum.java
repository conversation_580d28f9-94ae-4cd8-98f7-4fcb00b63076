package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 模特状态枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ModelStatusEnum {

    NORMAL(0,"正常合作"),
    PAUSE(1,"暂停合作"),
    JOURNEY(2,"行程中"),
    CANCEL(3,"取消合作"),
    ;

    private Integer code;
    private String label;

    public static List<Integer> getCodes() {
        List<Integer> codes = new ArrayList<>();
        for (ModelStatusEnum value : ModelStatusEnum.values()) {
            codes.add(value.getCode());
        }
        return codes;
    }

    public static String getLabelByCode(Integer code) {
        for (ModelStatusEnum value : ModelStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.label;
            }
        }
        return null;
    }
}
