package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * 催单状态枚举
 *
 * <AUTHOR>
 * @date 2024/8/15 9:30
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ReminderStatusEnum {
    UNTREATED(1, "未处理"),
    CONFIRM(2, "已确认"),
    FINISHED(3, "已完成"),
    ;
    private Integer code;
    private String label;

    public static Map<String, Integer> getCodeMap() {
        Map<String, Integer> statusMap = new HashMap<>();
        for (ReminderStatusEnum status : ReminderStatusEnum.values()) {
            statusMap.put(status.name(), status.getCode());
        }
        return statusMap;
    }
}
