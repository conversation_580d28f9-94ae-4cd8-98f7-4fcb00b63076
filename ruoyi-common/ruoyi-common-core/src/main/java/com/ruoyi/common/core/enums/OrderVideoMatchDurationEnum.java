package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单匹配时长数据分析枚举
 *
 * <AUTHOR>
 * @date 2025/6/9 15:14
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderVideoMatchDurationEnum {
    FIRST_GEAR(1, "2天内", new BigDecimal("0"), new BigDecimal("2")),
    SECOND_GEAR(2, "2-3天", new BigDecimal("2"), new BigDecimal("3")),
    THIRD_GEAR(3, "3-7天", new BigDecimal("3"), new BigDecimal("7")),
    FOURTH_GEAR(4, "7-14天", new BigDecimal("7"), new BigDecimal("14")),
    FIFTH_GEAR(5, "14-30天", new BigDecimal("14"), new BigDecimal("30")),
    SIXTH_GEAR(6, "超30天", new BigDecimal("30"), new BigDecimal("9999999")),
    ;

    private Integer code;
    private String label;
    private BigDecimal begin;
    private BigDecimal end;

    public static boolean isInSection(String label, BigDecimal section) {
        for (OrderVideoMatchDurationEnum value : values()) {
            if (value.getLabel().equals(label) && section.compareTo(value.begin) > 0 && section.compareTo(value.end) <= 0) {
                return true;
            }
        }
        return false;
    }

    public static List<String> getLabels() {
        List<String> labels = new ArrayList<>();

        for (OrderVideoMatchDurationEnum value : values()) {
            labels.add(value.label);
        }
        return labels;
    }
}
