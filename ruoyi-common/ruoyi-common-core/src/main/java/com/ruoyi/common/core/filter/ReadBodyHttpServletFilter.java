package com.ruoyi.common.core.filter;

import com.ruoyi.common.core.wrapper.RepeatedlyReadRequestWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Optional;

/**
 * 覆写request支持多次读取，避免二次读取时报错
 *
 * <AUTHOR>
 * @date 2023/12/26
 **/

@Component
@ConditionalOnClass({HttpServletResponse.class, HttpServletRequest.class, OncePerRequestFilter.class})
@WebFilter("/*")
@Order(1)
public class ReadBodyHttpServletFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
        //文件上传类型不处理，否则会报java.nio.charset.MalformedInputException: Input length = 1异常
        if (Optional.ofNullable(request.getContentType()).orElse(StringUtils.EMPTY).startsWith("multipart/")) {
            chain.doFilter(request, response);
            return;
        }

        RepeatedlyReadRequestWrapper requestWrapper = new RepeatedlyReadRequestWrapper(request);
        chain.doFilter(requestWrapper, response);
    }
}
