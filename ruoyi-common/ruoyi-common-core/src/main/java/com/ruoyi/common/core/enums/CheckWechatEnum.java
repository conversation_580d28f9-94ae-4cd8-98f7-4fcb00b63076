package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 审核状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CheckWechatEnum {

    CUSTOM(0, "自定义异常"),
    ALREADY_SON(1, "您已有%s子账号权限"),
    ALREADY_OTHER_SON(2, "您已是%s子账号"),
    ALREADY_OWNER(3, "您已是平台主账号"),
    OWNER_BUSINESS_EXPIRE(4, "企业主账号会员已到期，无法加入"),
    ACCOUNT_APPLY(5, "已申请%s子账号，无法加入"),
    SUCCESS(6, "有效账号"),
    NO_BIZ_USER(7, "不存在登录账号"),
    THE_USER_HAS_NOT_AUTHORIZED(8, "用户未授权"),
    NO_BIZ_USER_PHONE(9, "未填写手机号"),
    ;
    private Integer code;
    private String label;

}
