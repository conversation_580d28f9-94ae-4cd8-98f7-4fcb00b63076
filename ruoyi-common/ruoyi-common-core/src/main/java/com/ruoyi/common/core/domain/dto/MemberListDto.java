package com.ruoyi.common.core.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MemberListDto {
    @ApiModelProperty(value = "支付时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTimeBegin;

    @ApiModelProperty(value = "支付时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTimeEnd;

    private BigDecimal totalAmount;
}
