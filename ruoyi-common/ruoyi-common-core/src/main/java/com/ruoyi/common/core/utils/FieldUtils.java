package com.ruoyi.common.core.utils;

import java.lang.reflect.Field;

public class FieldUtils {

    /**
     * 获取对象中有值的字段数
     *
     * @param obj 需要检查的对象
     * @throws IllegalAccessException 如果访问字段时发生错误
     */
    public static int getNonNullFieldCount(Object obj) {
        // 获取对象的类类型
        Class<?> clazz = obj.getClass();

        // 获取类的所有字段
        Field[] fields = clazz.getDeclaredFields();

        int nonNullFieldCount = 0;

        try {
            // 遍历所有字段
            for (Field field : fields) {
                if ("serialVersionUID".equals(field.getName())) {
                    continue;
                }
                field.setAccessible(true);  // 设置字段可访问

                Object value = field.get(obj);  // 获取字段值

                // 判断字段值是否非空
                if (value != null) {
                    nonNullFieldCount++;
                }
            }

            return nonNullFieldCount;
        } catch (Exception e) {
            return nonNullFieldCount;
        }
    }
}
