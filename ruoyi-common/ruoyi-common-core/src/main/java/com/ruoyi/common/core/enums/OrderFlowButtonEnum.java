package com.ruoyi.common.core.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;


/**
 * 商家是否需要运营上传素材至平台枚举
 *
 * <AUTHOR>
 * @date 2024/9/6 11:30
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderFlowButtonEnum {

    CREATE_ORDER(1, "创建订单"),
    PAY_SUCCESS(2, "支付成功"),
    SUBMIT_CREDENTIAL(3, "提交凭证"),
    AUDIT_SUCCESS(4, "审核通过"),
    AUDIT_UN_SUCCESS(5, "审核异常"),
    CANCEL_ORDER(6, "取消订单"),
    ;
    private Integer code;
    private String label;
}
