package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/9/27 14:57
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SettleStatusEnum {
    UN_SETTLED(0, "未结算"),
    SETTLED(1, "已结算"),
    WAIT_SETTLED(2, "待生效"),
    UNABLE_SETTLED(3, "不可结算"),
    ;
    private Integer code;
    private String label;
}
