package com.ruoyi.common.core.enums;

import lombok.*;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum UpdateWechatRemarkType {

    EXPIRE("Expire", "会员过期"),
    VIP("Vip", "成为会员"),
    BIND_BUSINESS("Bind_Business", "绑定商家"),
    UNBIND_BUSINESS("Unbind_Business", "取消绑定");
    private String code;
    private String label;

    /**
     * 根据code找label
     */
    public static UpdateWechatRemarkType getLabel(String code) {
        for (UpdateWechatRemarkType e : UpdateWechatRemarkType.values()) {
            if (code.startsWith(e.getCode())) {
                return e;
            }
        }
        return null;
    }


}
