package com.ruoyi.common.core.utils.uuid;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.ruoyi.common.core.constant.OrderConstant;

import java.util.Date;

/**
 * ID生成器工具类
 * 
 * <AUTHOR>
 */
public class IdUtils
{
    /**
     * 获取随机UUID
     * 
     * @return 随机UUID
     */
    public static String randomUUID()
    {
        return UUID.randomUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线
     * 
     * @return 简化的UUID，去掉了横线
     */
    public static String simpleUUID()
    {
        return UUID.randomUUID().toString(true);
    }

    /**
     * 获取随机UUID，使用性能更好的ThreadLocalRandom生成UUID
     * 
     * @return 随机UUID
     */
    public static String fastUUID()
    {
        return UUID.fastUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线，使用性能更好的ThreadLocalRandom生成UUID
     * 
     * @return 简化的UUID，去掉了横线
     */
    public static String fastSimpleUUID()
    {
        return UUID.fastUUID().toString(true);
    }

    /**
     * 根据雪花算法得到随机数
     * @return
     */
    public static String createOrderNum(String prefix, int numDigits) {
        // 1. 获取当前时间并格式化
        String timestamp = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        // 2. 使用 Snowflake 算法生成唯一ID
        long uniqueId = IdUtil.getSnowflakeNextId();
        // 3. 计算指定数字位数的基数，并截取唯一ID的最后 numDigits 位
        long base = (long) Math.pow(10, numDigits);
        String randomDigits = String.format("%0" + numDigits + "d", uniqueId % base);
        // 4. 拼接生成最终的订单号
        return prefix + timestamp + randomDigits;
    }


    /**
     * 根据雪花算法得到随机数
     *
     * @return
     */
    public static String createOrderNum(String prefix) {
        // 1. 获取当前时间并格式化
        String timestamp = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        // 2. 使用Snowflake算法生成唯一ID
        long uniqueId = IdUtil.getSnowflakeNextId();
        // 3. 截取唯一ID的最后8位，确保是数字
        String random8Digits = String.format("%08d", uniqueId % 100_000_000);
        return prefix + OrderConstant.ORDER_NUM_PREFIX_WN + timestamp + random8Digits;
    }

    public static String createFlowOrderNo() {
        // 1. 获取当前时间并格式化
        String timestamp = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        // 2. 使用Snowflake算法生成唯一ID
        long uniqueId = IdUtil.getSnowflakeNextId();
        // 3. 截取唯一ID的最后8位，确保是数字
        String random8Digits = String.format("%04d", uniqueId % 10000);
        return "2"  + timestamp + random8Digits;
    }

}
