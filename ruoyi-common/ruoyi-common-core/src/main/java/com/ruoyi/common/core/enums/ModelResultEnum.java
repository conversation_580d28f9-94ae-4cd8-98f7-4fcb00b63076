package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :模特结果枚举
 * @create :2025-04-22 18:11
 **/
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ModelResultEnum {

    PENDING(0, "待处理"),
    INQUIRED(1, "已询问"),
    RECEIVED(2, "已收货"),
    LOST(3, "丢件"),
    ROLLBACK(4, "订单回退"),
    WAIT_NOTICE_SHOOTING(5, "待通知拍摄"),
    NOTICE_SHOOTING(6, "已通知拍摄"),
    ;

    private Integer code;
    private String label;

    public static String getLabelByCode(Integer code) {
        for (ModelResultEnum e : ModelResultEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getLabel();
            }
        }
        return "未知操作";
    }

}

