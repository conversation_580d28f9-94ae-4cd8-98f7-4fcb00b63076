package com.ruoyi.common.core.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 合作平台枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PlatformEnum {

    AMAZON(0, "Amazon", "Amazon Style"),
    TIKTOK(1, "TikTok", "Tiktok Style"),
    ELSE(2, "其他", "Tiktok Style"),
    APP(3, "APP/解说类", "Tiktok Style"),
    ;
    private Integer code;
    private String label;
    private String style;

    /**
     * 根据code找label
     */
    public static String getLabel(Integer code) {
        for (PlatformEnum e : PlatformEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getLabel();
            }
        }
        return null;
    }

    /**
     * 根据code找style
     */
    public static String getStyle(Integer code) {
        for (PlatformEnum e : PlatformEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getStyle();
            }
        }
        return StrUtil.EMPTY;
    }

    /**
     * 模糊搜索Label找code
     */
    public static List<Integer> getCode(String keyword) {
        List<Integer> codes = new ArrayList<>();
        for (PlatformEnum e : PlatformEnum.values()) {
            if (StrUtil.containsIgnoreCase(e.getLabel(), keyword)) {
                codes.add(e.getCode());
            }
        }
        return codes;
    }
}
