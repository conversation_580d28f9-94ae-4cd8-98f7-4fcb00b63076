package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * 退款状态枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum RefundStatusEnum {
    AFTER_SALE_UN_CHECK(0, "退款待审核"),
    AFTER_SALE(1, "退款中"),
    REJECT(2, "已拒绝"),
    CANCEL(3, "已取消"),
    AFTER_SALE_FINISHED(4, "退款成功"),
    ;
    private Integer code;
    private String label;

    public static Map<String, Integer> getCodeMap() {
        Map<String, Integer> statusMap = new HashMap<>();
        for (RefundStatusEnum status : RefundStatusEnum.values()) {
            statusMap.put(status.name(), status.getCode());
        }
        return statusMap;
    }
}
