package com.ruoyi.common.core.enums.statistics;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 商家排单标签枚举
 *
 * <AUTHOR>
 * @date 2024/5/21 14:47
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BusinessOrderLabelEnum {
    NO_ORDER(0, "未排单"),
    WITHIN_5(1, "5单内"),
    BETWEEN_5_9(2, "5-9单"),
    BETWEEN_10_19(3, "10-19单"),
    BETWEEN_20_49(4, "20-49单"),
    OVER_50(5, "50单以上");

    private Integer code;
    private String label;
}
