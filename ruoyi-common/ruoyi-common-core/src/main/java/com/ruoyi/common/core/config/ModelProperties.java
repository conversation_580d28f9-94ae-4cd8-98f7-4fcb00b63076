package com.ruoyi.common.core.config;

import com.ruoyi.common.core.enums.ModelCooperationEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/2/24 16:02
 */
@Component
@ConfigurationProperties(prefix = "model")
@Data
@RefreshScope
public class ModelProperties {

    private String baseUrl;

    private ApplyOrderNum applyOrderNum;

    @Data
    public static class ApplyOrderNum {
        private Integer quality;
        private Integer moderate;
        private Integer ordinary;

        public Integer getApplyOrderNum(Integer cooperation) {
            if (ModelCooperationEnum.QUALITY.getCode().equals(cooperation)) {
                return quality;
            } else if (ModelCooperationEnum.MODERATE.getCode().equals(cooperation)) {
                return moderate;
            } else if (ModelCooperationEnum.ORDINARY.getCode().equals(cooperation)) {
                return ordinary;
            }
            return 0;
        }
    }
}
