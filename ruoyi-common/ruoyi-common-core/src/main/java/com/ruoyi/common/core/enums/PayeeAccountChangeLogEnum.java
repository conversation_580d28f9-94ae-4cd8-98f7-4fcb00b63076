package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-20 13:36
 **/
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PayeeAccountChangeLogEnum {
    NEW(1, "新增","新增收款主体：%s"),
    MODIFY(2, "修改","修改%s主体信息"),
    DEL(3, "删除","原收款主体%s，变更为%s");


    private Integer code;
    private String label;
    private String comment;
}
