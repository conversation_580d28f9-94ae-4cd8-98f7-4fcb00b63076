package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;


/**
 * 分销渠道排序方式枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum LogisticFollowOrderByTypeEnum {
    LOGISTIC_UPDATE_TIME(1, "ovlf.logistic_update_time", "物流系统同步时间"),
    UPDATE_TIME(2, "ovlf.update_time", "修改时间"),
    ;
    private Integer code;
    private String value;
    private String label;


    public static String getValue(Integer code) {
        for (LogisticFollowOrderByTypeEnum payTypeEnum : LogisticFollowOrderByTypeEnum.values()) {
            if (payTypeEnum.getCode().equals(code)) {
                return payTypeEnum.value;
            }
        }
        return null;
    }
    public static String getLabel(Integer code) {
        for (LogisticFollowOrderByTypeEnum payTypeEnum : LogisticFollowOrderByTypeEnum.values()) {
            if (payTypeEnum.getCode().equals(code)) {
                return payTypeEnum.label;
            }
        }
        return null;
    }
}
