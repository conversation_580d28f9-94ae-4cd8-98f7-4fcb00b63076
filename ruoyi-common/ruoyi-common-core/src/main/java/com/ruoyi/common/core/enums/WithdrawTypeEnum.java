package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;


/**
 * 预付支付方式枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum WithdrawTypeEnum {
    //1-微信，2-支付宝，3-银行卡，4-境外汇款，5-其他，6-对公）
    WECHAT(1, "微信", PayTypeEnum.WECHAT.getCode()),
    ALIPAY(2, "支付宝", PayTypeEnum.ALIPAY.getCode()),
    BANK(3, "银行卡", PayTypeEnum.BANK.getCode()),
    OVERSEAS(4, "境外汇款", 998),
    FULL_CURRENCY(5, "其他", 999),
    PUBLIC(6, "公户收款", PayTypeEnum.PUBLIC.getCode()),
    ;
    private Integer code;
    private String label;
    private Integer payType;
    //根据code获取payType
    public static Integer getPayType(Integer code) {
        for (WithdrawTypeEnum typeEnum : WithdrawTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getPayType();
            }
        }
        return 999;
    }
}
