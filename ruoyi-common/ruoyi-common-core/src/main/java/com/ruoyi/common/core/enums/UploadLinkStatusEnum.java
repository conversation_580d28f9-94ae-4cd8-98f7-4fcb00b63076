package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/3/24 11:18
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum UploadLinkStatusEnum {
    HAVE_ALREADY_UPLOADED(0, "已上传"),
    HAVEN_T_UPLOADED(1, "未上传"),
    UPLOAD_TO_BE_CONFIRMED(2, "待确认上传"),
    CANCEL_UPLOAD(3, "取消上传"),
    FAIL_TO_UPLOAD(4, "上传失败"),
    NO_UPLOAD_REQUIRED(5, "无需上传"),
    ;
    private Integer code;
    private String label;
}
