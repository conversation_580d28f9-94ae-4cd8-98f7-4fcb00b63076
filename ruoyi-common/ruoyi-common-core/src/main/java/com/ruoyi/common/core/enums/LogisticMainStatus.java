package com.ruoyi.common.core.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.EnumMap;
import java.util.EnumSet;
import java.util.Map;

/**
 * 物流状态枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum LogisticMainStatus {

    NOT_FOUND(1, "NotFound", "查询不到，进行查询操作但没有得到结果，原因请参看子状态。", "查询不到"),
    INFO_RECEIVED(2, "InfoReceived", "收到信息，运输商收到下单信息，等待上门取件。", "收到信息"),
    IN_TRANSIT(3, "InTransit", "运输途中，包裹正在运输途中，具体情况请参看子状态。", "运输途中"),
    EXPIRED(4, "Expired", "运输过久，包裹已经运输了很长时间而仍未投递成功。", "运输过久"),
    AVAILABLE_FOR_PICKUP(5, "AvailableForPickup", "到达待取，包裹已经到达目的地的投递点，需要收件人自取。", "到达待取"),
    OUT_FOR_DELIVERY(6, "OutForDelivery", "派送途中，包裹正在投递过程中。", "派送途中"),
    DELIVERY_FAILURE(7, "DeliveryFailure", "投递失败，包裹尝试派送但未能成功交付，原因请参看子状态。原因可能是：派送时收件人不在家、投递延误重新安排派送、收件人要求延迟派送、地址不详无法派送、因偏远地区不提供派送服务等。", "投递失败"),
    DELIVERED(8, "Delivered", "成功签收，包裹已妥投。", "成功签收"),
    EXCEPTION(9, "Exception", "可能异常，包裹可能被退回，原因请参看子状态。原因可能是：收件人地址错误或不详、收件人拒收、包裹无人认领超过保留期等。包裹可能被海关扣留，常见扣关原因是：包含敏感违禁、限制进出口的物品、未交税款等。包裹可能在运输途中遭受损坏、丢失、延误投递等特殊情况。", "可能异常"),

    /**
     * 自定义物流状态
     */
    NOTICE_SHOOTING(99, "notice_shooting", "通知拍摄", "通知拍摄"),
    ;

    private Integer code;
    private String label;
    private String explain;
    private String sketch;

    public static String getSketchByLabel(String label) {
        for (LogisticMainStatus value : LogisticMainStatus.values()) {
            if (value.getLabel().equals(label)) {
                return value.getSketch();
            }
        }
        return StrUtil.EMPTY;
    }

    public static String getLabelByCode(Integer code) {
        for (LogisticMainStatus value : LogisticMainStatus.values()) {
            if (value.getCode().equals(code)) {
                return value.getLabel();
            }
        }
        return null;
    }

    // 使用静态代码块初始化主状态与子状态的映射关系
    private static final Map<LogisticMainStatus, EnumSet<LogisticSubStatus>> logisticSubStatusMap = new EnumMap<>(LogisticMainStatus.class);

    static {
        logisticSubStatusMap.put(NOT_FOUND, EnumSet.of(LogisticSubStatus.NOT_FOUND_OTHER, LogisticSubStatus.NOT_FOUND_INVALID_CODE));

        logisticSubStatusMap.put(INFO_RECEIVED, EnumSet.of(LogisticSubStatus.INFO_RECEIVED));

        logisticSubStatusMap.put(IN_TRANSIT, EnumSet.of(
                LogisticSubStatus.IN_TRANSIT_PICKED_UP,
                LogisticSubStatus.IN_TRANSIT_OTHER,
                LogisticSubStatus.IN_TRANSIT_DEPARTURE,
                LogisticSubStatus.IN_TRANSIT_ARRIVAL,
                LogisticSubStatus.IN_TRANSIT_CUSTOMS_PROCESSING,
                LogisticSubStatus.IN_TRANSIT_CUSTOMS_RELEASED,
                LogisticSubStatus.IN_TRANSIT_CUSTOMS_REQUIRING_INFORMATION
        ));

        logisticSubStatusMap.put(EXPIRED, EnumSet.of(LogisticSubStatus.EXPIRED_OTHER));

        logisticSubStatusMap.put(AVAILABLE_FOR_PICKUP, EnumSet.of(LogisticSubStatus.AVAILABLE_FOR_PICKUP_OTHER));

        logisticSubStatusMap.put(OUT_FOR_DELIVERY, EnumSet.of(LogisticSubStatus.OUT_FOR_DELIVERY_OTHER));

        logisticSubStatusMap.put(DELIVERY_FAILURE, EnumSet.of(
                LogisticSubStatus.DELIVERY_FAILURE_OTHER,
                LogisticSubStatus.DELIVERY_FAILURE_NOBODY,
                LogisticSubStatus.DELIVERY_FAILURE_SECURITY,
                LogisticSubStatus.DELIVERY_FAILURE_REJECTED,
                LogisticSubStatus.DELIVERY_FAILURE_INVALID_ADDRESS
        ));

        logisticSubStatusMap.put(DELIVERED, EnumSet.of(LogisticSubStatus.DELIVERED_OTHER));

        logisticSubStatusMap.put(EXCEPTION, EnumSet.of(
                LogisticSubStatus.EXCEPTION_OTHER,
                LogisticSubStatus.EXCEPTION_RETURNING,
                LogisticSubStatus.EXCEPTION_RETURNED,
                LogisticSubStatus.EXCEPTION_NOBODY,
                LogisticSubStatus.EXCEPTION_SECURITY,
                LogisticSubStatus.EXCEPTION_DAMAGE,
                LogisticSubStatus.EXCEPTION_REJECTED,
                LogisticSubStatus.EXCEPTION_DELAYED,
                LogisticSubStatus.EXCEPTION_LOST,
                LogisticSubStatus.EXCEPTION_DESTROYED,
                LogisticSubStatus.EXCEPTION_CANCEL
        ));
    }

    // 获取子状态集合
    public static EnumSet<LogisticSubStatus> getSubStatus(LogisticMainStatus mainState) {
        return logisticSubStatusMap.get(mainState);
    }

    public static LogisticMainStatus getMainStatusBySubStatusLabel(String subStatusLabel) {
        for (Map.Entry<LogisticMainStatus, EnumSet<LogisticSubStatus>> mapEntry : logisticSubStatusMap.entrySet()) {
            for (LogisticSubStatus logisticSubStatus : mapEntry.getValue()) {
                if (logisticSubStatus.getLabel().equals(subStatusLabel)) {
                    return mapEntry.getKey();
                }
            }
        }
        return null;
    }


    // 子状态枚举
    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    public enum LogisticSubStatus {
        NOT_FOUND_OTHER(101, "NotFound_Other", "运输商没有返回信息。", "运输商没有返回信息"),
        NOT_FOUND_INVALID_CODE(102, "NotFound_InvalidCode", "物流单号无效，无法进行查询。", "物流单号无效"),
        INFO_RECEIVED(201, "InfoReceived", "收到信息，暂无细分含义与主状态一致。", "收到信息"),
        IN_TRANSIT_PICKED_UP(301, "InTransit_PickedUp", "已揽收，运输商已从发件人处取回包裹。", "已揽收"),
        IN_TRANSIT_OTHER(302, "InTransit_Other", "其它情况，暂无细分除当前已知子状态之外的情况。", "其它情况"),
        IN_TRANSIT_DEPARTURE(303, "InTransit_Departure", "已离港，货物离开起运地（国家/地区）港口。", "已离港"),
        IN_TRANSIT_ARRIVAL(304, "InTransit_Arrival", "已到港，货物到达目的地（国家/地区）港口。", "已到港"),
        IN_TRANSIT_CUSTOMS_PROCESSING(305, "InTransit_CustomsProcessing", "清关中，货物在海关办理进入或出口的相关流程中。", "清关中"),
        IN_TRANSIT_CUSTOMS_RELEASED(306, "InTransit_CustomsReleased", "清关完成，货物在海关完成了进入或出口的流程。", "清关完成"),
        IN_TRANSIT_CUSTOMS_REQUIRING_INFORMATION(307, "InTransit_CustomsRequiringInformation", "需要资料，在清关中流程中承运人需要提供相关资料才能完成清关。", "需要资料"),
        EXPIRED_OTHER(401, "Expired_Other", "运输过久，暂无细分含义与主状态一致。", "运输过久"),
        AVAILABLE_FOR_PICKUP_OTHER(501, "AvailableForPickup_Other", "到达待取，暂无细分含义与主状态一致。", "到达待取"),
        OUT_FOR_DELIVERY_OTHER(601, "OutForDelivery_Other", "派送途中，暂无细分含义与主状态一致。", "派送途中"),
        DELIVERY_FAILURE_OTHER(701, "DeliveryFailure_Other", "其它情况，暂无细分除当前已知子状态之外的情况。", "其它情况"),
        DELIVERY_FAILURE_NOBODY(702, "DeliveryFailure_NoBody", "找不到收件人，派送中的包裹暂时无法联系上收件人，导致投递失败。", "找不到收件人"),
        DELIVERY_FAILURE_SECURITY(703, "DeliveryFailure_Security", "安全原因，派送中发现的包裹安全、清关、费用问题，导致投递失败。", "安全原因"),
        DELIVERY_FAILURE_REJECTED(704, "DeliveryFailure_Rejected", "拒收，收件人因某些原因拒绝接收包裹，导致投递失败。", "拒收"),
        DELIVERY_FAILURE_INVALID_ADDRESS(705, "DeliveryFailure_InvalidAddress", "地址错误，由于收件人地址不正确，导致投递失败。", "地址错误"),
        DELIVERED_OTHER(801, "Delivered_Other", "成功签收，暂无细分含义与主状态一致。", "成功签收"),
        EXCEPTION_OTHER(901, "Exception_Other", "其它情况，暂无细分除当前已知子状态之外的情况。", "其它情况"),
        EXCEPTION_RETURNING(902, "Exception_Returning", "退件中，包裹正在送回寄件人的途中。", "退件中"),
        EXCEPTION_RETURNED(903, "Exception_Returned", "退件签收，寄件人已成功收到退件。", "退件签收"),
        EXCEPTION_NOBODY(904, "Exception_NoBody", "找不到收件人，在派送之前发现的收件人信息异常。", "找不到收件人"),
        EXCEPTION_SECURITY(905, "Exception_Security", "安全原因，在派送之前发现异常，包含安全、清关、费用问题。", "安全原因"),
        EXCEPTION_DAMAGE(906, "Exception_Damage", "损坏，在承运过程中发现货物损坏了。", "损坏"),
        EXCEPTION_REJECTED(907, "Exception_Rejected", "拒收，在派送之前接收到有收件人拒收情况。", "拒收"),
        EXCEPTION_DELAYED(908, "Exception_Delayed", "延误，因各种情况导致的可能超出原定的运输周期。", "延误"),
        EXCEPTION_LOST(909, "Exception_Lost", "丢失，因各种情况导致的货物丢失。", "丢失"),
        EXCEPTION_DESTROYED(910, "Exception_Destroyed", "销毁，因各种情况无法完成交付的货物并进行销毁。", "销毁"),
        EXCEPTION_CANCEL(911, "Exception_Cancel", "取消，因为各种情况物流订单被取消了。", "取消"),
        ;

        private Integer code;
        private String label;
        private String explain;
        private String sketch;

        public static String getSketchByLabel(String label) {
            for (LogisticSubStatus value : LogisticSubStatus.values()) {
                if (value.getLabel().equals(label)) {
                    return value.getSketch();
                }
            }
            return null;
        }
    }
}
