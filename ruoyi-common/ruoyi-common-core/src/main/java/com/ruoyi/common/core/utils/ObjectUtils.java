package com.ruoyi.common.core.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.comparator.CompareUtil;
import io.swagger.annotations.ApiModelProperty;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * 对象工具类
 *
 * <AUTHOR>
 * @date 2024/8/28 10:14
 */
public class ObjectUtils {
    /**
     * @param oldObject 旧对象
     * @param newObject 新对象
     * @return 包含不同字段的对象
     * <p>
     * 例：
     * oldObject(id=null,name="张三",age=1,deptIds=[1,2])
     * newObject(id=null,name="李四",age=null,deptIds=[1,2,3])
     * return{name="李四",age=null,deptIds=[1,2,3]}
     */
    public static Map<String, Object> compareAndReturnDifferences(Object oldObject, Object newObject) throws IllegalAccessException {
        if (oldObject == null || newObject == null || !oldObject.getClass().equals(newObject.getClass())) {
            throw new IllegalArgumentException("对象必须为非空且具有相同类型！");
        }

        Class<?> clazz = newObject.getClass();

        Map<String, Object> result = new HashMap<>();

        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);

            Object value1 = field.get(oldObject);
            Object value2 = field.get(newObject);

            if (CompareUtil.compare(value1, value2, false) != 0) {
                result.put(field.getName(), value2);
            }
        }

        return result;
    }

    /**
     * @param oldObject 旧对象
     * @param newObject 新对象
     * @param fieldsToCompare 比较的字段
     * @return 包含不同字段的对象
     * <p>
     * 例：
     * oldObject(id=null,name="张三",age=1,deptIds=[1,2])
     * newObject(id=null,name="李四",age=null,deptIds=[1,2,3])
     * return{name="李四",age=null,deptIds=[1,2,3]}
     */
    public static Map<String, Object> compareAndReturnDifferences(Object oldObject, Object newObject, String... fieldsToCompare) throws IllegalAccessException {
        if (oldObject == null || newObject == null || !oldObject.getClass().equals(newObject.getClass())) {
            throw new IllegalArgumentException("对象必须为非空且具有相同类型！");
        }

        Class<?> clazz = newObject.getClass();
        Map<String, Object> result = new HashMap<>();

        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);

            // 只比较指定的字段
            if (CollUtil.toList(fieldsToCompare).contains(field.getName())) {
                Object value1 = field.get(oldObject);
                Object value2 = field.get(newObject);

                if (CompareUtil.compare(value1, value2, false) != 0) {
                    result.put(field.getName(), value2);
                }
            }
        }

        return result;
    }


    public static Map<String, Object> convertObjectToMap(Object obj) {
        Map<String, Object> map = new HashMap<>();

        for (Field field : obj.getClass().getDeclaredFields()) {
            try {
                field.setAccessible(true);
                Object value = field.get(obj);
                if (value == null) {
                    continue;
                }
                if (value instanceof Iterable) {
                    if (CollUtil.isEmpty((Iterable)value)) {
                        continue;
                    }
                }
                map.put(field.getName(), field.get(obj));
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
        return map;
    }



    private static String getFieldKey(Field field) {
        // 检查是否存在@ApiModelProperty注解
        ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
        if (annotation != null) {
            String value = annotation.value();
            if (value != null && !value.trim().isEmpty()) {
                String trimmedValue = value.trim(); // 清理首尾空格
                if (!trimmedValue.isEmpty()) {
                    return truncateAtFirstBracket(trimmedValue);
                }
            }
        }
        return field.getName(); // 默认返回字段名
    }

    public static String truncateAtFirstBracket(String input) {
        if (input == null) {
            return null;
        }

        // 查找中文左括号和英文左括号的位置
        int chineseBracketIndex = input.indexOf('（');
        int englishBracketIndex = input.indexOf('(');

        // 确定第一个有效截断点
        int truncateIndex = -1;
        if (chineseBracketIndex != -1 && englishBracketIndex != -1) {
            truncateIndex = Math.min(chineseBracketIndex, englishBracketIndex);
        } else if (chineseBracketIndex != -1) {
            truncateIndex = chineseBracketIndex;
        } else {
            truncateIndex = englishBracketIndex;
        }

        return (truncateIndex != -1)
                ? input.substring(0, truncateIndex).trim()  // 移除尾部空格
                : input;
    }


    public static Map<String, Object> compareAndReturnDifferencesKeyAnnotation(Object oldObject, Object newObject) throws IllegalAccessException {
        if (oldObject == null || newObject == null || !oldObject.getClass().equals(newObject.getClass())) {
            throw new IllegalArgumentException("对象必须为非空且具有相同类型！");
        }

        Class<?> clazz = newObject.getClass();
        Map<String, Object> result = new HashMap<>();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true); // 允许访问私有字段和注解

            Object value1 = field.get(oldObject);
            Object value2 = field.get(newObject);

            if (CompareUtil.compare(value1, value2, false) != 0) {
                // 获取字段名或@ApiModelProperty的value
                String key = getFieldKey(field);
                result.put(key, value2);
            }
        }
        return result;
    }

}
