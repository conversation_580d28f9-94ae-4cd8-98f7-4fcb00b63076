package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-28 14:07
 **/
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PhoneStatusEnum {
    NEW_ACCOUNT(1, "新账号"),
    BINDING_OWNER_ACCOUNT(2, "该手机已绑定主账号"),
    BINDING_OTHER_ACCOUNT(3, "该手机已绑定其它企业"),
    BINDING_ACCOUNT(4, "该手机已绑定本企业"),
    ;
    private Integer code;
    private String label;
}
