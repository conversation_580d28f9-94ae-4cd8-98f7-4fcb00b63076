package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;


/**
 * 跟进状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum HandleStatusEnum {

    UN_NOTIFIED(0, "未通知", FollowStatusEnum.NEED_HANDLE, "成功匹配模特"),
    NOTIFIED(1, "已通知", FollowStatusEnum.TEMP_HOLD, "#操作人#标记通知"),

    DELAY_SHIPPING(2, "延迟发货", FollowStatusEnum.TEMP_HOLD, "#操作人#标记延迟发货，预计发货时间：{}"),
    DELAY_REMINDER(3, "延迟发货提醒", FollowStatusEnum.NEED_HANDLE, "已到预计发货时间"),
    DELAY_REMINDER_OTHER(3, "延迟发货提醒", FollowStatusEnum.NEED_HANDLE, "已标记延迟发货超过{}天"),

    DELAY_NOTIFIED(4, "延迟发货已提醒", FollowStatusEnum.TEMP_HOLD, "#操作人#标记延迟发货已提醒"),
    URGE_SHIPPING_REMINDER(5, "催发货提醒", FollowStatusEnum.NEED_HANDLE, "距上次提醒已超过{}天"),

    URGE_SHIPPING_NOTIFIED(6, "催发货已提醒", FollowStatusEnum.TEMP_HOLD, "#操作人#标记通知"),

    ADDRESS_CHANGE_NOTICE(7, "地址变更通知", FollowStatusEnum.NEED_HANDLE, "{}模特地址被修改"),
    CHANGE_NOTIFIED(8, "变更已通知", FollowStatusEnum.TEMP_HOLD, "#操作人#标记通知地址变更"),
    REMARK_REPLENISH(9, "补充说明", FollowStatusEnum.TEMP_HOLD, "#操作人#补充说明：{}"),

    NOTIFIED_SHIPPING_REMINDER(11, "标记发货提醒", FollowStatusEnum.NEED_HANDLE, "距上次提醒标记发货已超过{}天"),

    NOTIFIED_SHIPPING(10, "标记发货", FollowStatusEnum.TEMP_HOLD, "#操作人#标记标记发货"),
    NOTIFIED_SHIPPING_NOTIFIED(12, "标记发货已提醒", FollowStatusEnum.TEMP_HOLD, "#操作人#标记标记发货已提醒"),

    UN_NOTIFIED_CONFIRM_MODEL(13, "通知确认模特", FollowStatusEnum.NEED_HANDLE, "成功匹配模特"),
    URGE_CONFIRM_MODEL_UN_NOTIFIED(15, "催确认模特提醒", FollowStatusEnum.NEED_HANDLE, "距上次提醒已超过{}天"),

    NOTIFIED_CONFIRM_MODEL(14, "已通知确认模特", FollowStatusEnum.TEMP_HOLD, "#操作人#通知确认模特"),
    URGE_CONFIRM_MODEL_NOTIFIED(16, "已通知催确认模特", FollowStatusEnum.TEMP_HOLD, "#操作人#催确认模特已提醒"),


    ;

    private Integer code;
    private String label;
    private FollowStatusEnum followStatusEnum;
    private String description;

    public static HandleStatusEnum getHandleStatusEnumByCode(Integer code,Integer isDefault) {
        if (HandleStatusEnum.DELAY_REMINDER.code.equals(code)){
            if (StatusTypeEnum.YES.getCode().equals(isDefault)) {
                return DELAY_REMINDER_OTHER;
            }else {
                return DELAY_REMINDER;
            }
        }

        for (HandleStatusEnum e : HandleStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static List<HandleStatusEnum> needHandleStatusList = new ArrayList<>();
    public static List<HandleStatusEnum> tempHoldStatusList = new ArrayList<>();

    static {
        for (HandleStatusEnum e : HandleStatusEnum.values()) {
            if (FollowStatusEnum.NEED_HANDLE == e.getFollowStatusEnum()) {
                needHandleStatusList.add(e);
            }else if (FollowStatusEnum.TEMP_HOLD == e.getFollowStatusEnum()){
                tempHoldStatusList.add(e);
            }
        }
    }
}
