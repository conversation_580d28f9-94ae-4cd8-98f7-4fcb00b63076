package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 市场渠道平台枚举
 *
 * <AUTHOR>
 * @date 2024/9/25 10:19
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum MarketingPlatformEnum {

    BAI_DU(1, "百度"),
    LITTLE_RED_BOOK(2, "小红书"),
    CSJPLATFORM(3, "穿山甲"),
    YLH(4, "优量汇"),
    TOU_TIAO(5, "今日头条"),
    TENCENT(6, "腾讯"),
    OTHER(7, "其他"),
    ;

    private Integer code;
    private String label;
}
