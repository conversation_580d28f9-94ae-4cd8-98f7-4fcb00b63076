package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 模特数据统计-模特排单情况枚举
 *
 * <AUTHOR>
 * @date 2025/6/30 14:58
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ModelOrderScheduledDataEnum {
    FIRST_GEAR(1, "0单", new BigDecimal("0"), new BigDecimal("0")),
    SECOND_GEAR(2, "1-3单", new BigDecimal("1"), new BigDecimal("3")),
    THIRD_GEAR(3, "4-7单", new BigDecimal("4"), new BigDecimal("7")),
    FOURTH_GEAR(4, "8-10单", new BigDecimal("8"), new BigDecimal("10")),
    FIFTH_GEAR(5, "11-20单", new BigDecimal("11"), new BigDecimal("20")),
    SIXTH_GEAR(6, "21-30单", new BigDecimal("21"), new BigDecimal("30")),
    SEVENTH_GEAR(7, "31单以上", new BigDecimal("31"), new BigDecimal("9999999")),
    ;


    private Integer code;
    private String label;
    private BigDecimal begin;
    private BigDecimal end;


    public static boolean isInSection(String label, BigDecimal section) {
        for (ModelOrderScheduledDataEnum value : values()) {
            if (value.getLabel().equals(label) && section.compareTo(value.begin) >= 0 && section.compareTo(value.end) <= 0) {
                return true;
            }
        }
        return false;
    }

    public static List<String> getLabels() {
        List<String> labels = new ArrayList<>();

        for (ModelOrderScheduledDataEnum value : values()) {
            labels.add(value.label);
        }
        return labels;
    }
}
