package com.ruoyi.common.core.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 视频格式类型枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum VideoFormatEnum {
    LANDSCAPE(1, "横屏16：9", "横屏Horizontal recording"),
    PORTRAIT(2, "竖屏9：16", "竖屏Vertical shooting"),
    ;

    private Integer code;
    private String label;
    private String description;

    public static String getDescription(Integer code) {
        for (VideoFormatEnum item : VideoFormatEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getDescription();
            }
        }
        return StrUtil.EMPTY;
    }
}
