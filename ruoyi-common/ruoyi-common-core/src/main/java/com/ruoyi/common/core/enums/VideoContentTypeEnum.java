package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 视频内容类型枚举
 *
 * <AUTHOR>
 * @date 2024/5/21 14:47
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum VideoContentTypeEnum {
    REQUIRE(1, "拍摄建议（原拍摄要求）"),

    CAUTIONS(2, "模特要求（原匹配模特注意事项）"),
    CLIPS_REQUIRED(3, "剪辑要求"),
    SELLING_POINT_PRODUCT(4, "产品卖点"),
    ORDER_SPECIFICATION_REQUIRE(5, "商品规格要求"),
    PARTICULAR_EMPHASIS(6, "特别强调"),
    PARTICULAR_EMPHASIS_PIC(7, "特别强调图片"),
    AUDIT_TYPE_NINE(9, "用于比较内容"),
    ;


    private Integer code;
    private String label;
}
