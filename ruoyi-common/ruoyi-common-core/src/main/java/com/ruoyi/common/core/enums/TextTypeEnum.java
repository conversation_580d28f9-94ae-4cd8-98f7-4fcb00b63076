package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :文本类型枚举
 * @create :2024-11-20 10:42
 **/
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum TextTypeEnum {
    PROTOCOL(0, "协议信息"),
    HELP_COMMON_PROBLEM(1, "帮助中心-常见问题"),
    HELP_NOVICE_GUIDE(2, "帮助中心-新手指南"),
    ;
    private Integer code;
    private String label;

    public List<Integer> getHelpCodes() {
        return Arrays.asList(HELP_COMMON_PROBLEM.code, HELP_NOVICE_GUIDE.code);
    }
}
