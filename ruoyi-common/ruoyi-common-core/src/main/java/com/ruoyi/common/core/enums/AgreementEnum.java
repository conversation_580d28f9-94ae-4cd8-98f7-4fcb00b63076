package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 协议枚举
 * <AUTHOR>
 * @date 2024/8/19 16:54
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum AgreementEnum {
    USER_AGREEMENT(1, "用户协议"),
    PAYMENT_AGREEMENT(2, "支付协议"),
    PRIVACY_AGREEMENT(3, "隐私协议"),
    PLATFORM_AGREEMENT(4, "平台协议"),
    FISSION_AGREEMENT(3015, "裂变活动规则"),
    ;
    private Integer code;
    private String label;
}
