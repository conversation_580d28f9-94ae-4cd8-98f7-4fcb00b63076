package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/18
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderPoolPreselectedTimeEnum {
    WITHIN_24_HOURS(1, "24小时内"),

    ONE_TO_TWO_DAYS(2, "1~2天"),

    TWO_TO_THREE_DAYS(3, "2~3天"),

    MORE_THAN_THREE_DAYS(4, "3天以上"),
    ;

    private Integer code;
    private String label;
}
