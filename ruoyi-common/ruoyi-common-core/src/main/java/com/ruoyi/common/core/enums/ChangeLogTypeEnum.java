package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 视频订单记录类型
 *
 * <AUTHOR>
 * @date 2024/8/28 11:18
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ChangeLogTypeEnum {
    INIT_LOG(1, "初始记录"),
    EDIT_VIDEO_LOG(2, "待匹配订单时修改"),
    AGREE_EDIT_LOG(3, "商家同意后修改"),
    AUDIT_EDIT_LOG(4, "审核订单时修改"),
    NEED_FILLED_EDIT_VIDEO_LOG(5, "待发货时修改"),
    UN_FINISHED_EDIT_VIDEO_LOG(6, "待完成时修改"),
    ;
    private Integer code;
    private String label;
}
