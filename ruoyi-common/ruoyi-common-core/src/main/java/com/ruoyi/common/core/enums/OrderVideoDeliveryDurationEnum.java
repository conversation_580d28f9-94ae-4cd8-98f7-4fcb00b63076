package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单发货时长数据分析枚举
 *
 * <AUTHOR>
 * @date 2025/6/9 16:38
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderVideoDeliveryDurationEnum {
    FIRST_GEAR(1, "1天内", new BigDecimal("0"), new BigDecimal("1")),
    SECOND_GEAR(2, "1-2天", new BigDecimal("1"), new BigDecimal("2")),
    THIRD_GEAR(3, "2-3天", new BigDecimal("2"), new BigDecimal("3")),
    FOURTH_GEAR(4, "3-5天", new BigDecimal("3"), new BigDecimal("5")),
    FIFTH_GEAR(5, "5-15天", new BigDecimal("5"), new BigDecimal("15")),
    SIXTH_GEAR(6, "超15天", new BigDecimal("15"), new BigDecimal("9999999")),
    ;

    private Integer code;
    private String label;
    private BigDecimal begin;
    private BigDecimal end;

    public static boolean isInSection(String label, BigDecimal section) {
        for (OrderVideoDeliveryDurationEnum value : values()) {
            if (value.getLabel().equals(label) && section.compareTo(value.begin) > 0 && section.compareTo(value.end) <= 0) {
                return true;
            }
        }
        return false;
    }

    public static List<String> getLabels() {
        List<String> labels = new ArrayList<>();

        for (OrderVideoDeliveryDurationEnum value : values()) {
            labels.add(value.label);
        }
        return labels;
    }
}
