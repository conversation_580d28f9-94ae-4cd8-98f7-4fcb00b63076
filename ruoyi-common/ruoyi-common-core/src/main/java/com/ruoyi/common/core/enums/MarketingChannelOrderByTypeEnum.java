package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;


/**
 * 市场渠道排序方式枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum MarketingChannelOrderByTypeEnum {
    UNIQUE_VISITOR_NUM(1, "visit.unique_visitor", "独立访客数"),
    REGISTER_NUM(2, "register.register_num", "注册数"),
    MEMBER_NUM(3, "ommc.member_num", "会员成交数"),
    MEMBER_TOTAL_AMOUNT(4, "ommc.real_pay_amount", "会员总金额"),
    WECHAT_COUNT(5, "wceu_c.wechat_count", "添加企微数"),
    ;
    private Integer code;
    private String value;
    private String label;


    public static String getValue(Integer code) {
        for (MarketingChannelOrderByTypeEnum payTypeEnum : MarketingChannelOrderByTypeEnum.values()) {
            if (payTypeEnum.getCode().equals(code)) {
                return payTypeEnum.value;
            }
        }
        return null;
    }
}
