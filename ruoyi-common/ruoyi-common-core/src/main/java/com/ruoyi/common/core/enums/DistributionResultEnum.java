package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/17 15:13
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum DistributionResultEnum {
    PENDING(1, "待处理"),
    WANT(2, "MT想要"),
    WANT_NOT(3, "MT不想要"),
    CANCEL_DISTRIBUTION(4, "取消分发"),
    ;
    private Integer code;
    private String label;
}
