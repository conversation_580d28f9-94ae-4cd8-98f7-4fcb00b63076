package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 模特合作深度枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ModelCooperationEnum {
    ORDINARY(0, "一般模特", BigDecimal.ZERO),
    QUALITY(1, "优质模特", new BigDecimal("7.8")),
    MODERATE(2, "中度模特", BigDecimal.ZERO),
    ;

    private Integer code;
    private String label;
    private BigDecimal score;

    public static ModelCooperationEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ModelCooperationEnum cooperationEnum : ModelCooperationEnum.values()) {
            if (cooperationEnum.getCode().equals(code)) {
                return cooperationEnum;
            }
        }
        return null;
    }
}
