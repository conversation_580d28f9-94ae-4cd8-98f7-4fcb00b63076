package com.ruoyi.common.core.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 事件执行对象（1:商家,2:运营,3:模特,9:系统）
 *
 * <AUTHOR>
 * @date 2024/9/2 16:59
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum EventExecuteObjectEnum {
    COMPANY(1, "商家"),
    BACK(2, "运营"),
    MODEL(3, "模特"),
    SYSTEM(9, "系统"),
    ;
    private Integer code;
    private String label;
}
