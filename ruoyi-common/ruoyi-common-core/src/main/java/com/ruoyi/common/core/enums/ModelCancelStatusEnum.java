package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 模特取消合作状态枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ModelCancelStatusEnum {

    OUR(0,"我们取消合作"),
    MODEL(1,"模特取消合作");
    ;

    private Integer code;
    private String label;

    public static List<Integer> getCodes() {
        List<Integer> codes = new ArrayList<>();
        for (ModelCancelStatusEnum value : ModelCancelStatusEnum.values()) {
            codes.add(value.getCode());
        }
        return codes;
    }

    public static String getLabelByCode(Integer code) {
        for (ModelCancelStatusEnum value : ModelCancelStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.label;
            }
        }
        return null;
    }
}
