package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;


/**
 * 分销渠道排序方式枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ChannelOrderByTypeEnum {
    ADD_WE_CHAT_NUM(1, "wechat.wechatNum", "邀请数"),
    REGISTER_NUM(2, "register.registerNum", "注册数"),
    MEMBER_NUM(3, "t.memberNum", "会员成交数"),
    BROKE_RAGE(4, "dc.broke_rage", "结算比例"),
    REAL_PAY_AMOUNT(5, "dco.real_pay_amount", "订单成交额"),
    UN_SETTLE_AMOUNT(6, "t.unSettleAmount", "待结算金额"),
    SETTLE_AMOUNT(7, "t.settleAmount", "已结算金额"),
    REAL_SETTLE_AMOUNT(8, "t.realSettleAmount", "实际结算金额"),
    SINGLE_CHANNEL_AMOUNT(9, "t.real_pay_amount", "单个渠道的会员订单成交金额")

    ;
    private Integer code;
    private String value;
    private String label;


    public static String getValue(Integer code) {
        for (ChannelOrderByTypeEnum payTypeEnum : ChannelOrderByTypeEnum.values()) {
            if (payTypeEnum.getCode().equals(code)) {
                return payTypeEnum.value;
            }
        }
        return null;
    }
    public static String getLabel(Integer code) {
        for (ChannelOrderByTypeEnum payTypeEnum : ChannelOrderByTypeEnum.values()) {
            if (payTypeEnum.getCode().equals(code)) {
                return payTypeEnum.label;
            }
        }
        return null;
    }
}
