package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Title: SkipTypeEnum
 * <AUTHOR>
 * @Package com.ruoyi.common.core.enums
 * @Date 2024/8/20 16:44
 * @description: 跳转类型
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SkipTypeEnum implements Serializable {
    //1-视频分组、2-视频链接、3-不跳转
    GROUP(1, "视频分组"),
    VIDEO(2, "视频链接"),
    NO_SKIP(3, "不跳转"),
    ;
    private Integer code;
    private String label;
}
