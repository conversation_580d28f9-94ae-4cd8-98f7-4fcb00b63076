package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 套餐类型枚举
 *
 * <AUTHOR>
 * @date 2024/9/23 16:39
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PackageTypeEnum {
    QUARTER(0, "会员季度套餐", 3, "119.7"),
    YEAR(1, "会员年度套餐", 12, "238.8"),
    TRIENNIUM(2, "会员三年套餐", 36, "644.4"),
    ;
    private Integer code;
    private String label;
    private Integer month;
    private String USD;

    public static String getUSDByCode(Integer code) {
        for (PackageTypeEnum packageTypeEnum : PackageTypeEnum.values()) {
            if (packageTypeEnum.getCode().equals(code)) {
                return packageTypeEnum.getUSD();
            }
        }
        return "0";
    }

    public static Integer getMonthByCode(Integer code) {
        for (PackageTypeEnum packageTypeEnum : PackageTypeEnum.values()) {
            if (packageTypeEnum.getCode().equals(code)) {
                return packageTypeEnum.getMonth();
            }
        }
        return null;
    }
}
