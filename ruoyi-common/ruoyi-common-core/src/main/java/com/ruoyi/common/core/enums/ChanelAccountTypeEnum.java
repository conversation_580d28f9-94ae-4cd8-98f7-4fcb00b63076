package com.ruoyi.common.core.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 渠道账号类型枚举
 *
 * <AUTHOR>
 * @date 2024/9/25
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ChanelAccountTypeEnum {

    NORMAL(1,"正常"),
    EXPIRED(2,"已过期"),
    UNBIND(3,"已解绑"),
    ;

    private Integer code;
    private String label;

    public static ChanelAccountTypeEnum findByCode(Integer code) {
        for (ChanelAccountTypeEnum value : ChanelAccountTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
