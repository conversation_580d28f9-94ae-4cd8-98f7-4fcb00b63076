package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 订单类型枚举
 * <AUTHOR>
 * @date 2024/6/24 11:06
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderTypeEnum {

    VIDEO_ORDER(0, "视频订单"),
    VIP_ORDER(1, "会员订单"),
    VIDEO_CART(2, "购物车订单"),
    PREPAY_ORDER(3, "线下钱包充值"),
    MERGE_ORDER(4, "合并单"),
    ONLINE_RECHARGE(5, "线上钱包充值"),
    ;
    private Integer code;
    private String label;

    public static String getLabelByCode(Integer code) {
        for (OrderTypeEnum orderTypeEnum : OrderTypeEnum.values()) {
            if (orderTypeEnum.getCode().equals(code)) {
                return orderTypeEnum.getLabel();
            }
        }
        return null;
    }

}
