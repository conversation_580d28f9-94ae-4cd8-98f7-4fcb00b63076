package com.ruoyi.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "thread-pool")
@Data
public class ThreadPoolProperties {
    /**
     * 核心线程数
     */
    private Integer corePoolSize;

    /**
     * 最大线程数
     */
    private Integer maxPoolSize;

    /**
     * 缓存队列
     */
    private Integer queueCapacity;

    /**
     * 线程的空闲时间（单位：秒）
     */
    private Integer keepAliveSeconds;
}