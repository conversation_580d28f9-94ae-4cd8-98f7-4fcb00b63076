package com.ruoyi.common.core.enums;

/**
 * 登录状态枚举
 *
 * <AUTHOR>
 * @date 2024/6/19
 */
public enum WxChatLoginStatusEnum {
    WAITING(0, "waiting", "等待扫码"),

    LOGINING(1, "logining", "已扫码未添加客服"),

    LOGIN_SUCCESS(2, "loginSuccess", "登录成功"),

    ACCOUNT_DISABLE(3, "disable", "账号禁用"),

    EXPIRE(4, "expire", "二维码过期"),

    UNKNOWN(4, "unknown", "未知状态"),

    BINDING(5, "banding", "账号已绑定"),

    UN_REGISTER(6, "un_register", "未注册"),

    LOGIN_NO_PHONE(11, "login_no_phone", "已添加客服未绑定手机号"),

    CAPTCHA_FAULT_ERROR(12, "captcha_fault_error", "验证码错误异常"),

    CAPTCHA_BLANK_ERROR(13, "captcha_blank_error", "验证码为空异常"),

    PHONE_REPEAT_ERROR(14, "phone_repeat_error", "手机号重复异常"),

    LOGIN_NO_WE_CHAT(15, "login_no_we_chat", "已存在手机号未绑定微信"),

    LOGIN_NO_CHANNEL(16, "login_no_channel", "不存在有效渠道信息"),
    LOGIN_CHANNEL_NO_MEMBER(17, "login_channel_no_member", "渠道登录非会员"),

    LOGIN_NO_PHONE_PLUS(17, "login_no_phone", "未绑定手机号"),

    WECHAT_BINDING(18, "wechat_binding", "微信号已绑定"),

    PHONE_BINDING(19, "phone_binding", "手机号已绑定"),

    ALIYUN_VERIFY_PHONE_ERROR(20, "aliyun_verify_phone_error", "阿里云校验手机号失败"),

    SNAP_USER(103,"snap_user","快照页用户")
    ;

    private Integer code;
    private String value;
    private String desc;

    WxChatLoginStatusEnum(Integer code, String value, String desc) {
        this.code = code;
        this.value = value;
        this.desc = desc;
    }

    public static WxChatLoginStatusEnum getStatusByCode(Integer code) {
        for (WxChatLoginStatusEnum e : WxChatLoginStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return EXPIRE;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(Integer code) {
        for (WxChatLoginStatusEnum e : WxChatLoginStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getDesc();
            }
        }
        return UNKNOWN.getDesc();
    }

}
