package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 账号类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BizUserAccountTypeEnum {

    COMMON(0, "普通账号"),
    OWNER_ACCOUNT(1, "主账号"),
    ACCOUNT(2, "子账号"),

    ;
    private Integer code;
    private String label;
    public static BizUserAccountTypeEnum getByCode(Integer code) {
        for (BizUserAccountTypeEnum item : BizUserAccountTypeEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }
}
