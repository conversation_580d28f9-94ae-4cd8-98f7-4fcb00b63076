package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-20 13:36
 **/
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ChannelDiscountTypeEnum {
    FIXED_AMOUNT(1, "固定金额"),
    FIXED_RATIO(2, "固定比例"),
            ;


    private Integer code;
    private String label;


    /**
     * 根据code找label
     */
    public static String getLabel(Integer code) {
        for (ChannelDiscountTypeEnum e : ChannelDiscountTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getLabel();
            }
        }
        return null;
    }
}
