package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 性别枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SexEnum {
    MAN(1, "男性"),
    WOMAN(0, "女性"),
    ;
    private Integer code;
    private String label;

    /**
     * 根据code找label
     */
    public static String getLabel(Integer code) {
        for (SexEnum e : SexEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getLabel();
            }
        }
        return null;
    }
}
