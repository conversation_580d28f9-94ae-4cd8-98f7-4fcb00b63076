package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/17 18:18
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CurrencyEnum {
    CNY(1, "CNY", "人民币"),
    CNH(2, "CNH", "离岸人民币"),
    USD(3, "USD", "美元"),
    AUD(4, "AUD", "澳元"),
    CAD(5, "CAD", "加币"),
    GBP(6, "GBP", "英镑"),
    HKD(7, "HKD", "港币"),
    JPY(8, "JPY", "日元"),
    NZD(9, "NZD", "新西兰元"),
    SGD(10, "SGD", "新加坡元"),
    ;
    private Integer code;
    private String unit;
    private String label;
}
