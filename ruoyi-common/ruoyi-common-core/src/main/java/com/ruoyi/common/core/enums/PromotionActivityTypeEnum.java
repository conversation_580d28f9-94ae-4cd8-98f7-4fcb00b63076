package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 优惠活动枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PromotionActivityTypeEnum {

    ORDER_COUNT_FULL_REDUCTION(1, "满5单减100", BigDecimal.valueOf(20), null),
    MEMBER_ORDER_RENEW_AT_HALF_PRICE(2, "会员订单临期续费半价优惠", null, new BigDecimal("0.5")),
    SEED_CODE_DISTRIBUTION_DISCOUNT(3, "渠道优惠", null, null),
    MONTH_FIRST_ORDER_DISCOUNTED(4, "每月首单立减", null, null),
    SEED_CODE_FISSION_DISCOUNT(5, "裂变优惠", null, null),
    ;
    private Integer code;
    private String label;
    private BigDecimal amount;
    private BigDecimal discountRatio;

    public static PromotionActivityTypeEnum getByCode(Integer code){
        for (PromotionActivityTypeEnum e : PromotionActivityTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
