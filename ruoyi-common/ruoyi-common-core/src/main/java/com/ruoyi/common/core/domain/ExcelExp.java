package com.ruoyi.common.core.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-11-08 09:35
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ExcelExp {

    /**
     * sheet的名称
     */
    private String fileName;
    /**
     * sheet里的标题
     */
    private String[] handers;
    /**
     * sheet里的数据集
     */
    private List dataset;
    private Class clazz;

    public ExcelExp(String fileName, List dataset, Class clazz) {
        this.fileName = fileName;
        this.dataset = dataset;
        this.clazz = clazz;
    }
}
